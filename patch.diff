From 0ede826f96cd95a8e9c0259902e366751b0fc1f1 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Tue, 24 Jun 2025 11:56:50 -0400
Subject: [PATCH 1/6] Only use API override query param if not in production

---
 src/app/domain/whitelabel.service.ts | 4 +++-
 src/environments/environment.ts      | 4 ++++
 2 files changed, 7 insertions(+), 1 deletion(-)

diff --git a/src/app/domain/whitelabel.service.ts b/src/app/domain/whitelabel.service.ts
index a9cb6938efa..d5905b82905 100644
--- a/src/app/domain/whitelabel.service.ts
+++ b/src/app/domain/whitelabel.service.ts
@@ -12,6 +12,7 @@ import { CAEC } from './contexts/caec';
 import { EResultsPageTypes } from '../ui-item-maker/item-set-editor/models/assessment-framework';
 import { ANON } from './contexts/anon';
 import { SCHL_BOARDS } from './contexts/school-boards';
+import { environment } from "../../environments/environment";
 
 const availableContexts:IContextData[] = [ // first entry is also the default
   // SCHL_BOARDS,  
@@ -154,10 +155,11 @@ export class WhitelabelService {
 
   getApiAddress() {
     const apiParam = new URLSearchParams(window.location.search).get("apiUrl");
-    if (apiParam) {
+    if (apiParam && !environment.production) {
       try {
         const url = new URL(apiParam);
         if (url.protocol === "https:" && url.hostname === "vea-api-staging.vretta.com" && /^\/[a-zA-Z0-9_-]+\/[a-zA-Z0-9_-]+\/?$/.test(url.pathname) && !url.search && !url.hash) {
+          console.info("Using API address from query parameter:", apiParam);
           return apiParam;
         }
       } catch {}
diff --git a/src/environments/environment.ts b/src/environments/environment.ts
index 56bd74c0f46..be31e102b5e 100644
--- a/src/environments/environment.ts
+++ b/src/environments/environment.ts
@@ -11,6 +11,10 @@
 //   appId: "1:441451168479:web:2660280a5681b0457bdf6a",
 //   measurementId: "G-42004FY2B0"
 // };
+ 
+export const environment = {
+    production: false
+};
 
 /*
  * In development mode, to ignore zone related error stack frames such as
-- 
GitLab


From 94e6fc6ecc423c142a6f2cb3d25d7c7963a998b5 Mon Sep 17 00:00:00 2001
From: Daniel <<EMAIL>>
Date: Tue, 24 Jun 2025 14:37:42 -0400
Subject: [PATCH 2/6] fix get API override query param

---
 src/app/domain/whitelabel.service.ts | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

diff --git a/src/app/domain/whitelabel.service.ts b/src/app/domain/whitelabel.service.ts
index d5905b82905..4987b90ce7d 100644
--- a/src/app/domain/whitelabel.service.ts
+++ b/src/app/domain/whitelabel.service.ts
@@ -154,7 +154,7 @@ export class WhitelabelService {
   }
 
   getApiAddress() {
-    const apiParam = new URLSearchParams(window.location.search).get("apiUrl");
+    const apiParam = (new URLSearchParams(window.location.search)).get("apiUrl");
     if (apiParam && !environment.production) {
       try {
         const url = new URL(apiParam);
-- 
GitLab


From 967f4ee48723d85d8f9d5f5d834fdb008b4ed4a9 Mon Sep 17 00:00:00 2001
From: Daniel <<EMAIL>>
Date: Tue, 24 Jun 2025 14:38:19 -0400
Subject: [PATCH 3/6] remove overly specific API override query param
 restrictions

---
 src/app/domain/whitelabel.service.ts | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

diff --git a/src/app/domain/whitelabel.service.ts b/src/app/domain/whitelabel.service.ts
index 4987b90ce7d..c2796d372e3 100644
--- a/src/app/domain/whitelabel.service.ts
+++ b/src/app/domain/whitelabel.service.ts
@@ -158,7 +158,7 @@ export class WhitelabelService {
     if (apiParam && !environment.production) {
       try {
         const url = new URL(apiParam);
-        if (url.protocol === "https:" && url.hostname === "vea-api-staging.vretta.com" && /^\/[a-zA-Z0-9_-]+\/[a-zA-Z0-9_-]+\/?$/.test(url.pathname) && !url.search && !url.hash) {
+        if (url.protocol === "https:" && url.hostname === "vea-api-staging.vretta.com") {
           console.info("Using API address from query parameter:", apiParam);
           return apiParam;
         }
-- 
GitLab


From 8cc2fadd2dcbf5a0fd5f433c9a37660f34577be5 Mon Sep 17 00:00:00 2001
From: Daniel <<EMAIL>>
Date: Tue, 24 Jun 2025 14:38:55 -0400
Subject: [PATCH 4/6] new hash location strategy to preserve query params

---
 src/app/app.module.ts                         |  3 ++-
 .../HashPreserveQueryLocationStrategy.ts      | 19 +++++++++++++++++++
 2 files changed, 21 insertions(+), 1 deletion(-)
 create mode 100644 src/app/patches/HashPreserveQueryLocationStrategy.ts

diff --git a/src/app/app.module.ts b/src/app/app.module.ts
index 633dcfcee4f..1810233f02c 100644
--- a/src/app/app.module.ts
+++ b/src/app/app.module.ts
@@ -49,6 +49,7 @@ import { MatSlideToggleModule } from '@angular/material/slide-toggle';
 import { MatTooltipModule } from '@angular/material/tooltip';
 import { MatCheckboxModule } from '@angular/material/checkbox';
 import { MatIconModule } from '@angular/material/icon';
+import { HashPreserveQueryLocationStrategy } from './patches/HashPreserveQueryLocationStrategy';
 
 export const ngMarkDownOptions = {
   markedOptions: {
@@ -80,7 +81,7 @@ FullCalendarModule.registerPlugins([ // register FullCalendar plugins
         MainNavComponent
     ],
     providers: [
-        {provide: LocationStrategy, useClass: HashLocationStrategy}
+        {provide: LocationStrategy, useClass: HashPreserveQueryLocationStrategy }
     ],
     imports: [
         BrowserModule.withServerTransition({appId: 'serverApp'}),
diff --git a/src/app/patches/HashPreserveQueryLocationStrategy.ts b/src/app/patches/HashPreserveQueryLocationStrategy.ts
new file mode 100644
index 00000000000..92abd4ef5d7
--- /dev/null
+++ b/src/app/patches/HashPreserveQueryLocationStrategy.ts
@@ -0,0 +1,19 @@
+import { APP_BASE_HREF, HashLocationStrategy, PlatformLocation } from "@angular/common";
+import { Inject, Injectable, Optional } from "@angular/core";
+
+@Injectable()
+export class HashPreserveQueryLocationStrategy extends HashLocationStrategy {
+  private readonly search: string;
+  constructor(
+    _platformLocation: PlatformLocation,
+    @Optional() @Inject(APP_BASE_HREF) _baseHref?: string,
+    ) {
+    super(_platformLocation, _baseHref);
+    this.search = window.location.search || '';
+  }
+
+  prepareExternalUrl(internal: string): string {
+    const hash = super.prepareExternalUrl(internal);
+    return this.search + hash;
+  }
+}
\ No newline at end of file
-- 
GitLab


From 9a4b59d07fae1e138c752707e04ea32abc866c36 Mon Sep 17 00:00:00 2001
From: Daniel <<EMAIL>>
Date: Tue, 24 Jun 2025 16:22:35 -0400
Subject: [PATCH 5/6] Revert "new hash location strategy to preserve query
 params"

This reverts commit 8cc2fadd2dcbf5a0fd5f433c9a37660f34577be5.
---
 src/app/app.module.ts                         |  3 +--
 .../HashPreserveQueryLocationStrategy.ts      | 19 -------------------
 2 <USER> <GROUP>, 1 insertion(+), 21 deletions(-)
 delete mode 100644 src/app/patches/HashPreserveQueryLocationStrategy.ts

diff --git a/src/app/app.module.ts b/src/app/app.module.ts
index 1810233f02c..633dcfcee4f 100644
--- a/src/app/app.module.ts
+++ b/src/app/app.module.ts
@@ -49,7 +49,6 @@ import { MatSlideToggleModule } from '@angular/material/slide-toggle';
 import { MatTooltipModule } from '@angular/material/tooltip';
 import { MatCheckboxModule } from '@angular/material/checkbox';
 import { MatIconModule } from '@angular/material/icon';
-import { HashPreserveQueryLocationStrategy } from './patches/HashPreserveQueryLocationStrategy';
 
 export const ngMarkDownOptions = {
   markedOptions: {
@@ -81,7 +80,7 @@ FullCalendarModule.registerPlugins([ // register FullCalendar plugins
         MainNavComponent
     ],
     providers: [
-        {provide: LocationStrategy, useClass: HashPreserveQueryLocationStrategy }
+        {provide: LocationStrategy, useClass: HashLocationStrategy}
     ],
     imports: [
         BrowserModule.withServerTransition({appId: 'serverApp'}),
diff --git a/src/app/patches/HashPreserveQueryLocationStrategy.ts b/src/app/patches/HashPreserveQueryLocationStrategy.ts
deleted file mode 100644
index 92abd4ef5d7..00000000000
--- a/src/app/patches/HashPreserveQueryLocationStrategy.ts
+++ /dev/null
@@ -1,19 +0,0 @@
-import { APP_BASE_HREF, HashLocationStrategy, PlatformLocation } from "@angular/common";
-import { Inject, Injectable, Optional } from "@angular/core";
-
-@Injectable()
-export class HashPreserveQueryLocationStrategy extends HashLocationStrategy {
-  private readonly search: string;
-  constructor(
-    _platformLocation: PlatformLocation,
-    @Optional() @Inject(APP_BASE_HREF) _baseHref?: string,
-    ) {
-    super(_platformLocation, _baseHref);
-    this.search = window.location.search || '';
-  }
-
-  prepareExternalUrl(internal: string): string {
-    const hash = super.prepareExternalUrl(internal);
-    return this.search + hash;
-  }
-}
\ No newline at end of file
-- 
GitLab


From 02c542c72be0607d0a45eca7dd8dcc89fa16b888 Mon Sep 17 00:00:00 2001
From: Daniel <<EMAIL>>
Date: Tue, 24 Jun 2025 16:50:58 -0400
Subject: [PATCH 6/6] IIFE to get API url query param upon page load before
 redirect and localstorage persistence

---
 src/app/domain/whitelabel.service.ts | 55 +++++++++++++++++++++++-----
 1 file changed, 45 insertions(+), 10 deletions(-)

diff --git a/src/app/domain/whitelabel.service.ts b/src/app/domain/whitelabel.service.ts
index c2796d372e3..85cfabd386f 100644
--- a/src/app/domain/whitelabel.service.ts
+++ b/src/app/domain/whitelabel.service.ts
@@ -40,6 +40,49 @@ export enum FLAGS
 
 export const TEMPLATE_BRAND_NAME = '%BRAND_NAME%';
 
+// API URL override logic
+const API_URL_STORAGE_KEY = "apiUrlOverride";
+const ALLOWED_API_HOSTS = ["vea-api-staging.vretta.com"];
+
+function getApiUrlOverride() {
+  const params = new URLSearchParams(window.location.search);
+  const apiParam = params.get("apiUrl");
+  if (apiParam !== null) {
+    if (apiParam === "") {
+      localStorage.removeItem(API_URL_STORAGE_KEY); // clear override
+      console.info("API address override cleared by empty query param.");
+      return null;
+    }
+    try {
+      const url = new URL(apiParam);
+      if (url.protocol === "https:" && ALLOWED_API_HOSTS.includes(url.hostname)) {
+        localStorage.setItem(API_URL_STORAGE_KEY, apiParam); // store valid override
+        console.info("Using API override address from query parameter:", apiParam);
+        return apiParam;
+      }
+    } catch {}
+    localStorage.removeItem(API_URL_STORAGE_KEY); // remove invalid
+    console.warn("Invalid API override address provided in query parameter:", apiParam);
+    return null;
+  }
+  // Fallback to localStorage
+  const stored = localStorage.getItem(API_URL_STORAGE_KEY);
+  if (stored) {
+    try {
+      const url = new URL(stored);
+      if (url.protocol === "https:" && ALLOWED_API_HOSTS.includes(url.hostname)) {
+        console.info("Using API override address from localStorage:", stored);
+        return stored;
+      }
+    } catch {}
+    localStorage.removeItem(API_URL_STORAGE_KEY); // remove invalid
+    console.warn("Invalid API override address in localStorage, removed:", stored);
+  }
+  return null;
+}
+
+const apiUrlOverride = getApiUrlOverride();
+
 @Injectable({
   providedIn: 'root'
 })
@@ -154,16 +197,8 @@ export class WhitelabelService {
   }
 
   getApiAddress() {
-    const apiParam = (new URLSearchParams(window.location.search)).get("apiUrl");
-    if (apiParam && !environment.production) {
-      try {
-        const url = new URL(apiParam);
-        if (url.protocol === "https:" && url.hostname === "vea-api-staging.vretta.com") {
-          console.info("Using API address from query parameter:", apiParam);
-          return apiParam;
-        }
-      } catch {}
-      console.warn("Invalid API address provided in query parameter:", apiParam);
+    if (!environment.production && apiUrlOverride) {
+      return apiUrlOverride;
     }
     return this.contextData.apiAddress(window.location.hostname);
   }
-- 
GitLab
import { IContentElement, IEntryStateScored, IScoredResponse } from "../models";
import { IElementTypeEditInfo } from './../models/index'

export enum ValidatorMode {
    NORMAL = "NORMAL",
    COMBINATION = "COMBINATIONS"
  }
  
  export interface IValidatorCombinationProp{
    validateId: number;
    elementType: string;
    correctValue?: string;
    dndElements?: Array<{
      targetId: string;
      correctValue: string;
      isOrderImp: boolean;
    }>
    
    score?: number  
  } 


export interface IContentElementValidator extends IValidatorCombinationProp, IContentElement, IScoredResponse {
    // validateId: number;
    // correctValue: string;
    validateProp: string;
    combinations?: IValidatorCombinationProp[][]
    mode: ValidatorMode;
  }

export interface IEntryStateValidator extends IEntryStateScored {
    value?: number;
}

export const validatorEditInfo: IElementTypeEditInfo = {
  editExcludeFields: []
}

export interface IValidatorAdvCombinationEntry {
  validateId: number;
  elementType: string;
}
import { IDgIntConstructionElement, DgIntConstructionType } from "../common"
import type { DgLib, DgInt, DgDiagram, DgVector2 } from "src/assets/lib/diagramatics/config";
import { HOME_LABEL_POSITION, IDgIntElementDnDTarget, IDgIntHomeConfig, IDgIntStyleConfig, IDgIntElementDnDAnswerSet, LAYOUT_HOME_POSITION, HOME_CONFIG_LAYOUT, DND_UNUSED_ID, resolveDnDTargetLabelChange, resolveDnDTargetOptions2, regenerateIdOnDnDTargets, getAllOptionIdsFromDnDTargets, resolveDnDHomeConfig, resolveDnDStyleConfig, resolveDnDAltAnswers, generateDefaultAnswerSet, generateDnDBlock, TAG_DND_DRAGGABLE, generateHomeTargetDiagramsWithLabel, groupByHome, isConfigIndividualHome, appendHomeSuffix, unusedId, TAG_DND_TARGET, IDgIntDnDConfig, isUnusedId, ReusableDnDStateManager, DnDStateManager, setupInteractiveDnD, attachLabelDiagram, DND_STYLE_MODE, generateElementColorsObject, getOptionLabel, assembleDraggableBlock, resolveTargetAndAnswersForCombinedOptions, generatePaddedDiagram, DND_ISFILLED_MODE, determineDnDIsFilledMode, generateHomeGroupBg, generateVoiceoverDataMap, resolveDnDAnswerGroup, getDnDElementBaseId } from "./dnd-common";
import { DgIntImageManager } from "../image-manager";
import { DgStyleProfile, getStyleParam } from "../style-profile";
import { IDgIntScoringParam, getStyle, formattedStringToBBCode, multilineTextWithSize, EX_TO_EM, flattenArray, CONTENT_JUSTIFY, DIAGRAMATICS_GLOBAL_SCALE_FACTOR } from "./common";
import { tex2dgMathExpression, tex2svg, svgToDataURL } from "../mathjax";
import { mathExpressionDg } from "../math-expression";
import { USE_MATHJAX } from "../math-expression";
import katex from 'katex';
import 'katex/contrib/mhchem/mhchem.js';
import { DgIntCallbacks } from "../renderer";
import { RenderMathComponent } from "../../render-math/render-math.component";

const EM_LINE_HEIGHT = 1.3;
const EM_DND_ELEMENT_PADDING = 0.5;
const PLACEHOLDER_ID_PREFIX = 'placeholder-math-cloze-'
const KATEX_HORIZONTAL_PADDING = 0.1;
const KATEX_VERTICAL_PADDING = 0.3;


export interface IDgIntElementDnDClozeMath extends IDgIntConstructionElement {
  constructionType: DgIntConstructionType.DND_CLOZE_MATH;
  content: string,
  label? : {
    text?: string;
    position?: HOME_LABEL_POSITION;
    isMath?: boolean;
  };
  dndTargets: IDgIntElementDnDTarget[];
  homeConfig: IDgIntHomeConfig;
  styleConfig: IDgIntStyleConfig;
  altAnswers?: IDgIntElementDnDAnswerSet[];
  _activeAnswerIndex?: number;
  _isAlwaysSeparateAnswerSetup?: boolean;
  diagramPadding?: {
    isEnabled?: boolean;
    top?: number;
    bottom?: number;
    left?: number;
    right?: number;
  }
  config: {
    isAllowMultipleAnswer: boolean;
    isAllowGrouping: boolean;
    isAllowEmptyTarget?: boolean;
    isAllowNonScoringTarget?: boolean;
    isUseImage: boolean;
    isUsingAnswerGroup?: boolean;
    isUsingReusableDraggable: boolean;
    homePosition : LAYOUT_HOME_POSITION;
    padding : [number, number];
    homeMaxWidth: number;
    draggableBgColor: string;
    targetBgColor: string;
    homeBgColor?: string;
    contentJustify?: CONTENT_JUSTIFY,
  };
  dndIsFilledMode?: 'auto' | DND_ISFILLED_MODE; // user specified value or auto
  _autoDnDIsFilledMode?: DND_ISFILLED_MODE; // auto calculated value
}

export function generateDefaultDgIntElementDnDClozeMath(): IDgIntElementDnDClozeMath {
  return {
    constructionType: DgIntConstructionType.DND_CLOZE_MATH,
    content: 'a + \\boxed{A} + c',
    styleConfig: {
      styleMode: DND_STYLE_MODE.DEFAULT,
    },
    homeConfig: {
      isGroupHome: false,
      layout: HOME_CONFIG_LAYOUT.DEFAULT,
      element: [[]]
    },
    dndTargets: [
      {
        label : 'A',
        content: [{value : 'b'}]
      },
      {
        label : DND_UNUSED_ID,
        content: [{value: 'd'}, {value:'e'}]
      }
    ],
    config: {
      isAllowMultipleAnswer: false,
      isAllowGrouping: false,
      isUseImage: false,
      isUsingReusableDraggable: false,
      homePosition: LAYOUT_HOME_POSITION.BOTTOM,
      padding : [0.5, 0.75],
      homeMaxWidth: 200,
      draggableBgColor: '#ffffff',
      targetBgColor: '#e0e0e0',
    },
  }
}

function normalizeTex(tex: string): string {
  // convert \boxed{\text{...}} to \boxed{...}
  tex = tex.replace(/\\boxed\{\\text\{([^}]*)\}\}/g, '\\boxed{$1}');
  // convert \boxed{...} to \fbox{...}
  tex = tex.replace(/\\boxed\{([^}]*)\}/g, '\\fbox{$1}');
  return tex;
}
function normalizeScriptSize(tex: string): string {
  if (tex.match(/^\^[a-zA-Z0-9]/)) {
    return tex.replace(/^\^/,'\\scriptsize ');
  } else if (tex.match(/^\^{[a-zA-Z0-9]*}/)) {
    return tex.replace(/^\^/,'\\scriptsize ');
  } else if (tex.match(/^_[a-zA-Z0-9]/)) {
    return tex.replace(/^_/,'\\scriptsize ');
  } else if (tex.match(/^_{[a-z]*}/)) {
    return tex.replace(/^_/,'\\scriptsize ');
  } else {
    return tex;
  }
}

export function resolveDnDClozeMathTargets(element : IDgIntElementDnDClozeMath) : void {
  const regex = /\\fbox\{([^}]*)\}/g;
  const normalizedContent = normalizeTex(element.content);
  const match = normalizedContent.match(regex);
  let targetNames = match?.map(m => m.slice(6,-1)) ?? [];
  
  if (element.altAnswers == undefined) element.altAnswers = [];
  if (element.label == undefined) element.label = {text: '', position: HOME_LABEL_POSITION.TOP};
  if (element.diagramPadding == undefined) element.diagramPadding = {left: 0, right: 0, top: 0, bottom: 0};
  
  const isCombineOptionContainer = element._isAlwaysSeparateAnswerSetup || element.config.isUsingReusableDraggable;
  element.dndTargets = resolveDnDTargetLabelChange(element.dndTargets, targetNames);
  element.dndTargets = resolveDnDTargetOptions2(element.dndTargets, targetNames, isCombineOptionContainer);
  const [idChangeMap, newOptionIds, newTargetIds] = regenerateIdOnDnDTargets(element.dndTargets);
  const allOptionIds = getAllOptionIdsFromDnDTargets(element.dndTargets);
  element.homeConfig = resolveDnDHomeConfig(element.homeConfig, idChangeMap, newOptionIds, allOptionIds);
  
  const homeCount = element.homeConfig.element.length;
  const targetIds = element.dndTargets.map(t => t.id);
  const optionIds = flattenArray(element.dndTargets.map(t => t.content.map(c => c.id)));
  const homeIds = Array.from({length: homeCount}, (_, i) => unusedId(i));
  element.styleConfig = resolveDnDStyleConfig(
    element.styleConfig, idChangeMap, newOptionIds, newTargetIds,
    targetIds, optionIds, homeIds
  );
  
  element.altAnswers = resolveDnDAltAnswers(element.altAnswers, idChangeMap, element.dndTargets);
  resolveTargetAndAnswersForCombinedOptions(isCombineOptionContainer, element.dndTargets, element.altAnswers);
  resolveDnDAnswerGroup(element.config.isUsingAnswerGroup, element.dndTargets, element.homeConfig, isCombineOptionContainer, element.altAnswers);
  
  if (element.dndIsFilledMode == undefined) { element.dndIsFilledMode = 'auto'; }
  if (element.dndIsFilledMode == 'auto') {
    element._autoDnDIsFilledMode = determineDnDIsFilledMode(
      element.dndTargets, element.altAnswers, 
      element.config.isUsingReusableDraggable, isCombineOptionContainer);
  }
}

export function renderDgIntDndClozeMath(
  element: IDgIntElementDnDClozeMath, svgElement: SVGSVGElement,
  dg: DgLib, int: DgInt, imageManager: DgIntImageManager, styleProfiles: Array<DgStyleProfile>,
  scoringParam: IDgIntScoringParam, callbacks: DgIntCallbacks
) {
  let answerSets = []
  if (!element.config.isUsingReusableDraggable && !element._isAlwaysSeparateAnswerSetup)
    answerSets.push(generateDefaultAnswerSet(element.dndTargets));
  if (element.config.isAllowMultipleAnswer || element.config.isUsingReusableDraggable || element._isAlwaysSeparateAnswerSetup){
    answerSets = answerSets.concat(element.altAnswers);
  }
  const styleParam = getStyleParam(styleProfiles);
  
  const homeMaxWidth = element.config.homeMaxWidth * 20/100;
  const homePosition = element.config.homePosition ?? LAYOUT_HOME_POSITION.BOTTOM;
  const maxTotalWidth = 200 * 20/100;
  const targetBgColor = element.config.targetBgColor;
  const draggableBgColor = element.config.draggableBgColor;
  const padding = element.config.padding ?? [0.5, 0.75];
  const isVisibleHome = styleParam.visible_home_target ?? false;
  const contentJustify = element.config.contentJustify ?? CONTENT_JUSTIFY.CENTER;
  const [elementColors, borderColors] = generateElementColorsObject(element.styleConfig, element.homeConfig)
  
  const canvasStyle = getStyle(svgElement);
  const fontSize = canvasStyle.pxFontSize;
  const multilineText = (s : string, maxWidth?: number) => {
    const bbcode = formattedStringToBBCode(s);
    return multilineTextWithSize(bbcode, dg, canvasStyle.pxFontSize, canvasStyle.fontFamily, EM_LINE_HEIGHT, maxWidth);
  }
  const mathStyleConfig = {svgFontSize: canvasStyle.pxFontSize, mathFontFamily: styleParam.math_font_family};
  const mathExpression = (s: string) => mathExpressionDg(dg, s, mathStyleConfig);
  
  const options = flattenArray(element.dndTargets.map(t => t.content));
  const idToIndexMap = new Map(options.map((option,i) => [option.id, i]));
  
  const optionInnerDiagrams: DgDiagram[] = options.map((s) => {
    if (USE_MATHJAX) {
      return mathExpression(s.value)
    } else {
      return mathExpression(normalizeScriptSize(s.value))
    }
  });
  const optionsBB = optionInnerDiagrams.map(o => o.bounding_box());
  
  let choicesMaxwidth  = Math.max(...optionsBB.map(bb => bb[1].x - bb[0].x)) + 2*padding[1];
  let choicesMaxheight = Math.max(...optionsBB.map(bb => bb[1].y - bb[0].y)) + 2*padding[0];
  choicesMaxwidth  = Math.max(choicesMaxwidth);
  choicesMaxheight = Math.max(choicesMaxheight);
  
  const dndBlock = generateDnDBlock(dg, choicesMaxwidth, choicesMaxheight, styleProfiles);
  const optionDiagrams = optionInnerDiagrams.map((o,i) => {
    const id = options[i].id;
    const source = o.move_origin('center-center').append_tags(TAG_DND_DRAGGABLE);
    let bg     = dndBlock.position(source.origin).fill(draggableBgColor)
      .append_tags(TAG_DND_DRAGGABLE);
    if (elementColors[id]) bg = bg.fill(elementColors[id]);
    if (element.styleConfig.isUseCustomBorderColors){
      bg = bg.stroke(element.styleConfig.commonDraggableBorderColor);
      if (borderColors[id]) bg = bg.stroke(borderColors[id]);
    }
    const combined = assembleDraggableBlock(dg, source, bg, padding, contentJustify);
    return dg.style.applyStyleProfiles(combined, styleProfiles);
  });
  
  // generate diagrams
  const normalizedContent = normalizeTex(element.content);
  // let [diagram, labelToPosMap] = generateClozeDiagram(dg, normalizedContent, choicesMaxwidth, choicesMaxheight, mathStyleConfig);
  // let [diagram, labelToPosMap] = generateClozeDiagramMjax(dg, normalizedContent, choicesMaxwidth, choicesMaxheight, mathStyleConfig.svgFontSize);
  const clozeDiagramGenerator = USE_MATHJAX ? generateClozeDiagramMjax : generateClozeDiagram;
  let [diagram, labelToPosMap] = clozeDiagramGenerator(dg, normalizedContent, choicesMaxwidth, choicesMaxheight, mathStyleConfig);
  
  // configure padding
  diagram = generatePaddedDiagram(dg, diagram, element.diagramPadding);
  
  if (element.label?.text){
    const textDiagramGenerator = element.label.isMath ? mathExpression : multilineText;
    const labelDiagram = textDiagramGenerator(element.label.text, maxTotalWidth);
    diagram = attachLabelDiagram(dg, diagram, labelDiagram, element.label.position, EM_DND_ELEMENT_PADDING)
  }
  
  const groupedOptionDiagrams = groupByHome(optionDiagrams, element.homeConfig.element, idToIndexMap);
  const [homeDiagrams, labelDiagram] = generateHomeTargetDiagramsWithLabel(
    dg, homePosition, element.homeConfig,
    groupedOptionDiagrams, diagram, 
    homeMaxWidth, EM_DND_ELEMENT_PADDING, multilineText, mathExpression, imageManager, 1);
  const homeGroupBg = !isVisibleHome ? [] : 
    generateHomeGroupBg(dg, homeDiagrams, element.homeConfig, styleParam, idToIndexMap, EM_DND_ELEMENT_PADDING).map(d => d.fill(targetBgColor));
  
  let homeIdList: string[];
  const sortedOptionIdList = flattenArray(element.homeConfig.element);
  if (isConfigIndividualHome(element.homeConfig)){
    homeIdList = sortedOptionIdList.map(id => appendHomeSuffix(id));
  } else {
    homeIdList = homeDiagrams.map((_, i) => unusedId(i));
  }
  
  const idToDiagramMap = new Map<string, DgDiagram>();
  {
    // setup diagram for dnd elements
    // dnd target
    for (let i = 0; i < element.dndTargets.length; i++) {
      let target = element.dndTargets[i];
      if (target.label === DND_UNUSED_ID) continue;
      
      const pos = labelToPosMap.get(target.label) ?? dg.V2(0,0);
      let block = dndBlock.position(pos).fill(targetBgColor).append_tags(TAG_DND_TARGET)
      if (elementColors[target.id]) block = block.fill(elementColors[target.id]);
      if (element.styleConfig.isUseCustomBorderColors){
        block = block.stroke(element.styleConfig.commonTargetBorderColor);
        if (borderColors[target.id]) block = block.stroke(borderColors[target.id]);
      }
      block = dg.style.applyStyleProfiles(block, styleProfiles);
      idToDiagramMap.set(target.id, block);
    }
    // dnd home
    for (let i in homeDiagrams){
      const homeDiagram = isVisibleHome ? homeDiagrams[i].fill(targetBgColor) : homeDiagrams[i];
      const homeId = homeIdList[i];
      idToDiagramMap.set(homeId, homeDiagram);
    }
    // dnd draggable
    for (let i in options){
      const optionDiagram = optionDiagrams[i];
      idToDiagramMap.set(options[i].id, optionDiagram);
    }
  }
  
  const idToGroupMap = new Map<string,number>();
  element.dndTargets.forEach((target, i) => {
    idToGroupMap.set(target.id, target.answerGroup ?? 0);
    target.content.forEach(content => {
      idToGroupMap.set(content.id, content.answerGroup ?? 0);
    })
  });
  const moveValidationFunction = (draggableId:string, targetId:string) : boolean =>  {
    if (isUnusedId(targetId)) return true;
    const baseDraggableId = getDnDElementBaseId(draggableId);
    const baseTargetId = getDnDElementBaseId(targetId);
    const draggableGroup = idToGroupMap.get(baseDraggableId);
    const targetGroup = idToGroupMap.get(baseTargetId);
    return draggableGroup == targetGroup;
  }
  
  
  const idToLabelMap = new Map<string, string>();
  {
    for (let target of element.dndTargets){
      idToLabelMap.set(target.id, target.label);
      for (let option of target.content){
        idToLabelMap.set(option.id, getOptionLabel(option));
      }
    }
  }
  
  if (element.dndIsFilledMode == undefined){ // backward compatibility
    element.dndIsFilledMode = 'auto';
    element._autoDnDIsFilledMode = determineDnDIsFilledMode(
      element.dndTargets, element.altAnswers, 
      element.config.isUsingReusableDraggable, element._isAlwaysSeparateAnswerSetup || element.config.isUsingReusableDraggable);
  }
  const isUsingReusableDraggable = element.config.isUsingReusableDraggable ?? false;
  const dgIntConfig: IDgIntDnDConfig = {
    idList: {
      options: options.map(o => o.id),
      targets: element.dndTargets.filter(t => !isUnusedId(t.label)).map(t => t.id),
      homes: homeIdList,
    },
    label: {
      idToLabelMap,
    },
    diagram: {
      staticDiagram: dg.diagram_combine(diagram, ...homeDiagrams, labelDiagram, ...homeGroupBg),
      idToDiagramMap,
    },
    home: {
      config: element.homeConfig,
      position: element.config.homePosition,
    },
    styleProfiles,
    svgElement,
    functions: {
      moveValidation: element.config.isUsingAnswerGroup ? moveValidationFunction : undefined,
      callbacks,
    },
    voiceover: {
      idToVoiceoverMap: generateVoiceoverDataMap(element.dndTargets)
    },
    config: {
      isAllowGrouping: false,
      isUsingReusableDraggable,
      dndIsFilledMode: element.dndIsFilledMode == 'auto' ? element._autoDnDIsFilledMode : element.dndIsFilledMode,
    },
    scoring: {
      isAllowMultipleAnswers: element.config.isAllowMultipleAnswer,
      answerSets,
      nonScoringTargetIds: element.config.isAllowNonScoringTarget ? element.dndTargets.filter(t => t.isNonScoring).map(t => t.id) : [],
      ...scoringParam
    },
    _showAnswer: {
      isShowAnswer: element._isShowAnswer ?? false,
      activeAnswerSetIndex: element._activeAnswerIndex ?? 0,
    },
    stateManager: isUsingReusableDraggable ? ReusableDnDStateManager : DnDStateManager,
  };
  setupInteractiveDnD(dg, int, dgIntConfig);
}

const hiddenContainer = document.createElement('div');
hiddenContainer.id = 'hidden-container-int-dg-cloze-math';
hiddenContainer.style.position = 'absolute';
hiddenContainer.style.left = '-100%';
hiddenContainer.style.top = '-100%';
hiddenContainer.style.visibility = 'hidden';
document.body.appendChild(hiddenContainer);
function generateClozeDiagram(
  dg: DgLib, normalizedTex: string, optionWidth: number, optionHeight: number, style: mathStyleConfig
): [DgDiagram, Map<string, DgVector2>] {
  let labelToPosMap = new Map<string, DgVector2>();
  
  normalizedTex = '\\displaystyle ' + normalizedTex;
  const regex = /\\fbox\{([^}]*)\}/g;
  const match = normalizedTex.match(regex);
  let targetNames = match?.map(m => m.slice(6,-1)) ?? [];
  const texWithPhantomPlaceholder = setTexTargetPhantomPlaceholder(
    normalizedTex, 
    optionWidth + 2*KATEX_HORIZONTAL_PADDING, 
    optionHeight + 2*KATEX_VERTICAL_PADDING,
    true
  );
  
  hiddenContainer.innerHTML = "";
  const katexContainer = document.createElement('div');
  hiddenContainer.appendChild(katexContainer);
  katex.render(texWithPhantomPlaceholder, katexContainer, {
    strict: false,
    throwOnError: false,
    trust: ({ command }) =>
      command === "\\htmlId" ||
      command === "\\htmlClass"
  });
  applyStyle(katexContainer, style);
  const katexRect = katexContainer.getBoundingClientRect();
  const widthPx = katexRect.width;
  const heightPx = katexRect.height;
  const widthEm = widthPx / style.svgFontSize;
  const heightEm = heightPx / style.svgFontSize;
  
  const katexElements = targetNames.map(label => {
    const id = `${PLACEHOLDER_ID_PREFIX}${label}`;
    return {label, element:katexContainer.querySelector(`#${id}`)};
  });
  katexElements.map(({label,element}) => {
    let el = element?.children?.[0] ?? element;
    if (el == undefined) return {x: 0, y: 0, isScript: false}
    const rect = el.getBoundingClientRect();
    let x = rect.x - katexRect.x;
    let y = (rect.y + rect.height) - (katexRect.y + katexRect.height);
    x += rect.width/2;
    y -= rect.height/2;
    const pos = dg.V2(x, -y).scale(1/style.svgFontSize);
    labelToPosMap.set(label, pos);
  });
  
  // hiddenContainer.removeChild(katexContainer)
  const mathDg = dg.foreign_object(katexContainer.innerHTML, widthEm, heightEm, DIAGRAMATICS_GLOBAL_SCALE_FACTOR/style.svgFontSize)
    .move_origin('bottom-left').position();
  return [mathDg, labelToPosMap];
}
type mathStyleConfig = {
  svgFontSize: number,
  mathFontFamily?: string,
}
function applyStyle(katexContainer: HTMLElement, style: mathStyleConfig) {
  const fontStyle = {
    'font-size': `${style.svgFontSize}px`,
    'font-family': style.mathFontFamily
  }
  RenderMathComponent.updateFontStyle(katexContainer, fontStyle);
}

function generateClozeDiagramMjax(
  dg: DgLib, normalizedContent: string, optionWidth: number, optionHeight: number, style: mathStyleConfig
): [DgDiagram, Map<string, DgVector2>] {
  const MathJax = window['MathJax'];
  if (MathJax == undefined) return [dg.empty(), new Map()];
  let labelToPosMap = new Map<string, DgVector2>();
  
  const fontSize = style.svgFontSize;
  const svgstring = tex2svg(MathJax, normalizedContent);
  const serializer = new XMLSerializer();
  const mencloseElements = Array.from(svgstring.querySelectorAll('[data-mml-node="menclose"]'));
  const addresses = mencloseElements.map(element => findIndexPath(element));
  const mencloseTexts = mencloseElements.map(element => getMencloseTextContent(element));
  
  const texWithPlaceholder = setTexTargetPlaceholder(
    normalizedContent,  optionWidth,  optionHeight,
  );
  const svgstringWithPlaceholder = tex2svg(MathJax, texWithPlaceholder);
  const svgForHidden = serializer.serializeToString(svgstringWithPlaceholder);
  
  const texWithPhantomPlaceholder = setTexTargetPhantomPlaceholder(
    normalizedContent,  optionWidth,  optionHeight,
  );
  const svgstringPhantomPlaceholder = tex2svg(MathJax, texWithPhantomPlaceholder);
  
  const hiddenSvg = document.createElement('div');
  // const hiddenFontSize = window.getComputedStyle(svgElement).fontSize;
  // const hiddenFontSize = hiddenSvg.getComputedStyle('font-size');
  hiddenSvg.style.position = 'absolute';
  hiddenSvg.style.visibility = 'hidden';
  hiddenSvg.style.fontSize = `${fontSize}px`;
  document.body.appendChild(hiddenSvg);
  hiddenSvg.innerHTML = svgForHidden;
  const mpaddedElements = addresses.map(address => getElement(hiddenSvg, address)) as HTMLElement[];
  const mpaddedElementsPositions = mpaddedElements.map(element => element.getBoundingClientRect());
  
  const rootSvg = hiddenSvg.children[0] as SVGSVGElement;
  const refSvg = rootSvg;
  const outerDOMRect = refSvg.getBoundingClientRect();
  
  const svgImageDataURL = svgToDataURL(svgstringPhantomPlaceholder);
  const mathWidth =  refSvg.width.baseVal.value / fontSize;
  const mathHeight = refSvg.height.baseVal.value / fontSize;
  
  const mathImg = dg.image(svgImageDataURL, mathWidth, mathHeight).move_origin('bottom-left').position();
  
  mpaddedElementsPositions.forEach((rect, i) => {
    let x = rect.x - outerDOMRect.x;
    let y = (rect.y + rect.height) - (outerDOMRect.y + outerDOMRect.height);
    x += rect.width/2;
    y -= rect.height/2;
    const pos = dg.V2(x, -y).scale(1/fontSize);
    const label = mencloseTexts[i];
    labelToPosMap.set(label, pos);
  });
  
  hiddenSvg.remove();
  
  return [mathImg, labelToPosMap];
}

function setTexTargetPlaceholder(texstr: string, width: number, height: number): string{
  const regexp = /\\fbox\{([^}]+)\}/g;
  const newtex = texstr.replace(regexp, () => {
    const baselineOffset = -0.2 - (height - 1) / 2;
    return `\\rule[${baselineOffset}em]{${width}em}{${height}em}`
  });
  return newtex;
}
function setTexTargetPhantomPlaceholder(texstr: string, width: number, height: number, setHtmlId = false): string {
  const regexp = /\\fbox\{([^}]+)\}/g;
  const newtex = texstr.replace(regexp, (match, p1) => {
    const baselineOffset = -0.2 - (height - 1) / 2;
    if (setHtmlId) {
      const id = PLACEHOLDER_ID_PREFIX + p1;
      return `\\htmlId{${id}}{\\phantom{\\rule[${baselineOffset}em]{${width}em}{${height}em}}}`
    } else {
      return `\\phantom{\\rule[${baselineOffset}em]{${width}em}{${height}em}}`
    }
  });
  return newtex;
}

function findIndexPath(element: any) {
    const path = [];
    let currentElement = element;
    while (currentElement.parentNode) {
        const siblings = Array.from(currentElement.parentNode.children);
        const index = siblings.indexOf(currentElement);
        path.unshift(index);
        currentElement = currentElement.parentNode;
    }
  return path;
}

function getElement(root: ParentNode, address: number[]){
  let currentElement = root;
  for (const index of address) {
    currentElement = currentElement.children[index];
  }
  return currentElement;
}

function getMencloseTextContent(element: Element){
  const mTextElement = element.children[0].children[0];
  if (!mTextElement) return undefined;
  if (mTextElement.getAttribute('data-mml-node') != 'mtext') return undefined;
  const childrens = Array.from(mTextElement.children);
  // the children is in the form 
  // <use data-c="74" xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#MJX-1-TEX-N-74"></use>
  const charCodes = childrens.map(e => parseInt(e.getAttribute('data-c'), 16));
  const chars = charCodes.map(c => String.fromCharCode(c));
  return chars.join('')
}

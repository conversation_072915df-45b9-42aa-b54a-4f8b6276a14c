import type { DgLib, DgDiagram, DgVector2 } from "src/assets/lib/diagramatics/config";
import { DgIntImageManager } from "../image-manager";
import { IContentElementImage } from "../../element-render-image/model";
import { QuestionState } from "../../models";
import { QuestionPubSub } from "../../question-runner/pubsub/question-pubsub";

const EM_LINE_HEIGHT = 1.3;
export const EX_TO_EM = 0.43; // in mathjax source code, they use 0.43 as the conversion factor from ex to em
export enum LAYOUT_IMAGE_LABEL_MODE {
  HIDDEN = 'hidden',
  TOP = 'top',
  BOTTOM = 'bottom',
}

export enum CONTENT_JUSTIFY {
  LEFT = 'left',
  RIGHT = 'right',
  CENTER = 'center',
}
export const contentJustifyOptions = [
  {id: CONTENT_JUSTIFY.LEFT, caption:'Left'},
  {id: CONTENT_JUSTIFY.CENTER, caption:'Center'},
  {id: CONTENT_JUSTIFY.RIGHT, caption:'Right'},
]

export interface IDgIntScoringParam {
  questionState: QuestionState;
  entryId: number;
  scoreWeight: number;
  enableProportionalScoring: boolean;
  questionPubSub?: QuestionPubSub;
}

export class IdGenerator {
  private indices : {[key:string] : number};
  constructor(
    public separator: string = "", 
    public ignore0: boolean = false
  ) {
    this.indices = {};
  }
  public generate(prefix : string = "") : string {
    if (this.indices[prefix] == undefined) this.indices[prefix] = -1;
    this.indices[prefix]++;
    if (this.ignore0 && this.indices[prefix] == 0) return prefix;
    return prefix + this.separator + this.indices[prefix].toString();
  }
  public reset(prefix : string = "") : void {
    this.indices[prefix] = -1;
  }
  public reset_all() : void {
    this.indices = {};
  }
}

export function numberDuplicateString(strArr: string[]) : string[] {
  const idGen = new IdGenerator(":", true);
  return strArr.map((s) => idGen.generate(s));
}

export function flattenArray<T>(arr : T[][]) : T[] {
  return arr.reduce((acc, val) => acc.concat(val), []);
}
export function uniqueArray<T>(arr: T[]): T[] {
  return [...new Set(arr)];
}

export function getPxFontSize(svgElement : SVGSVGElement) : number {
  return parseFloat(window.getComputedStyle(svgElement).fontSize);
}

export function getFontFamily(svgElement : SVGSVGElement) : string {
  return window.getComputedStyle(svgElement).fontFamily;
}

export interface IDgIntStyle {
  pxFontSize : number;
  fontFamily : string;
}
export function getStyle(svgElement : SVGSVGElement) : IDgIntStyle {
  return {
    pxFontSize : getPxFontSize(svgElement),
    fontFamily : getFontFamily(svgElement),
  }
}

// return true if the content of both array is the same (order doesn't matter)
export function arraysEqual<T>(arr1: T[], arr2: T[]): boolean {
  if (!Array.isArray(arr1)) return false;
  if (!Array.isArray(arr2)) return false;
  if (arr1.length !== arr2.length)  return false;
  const sortedArr1 = [...arr1].sort();
  const sortedArr2 = [...arr2].sort();
  for (let i = 0; i < sortedArr1.length; i++) {
      if (sortedArr1[i] !== sortedArr2[i])  return false;
  }
  return true;
}

export function arraysEqualOrdered<T>(arr1: T[], arr2: T[]): boolean {
  if (!Array.isArray(arr1)) return false;
  if (!Array.isArray(arr2)) return false;
  if (arr1.length !== arr2.length)  return false;
  for (let i = 0; i < arr1.length; i++) {
      if (arr1[i] !== arr2[i])  return false;
  }
  return true;
}

export function numberToAlphabet(num: number): string {
    let result = "";
    while (num >= 0) {
        result = String.fromCharCode((num % 26) + 'A'.charCodeAt(0)) + result;
        num = Math.floor(num / 26) - 1;
    }
    return result;
}
export function alphabetToNumber(alphabet: string): number {
    let result = 0;
    for (let i = 0; i < alphabet.length; i++) {
        result = result * 26 + alphabet.charCodeAt(i) - 'A'.charCodeAt(0);
    }
    return result;
}

export function formattedStringToBBCodeWithTarget(
  s: string, isExstraLineHeight: boolean,
  normalLineHeight: number, padding: [number, number]
) : string {
  // handle target
  let str = s.replace(/\[(.*?)\]/g, '[target]$1[/target]');
  str = formattedStringToBBCode(str);
  
  if (isExstraLineHeight) {
    const extraPad = (normalLineHeight - 1)/2
    // EM_LINE_HEIGHT + 2*padding[0] + (EM_LINE_HEIGHT-1);
    let splitByBr = str.split('[br]');
    let lineHeight = new Array(splitByBr.length).fill(normalLineHeight);
    for (let i = 0; i < splitByBr.length; i++) {
      if (splitByBr[i].includes('[target]')) {
        lineHeight[i] += padding[0] + extraPad;
        if (i+1 < splitByBr.length) lineHeight[i+1] += padding[0] + extraPad;
      }
    }
    for (let i = 0; i < lineHeight.length; i++) {
      if (i == 0) continue;
      let lh = lineHeight[i];
      if (lh == normalLineHeight) continue;
      splitByBr[i-1] = splitByBr[i-1] + `[lineheight=${lh}em]`;
      splitByBr[i] = `[/lineheight]` + splitByBr[i];
    }
    str = splitByBr.join('[br]');
  }
  
  return str;
}

// replace "\{id\}" with `[tag=${id}][dx=${dx}][color=none].[/color][/dx][/tag]`
// and set the lineheight
export function formatBBCodeTaggedPlaceholder(
  bbtext: string, idToSizeMap: Map<string, [number, number]>
): string {

  const regex = /\\\{(.*?)\\\}/g;
  const matches = bbtext.match(regex) ?? [];
  for (let i = 0; i < matches.length; i++) {
    const id = matches[i].slice(2, -2);
    const [width, height] = idToSizeMap.get(id) ?? [0, 0];
    bbtext = bbtext.replace(matches[i], `[tag=${id}][dx=${width}][color=none].[/color][/dx][/tag]`);
  }
  return bbtext;
}

export function formattedStringToBBCode(s: string): string {
  let str = s;
  
  // replace " * " with " [asterisk] "
  str = str.replace(/(\s)\*(\s)/g, '$1[asterisk]$2');

  // handle ** (bold)
  const boldSplit = str.split('**')
  str = boldSplit.map((item, index) => index % 2 === 0 ? item : `[b]${item}[/b]`).join('');

  // handle * (italic)
  const italicSplit = str.split('*')
  str = italicSplit.map((item, index) => index % 2 === 0 ? item : `[i]${item}[/i]`).join('');

  // handle newline
  str = str.replace(/\n/g, '[br]');
  // TODO: fix this behaviour in diagramatics
  // hack to get no empty line, make sure every newline is displayed
  str = str.replace(/\[br\]\[br\]/g, '[br] [br]');
  str = str.replace(/\[br\]\[br\]/g, '[br] [br]');
  str = str.replace(/^\[br\]/, ' [br]');
  
  // bring back "*"
  str = str.replace(/\[asterisk\]/g, '*');
  
  // replace <sup> with [sup]
  str = str.replace(/<sup>/g, '[sup]');
  str = str.replace(/<\/sup>/g, '[/sup]');
  
  // replace <sub> with [sub]
  str = str.replace(/<sub>/g, '[sub]');
  str = str.replace(/<\/sub>/g, '[/sub]');
  
  // replace 'abc^def(\s)' with abc[sup]def[/sup]
  str = str.replace(/\^([a-zA-Z0-9]+)/g, '[sup]$1[/sup]');
  // replace 'abc_vw(\s)' with abc[sub]vw[/sub]
  str = str.replace(/_([a-zA-Z0-9]+)/g, '[sub]$1[/sub]');

  return sanitizeXMLTag(str);
}

function sanitizeXMLTag(str: string): string {
  // str = str.replace(/&/g, '&amp;');
  str = str.replace(/</g, '&lt;');
  str = str.replace(/>/g, '&gt;');
  return str;
}

//TODO: Move this to dg-ext project
const textMeasureCanvas = document.createElement('canvas');
const textMeasureContext = textMeasureCanvas.getContext('2d');
export function multilineTextWithSize(
  bbcode: string, dg: DgLib, pxFontSize: number, fontFamily: string, 
  emLineHeight: number, maxWidth: number = Infinity, justification: CONTENT_JUSTIFY = CONTENT_JUSTIFY.CENTER,
  idToSizeMap?: Map<string, [number,number]>
) : DgDiagram {
  let m = dg.multiline_bb(bbcode, `${emLineHeight}em`, true).fontsize(pxFontSize).fontfamily(fontFamily);
  switch (justification) {
    case CONTENT_JUSTIFY.CENTER: m = m.move_origin_text('center-center'); break;
    case CONTENT_JUSTIFY.LEFT: m = m.move_origin_text('center-left'); break;
    case CONTENT_JUSTIFY.RIGHT: m = m.move_origin_text('center-right'); break;
  }
  if (!m.multilinedata?.content) return dg.empty();
  m.multilinedata.content = lineWrapTextSpanData(m.multilinedata.content, pxFontSize, fontFamily, emLineHeight, maxWidth);
  
  if (idToSizeMap){
    m.multilinedata.content = handleTextSpanDataLineHeight(m.multilinedata.content, emLineHeight, idToSizeMap);
  }
  
  let height = emLineHeight;
  const dy = emLineHeight/2;
  let widths = [0];
  for (let tspanData of m.multilinedata.content) {
    
    let fontData = `${pxFontSize}px ${fontFamily}`
    if (tspanData?.style?.['font-weight'] == 'bold') fontData = `bold ${fontData}`;
    if (tspanData?.style?.['font-style'] == 'italic') fontData = `italic ${fontData}`;
    textMeasureContext.font = fontData;
    
    if (tspanData.text == '\n') {
      height += parseFloat(tspanData.style?.dy);
      widths.push(0);
    } else {
      let tspanWidth = textMeasureContext.measureText(tspanData.text).width / pxFontSize;
      if (tspanData?.style?.dx) tspanWidth += parseFloat(tspanData.style.dx);
      widths[widths.length - 1] += tspanWidth;
    }
  }
  const width = Math.max(...widths);
  
  let bboxRect: DgDiagram = dg.empty();
  switch (justification) {
    case CONTENT_JUSTIFY.CENTER: bboxRect = dg.rectangle_corner(dg.V2(-width/2,-height+dy), dg.V2(width/2,0+dy)); break;
    case CONTENT_JUSTIFY.LEFT: bboxRect = dg.rectangle_corner(dg.V2(0, -height+dy), dg.V2(width, 0+dy)); break;
    case CONTENT_JUSTIFY.RIGHT: bboxRect = dg.rectangle_corner(dg.V2(-width, -height+dy), dg.V2(0, 0+dy)); break;
  }
  bboxRect = bboxRect.stroke('none').fill('none');
  if (!m.multilinedata?.content)  return bboxRect;
  return dg.diagram_combine(bboxRect, m).move_origin('center-center').position(dg.V2(0,0));
};

type TextSpanData = Array<{text:string, style:any}>;
function lineWrapTextSpanData(
  textSpanData: TextSpanData, pxFontSize: number, fontFamily: string, emLineHeight: number,
  maxWidth: number = Infinity
): TextSpanData {
  const newlineSpan = { text: '\n', style: { dy: `${emLineHeight}em` } };
  if (maxWidth == Infinity) return textSpanData;
  let newTextSpanData = [];
  
  let currentLineWidth = 0;
  for (let i = 0; i < textSpanData.length; i++){
    const span = textSpanData[i];
    let fontData = `${pxFontSize}px ${fontFamily}`
    if (span?.style?.['font-weight'] == 'bold') fontData = `bold ${fontData}`;
    if (span?.style?.['font-style'] == 'italic') fontData = `italic ${fontData}`;
    textMeasureContext.font = fontData;
    
    if (span.text == '\n'){
      newTextSpanData.push(span);
      currentLineWidth = 0;
      continue;
    } 
    
    let tspanWidth = textMeasureContext.measureText(span.text).width / pxFontSize;
    if (span?.style?.dx) tspanWidth += parseFloat(span.style.dx);
    const newLineWidth = currentLineWidth + tspanWidth;
    if (newLineWidth <= maxWidth) {
      currentLineWidth = newLineWidth;
      newTextSpanData.push(span);
    } else if (span?.style?.['is-prev-word']) {
      // newline, but also take the previous word with it
      let linkedWordsSpanData = [span];
      while (linkedWordsSpanData[linkedWordsSpanData.length-1]?.style?.['is-prev-word']){
        if (newTextSpanData.length == 0) break;
        const prevSpanData = newTextSpanData.pop();
        linkedWordsSpanData.push(prevSpanData);
      }
      linkedWordsSpanData.reverse();
      const linkedWordsWidth = linkedWordsSpanData.reduce((acc, span) => {
        let tspanWidth = textMeasureContext.measureText(span.text).width / pxFontSize;
        if (span?.style?.dx) tspanWidth += parseFloat(span.style.dx);
        return acc + tspanWidth;
      }, 0);
      currentLineWidth = linkedWordsWidth;
      newTextSpanData.push(newlineSpan);
      newTextSpanData.push(...linkedWordsSpanData);
    } else {
      // newline
      currentLineWidth = tspanWidth;
      if (newTextSpanData.length > 0) newTextSpanData.push(newlineSpan);
      newTextSpanData.push(span);
    }
  }
  return newTextSpanData;
}

function handleTextSpanDataLineHeight(
  textSpanData: TextSpanData, emLineHeight: number, idToSizeMap: Map<string, [number,number]>
): TextSpanData {
  let lines: TextSpanData[] = [[]];
  for (let tspan of textSpanData){
    if (tspan.text == '\n'){
      lines.push([]);
    }
    lines[lines.length - 1].push(tspan);
  }
  
  const extraHeight = emLineHeight - 1;
  
  let lineHeights = lines.map((line) => {
    const wordHeights = line.map((tspan) => {
      const tag = tspan.style?.['tag'];
      const height = idToSizeMap.get(tag)?.[1] ?? emLineHeight;
      return height;
    });
    return Math.max(...wordHeights);
  });
  
  for (let i = 0; i < lines.length; i++){
    const line = lines[i];
    if (line[0]?.text != '\n') continue;
    
    const prevLineHeight = lineHeights[i-1] ?? 0;
    const prevContribution = (prevLineHeight - 1) / 2;
    const selfContribution = (lineHeights[i] + 1) / 2;
    const dy = prevContribution + selfContribution + extraHeight;
    line[0].style.dy = `${dy}em`;
  }
  
  return flattenArray(lines);
}


/**
* 1unit = 1em
*/
export function resizeSVGElementAndDraw(dg: DgLib, diagram : DgDiagram, svgElement : SVGSVGElement, options?: any) {
  const diagramSizeEm = dg.geometry.size(diagram);
  svgElement.setAttribute('width', `${diagramSizeEm[0]}em`);
  svgElement.setAttribute('height', `${diagramSizeEm[1]}em`);
  dg.draw_to_svg_element(svgElement, diagram, {padding: 0, ...options});
  
  (svgElement.children[0] as SVGSVGElement).style.overflow = 'visible';
}

export function generateImageTextDiagram(
  dg: DgLib, imageManager: DgIntImageManager, 
  text: string, imageElement: IContentElementImage,
  textDiagramGenerator: (text: string) => DgDiagram,
  imageLabelPosition: LAYOUT_IMAGE_LABEL_MODE, padding: [number,number],
  alignment?: 'left' | 'center' | 'right'
) : DgDiagram {
  if (imageElement?.url) {
    const url = imageElement.url;
    const scale = imageElement.scale;
    let mode = imageLabelPosition ?? LAYOUT_IMAGE_LABEL_MODE.BOTTOM;
    if (text == "") mode = LAYOUT_IMAGE_LABEL_MODE.HIDDEN;
    // 100unit -> 20em (1unit = 1/5em)
    const widthEm = scale / 5; 
    const sz = imageManager.getSize(url, widthEm);
    const img = dg.image(url, sz.width, sz.height);
    const txt = textDiagramGenerator(text);
    const cellDiagram = generateLabeledImage(dg, img, txt, mode, padding[0], alignment)
    return cellDiagram;
  } else {
    return textDiagramGenerator(text);
  }
}
function generateLabeledImage(
  dg: DgLib, img: DgDiagram, txt: DgDiagram, mode: LAYOUT_IMAGE_LABEL_MODE, space: number, 
  alignment?: 'left'|'center'|'right'
) : DgDiagram {
  switch (mode) {
    case LAYOUT_IMAGE_LABEL_MODE.TOP:{
      return dg.distribute_vertical_and_align([txt, img], space, alignment);
    } 
    case LAYOUT_IMAGE_LABEL_MODE.BOTTOM:{
      return dg.distribute_vertical_and_align([img, txt], space, alignment);
    } 
    case LAYOUT_IMAGE_LABEL_MODE.HIDDEN:{
      return img;
    } 
  }
}

export function displayLoadingText(dg: DgLib, svgElement: SVGSVGElement) {
    const canvasStyle = getStyle(svgElement);
    const text = "[color=gray]loading...[/color]";
    const loadingText = multilineTextWithSize(text, dg, canvasStyle.pxFontSize, canvasStyle.fontFamily, EM_LINE_HEIGHT)
    resizeSVGElementAndDraw(dg, loadingText, svgElement);
}

export function getFilenameFromPath(path: string, removeExtension = true): string {
  const lastIndex = path.lastIndexOf('/');
  if (lastIndex == -1) return path;
  const filename = path.substring(lastIndex + 1);
  if (!removeExtension) return filename;
  const lastIndex2 = filename.lastIndexOf('.');
  if (lastIndex2 == -1) return filename;
  return filename.substring(0, lastIndex2);
}

export type DgButtonData = { id: string, diagram: DgDiagram, hoveredDiagram: DgDiagram, onclick: () => void }

const bookmarkLinkColor = "#3e72c0"
const bookmarkLinkHoverColor = "#3867ad" // darken(bookmarkLinkColor, 10%)
export type DgBookmarkData = { id: string, caption: string, bookmarkId: string, targetItemLabel: string}
export function generateBookmarkButton(dg: DgLib, data: DgBookmarkData, style: IDgIntStyle, pos: DgVector2, callback: (data: DgBookmarkData) => void): DgButtonData {
  const normaltext = `[u][color=${bookmarkLinkColor}]${data.caption}[/color][/u]`;
  const hoveredtext = `[u][color=${bookmarkLinkHoverColor}]${data.caption}[/color][/u]`;
  const diagram= multilineTextWithSize(normaltext, dg, style.pxFontSize, style.fontFamily, EM_LINE_HEIGHT).position(pos);
  const hoveredDiagram = multilineTextWithSize(hoveredtext, dg, style.pxFontSize, style.fontFamily, EM_LINE_HEIGHT).position(pos);
  return {id: data.id, diagram, hoveredDiagram, onclick: () => callback(data)}
}

export function setRectangleHeight(dg: DgLib, rect: DgDiagram, height: number): DgDiagram {
  const [vBotLeft, vTopRight] = rect.bounding_box();
  const originalHeight = vTopRight.y - vBotLeft.y;
  const factor = height / originalHeight;
  return rect.scale(dg.V2(1, factor), rect.get_anchor('center-center'));
}
export function setRectangleWidth(dg: DgLib, rect: DgDiagram, width: number): DgDiagram {
  const [vBotLeft, vTopRight] = rect.bounding_box();
  const originalWidth = vTopRight.x - vBotLeft.x;
  const factor = width / originalWidth;
  return rect.scale(dg.V2(factor, 1), rect.get_anchor('center-center'));
}
import type { DgLib, DgInt, DgDiagram } from "src/assets/lib/diagramatics/config";
import { IDgIntStateManager, IDgIntDnDState } from "../common";
import { ScoringTypes } from "../../models";
import { arraysEqual, CONTENT_JUSTIFY, DgButtonData, flattenArray, getFilenameFromPath, IdGenerator, IDgIntScoringParam, LAYOUT_IMAGE_LABEL_MODE, resizeSVGElementAndDraw, setRectangleHeight, setRectangleWidth, uniqueArray } from './common'
import { IContentElementImage } from "../../element-render-image/model";
import { DgIntStyleParam, DgStyleProfile, getStyleParam } from "../style-profile";
import { IDgIntAdvancedHomeLayoutElement, defaultElement, IHomeLayoutGenerationContext, generateAdvancedHomeDiagramLayout } from "../advanced-home-layout";
import { PubSubTypes } from "../../element-render-frame/pubsub/types";
import { DgIntImageManager } from "../image-manager";
import { InteractionVoiceover } from "../../voiceover-state.service";
import { Subject } from "rxjs";
import { DgIntCallbacks } from "../renderer";
export const DND_UNUSED_ID = "_unused";
export const TAG_DND_BLOCK = "dnd_block"
export const TAG_DND_DRAGGABLE = "dnd_draggable"
export const TAG_DND_TARGET = "dnd_target"
export const TAG_MAIN_DIAGRAM = "main_diagram"
export const TAG_EXTENDED_HOME_INVISIBLE_BG = "extended_home_invisible_bg"
const TAG_DND_FREEFORM_HOME_BGIMG = "dnd-freeform-home-bgimg"
const EM_DND_ELEMENT_PADDING = 0.5;
export const HOME_SUFFIX = ':home';
export const HOME_CONTENT_SUFFIX = ':content';

export type DgIntVoiceoverData = {url: string, trigger: Subject<boolean>}

export type AnswerGroup<T> = Array<T>;
export function unusedId(group: number) : string {
  return DND_UNUSED_ID + group.toString();
}
export function isUnusedId(id: string) : boolean {
  return id.startsWith(DND_UNUSED_ID);
}
export function isHomeId(id: string) : boolean {
  return isUnusedId(id) || id.endsWith(HOME_SUFFIX);
}

export function getOptionLabel(option: IDgIntElementDnDOption): string {
  if (option.value) {
    return option.value;
  } else if (option.image?.url) {
    return getFilenameFromPath(option.image.url);
  } else {
    return option.id;
  }
}

export enum HOME_CONFIG_LAYOUT {
  DEFAULT = 'default',
  HORIZONTAL_SINGLE_COLUMN = 'horizontal_single_column',
  // ROW = 'row',
  INDIVIDUAL_DEFAULT = 'individual_default',
  INDIVIDUAL_COLUMN = 'individual_column',
  // INDIVIDUAL_ROW = 'individual_row',
  TOP_AND_BOTTOM = 'top_and_bottom',
  INDIVIDUAL_TOP_AND_BOTTOM = 'individual_top_and_bottom',
  FREEFORM = 'freeform',
  CUSTOM_ADVANCED = 'custom_advanced',
}
// export const homeConfigLayoutFixedCapacity: { [key in HOME_CONFIG_LAYOUT]?: number } = {
//   [HOME_CONFIG_LAYOUT.TOP_AND_BOTTOM]: 2,
// }
export function homeConfigLayoutFixedCapacity(layout: HOME_CONFIG_LAYOUT): number {
  switch (layout) {
    case HOME_CONFIG_LAYOUT.TOP_AND_BOTTOM: return 2;
    case HOME_CONFIG_LAYOUT.INDIVIDUAL_TOP_AND_BOTTOM: return 2;
    case HOME_CONFIG_LAYOUT.FREEFORM: return 1;
    case HOME_CONFIG_LAYOUT.CUSTOM_ADVANCED: return undefined;
    default: return undefined;
  }
}
export function isHomeConfigLayoutFixedPosition(layout: HOME_CONFIG_LAYOUT): boolean {
  switch (layout) {
    case HOME_CONFIG_LAYOUT.TOP_AND_BOTTOM: return true;
    case HOME_CONFIG_LAYOUT.INDIVIDUAL_TOP_AND_BOTTOM: return true;
    default: return false;
  }
}
export function isHomeConfigLayoutAllowGroupLabel(layout: HOME_CONFIG_LAYOUT): boolean {
  switch (layout) {
    case HOME_CONFIG_LAYOUT.DEFAULT:
    case HOME_CONFIG_LAYOUT.INDIVIDUAL_DEFAULT: 
    case HOME_CONFIG_LAYOUT.HORIZONTAL_SINGLE_COLUMN: 
    case HOME_CONFIG_LAYOUT.INDIVIDUAL_COLUMN: 
      return true;
    default: return false;
  }
}

export enum LAYOUT_HOME_POSITION {
  LEFT = 'left',
  BOTTOM = 'bottom',
  RIGHT = 'right',
  TOP = 'top',
  LEFT_TOP = 'left_top',
  RIGHT_TOP = 'right_top',
  BOTTOM_LEFT = 'bottom_left',
  TOP_LEFT = 'top_left',
}
export enum HOME_LABEL_POSITION {
  TOP = 'top',
  LEFT = 'left',
  LEFT_TOP = 'left_top',
  TOP_LEFT = 'top_left',
  BOTTOM = 'bottom',
  BOTTOM_LEFT = 'bottom_left',
}
export const homeLabelPositionOptions = [
  { id: HOME_LABEL_POSITION.TOP, caption: 'Top' },
  { id: HOME_LABEL_POSITION.TOP_LEFT, caption: 'Top Left'},
  { id: HOME_LABEL_POSITION.LEFT, caption: 'Left' },
  { id: HOME_LABEL_POSITION.LEFT_TOP, caption: 'Left Top' },
]
export const diagramLabelPositionOptions = [
  { id: HOME_LABEL_POSITION.TOP, caption: 'Top' },
  { id: HOME_LABEL_POSITION.TOP_LEFT, caption: 'Top Left'},
  { id: HOME_LABEL_POSITION.LEFT, caption: 'Left' },
  { id: HOME_LABEL_POSITION.LEFT_TOP, caption: 'Left Top' },
  { id: HOME_LABEL_POSITION.BOTTOM, caption: 'Bottom' },
  { id: HOME_LABEL_POSITION.BOTTOM_LEFT, caption: 'Bottom Left'},
]
export enum DND_OPTION_TEXT_MODE {
  TEXT = 'text',
  MATH = 'math',
}
export enum DND_ISFILLED_MODE {
  PLACE_ALL_DRAGGABLE = 'place_all_draggable',
  FILL_ALL_TARGET = 'fill_all_target',
  FILL_ANY_TARGET = 'fill_any_target',
  FILL_ALL_SCORING_TARGET = 'fill_all_scoring_target',
  FILL_TARGET_CAPACITY = 'fill_target_capacity',
}
export const dndIsFilledModeCaptionMap = {
  [DND_ISFILLED_MODE.PLACE_ALL_DRAGGABLE]: 'Place All Draggables',
  [DND_ISFILLED_MODE.FILL_ALL_TARGET]: 'Fill All Targets',
  [DND_ISFILLED_MODE.FILL_ANY_TARGET]: 'Fill Any Target',
  [DND_ISFILLED_MODE.FILL_ALL_SCORING_TARGET]: 'Fill All Scoring Targets',
  [DND_ISFILLED_MODE.FILL_TARGET_CAPACITY]: 'Fill Based on Capacity',
}
export const dndIsFilledModeOptions = [
  { id: 'auto', caption: 'Auto' },
  { id: DND_ISFILLED_MODE.PLACE_ALL_DRAGGABLE, caption: dndIsFilledModeCaptionMap[DND_ISFILLED_MODE.PLACE_ALL_DRAGGABLE] },
  { id: DND_ISFILLED_MODE.FILL_ALL_TARGET, caption: dndIsFilledModeCaptionMap[DND_ISFILLED_MODE.FILL_ALL_TARGET] },
  { id: DND_ISFILLED_MODE.FILL_ANY_TARGET, caption: dndIsFilledModeCaptionMap[DND_ISFILLED_MODE.FILL_ANY_TARGET] },
  { id: DND_ISFILLED_MODE.FILL_ALL_SCORING_TARGET, caption: dndIsFilledModeCaptionMap[DND_ISFILLED_MODE.FILL_ALL_SCORING_TARGET] },
];
export const dndIsFilledModeForGroupingOptions = [
    { id: 'auto', caption: 'Auto' },
    { id: DND_ISFILLED_MODE.PLACE_ALL_DRAGGABLE, caption: dndIsFilledModeCaptionMap[DND_ISFILLED_MODE.PLACE_ALL_DRAGGABLE] },
    { id: DND_ISFILLED_MODE.FILL_ALL_TARGET, caption: dndIsFilledModeCaptionMap[DND_ISFILLED_MODE.FILL_ALL_TARGET] },
    { id: DND_ISFILLED_MODE.FILL_ANY_TARGET, caption: dndIsFilledModeCaptionMap[DND_ISFILLED_MODE.FILL_ANY_TARGET] },
    { id: DND_ISFILLED_MODE.FILL_TARGET_CAPACITY, caption: dndIsFilledModeCaptionMap[DND_ISFILLED_MODE.FILL_TARGET_CAPACITY] },
]

export type AnswerGroupOf<T> = {[group: number]: T}
export interface IDgIntElementDnDOption {
  id? : string, 
  value : string,
  textMode? : DND_OPTION_TEXT_MODE,
  image? : IContentElementImage,
  _cachedImageSize? : {width: number, height: number};
  imageLabelPosition?: LAYOUT_IMAGE_LABEL_MODE,
  answerGroup?: number;
  voiceover?: {url?: string, script?: string, entryId?: number};
}
export interface IDgIntElementDnDTarget {
  label: string;
  isLabelMath?: boolean;
  id? : string;
  answerGroup?: number;
  content : IDgIntElementDnDOption[];
  isNonScoring?: boolean;
  maxCapacity?: number;
}
export type IDgIntElementDnDAnswerSet = Array<{
  targetId: string,
  optionIds: string[],
  targetValue?: any,
}>
export enum DND_STYLE_MODE {
  DEFAULT = 'default',
  INDIVIDUAL = 'individual',
  HOME = 'home',
}
export interface IDgIntStyleConfig {
  styleMode?: DND_STYLE_MODE;
  elementColors?: {[id: string] : string}
  isUseCustomBorderColors?: boolean;
  elementBorderColors?: {[id: string] : string}
  //
  commonTargetBorderColor?: string;
  commonDraggableBorderColor?: string;
  // deprecated
  isStyleIndividualElement?: boolean;
}
export interface IDgIntFreeformHomeLayoutConfig {
  image?: IContentElementImage;
  _cachedImageSize?: {width: number, height: number};
  positionMap?: {[id: string]: {x: number, y: number}}
  _homePositionOffset?: {x: number, y: number}; // needed by config UI to calculate mouse position
}
export interface IDgIntHomeConfig {
  isGroupHome: boolean;
  layout: HOME_CONFIG_LAYOUT;
  element: string[][]; 
  isAddHomeLabel?: boolean;
  label?: string;
  isLabelMath?: boolean;
  labelPosition?: HOME_LABEL_POSITION;
  isAddHomeNote?: boolean;
  isNoteMath?: boolean;
  note?: string;
  advancedLayoutConfig?: IDgIntAdvancedHomeLayoutElement;
  freeformLayoutConfig?: IDgIntFreeformHomeLayoutConfig;
  isAddGroupHomeLabels?: boolean;
  groupHomeLabels?: {
    label: string;
    isLabelMath?: boolean;
  }[]
}
export interface IDgIntDnDScoringParam extends IDgIntScoringParam {
  isAllowMultipleAnswers: boolean;
  answerSets: IDgIntElementDnDAnswerSet[];
  idToValueMap?: Map<string, any>;
  nonScoringTargetIds?: string[];
}
export interface IDgIntDnDConfig {
  highlightedOptionId?:string;
  idList: {
    options: string[];
    targets: string[];
    homes: string[];
  }
  label: {
    idToLabelMap: Map<string, string>;
  }
  diagram: {
    staticDiagram: DgDiagram;
    idToDiagramMap: Map<string, DgDiagram>; // for options, targets, and homes
    buttons?: DgButtonData[];
  }
  getDiagramUpdate?: () => {
    // will be called on each redraw
    // for each element in the map, the diagram will be updated
    staticDiagram?: DgDiagram;
    idToDiagramMap?: Map<string, DgDiagram>;
  }
  dndElementConfig?: {
    targetMaxCapacity?: number;
    globalTargetConfig?: any, // dndTarget config
    idToTargetConfig?: Map<string, any>
    idToTargetCapacity?: Map<string, number>
  }
  home: {
    config: IDgIntHomeConfig;
    position?: LAYOUT_HOME_POSITION,
  }
  functions?: {
    callbacks?: DgIntCallbacks;
    moveValidation?: (draggableId: string, targetId: string) => boolean;
  }
  voiceover? : {
    idToVoiceoverMap?: Map<string, DgIntVoiceoverData>;
  }
  styleProfiles: DgStyleProfile[];
  svgElement: SVGSVGElement;
  scoring: IDgIntDnDScoringParam;
  config: {
    isAllowGrouping: boolean;
    isUsingReusableDraggable: boolean;
    dndIsFilledMode: DND_ISFILLED_MODE;
  }
  _showAnswer: {
    isShowAnswer: boolean;
    activeAnswerSetIndex: number;
  }
  stateManager: typeof DnDStateManager;
}


export function isDnDTargetsContainMathOption(targets : IDgIntElementDnDTarget[]) : boolean {
  for (let target of targets) {
    for (let option of target.content) {
      if (option.textMode == DND_OPTION_TEXT_MODE.MATH) return true;
    }
  }
  return false;
}



function getDiffPair<T>(a : T[], b : T[]) : [T,T][] {
  const n = Math.max(a.length, b.length);
  let diffPairs : [any,any][] = [];
  for (let i = 0; i < n; i++) {
    if (a[i] !== b[i]) diffPairs.push([a[i], b[i]]);
  };
  return diffPairs;
}

export function resolveDnDTargetLabelChange(
  targets : IDgIntElementDnDTarget[],
  validTargetNames : string[],
) : IDgIntElementDnDTarget[] {
  const prevTargetNames = targets.map(target => target.label).filter(name => !isUnusedId(name));
  if (validTargetNames.length != prevTargetNames.length) return targets;
  const diffPairs = getDiffPair(validTargetNames, prevTargetNames);
  if (diffPairs.length != 1) return targets;
  const [newLabel, prevLabel] = diffPairs[0];
  // rename the target
  for (let target of targets){
    if (target.label == prevLabel) target.label = newLabel;
  }
  return targets;
}

export function resolveDnDTargetOptions(targets : IDgIntElementDnDTarget[], 
  validTargetNames : string[], isAllowGrouping = false
) : IDgIntElementDnDTarget[] {
  
  const oldTargetMap = new Map<string, IDgIntElementDnDTarget>();
  const targetIdMap = new Map<string, string>();
  for (let target of targets) {
    targetIdMap.set(target.label, target.id);
    oldTargetMap.set(target.label, target);
  }
  
  type targetContent = IDgIntElementDnDTarget['content'][number];
  let validTargetsMap = new Map<string, targetContent[]>();
  validTargetsMap.set(DND_UNUSED_ID, []);
  for (let name of validTargetNames) validTargetsMap.set(name, []);
  
  let invalidOptions : targetContent[] = []; // will be added to unused
  
  for (let target of targets){
    if (validTargetNames.includes(target.label) || target.label == DND_UNUSED_ID) {
      validTargetsMap.get(target.label)?.push(...target.content);
    } else {
      invalidOptions.push(...target.content);
    }
  }
  
  // if grouping is not allowed, make sure each target has only one content
  if (!isAllowGrouping) {
    for (let label of validTargetNames) {
      let target = validTargetsMap.get(label);
      if (target.length == 0) {
        target.push({value: ""});
      }
      if (target.length > 1) {
        invalidOptions.push(...target.slice(1));
        target.length = 1;
      }
    }
  }
  invalidOptions = invalidOptions.filter(option => { 
    if (option.value) return true;
    if (option.image) return true;
    return false;
  });
  validTargetsMap.get(DND_UNUSED_ID)?.push(...invalidOptions);

  let validTargets: IDgIntElementDnDTarget[] = validTargetNames.map(name => {
    const oldTarget = oldTargetMap.get(name);
    return {
      ...oldTarget,
      label: name,
      id: targetIdMap.get(name),
      content: validTargetsMap.get(name) ?? []
    }
  });
  validTargets.push({
    label: DND_UNUSED_ID,
    id: targetIdMap.get(DND_UNUSED_ID),
    content: validTargetsMap.get(DND_UNUSED_ID) ?? []
  }); // make sure unused targets are at the end
  
  return validTargets;
}

export function resolveDnDTargetOptions2(targets : IDgIntElementDnDTarget[], 
  validTargetNames : string[], isAllowGrouping = false,
) : IDgIntElementDnDTarget[] {
  
  const targetIdMap = new Map<string, string>();
  const targetIsLabelMathSet = new Set<string>();
  const targetIsNonScoringSet = new Set<string>();
  const targetAnswerGroupMap = new Map<string, number>();
  for (let target of targets) {
    targetIdMap.set(target.label, target.id);
    if (target.answerGroup) targetAnswerGroupMap.set(target.label, target.answerGroup);
    if (target.isLabelMath) targetIsLabelMathSet.add(target.label);
    if (target.isNonScoring) targetIsNonScoringSet.add(target.label);
  }
  
  const oldTargetLabels = targets.map(target => target.label);
  
  type targetContent = IDgIntElementDnDTarget['content'][number];
  let validTargetsMap = new Map<string, targetContent[]>();
  validTargetsMap.set(DND_UNUSED_ID, []);
  for (let name of validTargetNames) {
    if (oldTargetLabels.includes(name) || isAllowGrouping) validTargetsMap.set(name, []);
    else validTargetsMap.set(name, [{value: ""}]);
  }
  
  let invalidOptions : targetContent[] = []; // will be added to unused
  
  for (let target of targets){
    if (validTargetNames.includes(target.label) || target.label == DND_UNUSED_ID) {
      validTargetsMap.get(target.label)?.push(...target.content);
    } else {
      invalidOptions.push(...target.content);
    }
  }
  
  // if grouping is not allowed, make sure each target has only one content
  if (!isAllowGrouping) {
    for (let label of validTargetNames) {
      let target = validTargetsMap.get(label);
      // if (target.length == 0) {
      //   target.push({value: ""});
      // }
      if (target.length > 1) {
        invalidOptions.push(...target.slice(1));
        target.length = 1;
      }
    }
  }
  invalidOptions = invalidOptions.filter(option => { 
    if (option.value) return true;
    if (option.image) return true;
    return false;
  });
  validTargetsMap.get(DND_UNUSED_ID)?.push(...invalidOptions);

  let validTargets: IDgIntElementDnDTarget[] = validTargetNames.map(name => {
    return {
      label: name,
      id: targetIdMap.get(name),
      isLabelMath: targetIsLabelMathSet.has(name),
      answerGroup: targetAnswerGroupMap.get(name),
      isNonScoring: targetIsNonScoringSet.has(name),
      content: validTargetsMap.get(name) ?? []
    }
  });
  validTargets.push({
    label: DND_UNUSED_ID,
    id: targetIdMap.get(DND_UNUSED_ID),
    content: validTargetsMap.get(DND_UNUSED_ID) ?? []
  }); // make sure unused targets are at the end
  
  return validTargets;
}

export function resolveDnDOptionOrdering(targets : IDgIntElementDnDTarget[], ordering : number[]) : number[] {
  const optionCount = targets.reduce((acc, target) => acc + target.content.length, 0);
  if (ordering.length != optionCount) {
    ordering = Array.from({length: optionCount}, (_, i) => i);
  }
  return ordering;
}

// export function isAllowEmptyTarget(targets: IDgIntElementDnDTarget[]): boolean {
//   const activeTargets = targets.filter(t => !isUnusedId(t.label));
//   for (let target of activeTargets) {
//     if (target.content.length == 0) return true;
//   }
//   return false;
// }

// backward compatibility
export function generateDnDHomeConfigFromOrdering(dndTargets: IDgIntElementDnDTarget[], ordering: number[]) 
  : IDgIntHomeConfig 
{
  try {
    if (dndTargets[0].id == undefined) generateIdOnDnDTargets(dndTargets);
    const options = flattenArray(dndTargets.map(t => t.content));
    const orderingWithId = ordering.map(i => options[i].id);
    return {
      isGroupHome: false,
      layout: HOME_CONFIG_LAYOUT.DEFAULT,
      element: [orderingWithId]
    }
  } catch(e){
    return {
      isGroupHome: false,
      layout: HOME_CONFIG_LAYOUT.DEFAULT,
      element: [[]]
    }
  }
}

const DEFAULT_DND_DRAGGABLE_COLOR = '#ffffff';
const DEFAULT_DND_TARGET_COLOR = '#e0e0e0';
const DEFAULT_DND_HOME_COLOR = '#e0e0e0';
const DEFAULT_DND_DRAGGABLE_BORDER_COLOR = '#000000';
const DEFAULT_DND_TARGET_BORDER_COLOR = '#000000';
export function resolveDnDStyleConfig(
  styleConfig: IDgIntStyleConfig,
  idChangeMap: Map<string, string>, newOptionIds: string[], newTargetIds: string[],
  targetIds: string[], optionIds: string[], homeIds: string[]
): IDgIntStyleConfig {
  // backward compatibility
  if (styleConfig == undefined) return ({ styleMode: DND_STYLE_MODE.DEFAULT });
  if (styleConfig.styleMode == undefined) {
    styleConfig.styleMode = DND_STYLE_MODE.DEFAULT;
    if (styleConfig.isStyleIndividualElement) styleConfig.styleMode = DND_STYLE_MODE.INDIVIDUAL;
  }
  //
  if (styleConfig.styleMode == DND_STYLE_MODE.DEFAULT) return styleConfig;

  if (styleConfig.elementColors == undefined) styleConfig.elementColors = {};
  if (styleConfig.elementBorderColors == undefined) styleConfig.elementBorderColors = {};
  
  
  // resolve changes
  {
    let newElementColors = {};
    let newBorderColors = {};
    for (let id in styleConfig.elementColors){
      const newId = idChangeMap.get(id);
      if (!newId) continue;
      newElementColors[newId] = styleConfig.elementColors[id];
      newBorderColors[newId] = styleConfig.elementBorderColors[id];
    }
    for (let i = 0; i < homeIds.length; i++){
      const homeId = unusedId(i);
      const homeContentId = homeId + HOME_CONTENT_SUFFIX;
      newElementColors[homeId] = styleConfig.elementColors[homeId] 
        ?? DEFAULT_DND_HOME_COLOR;
      newElementColors[homeContentId] = styleConfig.elementColors[homeContentId] 
        ?? DEFAULT_DND_DRAGGABLE_COLOR;
    }
    styleConfig.elementColors = newElementColors;
    styleConfig.elementBorderColors = newBorderColors;
  }
  
  const homeContentIds = homeIds.map(id => id + HOME_CONTENT_SUFFIX);
  fillMissingDefaultDnDElementColors(
    styleConfig, targetIds, optionIds, homeIds, homeContentIds
  );
  
  return styleConfig;
}

function fillMissingDefaultDnDElementColors(
  styleConfig: IDgIntStyleConfig,
  targetIds: string[], optionIds: string[], homeIds: string[], homeContentIds: string[]
): void {
  const elementColors = styleConfig.elementColors;
  const borderColors = styleConfig.elementBorderColors;
  for (let id of targetIds){
    if (elementColors[id] == undefined) elementColors[id] = DEFAULT_DND_TARGET_COLOR;
    if (borderColors[id] == undefined) borderColors[id] = DEFAULT_DND_TARGET_BORDER_COLOR;
  }
  for (let id of optionIds){
    if (elementColors[id] == undefined) elementColors[id] = DEFAULT_DND_DRAGGABLE_COLOR;
    if (borderColors[id] == undefined) borderColors[id] = DEFAULT_DND_DRAGGABLE_BORDER_COLOR;
  }
  for (let id of homeContentIds){
    if (elementColors[id] == undefined) elementColors[id] = DEFAULT_DND_DRAGGABLE_COLOR;
  }
  for (let id of homeIds){
    if (elementColors[id] == undefined) elementColors[id] = DEFAULT_DND_HOME_COLOR;
  }
}

export function generateElementColorsObject(
  styleConfig: IDgIntStyleConfig, homeConfig: IDgIntHomeConfig
) : [IDgIntStyleConfig['elementColors'], IDgIntStyleConfig['elementBorderColors']] {
  switch (styleConfig.styleMode){
    case DND_STYLE_MODE.HOME: {
      const elementColors = {...styleConfig.elementColors};
      const elementBorderColors = { ...styleConfig.elementBorderColors ?? {} };
      homeConfig.element.forEach((homeGroup, i) => {
        const homeId = unusedId(i) + HOME_CONTENT_SUFFIX;
        for (let optionId of homeGroup){
          elementColors[optionId] = elementColors[homeId] ?? DEFAULT_DND_DRAGGABLE_COLOR;
          elementBorderColors[optionId] = elementBorderColors[homeId] ?? DEFAULT_DND_DRAGGABLE_BORDER_COLOR;
        }
      });
      return [elementColors, elementBorderColors];
    }
    case DND_STYLE_MODE.INDIVIDUAL: {
      return [styleConfig.elementColors, styleConfig.elementBorderColors];
    }
    case DND_STYLE_MODE.DEFAULT:
    default: {
      return [{}, {}];
    }
  }
}

export function getAllOptionIdsFromDnDTargets(dndTargets: IDgIntElementDnDTarget[]): string[] {
  return flattenArray(dndTargets.map(t => t.content.map(c => c.id)));
}

export function resolveDnDHomeConfig(
  homeConfig: IDgIntHomeConfig, idChangeMap: Map<string,string>, newOptionIds: string[],
  allOptionIds: string[],
): IDgIntHomeConfig {
  
  resolveFreeformHomeConfig(homeConfig, idChangeMap, newOptionIds, allOptionIds);
  resolveGroupHomeLabels(homeConfig);
  
  // if (!homeConfig.isGroupHome) {
  //   homeConfig.element = [flattenArray(homeConfig.element)];
  // }
  const maxCapacity = homeConfigLayoutFixedCapacity(homeConfig.layout);
  if (maxCapacity != undefined && maxCapacity != homeConfig.element.length){
    let newElements = [];
    for (let i = 0; i < maxCapacity; i++){
      newElements.push(homeConfig.element[i] ?? []);
    }
    for (let i = maxCapacity; i < homeConfig.element.length; i++){
      newElements[maxCapacity - 1].push(...homeConfig.element[i]);
    }
    homeConfig.element = newElements;
  }
  
  if (homeConfig.layout == HOME_CONFIG_LAYOUT.CUSTOM_ADVANCED && homeConfig.advancedLayoutConfig == undefined){
    homeConfig.advancedLayoutConfig = defaultElement;
  }
  
  for (let i in homeConfig.element){
    let row = homeConfig.element[i];
    for (let j in row) {
      row[j] = idChangeMap.get(row[j]);
    }
    homeConfig.element[i] = row.filter(x => x);
  }
  if (homeConfig.element[0] == undefined) homeConfig.element[0] = [];
  homeConfig.element[0].push(...newOptionIds)
  
  // revalidate, make sure all ids is covered
  const generatedOptionIds = flattenArray(homeConfig.element);
  const isValid = arraysEqual(allOptionIds, generatedOptionIds);
  if (!isValid){
    homeConfig.element = [allOptionIds];
  }
  
  return homeConfig
}
function resolveFreeformHomeConfig(
  homeConfig: IDgIntHomeConfig, idChangeMap: Map<string,string>, newOptionIds: string[], allOptionIds: string[],
){ 
  if (homeConfig.layout != HOME_CONFIG_LAYOUT.FREEFORM) return;
  const newPositions: { [id: string]: { x: number, y: number } } = {};
  for (let id of allOptionIds) {
    newPositions[id] = { x: 0, y: 0 };
  }
  if (homeConfig.freeformLayoutConfig.positionMap){
    for (let id of Object.keys(homeConfig.freeformLayoutConfig.positionMap)) {
      const newId = idChangeMap.get(id);
      if (!newId) continue;
      newPositions[newId] = homeConfig.freeformLayoutConfig.positionMap[id];
    }
  }
  homeConfig.freeformLayoutConfig.positionMap = newPositions;
}
function resolveGroupHomeLabels(homeConfig: IDgIntHomeConfig): void {
  if (homeConfig.groupHomeLabels == undefined) {
    homeConfig.groupHomeLabels = [];
  }
  const homeCount = homeConfig.element.length;
  if (homeConfig.groupHomeLabels.length < homeCount) {
    const diff = homeCount - homeConfig.groupHomeLabels.length;
    for (let i = 0; i < diff; i++) {
      homeConfig.groupHomeLabels.push({
        label: "",
        isLabelMath: false,
      })
    }
  }
}


export function resolveDnDAltAnswers(
  altAnswers: undefined | IDgIntElementDnDAnswerSet[], idChangeMap: Map<string,string>, 
  dndTargets: IDgIntElementDnDTarget[],
) : undefined | IDgIntElementDnDAnswerSet[] {
  if (altAnswers == undefined) return undefined;
  // substitute for idChanges
  const targetIds = dndTargets.filter(t => !isUnusedId(t.label) && !t.isNonScoring).map(t => t.id);
  for (let i in altAnswers){
    const targetMap = new Map<string, string[]>();
    for (let target of altAnswers[i]){
      const targetId = idChangeMap.get(target.targetId);
      const optionIds = target.optionIds.map(optionId => idChangeMap.get(optionId)).filter(x => x);
      targetMap.set(targetId, optionIds);
    }
    const answerSet = targetIds.map(targetId => {
      const optionIds = targetMap.get(targetId) ?? [];
      return { targetId, optionIds }
    })
    altAnswers[i] = answerSet;
  }
  return altAnswers;
}

function isTargetAllEmpty(dndTargets: IDgIntElementDnDTarget[]): boolean {
  for (let target of dndTargets){
    if (isUnusedId(target.label)) continue;
    if (target.content.length != 0) return false;
  }
  return true;
}
export function resolveTargetAndAnswersForCombinedOptions(
  isCombineOption: boolean, dndTargets: IDgIntElementDnDTarget[], altAnswers: IDgIntElementDnDAnswerSet[]
){
  if (isCombineOption){
    if (isTargetAllEmpty(dndTargets) && altAnswers.length > 0) return; // detect change
    // individual -> combined
    const defaultAnswerSet = generateDefaultAnswerSet(dndTargets);
    console.log(defaultAnswerSet)
    altAnswers.unshift(defaultAnswerSet);
    
    const contents = flattenArray(dndTargets.map(t => t.content));
    dndTargets.forEach(t => t.content = []);
    const unusedTarget = dndTargets.find(t => isUnusedId(t.label));
    unusedTarget.content = contents;
  } else {
    if (!isTargetAllEmpty(dndTargets)) return; // detect change
    
    // combined -> individual
    const dndCombinedTarget = dndTargets[dndTargets.length-1];
    if (!dndCombinedTarget) return;
    
    const defaultAnswerSet = altAnswers.shift();
    if (!defaultAnswerSet) return;
    
    for (let targetAnswer of defaultAnswerSet){
      const target = dndTargets.find(t => t.id == targetAnswer.targetId);
      if (!target) continue;
      for (let optionId of targetAnswer.optionIds) {
        const optionIndex = dndCombinedTarget.content.findIndex(o => o.id == optionId);
        if (optionIndex == -1) continue;
        
        target.content.push(dndCombinedTarget.content[optionIndex]);
        dndCombinedTarget.content.splice(optionIndex, 1);
      }
    }
  }
}

export function resolveDnDAnswerGroup(
  isUsingAnswerGroup: boolean, dndTargets: IDgIntElementDnDTarget[], homeConfig: IDgIntHomeConfig,
  isCombineOptionContainer: boolean, answerSets: IDgIntElementDnDAnswerSet[] = []
) {
  if (isUsingAnswerGroup) {
    assignOptionsAnswerGroupFromHomeConfig(dndTargets, homeConfig);
    alignDnDTargetAndContentAnswerGroup(dndTargets);
    if (isCombineOptionContainer && answerSets.length > 0) {
      alignDnDTargetAnswerGroupBasedOnAnswerSet(dndTargets, answerSets[0]);
    }
  } else {
    dndTargets.forEach(t => delete t.answerGroup);
    dndTargets.forEach(t => t.content.forEach(c => delete c.answerGroup));
  }
}
function alignDnDTargetAndContentAnswerGroup(dndTargets: IDgIntElementDnDTarget[]){
  for (let target of dndTargets) {
    if (isUnusedId(target.label)) continue;
    if (target.content.length == 1 && target.content[0].answerGroup != undefined) {
      target.answerGroup = target.content[0].answerGroup;
    }
  }
}
function assignOptionsAnswerGroupFromHomeConfig(dndTargets: IDgIntElementDnDTarget[], homeConfig: IDgIntHomeConfig) {
  const idToGroupMap = new Map<string,number>();
  homeConfig.element.forEach((row, i) => {
    row.forEach((id) => { 
      idToGroupMap.set(id, i) 
    });
  })
  dndTargets.forEach(target => {
    target.content.forEach(content => {
      content.answerGroup = idToGroupMap.get(content.id);
    })
  })
}
function alignDnDTargetAnswerGroupBasedOnAnswerSet(dndTargets: IDgIntElementDnDTarget[], answerSet: IDgIntElementDnDAnswerSet) {
  const idToGroupMap = getOptionIdToGroupMap(dndTargets);
  for (let targetData of answerSet) {
    if (targetData.optionIds.length == 1 && idToGroupMap.get(targetData.optionIds[0]) != undefined) {
      idToGroupMap.set(targetData.targetId, idToGroupMap.get(targetData.optionIds[0]));
    }
  }
  for (let target of dndTargets) {
    const group = idToGroupMap.get(target.id);
    if (group == undefined) continue;
    target.answerGroup = group;
  }
}
function getOptionIdToGroupMap(dndTargets: IDgIntElementDnDTarget[]): Map<string, number> {
  const optionIdToGroupMap = new Map<string, number>();
  dndTargets.forEach(target => {
    target.content.forEach(content => {
      optionIdToGroupMap.set(content.id, content.answerGroup ?? 0);
    })
  })
  return optionIdToGroupMap;
}

export function generateIdOnDnDTargets(dndTargets : IDgIntElementDnDTarget[]) : void{
  const idGenerator = new IdGenerator();
  for (let target of dndTargets){
    target.id = idGenerator.generate('target_');
    for (let content of target.content){
      content.id = idGenerator.generate('option_');
    }
  }
}

export function generateOptionIdMap(dndTargets : IDgIntElementDnDTarget[]) : Map<string,IDgIntElementDnDOption> {
  const idMap = new Map<string,IDgIntElementDnDOption>();
  for (let target of dndTargets){
    for (let option of target.content){
      idMap.set(option.id, option);
    }
  }
  return idMap;
}

export function generateDefaultAnswerSet(dndTargets: IDgIntElementDnDTarget[]) : IDgIntElementDnDAnswerSet {
  return dndTargets
    .filter(target => !isUnusedId(target.label) && !target.isNonScoring)
    .map(target => {
      const optionIds = target.content.map(o => o.id);
      return {
        targetId: target.id,
        optionIds
    }
  });
}

/**
 * also return the map of the changed target id and the new ids
 *
 * return [idChangeMap, newOptionIds, newTargetIds]
*/
export function regenerateIdOnDnDTargets(dndTargets : IDgIntElementDnDTarget[]) : 
  [Map<string,string>, string[], string[]]
{
  let changeMap = new Map<string,string>();
  let newOptionIds = [];
  let newTargetIds = [];
  const idGenerator = new IdGenerator();
  for (let target of dndTargets){
    let id = idGenerator.generate('target_');
    if (target.id) changeMap.set(target.id, id); else newTargetIds.push(id);
    target.id = id;
    for (let content of target.content){
      let id = idGenerator.generate('option_');
      if (content.id) changeMap.set(content.id, id); else newOptionIds.push(id);
      content.id = id;
    }
  }
  return [changeMap, newOptionIds, newTargetIds];
}

export function groupByHome<T>(elements: T[], homeConfigElement: string[][], idToIndexMap: Map<string,number>) : T[][] {
  let groupedElement : T[][] = [];
  for (let row of homeConfigElement){
    let group = [];
    for (let id of row){
      const index = idToIndexMap.get(id);
      group.push(elements[index]);
    }
    groupedElement.push(group);
  }
  return groupedElement;
}

const EM_BORDER_RADIUS = 0.8;
const BORDER_RADIUS_RESOLUTION = 10;
export function generateDnDBlock(
  dg: DgLib, width: number, height: number, styleProfiles: Array<DgStyleProfile>,
  isSolidByDefault: boolean = false
) {
  const styleParam = getStyleParam(styleProfiles);
  const dndBlockBorderRadius = styleParam.dnd_block_border_radius ?? EM_BORDER_RADIUS;
  let dndBlock : DgDiagram = dg.rectangle(width, height).append_tags(TAG_DND_BLOCK)
  if (!isSolidByDefault) dndBlock = dndBlock.strokedasharray([5]);
  dndBlock = dndBlock.apply(dg.mod.round_corner(dndBlockBorderRadius, undefined, BORDER_RADIUS_RESOLUTION));
  return dg.style.applyStyleProfiles(dndBlock, styleProfiles) as DgDiagram;
}


export function attachLabelDiagram(
  dg: DgLib, diagram: DgDiagram, labelDiagram: DgDiagram, pos: HOME_LABEL_POSITION, padding: number
): DgDiagram{
  switch (pos){
    case HOME_LABEL_POSITION.LEFT:{
      const pos = diagram.get_anchor('center-left').add(dg.V2(-padding, 0));
      const positionedLabelDiagram = labelDiagram.move_origin('center-right').position(pos);
      return dg.diagram_combine(positionedLabelDiagram, diagram);
    }
    case HOME_LABEL_POSITION.LEFT_TOP :{
      const pos = diagram.get_anchor('top-left').add(dg.V2(-padding, 0));
      const positionedLabelDiagram = labelDiagram.move_origin('top-right').position(pos);
      return dg.diagram_combine(positionedLabelDiagram, diagram);
    }
    case HOME_LABEL_POSITION.TOP_LEFT :{
      const pos = diagram.get_anchor('top-left').add(dg.V2(0, padding));
      const positionedLabelDiagram = labelDiagram.move_origin('bottom-left').position(pos);
      return dg.diagram_combine(positionedLabelDiagram, diagram);
    }
    case HOME_LABEL_POSITION.BOTTOM :{
      const pos = diagram.get_anchor('bottom-center').add(dg.V2(0, -padding));
      const positionedLabelDiagram = labelDiagram.move_origin('top-center').position(pos);
      return dg.diagram_combine(positionedLabelDiagram, diagram);
    }
    case HOME_LABEL_POSITION.BOTTOM_LEFT :{
      const pos = diagram.get_anchor('bottom-left').add(dg.V2(0, -padding));
      const positionedLabelDiagram = labelDiagram.move_origin('top-left').position(pos);
      return dg.diagram_combine(positionedLabelDiagram, diagram);
    }
    case HOME_LABEL_POSITION.TOP : 
    default:{
      const pos = diagram.get_anchor('top-center').add(dg.V2(0, padding));
      const positionedLabelDiagram = labelDiagram.move_origin('bottom-center').position(pos);
      return dg.diagram_combine(positionedLabelDiagram, diagram);
    }
  }
}

export function generateHomeTargetDiagramsWithLabel(
  dg : DgLib,
  homePosition : LAYOUT_HOME_POSITION,
  homeConfig: IDgIntHomeConfig,
  groupedOptionDiagrams : DgDiagram[][],
  diagram: DgDiagram,
  homeMaxWidth : number,
  padding: number,
  textDiagramGenerator: (s: string) => DgDiagram,
  mathDiagramGenerator: (s: string) => DgDiagram,
  imageManager: DgIntImageManager,
  extraWidth?: number,
  styleParam: DgIntStyleParam = {},
) : [DgDiagram[], DgDiagram, ] {
  const [homeDgs, labelDg] =  generateHomeTargetDiagramsWithLabel0(
    dg, homePosition, homeConfig, groupedOptionDiagrams,
    diagram, homeMaxWidth, padding, textDiagramGenerator, mathDiagramGenerator, imageManager, extraWidth, styleParam);
  if (!homeConfig.isAddHomeNote) return [homeDgs, labelDg];
  
  // ----- code below is for notes
  const noteDiagram = homeConfig.isNoteMath ?
    mathDiagramGenerator(homeConfig.note ?? "") : textDiagramGenerator(homeConfig.note ?? "");
  
  const homeCombinedDg = dg.diagram_combine(...homeDgs, labelDg);
  const noteDiagramSize = dg.geometry.size(noteDiagram);
  const diagramSize = dg.geometry.size(diagram);
  const homeSize = dg.geometry.size(homeCombinedDg);
  
  const isTopHomePosition = homePosition == LAYOUT_HOME_POSITION.TOP || homePosition == LAYOUT_HOME_POSITION.TOP_LEFT;
  const isLeftAlignedHomePotision = homePosition == LAYOUT_HOME_POSITION.TOP_LEFT || homePosition == LAYOUT_HOME_POSITION.BOTTOM_LEFT;
  
  const vhomeBotLeft = homeCombinedDg.get_anchor('bottom-left');
  const yPos = vhomeBotLeft.y;
  let xPos = vhomeBotLeft.x;
  if (diagramSize[0] > homeSize[0] && !isLeftAlignedHomePotision){
    const dx = (diagramSize[0] - homeSize[0]) / 2;
    xPos -= dx;
  }
  
  if (isTopHomePosition && !isHomeConfigLayoutFixedPosition(homeConfig.layout)){
    const positionedNoteDg  = noteDiagram.move_origin("bottom-left").position(dg.V2(xPos, yPos));
    const positionedHomeDgs = homeDgs.map(d => d.translate(dg.V2(0, noteDiagramSize[1]+padding)))
    const positionedLabelDg = labelDg.translate(dg.V2(0, noteDiagramSize[1]+padding));
    return [positionedHomeDgs, dg.diagram_combine(positionedLabelDg, positionedNoteDg)];
  } else {
    const positionedNoteDg  = noteDiagram.move_origin("top-left").position(dg.V2(xPos, yPos - padding));
    const positionedHomeDgs = homeDgs;
    const positionedLabelDg = labelDg;
    return [positionedHomeDgs, dg.diagram_combine(positionedLabelDg, positionedNoteDg)];
  }
}

function generateHomeTargetDiagramsWithLabel0(
  dg : DgLib,
  homePosition : LAYOUT_HOME_POSITION,
  homeConfig: IDgIntHomeConfig,
  groupedOptionDiagrams : DgDiagram[][],
  diagram: DgDiagram,
  homeMaxWidth : number,
  padding: number,
  textDiagramGenerator: (s: string) => DgDiagram,
  mathDiagramGenerator: (s: string) => DgDiagram,
  imageManager: DgIntImageManager,
  extraWidth?: number,
  styleParam: DgIntStyleParam = {},
) : [DgDiagram[], DgDiagram, ] {
  
  const homeLayoutDiagram = generateHomeTargetDiagrams(
    dg, homePosition, homeConfig, groupedOptionDiagrams, diagram, 
    homeMaxWidth, padding, textDiagramGenerator, mathDiagramGenerator, imageManager, extraWidth, styleParam);
  
  const originalHomeTargetDiagrams = homeLayoutDiagram.homes; // list of individual home diagrams
  const originalHomeTargetGrouped = dg.diagram_combine(...originalHomeTargetDiagrams);
  const staticHomeDiagram = homeLayoutDiagram.static ?? dg.empty(originalHomeTargetGrouped.get_anchor('center-center')); // static part of the home diagram
  
  if (!homeConfig.isAddHomeLabel || !homeConfig.label || isHomeConfigLayoutFixedPosition(homeConfig.layout)) 
    return [originalHomeTargetDiagrams, staticHomeDiagram];
  
  // code below is for home label
  const labelDiagram = homeConfig.isLabelMath ?
    mathDiagramGenerator(homeConfig.label ?? ""):
    textDiagramGenerator(homeConfig.label ?? "");
  const combinedHomeLayoutDiagram = dg.diagram_combine(staticHomeDiagram, originalHomeTargetGrouped)
  const isIndividualLayout = isConfigIndividualHome(homeConfig)
  const offset = isIndividualLayout ? 2*SOURCE_ZONE_OFFSET : SOURCE_ZONE_OFFSET;
  
  switch (homeConfig.labelPosition){
    case HOME_LABEL_POSITION.LEFT : {
      const distributedDiagram = dg.distribute_horizontal_and_align(
        [labelDiagram, combinedHomeLayoutDiagram], padding, 'center');
      const positionedDiagram = alignHomeToReference(dg, distributedDiagram, diagram, homePosition, offset);
      const labelDg: DgDiagram   = positionedDiagram.children[0];
      const staticDg: DgDiagram  = positionedDiagram.children[1].children[0];
      const homeDgs: DgDiagram[] = positionedDiagram.children[1].children[1].children;
      return [homeDgs, labelDg.combine(staticDg)];
    }
    case HOME_LABEL_POSITION.LEFT_TOP : {
      const distributedDiagram = dg.distribute_horizontal_and_align(
        [labelDiagram, combinedHomeLayoutDiagram], padding, 'top');
      const positionedDiagram = alignHomeToReference(dg, distributedDiagram, diagram, homePosition, offset);
      const labelDg: DgDiagram   = positionedDiagram.children[0];
      const staticDg: DgDiagram  = positionedDiagram.children[1].children[0];
      const homeDgs: DgDiagram[] = positionedDiagram.children[1].children[1].children;
      return [homeDgs, labelDg.combine(staticDg)];
    }
    case HOME_LABEL_POSITION.TOP_LEFT : {
      const distributedDiagram = dg.distribute_vertical_and_align(
        [labelDiagram, combinedHomeLayoutDiagram], padding, 'left');
      const positionedDiagram = alignHomeToReference(dg, distributedDiagram, diagram, homePosition, offset);
      const staticDg: DgDiagram  = positionedDiagram.children[1].children[0];
      const homeDgs: DgDiagram[] = positionedDiagram.children[1].children[1].children;
      let labelDg: DgDiagram   = positionedDiagram.children[0];
      
      const diagramSize = dg.geometry.size(diagram);
      const homeSize = dg.geometry.size(combinedHomeLayoutDiagram);
      if (diagramSize[0] > homeSize[0]){
        const dx = (diagramSize[0] - homeSize[0]) / 2;
        labelDg = labelDg.translate(dg.V2(-dx, 0));
      }
      return [homeDgs, labelDg.combine(staticDg)];
    }
    case HOME_LABEL_POSITION.TOP : 
    default: {
      const distributedDiagram = dg.distribute_vertical_and_align(
        [labelDiagram, combinedHomeLayoutDiagram], padding, 'center');
      const positionedDiagram = alignHomeToReference(dg, distributedDiagram, diagram, homePosition, offset);
      const labelDg: DgDiagram   = positionedDiagram.children[0];
      const staticDg: DgDiagram  = positionedDiagram.children[1].children[0];
      const homeDgs: DgDiagram[] = positionedDiagram.children[1].children[1].children;
      return [homeDgs, labelDg.combine(staticDg)];
    }
  }
}

export function generateHomeTargetDiagrams(
  dg : DgLib,
  homePosition : LAYOUT_HOME_POSITION,
  homeConfig: IDgIntHomeConfig,
  groupedOptionDiagrams : DgDiagram[][],
  diagram: DgDiagram,
  homeMaxWidth : number,
  padding: number,
  textDiagramGenerator: (s: string) => DgDiagram,
  mathDiagramGenerator: (s: string) => DgDiagram,
  imageManager: DgIntImageManager,
  extraWidth?: number,
  styleParam: DgIntStyleParam = {},
): { static?: DgDiagram, homes: DgDiagram[] } {
  const homeLayout = homeConfig.layout;
  
  const isAddGroupHomeLabels = homeConfig.isAddGroupHomeLabels && homeConfig.isAddHomeLabel && 
    isHomeConfigLayoutAllowGroupLabel(homeConfig.layout);
  if (isAddGroupHomeLabels) {
    return generateHomeTargetDiagramsWithGroupLabel(
      dg, homePosition, homeConfig, groupedOptionDiagrams, 
      diagram, homeMaxWidth, padding, textDiagramGenerator, mathDiagramGenerator, imageManager, extraWidth);
  }
  
  const isExtendHomeGroup = styleParam?.dnd_extend_home_group;
  const extensionDirection = getHomeExtensionDirection(homePosition);
  
  switch (homeLayout) {
    case HOME_CONFIG_LAYOUT.CUSTOM_ADVANCED: {
      const config = homeConfig.advancedLayoutConfig;
      const optionDiagrams = flattenArray(groupedOptionDiagrams);
      const bgDiagrams = optionDiagrams.map(o => { 
        const bgDiagram = o.get_tagged_elements(TAG_DND_BLOCK)[0];
        if (bgDiagram) return bgDiagram.copy();
        const bbox = o.bounding_box();
        return dg.rectangle_corner(bbox[0], bbox[1]);
      });
      const ctx: IHomeLayoutGenerationContext = {
        targetPlaceholder: bgDiagrams[0], // TODO: fix this
        textDiagramGenerator,
      }
      const homeLayout = generateAdvancedHomeDiagramLayout(dg, config, ctx);
      const combined = dg.diagram_combine(homeLayout.static, dg.diagram_combine(...flattenArray(homeLayout.homes)));
      const positionedSourceZones = alignHomeToReference(dg, combined, diagram, homePosition);
      
      const positionedStatic = positionedSourceZones.children[0];
      const positionedHomes = positionedSourceZones.children[1].children;
      return {
        static: positionedStatic,
        homes: positionedHomes,
      }
    }
    case HOME_CONFIG_LAYOUT.FREEFORM: {
      const config = homeConfig.freeformLayoutConfig;
      const optionIds = homeConfig.element[0];
      if (config.image?.url) {
        const url = config.image.url;
        const scale = config.image.scale;
        imageManager?.queueSizeCalculation(url, config);
        // 100unit -> 20em (1unit = 1/5em)
        const widthEm = scale / 5; 
        const sz = imageManager?.getSize(url, widthEm) ?? {width: 1, height: 1};
        const img = dg.image(url, sz.width, sz.height).append_tags(TAG_DND_FREEFORM_HOME_BGIMG);
        
        const blockDiagrams = groupedOptionDiagrams[0].map((o,i) => { 
          const bgDiagram = o.get_tagged_elements(TAG_DND_BLOCK)[0];
          const blockDiagram = bgDiagram ? bgDiagram.copy() : dg.rectangle_corner(o.bounding_box()[0], o.bounding_box()[1]);
          const optionId = optionIds[i];
          const pos = config.positionMap[optionId] ?? {x: 0, y: 0};
          return blockDiagram.position(dg.V2(pos.x, pos.y));
        });
        
        const combined = dg.diagram_combine(img, dg.diagram_combine(...blockDiagrams));
        const positionedSourceZones = alignHomeToReference(dg, combined, diagram, homePosition);
        const positionedBgImg: DgDiagram = positionedSourceZones.children[0];
        const positionedHomes: DgDiagram[] = positionedSourceZones.children[1].children;
        return {
          static: positionedBgImg,
          homes: positionedHomes,
        }
      }
    }
    case HOME_CONFIG_LAYOUT.INDIVIDUAL_COLUMN: {
      const sourceZones = groupedOptionDiagrams.map(optionDiagrams => {
        return generateIndividualSourceZone(
          dg, LAYOUT_HOME_POSITION.RIGHT, optionDiagrams, dg.empty(), homeMaxWidth, padding);
      });
      // use "3*padding" to make `Column` and `Column(Individual)` have the same spacing
      const distributedSourceZones = dg.distribute_horizontal_and_align(sourceZones, 3*padding, 'top');
      const positionedSourceZones = alignHomeToReference(dg, distributedSourceZones, diagram, homePosition, 2*SOURCE_ZONE_OFFSET);
      const groupZones = positionedSourceZones.children as DgDiagram[];
      const individualSourceZones = flattenArray(groupZones.map(g => g.children as DgDiagram[]));
      
      if (!isExtendHomeGroup) {
        return { homes: individualSourceZones };
      } else {
        const groupExtensionBgs = groupZones.map(g => generateExtendedHome(dg, g, diagram, extensionDirection, true))
        return { homes: individualSourceZones,  static: dg.diagram_combine(...groupExtensionBgs)}
      }
    }
    case HOME_CONFIG_LAYOUT.INDIVIDUAL_DEFAULT: {
      const isReverse = homePosition == LAYOUT_HOME_POSITION.TOP || homePosition == LAYOUT_HOME_POSITION.LEFT || homePosition == LAYOUT_HOME_POSITION.LEFT_TOP;
      let sourceZones : DgDiagram[] = [];
      if (isReverse) groupedOptionDiagrams = [...groupedOptionDiagrams].reverse();
      for (let optionDiagrams of groupedOptionDiagrams){
        const reference = sourceZones.length == 0 ? diagram : sourceZones[sourceZones.length-1];
        const sourceZone = generateIndividualSourceZone(dg, homePosition, optionDiagrams, reference, homeMaxWidth, padding);
        sourceZones.push(sourceZone);
      }
      if (isReverse) sourceZones = sourceZones.reverse();
      const individualSourceZones = flattenArray(sourceZones.map(s => s.children as DgDiagram[]));
      
      if (!isExtendHomeGroup) {
        return { homes: individualSourceZones };
      } else {
        const groupExtensionBgs = sourceZones.map(g => generateExtendedHome(dg, g, diagram, extensionDirection, true))
        return { homes: individualSourceZones,  static: dg.diagram_combine(...groupExtensionBgs)}
      }
    }
    case HOME_CONFIG_LAYOUT.HORIZONTAL_SINGLE_COLUMN: {
      const sourceZones = groupedOptionDiagrams.map(optionDiagrams => {
        return generateSourceZone(dg, LAYOUT_HOME_POSITION.RIGHT, optionDiagrams, dg.empty(), homeMaxWidth, padding, extraWidth-1);
      });
      const distributedSourceZones = dg.distribute_horizontal_and_align(sourceZones, padding, 'top');
      const positionedSourceZones = alignHomeToReference(dg, distributedSourceZones, diagram, homePosition);
      if (!isExtendHomeGroup || extensionDirection != 'vertical') {
        return { homes: positionedSourceZones.children };
      } else {
        return { homes: positionedSourceZones.children.map(d => generateExtendedHome(dg, d, diagram, extensionDirection))}
      }
    }
    case HOME_CONFIG_LAYOUT.TOP_AND_BOTTOM: {
      const topOptionDiagrams = groupedOptionDiagrams[0] ?? [];
      const sourceZoneTop = generateSourceZone(
        dg, LAYOUT_HOME_POSITION.TOP, topOptionDiagrams, diagram, homeMaxWidth, padding);
      const bottomOptionDiagrams = groupedOptionDiagrams[1] ?? [];
      const sourceZoneBottom = generateSourceZone(
        dg, LAYOUT_HOME_POSITION.BOTTOM, bottomOptionDiagrams, diagram, homeMaxWidth, padding);
      return { homes: [sourceZoneTop, sourceZoneBottom] };
    }
    case HOME_CONFIG_LAYOUT.INDIVIDUAL_TOP_AND_BOTTOM: {
      const topOptionDiagrams = groupedOptionDiagrams[0] ?? [];
      const sourceZoneTop = generateIndividualSourceZone(
        dg, LAYOUT_HOME_POSITION.TOP, topOptionDiagrams, diagram, homeMaxWidth, padding);
      const bottomOptionDiagrams = groupedOptionDiagrams[1] ?? [];
      const sourceZoneBottom = generateIndividualSourceZone(
        dg, LAYOUT_HOME_POSITION.BOTTOM, bottomOptionDiagrams, diagram, homeMaxWidth, padding);
      
      const individualSourceZonesTop = sourceZoneTop.children as DgDiagram[];
      const individualSourceZonesBottom = sourceZoneBottom.children as DgDiagram[];
      return { homes: [...individualSourceZonesTop, ...individualSourceZonesBottom] };
    }
    case HOME_CONFIG_LAYOUT.DEFAULT: 
    default: {
      const isReverse = homePosition == LAYOUT_HOME_POSITION.TOP || homePosition == LAYOUT_HOME_POSITION.LEFT || homePosition == LAYOUT_HOME_POSITION.LEFT_TOP;
      let sourceZones : DgDiagram[] = [];
      if (isReverse) groupedOptionDiagrams = [...groupedOptionDiagrams].reverse();
      for (let optionDiagrams of groupedOptionDiagrams){
        const reference = sourceZones.length == 0 ? diagram : sourceZones[sourceZones.length-1];
        const sourceZone = generateSourceZone(dg, homePosition, optionDiagrams, reference, homeMaxWidth, padding, extraWidth);
        sourceZones.push(sourceZone);
      }
      if (isReverse) sourceZones = sourceZones.reverse();
      
      if (!isExtendHomeGroup) {
        return { homes: sourceZones };
      } else {
        return { homes: sourceZones.map(d => generateExtendedHome(dg, d, diagram, extensionDirection))}
      }
    }
  }
}

function generateExtendedHome(
  dg: DgLib, homeDiagram: DgDiagram, refDiagram: DgDiagram, orientation: 'vertical' | 'horizontal', isInvisible = false
): DgDiagram 
{
  const mainDiagrams = refDiagram.get_tagged_elements(TAG_MAIN_DIAGRAM);
  refDiagram = mainDiagrams.length == 0 ? refDiagram : mainDiagrams[0];
    
  const homeBBox = homeDiagram.bounding_box();
  if (orientation === 'vertical') {
    const homeHeight = dg.geometry.size(homeDiagram)[1];
    const refHeight = dg.geometry.size(refDiagram)[1];
    let rect = dg.rectangle_corner(homeBBox[0], homeBBox[1])
    if (isInvisible) {
      rect = rect.opacity(0).append_tags(TAG_EXTENDED_HOME_INVISIBLE_BG);
    }
    return homeHeight > refHeight ? rect : setRectangleHeight(dg, rect, refHeight);
  } else {
    const homeWidth = dg.geometry.size(homeDiagram)[0];
    const refWidth = dg.geometry.size(refDiagram)[0];
    const rect = dg.rectangle_corner(homeBBox[0], homeBBox[1]).opacity(0)
      .append_tags(TAG_EXTENDED_HOME_INVISIBLE_BG);
    return homeWidth > refWidth ? rect : setRectangleWidth(dg, rect, refWidth);
  }
}


export function generateHomeTargetDiagramsWithGroupLabel(
  dg : DgLib,
  homePosition : LAYOUT_HOME_POSITION,
  homeConfig: IDgIntHomeConfig,
  groupedOptionDiagrams : DgDiagram[][],
  diagram: DgDiagram,
  homeMaxWidth : number,
  padding: number,
  textDiagramGenerator: (s: string) => DgDiagram,
  mathDiagramGenerator: (s: string) => DgDiagram,
  imageManager: DgIntImageManager,
  extraWidth?: number,
): { static?: DgDiagram, homes: DgDiagram[] } {
  const homeLayout = homeConfig.layout;
  const textOrMathDg = (s: string, isMath: boolean) => isMath ? mathDiagramGenerator(s) : textDiagramGenerator(s);
  switch (homeLayout) {
    case HOME_CONFIG_LAYOUT.INDIVIDUAL_COLUMN: {
      const sourceZones = groupedOptionDiagrams.map((optionDiagrams,i) => {
        const sourceZone = generateIndividualSourceZone(dg, LAYOUT_HOME_POSITION.RIGHT, optionDiagrams, dg.empty(), homeMaxWidth, padding);
        const labelDg = textOrMathDg(homeConfig.groupHomeLabels?.[i]?.label ?? "", homeConfig.groupHomeLabels?.[i]?.isLabelMath);
        return dg.distribute_vertical_and_align([labelDg, sourceZone]);
      });
      // use "3*padding" to make `Column` and `Column(Individual)` have the same spacing
      const distributedSourceZones = dg.distribute_horizontal_and_align(sourceZones, 3*padding, 'top');
      const positionedSourceZones = alignHomeToReference(dg, distributedSourceZones, diagram, homePosition, 2*SOURCE_ZONE_OFFSET);
      const groupZones = positionedSourceZones.children.map(d => d.children[1]) as DgDiagram[];
      const individualSourceZones = flattenArray(groupZones.map(g => g.children as DgDiagram[]));
      return { 
        static: dg.diagram_combine(...positionedSourceZones.children.map(d => d.children[0])),
        homes: individualSourceZones 
      };
    }
    case HOME_CONFIG_LAYOUT.INDIVIDUAL_DEFAULT: {
      const isReverse = homePosition == LAYOUT_HOME_POSITION.TOP || homePosition == LAYOUT_HOME_POSITION.LEFT || homePosition == LAYOUT_HOME_POSITION.LEFT_TOP;
      let sourceZones : DgDiagram[] = [];
      if (isReverse) groupedOptionDiagrams = [...groupedOptionDiagrams].reverse();
      groupedOptionDiagrams.forEach((optionDiagrams, i) => {
        const reference = sourceZones.length == 0 ? diagram : sourceZones[sourceZones.length - 1];
        const labelStr = homeConfig.groupHomeLabels?.[i]?.label;
        const isLabelMath = homeConfig.groupHomeLabels?.[i]?.isLabelMath;
        const labelDg = labelStr ? textOrMathDg(labelStr, isLabelMath) : dg.empty();
        const sourceZone = generateIndividualSourceZone(dg, homePosition, optionDiagrams, reference, homeMaxWidth, padding, labelDg);
        sourceZones.push(sourceZone);
      });
      if (isReverse) sourceZones = sourceZones.reverse();
      const individualSourceZones = flattenArray(sourceZones.map(s => s.children[1].children as DgDiagram[]));
      return { 
        static: dg.diagram_combine(...sourceZones.map(d => d.children[0])),
        homes: individualSourceZones 
      };
    }
    case HOME_CONFIG_LAYOUT.HORIZONTAL_SINGLE_COLUMN: {
      const sourceZones = groupedOptionDiagrams.map((optionDiagrams,i) => {
        const sourceZone = generateSourceZone(dg, LAYOUT_HOME_POSITION.RIGHT, optionDiagrams, dg.empty(), homeMaxWidth, padding, extraWidth-1);
        const labelDg = textOrMathDg(homeConfig.groupHomeLabels?.[i]?.label ?? "", homeConfig.groupHomeLabels?.[i]?.isLabelMath);
        return dg.distribute_vertical_and_align([labelDg, sourceZone]);
      });
      const distributedSourceZones = dg.distribute_horizontal_and_align(sourceZones, padding, 'top');
      const positionedSourceZones = alignHomeToReference(dg, distributedSourceZones, diagram, homePosition);
      return {
        static: dg.diagram_combine(...positionedSourceZones.children.map(d => d.children[0])),
        homes: positionedSourceZones.children.map(d => d.children[1]),
      }
    }
    case HOME_CONFIG_LAYOUT.DEFAULT: 
    default: {
      const isReverse = homePosition == LAYOUT_HOME_POSITION.TOP || homePosition == LAYOUT_HOME_POSITION.LEFT || homePosition == LAYOUT_HOME_POSITION.LEFT_TOP;
      let sourceZones : DgDiagram[] = [];
      if (isReverse) groupedOptionDiagrams = [...groupedOptionDiagrams].reverse();
      groupedOptionDiagrams.forEach((optionDiagrams, i) => {
        const reference = sourceZones.length == 0 ? diagram : sourceZones[sourceZones.length-1];
        const labelStr = homeConfig.groupHomeLabels?.[i]?.label;
        const isLabelMath = homeConfig.groupHomeLabels?.[i]?.isLabelMath;
        const labelDg = labelStr ? textOrMathDg(labelStr, isLabelMath) : dg.empty();
        const sourceZone = generateSourceZone(dg, homePosition, optionDiagrams, reference, homeMaxWidth, padding, extraWidth, labelDg);
        sourceZones.push(sourceZone);
      });
      if (isReverse) sourceZones = sourceZones.reverse();
      return { 
        static: dg.diagram_combine(...sourceZones.map(d => d.children[0])),
        homes: sourceZones.map(d => d.children[1]),
      };
    }
  }
}

export function generateHomeGroupBg(
  dg: DgLib, homeDiagrams: DgDiagram[], homeConfig: IDgIntHomeConfig, styleParam: DgIntStyleParam, idToIndexMap: Map<string, number>,
  padding: number, staticHomeDiagram?: DgDiagram, homePosition?: LAYOUT_HOME_POSITION
): DgDiagram[] {
  const isVisibleHome = styleParam.visible_home_target;
  if (!isVisibleHome) return [];
  if (!isConfigIndividualHome(homeConfig)) return [];
  const groupedHomeDiagrams = groupByHome(homeDiagrams, homeConfig.element, idToIndexMap);
  
  let isExtendHomeGroup = styleParam?.["dnd_extend_home_group"];
  const extensionDirection = getHomeExtensionDirection(homePosition);
  let extensionSize = 0;
  if (isExtendHomeGroup) {
    const invisibleHomeExtensions = staticHomeDiagram.get_tagged_elements(TAG_EXTENDED_HOME_INVISIBLE_BG);
    if (invisibleHomeExtensions.length == 0) {
      isExtendHomeGroup = false
    } else {
      const invisibeHomeExtension = invisibleHomeExtensions[0];
      if (extensionDirection == 'vertical') {
        extensionSize = dg.geometry.size(invisibeHomeExtension)[1];
      } else {
        extensionSize = dg.geometry.size(invisibeHomeExtension)[0];
      }
    }
  }
  
  return groupedHomeDiagrams.map((diagrams, i) => {
    const combined = dg.diagram_combine(...diagrams);
    const bbox = combined.bounding_box();
    const bottomLeft = bbox[0].sub(dg.V2(padding, padding));
    const topRight = bbox[1].add(dg.V2(padding, padding));
    const homeBg = dg.rectangle_corner(bottomLeft, topRight).stroke('none');
    
    if (!isExtendHomeGroup) {
      return homeBg;
    } else {
      const originalSize = dg.geometry.size(combined);
      if (extensionDirection == 'vertical' && extensionSize > originalSize[1]) {
        return setRectangleHeight(dg, homeBg, extensionSize);
      } else {
        return setRectangleWidth(dg, homeBg, extensionSize);
      }
    }
  });
}

function getHomeExtensionDirection(homePosition: LAYOUT_HOME_POSITION): 'vertical' | 'horizontal' {
  if (homePosition == 'left' || homePosition == 'right') return 'vertical';
  return 'horizontal';
}

type diagramPadding = { left?: number, right?: number, top?: number, bottom?: number  }
export function generatePaddedDiagram(dg: DgLib, diagram: DgDiagram, paddingConfig: diagramPadding | undefined, factor = 20/100): DgDiagram {
  if (paddingConfig == undefined) return diagram;
  
  let padDiagrams: DgDiagram[] = [];
  const [imgWidth, imgHeight] = dg.geometry.size(diagram);
  if (paddingConfig.top){
    padDiagrams.push(
      dg.rectangle(imgWidth, paddingConfig.top * factor).opacity(0)
        .move_origin('bottom-left').position(diagram.get_anchor('top-left'))
    );
  }
  if (paddingConfig.bottom){
    padDiagrams.push(
      dg.rectangle(imgWidth, paddingConfig.bottom * factor).opacity(0)
        .move_origin('top-left').position(diagram.get_anchor('bottom-left'))
    );
  }
  if (paddingConfig.left){
    padDiagrams.push(
      dg.rectangle(paddingConfig.left * factor, imgHeight).opacity(0)
        .move_origin('top-right').position(diagram.get_anchor('top-left'))
    );
  }
  if (paddingConfig.right){
    padDiagrams.push(
      dg.rectangle(paddingConfig.right * factor, imgHeight).opacity(0)
        .move_origin('top-left').position(diagram.get_anchor('top-right'))
    );
  }
  
  if (padDiagrams.length == 0){
    return diagram;
  } else {
    return dg.diagram_combine(diagram, ...padDiagrams);
  }
}

const SOURCE_ZONE_OFFSET = 0.5;
export function generateSourceZone(
  dg : DgLib, position : LAYOUT_HOME_POSITION, optionDiagrams : DgDiagram[], diagram : DgDiagram,
  maxWidth: number, padding: number, extraWidth = 0, labelDiagram?: DgDiagram
) : DgDiagram {
  if (position == LAYOUT_HOME_POSITION.LEFT || position == LAYOUT_HOME_POSITION.RIGHT || position == LAYOUT_HOME_POSITION.LEFT_TOP || position == LAYOUT_HOME_POSITION.RIGHT_TOP) {
    maxWidth = Math.max(...optionDiagrams.map(d => dg.geometry.size(d)[0] ));
  }
  
  //TODO: make target dnd accept 2d padding                               
  const alignedDiagram = dg.distribute_variable_row(optionDiagrams, maxWidth, padding, padding);
  const [contentWidth, contentHeight] = dg.geometry.size(alignedDiagram);
  const width = contentWidth + (3 + extraWidth)*padding;
  const height = contentHeight + 2*padding;
  
  if (labelDiagram) {
    const homeAndLabel = dg.distribute_vertical_and_align([labelDiagram, dg.rectangle(width, height)]);
    return alignHomeToReference(dg, homeAndLabel, diagram, position)
  } else {
    return alignHomeToReference(dg, dg.rectangle(width, height), diagram, position)
  }
}

/**
 * Generate a diagram with diagram.children being list of individual homes
 */
export function generateIndividualSourceZone(
  dg : DgLib, position : LAYOUT_HOME_POSITION, optionDiagrams : DgDiagram[], diagram : DgDiagram,
  maxWidth: number, padding: number, labelDiagram?: DgDiagram
) : DgDiagram {
  if (position == LAYOUT_HOME_POSITION.LEFT || position == LAYOUT_HOME_POSITION.RIGHT || position == LAYOUT_HOME_POSITION.LEFT_TOP || position == LAYOUT_HOME_POSITION.RIGHT_TOP) {
    maxWidth = Math.max(...optionDiagrams.map(d => dg.geometry.size(d)[0] ));
  }
  
  const bgDiagrams = optionDiagrams.map(o => { 
    const bgDiagram = o.get_tagged_elements(TAG_DND_BLOCK)[0];
    if (bgDiagram) return bgDiagram.copy();
    const bbox = o.bounding_box();
    return dg.rectangle_corner(bbox[0], bbox[1]);
  });
  
  const alignedDiagram = dg.distribute_variable_row(bgDiagrams, maxWidth, padding, padding);
  if (labelDiagram) {
    const homeAndLabel = dg.distribute_vertical_and_align([labelDiagram, alignedDiagram]);
    return alignHomeToReference(dg, homeAndLabel, diagram, position, 2*SOURCE_ZONE_OFFSET)
  } else {
    return alignHomeToReference(dg, alignedDiagram, diagram, position, 2*SOURCE_ZONE_OFFSET)
  }
}

export function assembleDraggableBlock(
  dg: DgLib, contentDiagram: DgDiagram, bgDiagram: DgDiagram, 
  padding: [number, number],justification: CONTENT_JUSTIFY
): DgDiagram {
  switch (justification){
    case CONTENT_JUSTIFY.LEFT: {
      const textLeftOrigin = contentDiagram.move_origin('center-left');
      const pos = bgDiagram.get_anchor('center-left').add(dg.V2(padding[1],0))
      return bgDiagram.combine(textLeftOrigin.position(pos));
    } break;
    case CONTENT_JUSTIFY.RIGHT: {
      const textRightOrigin = contentDiagram.move_origin('center-right');
      const pos = bgDiagram.get_anchor('center-right').add(dg.V2(-padding[1],0))
      return bgDiagram.combine(textRightOrigin.position(pos));
    } break;
    case CONTENT_JUSTIFY.CENTER: 
    default: {
      return bgDiagram.combine(contentDiagram);
    } break;
  }
}

export function alignHomeToReference(
  dg: DgLib, homeDiagram: DgDiagram, reference: DgDiagram, 
  position: LAYOUT_HOME_POSITION, offset: number = SOURCE_ZONE_OFFSET
) : DgDiagram {
  const V2 = dg.V2;
  switch (position) {
    case LAYOUT_HOME_POSITION.LEFT: {
      return homeDiagram
        .move_origin('center-right')
        .position(reference.get_anchor('center-left'))
        .translate(V2(-offset, 0))
        .move_origin('center-center')
        .stroke('none');
    }
    case LAYOUT_HOME_POSITION.LEFT_TOP: {
      return homeDiagram
        .move_origin('top-right')
        .position(reference.get_anchor('top-left'))
        .translate(V2(-offset, 0))
        .move_origin('center-center')
        .stroke('none');
    }
    case LAYOUT_HOME_POSITION.RIGHT: {
      return homeDiagram
        .move_origin('center-left')
        .position(reference.get_anchor('center-right'))
        .translate(V2(offset, 0))
        .move_origin('center-center')
        .stroke('none');
    }
    case LAYOUT_HOME_POSITION.RIGHT_TOP: {
      return homeDiagram
        .move_origin('top-left')
        .position(reference.get_anchor('top-right'))
        .translate(V2(offset, 0))
        .move_origin('center-center')
        .stroke('none');
    }
    case LAYOUT_HOME_POSITION.TOP: {
      return homeDiagram
        .move_origin('bottom-center')
        .position(reference.get_anchor('top-center'))
        .translate(V2(0, offset))
        .move_origin('center-center')
        .stroke('none');
    }
    case LAYOUT_HOME_POSITION.TOP_LEFT: {
      return homeDiagram
        .move_origin('bottom-left')
        .position(reference.get_anchor('top-left'))
        .translate(V2(0, offset))
        .move_origin('center-center')
        .stroke('none');
    }
    case LAYOUT_HOME_POSITION.BOTTOM_LEFT: {
      return homeDiagram
        .move_origin('top-left')
        .position(reference.get_anchor('bottom-left'))
        .translate(V2(0, -offset))
        .move_origin('center-center')
        .stroke('none');
    }
    case LAYOUT_HOME_POSITION.BOTTOM: 
    default:{
      return homeDiagram
        .move_origin('top-center')
        .position(reference.get_anchor('bottom-center'))
        .translate(V2(0, -offset))
        .move_origin('center-center')
        .stroke('none');
    }
  }
}

export function appendHomeSuffix(optionId: string){
  return optionId + HOME_SUFFIX;
}
export function isConfigIndividualHome(homeConfig: IDgIntHomeConfig): boolean {
  const homeLayout = homeConfig.layout;
  return homeLayout == HOME_CONFIG_LAYOUT.INDIVIDUAL_DEFAULT
    || homeLayout == HOME_CONFIG_LAYOUT.INDIVIDUAL_COLUMN
    || homeLayout == HOME_CONFIG_LAYOUT.INDIVIDUAL_TOP_AND_BOTTOM
    || homeLayout == HOME_CONFIG_LAYOUT.CUSTOM_ADVANCED
    || homeLayout == HOME_CONFIG_LAYOUT.FREEFORM;
    // || homeLayout == HOME_CONFIG_LAYOUT.INDIVIDUAL_ROW
}

export function generateVoiceoverDataMap(dndTargets: IDgIntElementDnDTarget[]): Map<string, DgIntVoiceoverData> {
  const map = new Map<string, DgIntVoiceoverData>();
  for (const target of dndTargets) {
    for (const option of target.content) {
      const id = option.id;
      const voiceover = option.voiceover;
      if (voiceover && voiceover.url && voiceover.script) {
        const trigger = new Subject<boolean>();
        map.set(id, {url: voiceover.url, trigger});
      }
    }
  }
  return map;
}

export function setupInteractiveDnD(dg: DgLib, int: DgInt, config: IDgIntDnDConfig): void {
  const isIndividualHome = isConfigIndividualHome(config.home.config);
  const baseId = getDnDElementBaseId;
  const isShowAnswer = config._showAnswer?.isShowAnswer ?? false;
  
  // transparent answer diagrams
  let answerDiagrams = [];
  if (isShowAnswer) {
    const answerSet = config.scoring.answerSets[config._showAnswer.activeAnswerSetIndex];
    //TODO: allow this for grouped answers
    for (let target of answerSet){
      const targetId = target.targetId;
      const targetDiagram = config.diagram.idToDiagramMap.get(targetId);
      if (!targetDiagram) continue;
      const targetPos = targetDiagram.origin;
      for (let optionId of target.optionIds){
        const optionDiagram = config.diagram.idToDiagramMap.get(optionId);
        if (!optionDiagram) continue;
        answerDiagrams.push(optionDiagram.position(targetPos))
      }
    }
  }
  
  const isUsingReusableDraggable = config.config.isUsingReusableDraggable ?? false;
  const stateManager = new config.stateManager(int, config);
  
  // register voiceover
  if (config.voiceover?.idToVoiceoverMap && config.functions.callbacks?.registerVoiceoverData) {
    const voiceoverDataList = Array.from(config.voiceover.idToVoiceoverMap.values());
    config.functions.callbacks.registerVoiceoverData(voiceoverDataList);
  }
  
  // draw the static diagram
  let staticDiagram = dg.style.applyStyleProfiles(config.diagram.staticDiagram, config.styleProfiles);
  staticDiagram = dg.diagram_combine(staticDiagram, ...answerDiagrams);
  resizeSVGElementAndDraw(dg, staticDiagram, config.svgElement);
  storeFreeformHomeOffset(staticDiagram, config.home.config);
  
  // setup dnd target
  const targetMaxCapacity = config.dndElementConfig?.targetMaxCapacity ?? 1;
  const targetGlobalConfig = config.dndElementConfig?.globalTargetConfig;
  for (let targetId of config.idList.targets){
    let targetDiagram = config.diagram.idToDiagramMap.get(targetId);
    if (targetDiagram == undefined) continue;
    if (isShowAnswer) targetDiagram = targetDiagram.opacity(0.5);
    let targetConfig = config.dndElementConfig?.idToTargetConfig?.get(targetId) ?? targetGlobalConfig;
    const capacity = config.dndElementConfig?.idToTargetCapacity?.get(targetId) ?? targetMaxCapacity;
    int.dnd_container(targetId, targetDiagram, capacity, targetConfig);
  }
  
  // setup home targets
  const idToHomeMap = new Map<string,string>();
  if (isIndividualHome){
    for (let optionId of config.idList.options){
      idToHomeMap.set(optionId, appendHomeSuffix(optionId));
    }
    for (let i in config.idList.homes){
      const homeId = config.idList.homes[i];
      const homeDiagram = config.diagram.idToDiagramMap.get(homeId) ?? dg.empty(staticDiagram.origin);
      const homeCapacity = isUsingReusableDraggable ? 2 : 1;
      let homeTargetConfig = {padding : 0}
      int.dnd_container(homeId, homeDiagram, homeCapacity, homeTargetConfig);
    }
    
  } else {
    const idToHomeIndexMap = new Map<string,number>();
    config.home.config.element.forEach((row, i) => {
      row.forEach((id, j) => { 
        idToHomeIndexMap.set(baseId(id), j) 
        idToHomeMap.set(id, config.idList.homes[i]);
      });
    })
    let homeTargetConfig = { 
      type : "flex-row", padding : EM_DND_ELEMENT_PADDING, horizontal_alignment: "center",
      sorting_function: (idA:string, idB:string) => 
        idToHomeIndexMap.get(baseId(idA)) - idToHomeIndexMap.get(baseId(idB))
    };
    for (let i in config.idList.homes){
      const homeId = config.idList.homes[i];
      const homeDiagram = config.diagram.idToDiagramMap.get(homeId);
      const homeCapacity = isUsingReusableDraggable ? Infinity : config.home.config.element[i].length;
      int.dnd_container(homeId, homeDiagram, homeCapacity, homeTargetConfig);
    }
  }
  
  // setup move validation
  if (!isUsingReusableDraggable){
    int.dnd_register_drop_outside_callback((draggbleId: string) => {
      int.dnd_move_to_container(draggbleId, idToHomeMap.get(draggbleId));
      stateManager.interact();
    })
    int.dnd_register_move_validation_function((draggableId:string, targetId:string) : boolean =>  {
      if (config.functions?.moveValidation)
        if (!config.functions.moveValidation(draggableId, targetId)) return false;
      if (isHomeId(targetId)){
        if (
          (idToHomeMap.get(draggableId) != targetId) &&
          (idToHomeMap.get(baseId(draggableId)) != targetId)
        ) return false;
      }
      return true;
    });
  }else{
    int.dnd_register_drop_outside_callback((draggbleId: string) => {
      int.dnd_move_to_container(draggbleId, idToHomeMap.get(getDnDElementBaseId(draggbleId)));
      stateManager.interact();
    })
    int.dnd_register_move_validation_function((draggableId:string, targetId:string) : boolean =>  {
      if (config.functions?.moveValidation)
        if (!config.functions.moveValidation(draggableId, targetId)) return false;
      return true;
    });
  }
  
  // setup dnd draggables
  for (let optionId of config.idList.options){
    let optionDiagram = config.diagram.idToDiagramMap.get(optionId).copy();
    if(optionId == config.highlightedOptionId) {
      optionDiagram = optionDiagram.stroke('blue').strokewidth(2);
    }
    const homeId = idToHomeMap.get(optionId);
    const voiceoverData = config.voiceover?.idToVoiceoverMap?.get(optionId);
    const afterMovedCallback = () => { stateManager.interact() };
    const onClickstartCallback = () => {
      if (voiceoverData) voiceoverData.trigger.next(true);
    }
    int.dnd_draggable_to_container(
      optionId, optionDiagram, homeId, 
      afterMovedCallback, onClickstartCallback
    );
  }
  
  // setup buttons
  if (config.diagram.buttons) {
    for (let button of config.diagram.buttons) {
      int.button_click_hover(button.id, button.diagram, button.hoveredDiagram, button.hoveredDiagram, button.onclick);
    }
  }
  
  const containerIdList = [...config.idList.targets, ...config.idList.homes];
  int.draw_function = () => {
    if (config.getDiagramUpdate){
      let {staticDiagram, idToDiagramMap} = config.getDiagramUpdate();
      
      if (staticDiagram){
        staticDiagram = dg.style.applyStyleProfiles(staticDiagram, config.styleProfiles);
        staticDiagram = dg.diagram_combine(staticDiagram, ...answerDiagrams);
        resizeSVGElementAndDraw(dg, staticDiagram, config.svgElement);
        storeFreeformHomeOffset(staticDiagram, config.home.config);
      }
      
      if (idToDiagramMap){
        for (let containerId of containerIdList){
          const newDiagram = idToDiagramMap.get(containerId);
          if (newDiagram == undefined) continue;
          if (newDiagram) int.dnd_container(containerId, newDiagram, undefined, undefined);
        }
      }
    }
    
    if (!isUsingReusableDraggable){
      homeTargetValidation(int, idToHomeMap);
    }
    stateManager.onDraw();
  }
  
  int.dnd_initial_draw();
  int.dnd_reorder_tabindex(getTabIndexOrder(config));
  int.draw();
  
  
  const thisQuestionState = config.scoring.questionState[config.scoring.entryId];
  const isInitialState = thisQuestionState == undefined || !thisQuestionState.isStarted;
  if (!isInitialState) {
    stateManager.setState(thisQuestionState);
  } else {
    stateManager.publishState();
  }
}

function storeFreeformHomeOffset(staticDiagram: DgDiagram, homeConfig: IDgIntHomeConfig) {
  if (homeConfig.layout != HOME_CONFIG_LAYOUT.FREEFORM) return;
  const freeformBgImg = staticDiagram.get_tagged_elements(TAG_DND_FREEFORM_HOME_BGIMG)[0];
  if (!freeformBgImg) return;
  const freeformHomeOffset = freeformBgImg.get_anchor('center-center');
  if (homeConfig?.freeformLayoutConfig) {
    homeConfig.freeformLayoutConfig._homePositionOffset = {x: freeformHomeOffset.x, y: freeformHomeOffset.y};
  }
}

function getTabIndexOrder(config: IDgIntDnDConfig): string[] {
  switch (config.home.position){
    case LAYOUT_HOME_POSITION.RIGHT:
    case LAYOUT_HOME_POSITION.RIGHT_TOP:
    case LAYOUT_HOME_POSITION.BOTTOM: 
      return [...config.idList.targets, ...config.idList.homes];
    case LAYOUT_HOME_POSITION.TOP: 
    case LAYOUT_HOME_POSITION.LEFT: 
    case LAYOUT_HOME_POSITION.LEFT_TOP: 
    default: 
      return [...config.idList.homes, ...config.idList.targets];
  }
}

function homeTargetValidation(int: DgInt, idToHomeMap: Map<string,string>){
  const state = int.get_dnd_data();
  for (let container of state){
    const targetId = container.container;
    if (!isHomeId(targetId)) continue;
    for (let optionId of container.content){
      if (idToHomeMap.get(optionId) != targetId){
        int.dnd_move_to_container(optionId, idToHomeMap.get(optionId))
      }
    }
  }
}


export enum DG_INT_MATCHING_TYPE {
  SINGLE = 'diagram-interactive-matching',
  GROUP = 'diagram-interactive-grouping',
  MCQ = 'diagram-interactive-mcq',
}

export class DnDStateManager implements IDgIntStateManager {
  isStarted   = false;
  isResponded = false;
  
  _correctTargetOfOptionMapCache = new WeakMap<IDgIntElementDnDAnswerSet, Map<string, string>>();
  _correctOptionsOfTargetMapCache = new WeakMap<IDgIntElementDnDAnswerSet, Map<string, string[]>>();
  _idToTargetCapacity = new Map<string, number>();

  constructor(
    public int: DgInt,
    public config: IDgIntDnDConfig,
  ) {
    if (this.config.dndElementConfig?.idToTargetCapacity) {
      this._idToTargetCapacity = this.config.dndElementConfig?.idToTargetCapacity;
    }
  }
  interact() {
    this.isResponded = true;
    this.isStarted = true;
    this.publishState();
  }
  publishState() {
    this.config.scoring.questionState[this.config.scoring.entryId] = this.getState();
    this.config.scoring.questionPubSub?.allPub({entryId: this.config.scoring.entryId, type: PubSubTypes.UPDATE_VALIDATOR, data: {}})
  }
  getState(): IDgIntDnDState {
    const normalizedScore = this.getNormalizedScore();
    return {
      type: this.getType(),
      isCorrect: normalizedScore == 1,
      isStarted: this.isStarted,
      isFilled: this.isFilled(),
      isResponded: this.isResponded,
      formattedResponse: this.getFormattedResponse(),
      score: normalizedScore * this.config.scoring.scoreWeight,
      weight: this.config.scoring.scoreWeight,
      scoring_type: ScoringTypes.AUTO,
      dndState : this.int.get_dnd_data(),
    }
  }
  isValidDnDState(state: IDgIntDnDState): boolean {
    const thisDnDState = this.int.get_dnd_data();
    const dndState = state.dndState as typeof thisDnDState;
    if (thisDnDState.length != dndState.length) return false;
    for (let i = 0; i < thisDnDState.length; i++){
      const thisId = thisDnDState[i].container;
      const thatId = dndState[i].container;
      if (thisId !== thatId) return false;
    }
    return true;
  }
  setState(state: IDgIntDnDState): void {
    if (!this.isValidDnDState(state)) return;
    this.isStarted = state.isStarted;
    this.isResponded = state.isResponded;
    try {
      this.int.set_dnd_data(state.dndState);
    } catch (e) {
      console.log('invalid state');
    }
  }
  
  isFilled() : boolean {
    const state: {container:string, content:string[]}[] = this.int.get_dnd_data();
    const activeTargets = state.filter(c => !isHomeId(c.container));
    const homeTargets = state.filter(c => isHomeId(c.container));
    switch (this.config.config.dndIsFilledMode) {
      case DND_ISFILLED_MODE.PLACE_ALL_DRAGGABLE:
        // true if all home is empty
        return homeTargets.every(t => t.content.length == 0);
      case DND_ISFILLED_MODE.FILL_ALL_TARGET:
        // true if all target is not empty
        return activeTargets.every(t => t.content.length > 0);
      case DND_ISFILLED_MODE.FILL_ANY_TARGET: 
        // true if any target is filled
        return activeTargets.some(t => t.content.length > 0);
      case DND_ISFILLED_MODE.FILL_ALL_SCORING_TARGET:
        // true if all scoring target is not empty
        const activeScoringTargets = activeTargets.filter(c => !this.config.scoring.nonScoringTargetIds?.includes(c.container));
        return activeScoringTargets.every(t => t.content.length > 0);
      case DND_ISFILLED_MODE.FILL_TARGET_CAPACITY:
        // true if all target is filled to it's capacity
        for (const target of activeTargets) {
          const capacity = this._idToTargetCapacity.get(target.container) ?? 0;
          // if capacity is not defined, the capacity is technically infinite, 
          // treat like it is filled for any case (event if it's empty)
          if (target.content.length < capacity) return false;
        }
        return true;
      default: 
        return false;
    }
  }
  
  getNormalizedScore() : number {
    if (this.config.scoring.isAllowMultipleAnswers){
      return this.getMultipleAnswerNormalizedScore(this.config.scoring.answerSets);
    }
    
    const answerSet = this.config.scoring.answerSets[0];
    if (answerSet == undefined) return 0;
    const score = this.getProportionalNormalizedScore(answerSet);
    if (this.config.scoring.enableProportionalScoring){
      return score;
    } else {
      return score == 1 ? 1 : 0;
    }
  }
  
  getProportionalNormalizedScore(answerSet: IDgIntElementDnDAnswerSet): number {
    switch (this.getType()) {
      // case DG_INT_MATCHING_TYPE.MCQ: -1;
      case DG_INT_MATCHING_TYPE.GROUP: 
      case DG_INT_MATCHING_TYPE.SINGLE:
      default: return this.getProportionalNormalizedScoreBasedOnCorrectTarget(answerSet);
    }
  }
  getCorrectTargetOfOptionMap(answerSet: IDgIntElementDnDAnswerSet): Map<string, string> {
    let cached = this._correctTargetOfOptionMapCache.get(answerSet);
    if (cached) return cached;
    
    const correctTargetOfOptionMap = new Map<string,string>();
    for (let target of answerSet){
      for (let optionId of target.optionIds){
        correctTargetOfOptionMap.set(optionId, target.targetId);
      }
    }
    this._correctTargetOfOptionMapCache.set(answerSet, correctTargetOfOptionMap);
    return correctTargetOfOptionMap;
  }
  getCorrectOptionsOfTargetMap(answerSet: IDgIntElementDnDAnswerSet): Map<string, string[]> {
    let cached = this._correctOptionsOfTargetMapCache.get(answerSet);
    if (cached) return cached;
    
    const correctOptionsOfTargetMap = new Map<string, string[]>();
    for (let target of answerSet){
      correctOptionsOfTargetMap.set(target.targetId, target.optionIds);
    }
    this._correctOptionsOfTargetMapCache.set(answerSet, correctOptionsOfTargetMap);
    return correctOptionsOfTargetMap;
  }
  
  getProportionalNormalizedScoreBasedOnCorrectTarget(answerSet: IDgIntElementDnDAnswerSet): number {
    const answerState = this.getAnswerState();
    const correctOptionsOfTargetMap = this.getCorrectOptionsOfTargetMap(answerSet);
    const targetCount = answerSet.length;
    let correctCount = 0;
    
    for (let target of answerState){
      if (arraysEqual(target.optionIds, correctOptionsOfTargetMap.get(target.targetId)))
        correctCount++;
    }
    return correctCount / targetCount;
  }
  getProportionalNormalizedScoreBasedOnCorrectOption(answerSet: IDgIntElementDnDAnswerSet): number {
    // NOTE: target that is unused will not be counted for scoring
    // each correct option (placed in the correct target) will be given 1 point
    const answerState = this.getAnswerState();
    const correctTargetOfOptionMap = this.getCorrectTargetOfOptionMap(answerSet);
    const optionCount = answerSet.reduce((acc, target) => acc + target.optionIds.length, 0);
    let correctCount = 0;
    
    for (let target of answerState){
      for (let optionId of target.optionIds){
        if (target.targetId == correctTargetOfOptionMap.get(optionId))
          correctCount++;
      }
    }
    return correctCount / optionCount;
  }
  getMultipleAnswerNormalizedScore(answerSets: IDgIntElementDnDAnswerSet[]): number {
    const answerState = this.getAnswerState();
    for (let answerSet of answerSets){
      if (this.isAnswerMatchAnswerSet(answerState, answerSet)) return 1;
    }
    return 0;
  }
  isAnswerMatchAnswerSet(answerState: IDgIntElementDnDAnswerSet, answerSet: IDgIntElementDnDAnswerSet): boolean {
    if (answerState.length != answerSet.length) return false;
    for (let i = 0; i < answerState.length; i++){
      if (answerState[i].targetId != answerSet[i].targetId) return false;
      if (!arraysEqual(answerState[i].optionIds, answerSet[i].optionIds)) return false;
    }
    return true;
  }
  
  getAnswerState(): IDgIntElementDnDAnswerSet {
    const dndState = this.int.get_dnd_data();
    const nonScoringTargetIds = this.config.scoring.nonScoringTargetIds ?? [];
    return dndState
      .filter(c => !isHomeId(c.container) && !nonScoringTargetIds.includes(c.container))
      .map(c => ({
        targetId: c.container,
        optionIds: c.content,
      }))
  }
  
  getType(): string {
    if (this.isMCQ()) return DG_INT_MATCHING_TYPE.MCQ;
    if (this.config.config.isAllowGrouping) return DG_INT_MATCHING_TYPE.GROUP;
    return DG_INT_MATCHING_TYPE.SINGLE;
  }
  
  isMCQ(): boolean {
    return false;
  }
  
  getFormattedResponse(): string {
    switch (this.getType()) {
      case DG_INT_MATCHING_TYPE.SINGLE: return this.getFormattedResponseSingle();
      case DG_INT_MATCHING_TYPE.GROUP: return this.getFormattedResponseGroup();
      case DG_INT_MATCHING_TYPE.MCQ: return this.getFormattedResponseMCQ();
      default: return "";
    }
  }
  
  getFormattedResponseSingle(): string {
    const state = this.int.get_dnd_data();
    const nonScoringTargetIds = this.config.scoring.nonScoringTargetIds ?? [];
    let response = '';
    // targetLabel=>draggableLabel;targetLabel=>draggableLabel;...
    for (let container of state) {
      let containerId = container.container;
      if (isHomeId(containerId)) continue;
      if (nonScoringTargetIds.includes(containerId)) continue;
      const contentCount = container.content.length;
      const containerLabel = this.config.label.idToLabelMap.get(containerId);
      if (contentCount < 1) {
        response += `${containerLabel}=>(EMPTY);`;
      } else {
        let contentId = getDnDElementBaseId(container.content[0]);
        let contentLabel = this.config.label.idToLabelMap.get(contentId);
        response += `${containerLabel}=>${contentLabel};`;
      }
    }
    if (response) response = response.slice(0, -1);
    return response;
  }
  
  getFormattedResponseGroup(): string {
    const state = this.int.get_dnd_data();
    const nonScoringTargetIds = this.config.scoring.nonScoringTargetIds ?? [];
    let response = '';
    // targetLabel=>[draggableLabel,draggableLabel];targetLabel=>[draggableLabel];...
    for (let container of state) {
      let containerId = container.container;
      if (isHomeId(container.container)) continue;
      if (nonScoringTargetIds.includes(containerId)) continue;
      let containerLabel = this.config.label.idToLabelMap.get(containerId);
      let contentLabels = container.content.map(c => 
        this.config.label.idToLabelMap.get(getDnDElementBaseId(c)));
      let contentString = contentLabels.join(',');
      response += `${containerLabel}=>[${contentString}];`;
    }
    if (response) response = response.slice(0, -1);
    return response;
  }
  
  getFormattedResponseMCQ(): string {
    return "TODO!"
  }
  
  onDraw(): void {
  }
}

type DgDnDState = {
  container: string;
  content: string[];
}[];

export class ReusableDnDStateManager extends DnDStateManager {
  
  idGen = new IdGenerator(':', true);
  isIndividualHome: boolean;
  homeIdToIndexMap : Map<string, number>;
  constructor(
    public int: DgInt,
    public config: IDgIntDnDConfig,
  ) {
    super(int, config);
    for (let optionId of config.idList.options) this.idGen.generate(optionId);
    this.homeIdToIndexMap = new Map(config.idList.homes.map((id, index) => [id, index]));
    this.isIndividualHome = isConfigIndividualHome(config.home.config);
  }
  
  interact(): void {
    super.interact();
  }
  onDraw(): void {
    super.onDraw();
    if (this.isNeedRefill()) this.refillHomes();
  }
  
  isNeedRefill(): boolean {
    const state = this.int.get_dnd_data();
    let homeState = state.filter(c => isHomeId(c.container));
    
    // only refill if home is modified
    for (let i = 0; i < homeState.length; i++) {
      const contents = homeState[i].content as string[];
      const contentBaseIds = contents.map(id => getDnDElementBaseId(id));
      const originalContentBaseIds = this.config.home.config.element[i];
      if (!arraysEqual(contentBaseIds, originalContentBaseIds)) return true;
    }
    return false
  }
  
  refillHomes(): void{
    // clear all homes
    const state = this.int.get_dnd_data();
    let homeState = state.filter(c => isHomeId(c.container));
    for (const home of homeState){
      (home.content as string[]).forEach(id => this.int.remove_dnd_draggable(id));
    }
    
    
    let homeContentIds = this.config.home.config.element;
    if (this.isIndividualHome){
      homeContentIds = flattenArray(this.config.home.config.element).map(id => [id]);
    }
    
    // refill homes
    for (let i in this.config.idList.homes){
      const homeId = this.config.idList.homes[i];
      for (let baseOptionId of homeContentIds[i]){
        const optionDiagram = this.config.diagram.idToDiagramMap.get(baseOptionId).copy();
        const optionId = this.idGen.generate(baseOptionId);
        const voiceoverData = this.config.voiceover?.idToVoiceoverMap?.get(baseOptionId);
        
        const afterMovedCallback = () => { this.interact() };
        const onClickstartCallback = () => {
          if (voiceoverData) voiceoverData.trigger.next(true);
        }
        this.int.dnd_draggable_to_container(
          optionId, optionDiagram, homeId, 
          afterMovedCallback, onClickstartCallback
        );
      }
    }
    this.int.dnd_initial_draw();
  }
  
  isValidState(state: IDgIntDnDState): boolean {
    const dndState = state.dndState as DgDnDState;
    if (!dndState) return false;
    
    const containerIds = dndState.map(c => c.container);
    if (!arraysEqual(containerIds, [...this.config.idList.targets, ...this.config.idList.homes])) 
      return false;
    
    const optionIds = flattenArray(dndState.map(c => c.content));
    const optionBaseIds = uniqueArray(optionIds.map(id => getDnDElementBaseId(id)));
    if (!arraysEqual(optionBaseIds, this.config.idList.options)) return false;
    
    return true;
  }
  
  setState(state: IDgIntDnDState): void {
    if (!this.isValidState(state)) return;
    
    this.isStarted = state.isStarted;
    this.isResponded = state.isResponded;
    try {
      // deleta all draggable
      const currentDnDState = this.int.get_dnd_data();
      const optionIds = flattenArray(currentDnDState.map(c => c.content));
      optionIds.forEach(id => this.int.remove_dnd_draggable(id));
      this.idGen.reset_all();
      
      // construct new state
      const dndState = state.dndState as DgDnDState;
      for (let s of dndState){
        const containerId = s.container;
        if (isHomeId(containerId)) continue;
        for (let optionId of s.content){
          const baseId = getDnDElementBaseId(optionId);
          const newId = this.idGen.generate(baseId);
          const diagram = this.config.diagram.idToDiagramMap.get(baseId);
          this.int.dnd_draggable_to_container(
            newId, diagram, containerId, 
            () => { this.interact() },
            undefined
          );
        }
      }
      this.int.draw();
    } catch (e) {
      console.log('invalid DnD state');
    }
  }
  
  getAnswerState(): IDgIntElementDnDAnswerSet {
    const originalAnswerState = super.getAnswerState();
    return originalAnswerState.map(target => ({
      targetId: target.targetId,
      optionIds: target.optionIds.map(id => getDnDElementBaseId(id))
    }));
  }
}

export function getDnDElementBaseId(id: string): string{
  return id.split(':')[0];
}

type DnDIsFilledData = {
  isOtherOptionsPresent: boolean;
  isEmptyTargetPresent: boolean;
}
export function determineDnDIsFilledMode(
  targets: IDgIntElementDnDTarget[], answerSets: IDgIntElementDnDAnswerSet[], 
  isReusableDraggable: boolean, isIgnoreDefaultTarget: boolean,
): DND_ISFILLED_MODE 
{
  // if non scoring target exist, always use FILL_ALL_SCORING_TARGET
  if (targets.some(t => t.isNonScoring)) return DND_ISFILLED_MODE.FILL_ALL_SCORING_TARGET;
  
  const dataFromTargets = !isIgnoreDefaultTarget ? determineDnDIsFilledDataFromTargets(targets) : {
    isOtherOptionsPresent: false,
    isEmptyTargetPresent: false
  };
  const optionIds = flattenArray(targets.map(t => t.content.map(c => c.id)));
  const dataFromAnswerSets = answerSets.map(s => determineDnDIsFilledDataFromAnswerSet(s, optionIds));
  const dataList = [dataFromTargets, ...dataFromAnswerSets];
  let data = {
    // if isReusableDraggable is true, we don't need to check isOtherOptionsPresent (Place All Draggables is impossible)
    isOtherOptionsPresent: isReusableDraggable,
    isEmptyTargetPresent: false
  }
  for (let d of dataList) {
    data.isOtherOptionsPresent ||= d.isOtherOptionsPresent;
    data.isEmptyTargetPresent ||= d.isEmptyTargetPresent;
  }
  // logic table
  // case | isOtherOptionsPresent | isEmptyTargetPresent  | mode
  // --------------------------------------------------------------------------------
  // 1    | false                 | false                 | place all draggables
  // 2    | false                 | true                  | palce all draggables
  // 3    | true                  | false                 | fill all targets
  // 4    | true                  | true                  | fill any target
  if (!data.isOtherOptionsPresent) {
    return DND_ISFILLED_MODE.PLACE_ALL_DRAGGABLE;
  } else if (!data.isEmptyTargetPresent) {
    return DND_ISFILLED_MODE.FILL_ALL_TARGET;
  } else {
    return DND_ISFILLED_MODE.FILL_ANY_TARGET;
  }
}
function determineDnDIsFilledDataFromTargets(targets: IDgIntElementDnDTarget[]): DnDIsFilledData {
  let data = {
    isOtherOptionsPresent: false,
    isEmptyTargetPresent: false
  }
  const activeTargets = targets.filter(t => !isHomeId(t.label));
  const homeTargets = targets.filter(t => isHomeId(t.label));
  for (let target of activeTargets){
    if (target.content.length == 0) {
      data.isEmptyTargetPresent = true;
      break;
    }
  }
  for (let target of homeTargets){
    if (target.content.length > 0) {
      data.isOtherOptionsPresent = true;
      break;
    }
  }
  return data;
}
function determineDnDIsFilledDataFromAnswerSet(answerSet: IDgIntElementDnDAnswerSet, optionIds: string[]): DnDIsFilledData {
  let data = {
    isOtherOptionsPresent: false,
    isEmptyTargetPresent: false
  }
  const activeTargets = answerSet.filter(t => !isUnusedId(t.targetId));
  for (let target of activeTargets){
    if (target.optionIds.length == 0) {
      data.isEmptyTargetPresent = true;
      break;
    }
  }
  const usedOptionIds = new Set(flattenArray(activeTargets.map(t => t.optionIds)));
  for (let optionId of optionIds){
    if (!usedOptionIds.has(optionId)) {
      data.isOtherOptionsPresent = true;
      break;
    }
  }
  return data;
}
import { IDgIntConstructionElement, DgIntConstructionType } from "../common"
import type { DgLib, DgInt, DgDiagram, DgAlignment } from "src/assets/lib/diagramatics/config";
import { mathExpressionDg } from "../math-expression";
import { 
  IDgIntElementDnDTarget, DND_UNUSED_ID, LAYOUT_HOME_POSITION,
  generateIdOnDnDTargets, resolveDnDTargetLabelChange,
  unusedId, isUnusedId, groupByHome, regenerateIdOnDnDTargets,
  IDgIntElementDnDAnswerSet, generateDefaultAnswerSet, IDgIntHomeConfig,
  HOME_CONFIG_LAYOUT, generateDnDHomeConfigFromOrdering, resolveDnDHomeConfig,
  resolveDnDAltAnswers,
  DND_OPTION_TEXT_MODE,
  isDnDTargetsContainMathOption,
  generateHomeTargetDiagramsWithLabel,
  generateDnDBlock,
  TAG_DND_DRAGGABLE,
  TAG_DND_TARGET,
  IDgIntStyleConfig,
  resolveDnDStyleConfig,
  DnDStateManager,
  IDgIntDnDScoringParam,
  ReusableDnDStateManager,
  IDgIntDnDConfig,
  setupInteractiveDnD,
  getAllOptionIdsFromDnDTargets,
  HOME_LABEL_POSITION,
  resolveDnDTargetOptions2,
  isConfigIndividualHome,
  appendHomeSuffix,
  attachLabelDiagram,
  DND_STYLE_MODE,
  generateElementColorsObject,
  getOptionLabel,
  assembleDraggableBlock,
  resolveTargetAndAnswersForCombinedOptions,
  generatePaddedDiagram,
  DND_ISFILLED_MODE,
  determineDnDIsFilledMode,
  generateHomeGroupBg,
  generateVoiceoverDataMap,
  TAG_MAIN_DIAGRAM,
  resolveDnDAnswerGroup,
  getDnDElementBaseId,
} from "./dnd-common";
import { flattenArray, formattedStringToBBCode, multilineTextWithSize, getStyle, LAYOUT_IMAGE_LABEL_MODE, generateImageTextDiagram, numberDuplicateString, IDgIntScoringParam, EX_TO_EM, CONTENT_JUSTIFY } from "./common";
import { IContentElementImage } from "../../element-render-image/model";
import { DgIntImageManager } from "../image-manager";
import { cloneDeep } from 'lodash';
import { DgStyleProfile, getStyleParam } from "../style-profile";
import { tex2dgMathExpression } from "../mathjax";
import { DgIntCallbacks } from "../renderer";

const EM_LINE_HEIGHT = 1.3;
const EM_DND_ELEMENT_PADDING = 0.5;

export interface IDgIntElementDnDTable extends IDgIntConstructionElement {
  constructionType: DgIntConstructionType.DND_TABLE;
  table : IDgIntElementDnDTableCell[][],
  label? : {
    text?: string;
    position?: HOME_LABEL_POSITION;
    isMath?: boolean;
  };
  dndTargets: IDgIntElementDnDTarget[];
  homeConfig: IDgIntHomeConfig;
  styleConfig: IDgIntStyleConfig;
  altAnswers?: IDgIntElementDnDAnswerSet[];
  _activeAnswerIndex?: number;
  _isAlwaysSeparateAnswerSetup?: boolean;
  diagramPadding?: {
    isEnabled?: boolean;
    top?: number;
    bottom?: number;
    left?: number;
    right?: number;
  }
  config: {
    isAllowMultipleAnswer: boolean;
    isAllowGrouping: boolean;
    isUseImage: boolean;
    isUsingAnswerGroup?: boolean;
    isUsingReusableDraggable: boolean;
    isAllowEmptyTarget?: boolean;
    isAllowNonScoringTarget?: boolean;
    homePosition : LAYOUT_HOME_POSITION;
    padding : [number, number];
    optionMaxWidth: number;
    optionFixedWidth?: number;
    homeMaxWidth: number;
    cellMinWidth: number;
    cellMinHeight: number;
    draggableBgColor: string;
    targetBgColor: string;
    homeBgColor?: string;
    contentJustify?: CONTENT_JUSTIFY,
    alignHomeLabelToFirstRow?: boolean; // only active if there is no label and home position is left_top or right_top
  };
  dndIsFilledMode?: 'auto' | DND_ISFILLED_MODE; // user specified value or auto
  _autoDnDIsFilledMode?: DND_ISFILLED_MODE; // auto calculated value
  //
  ordering? : number[]; //deprecated use `homeConfig` instead
}

export interface IDgIntElementDnDTableCell {
  isTarget? : boolean;
  text : string;
  isMath? : boolean;
  image? : IContentElementImage;
  _cachedImageSize? : {width: number, height: number};
  imageLabelPosition?: LAYOUT_IMAGE_LABEL_MODE;
  alignment?: DgAlignment;
}

export function generateDefaultDgIntElementDnDTable(): IDgIntElementDnDTable {
  return {
    constructionType: DgIntConstructionType.DND_TABLE,
    table: [
      [
        {text:'x'},
        {text:'y'},
      ],
      [
        {text:'1'},
        {text:'two', isTarget:true},
      ],
    ],
    styleConfig: {
      styleMode: DND_STYLE_MODE.DEFAULT,
    },
    homeConfig: {
      isGroupHome: false,
      layout: HOME_CONFIG_LAYOUT.DEFAULT,
      element: [[]]
    },
    dndTargets: [
      {
        label : 'two',
        content: [{value : '2'}]
      },
      {
        label : DND_UNUSED_ID,
        content: [{value: '1'}, {value:'7'}]
      }
    ],
    config: {
      isAllowMultipleAnswer: false,
      isAllowGrouping: false,
      isUseImage: false,
      isUsingReusableDraggable: false,
      homePosition: LAYOUT_HOME_POSITION.BOTTOM,
      padding : [0.5, 0.75],
      cellMinWidth: 2,
      cellMinHeight: 1,
      optionMaxWidth: 200,
      homeMaxWidth: 200,
      draggableBgColor: '#ffffff',
      targetBgColor: '#e0e0e0',
    },
  };
}

export function resolveDnDTableTargets(element : IDgIntElementDnDTable) : void {
  let targetNames = [];
  element.table.forEach(row => {
    row.forEach(cell => {
      if (cell.isTarget) targetNames.push(cell.text);
    });
  });
  targetNames = numberDuplicateString(targetNames);
  
  if (element.homeConfig == undefined) 
    element.homeConfig = generateDnDHomeConfigFromOrdering(element.dndTargets, element.ordering);
  if (element.altAnswers == undefined) element.altAnswers = [];
  if (element.label == undefined) element.label = {text: '', position: HOME_LABEL_POSITION.TOP};
  if (element.diagramPadding == undefined) element.diagramPadding = {left: 0, right: 0, top: 0, bottom: 0};
  
  const isCombineOptionContainer = element._isAlwaysSeparateAnswerSetup || element.config.isUsingReusableDraggable;
  element.dndTargets = resolveDnDTargetLabelChange(element.dndTargets, targetNames);
  element.dndTargets = resolveDnDTargetOptions2(element.dndTargets, targetNames, isCombineOptionContainer);
  const [idChangeMap, newOptionIds, newTargetIds] = regenerateIdOnDnDTargets(element.dndTargets);
  const allOptionIds = getAllOptionIdsFromDnDTargets(element.dndTargets);
  element.homeConfig = resolveDnDHomeConfig(element.homeConfig, idChangeMap, newOptionIds, allOptionIds);
  const homeCount = element.homeConfig.element.length;
  
  const targetIds = element.dndTargets.map(t => t.id);
  const optionIds = flattenArray(element.dndTargets.map(t => t.content.map(c => c.id)));
  const homeIds = Array.from({length: homeCount}, (_, i) => unusedId(i));
  
  element.styleConfig = resolveDnDStyleConfig(
    element.styleConfig, idChangeMap, newOptionIds, newTargetIds,
    targetIds, optionIds, homeIds
  );
  element.altAnswers = resolveDnDAltAnswers(element.altAnswers, idChangeMap, element.dndTargets);
  resolveTargetAndAnswersForCombinedOptions(isCombineOptionContainer, element.dndTargets, element.altAnswers);
  resolveDnDAnswerGroup(element.config.isUsingAnswerGroup, element.dndTargets, element.homeConfig, isCombineOptionContainer, element.altAnswers);
  
  if (element.dndIsFilledMode == undefined) { element.dndIsFilledMode = 'auto'; }
  if (element.dndIsFilledMode == 'auto') {
    element._autoDnDIsFilledMode = determineDnDIsFilledMode(
      element.dndTargets, element.altAnswers, 
      element.config.isUsingReusableDraggable, isCombineOptionContainer);
  }
}

export function renderDgIntDndTable(
  element: IDgIntElementDnDTable, svgElement: SVGSVGElement, 
  dg: DgLib, int: DgInt, imageManager: DgIntImageManager, styleProfiles: Array<DgStyleProfile>,
  scoringParam: IDgIntScoringParam, callbacks: DgIntCallbacks
) {
  // backward compatibility
  if (element.styleConfig == undefined) element.styleConfig = {};
  let dndTargets = element.dndTargets;
  if (dndTargets.length > 0 && dndTargets[0].id == undefined){
    dndTargets = cloneDeep(element.dndTargets) as typeof element.dndTargets;
    generateIdOnDnDTargets(dndTargets);
  }
  let homeConfig = element.homeConfig;
  if (homeConfig == undefined) {
    homeConfig = generateDnDHomeConfigFromOrdering(dndTargets, element.ordering);
  }
  //
  
  let answerSets = []
  if (!element.config.isUsingReusableDraggable && !element._isAlwaysSeparateAnswerSetup)
    answerSets.push(generateDefaultAnswerSet(dndTargets));
  if (element.config.isAllowMultipleAnswer || element.config.isUsingReusableDraggable || element._isAlwaysSeparateAnswerSetup){
    answerSets = answerSets.concat(element.altAnswers);
  }
  const styleParam = getStyleParam(styleProfiles);
  
  // queue the image size calculation
  if (element.config.isUseImage) {
    for (let row of element.table) {
      for (let cell of row){
        const url = cell.image?.url;
        if (url) {
          imageManager.queueSizeCalculation(url, cell);
        }
      }
    }
    for (let target of dndTargets){
      for (let c of target.content){
        const url = c.image?.url;
        if (url) {
          imageManager.queueSizeCalculation(url, c);
        }
      }
    }
  }
  
  const minRowsize = element.config.cellMinHeight;
  const minColsize = element.config.cellMinWidth;
  const homeMaxWidth = element.config.homeMaxWidth * 20/100;
  const homePosition = element.config.homePosition ?? LAYOUT_HOME_POSITION.BOTTOM;
  const maxTotalWidth = 200 * 20/100;
  const optionMaxWidth = (element.config.optionMaxWidth  ?? 200 ) * 20 / 100;
  const targetBgColor = element.config.targetBgColor;
  const draggableBgColor = element.config.draggableBgColor;
  const padding = element.config.padding ?? [0.5, 0.75];
  const isVisibleHome = styleParam.visible_home_target ?? false;
  const isDnDBlockFillCell = styleParam.table_dnd_target_fill_cell ?? false;
  const isDnDBlockNoCellPadding = styleParam.table_dnd_target_no_cell_padding ?? false;
  const [elementColors, borderColors] = generateElementColorsObject(element.styleConfig, homeConfig)
  const isAlignHomeToFirstRow = (element.config.alignHomeLabelToFirstRow ?? false) && isPossibleToAlignHomeLabelToFirstRow(element);
  const contentJustify = element.config.contentJustify ?? CONTENT_JUSTIFY.CENTER;
  
  const canvasStyle = getStyle(svgElement);
  const multilineText = (s : string, maxWidth?: number, justification: CONTENT_JUSTIFY = CONTENT_JUSTIFY.CENTER) => {
    const bbcode = formattedStringToBBCode(s);
    return multilineTextWithSize(bbcode, dg, canvasStyle.pxFontSize, canvasStyle.fontFamily, EM_LINE_HEIGHT, maxWidth, justification);
  }
  const mathStyleConfig = {svgFontSize: canvasStyle.pxFontSize, mathFontFamily: styleParam.math_font_family};
  const mathExpression = (s: string) => mathExpressionDg(dg, s, mathStyleConfig);
  
  // generate diagrams
  let targetCellIndices : [number, number][] = [];
  let dgContent : DgDiagram[][] = [];
  let targetTableIndices: [number, number][] = [];
  {
    // populate table content
    for (let i = 0; i < element.table.length; i++){
      let dgRow = [];
      for (let j = 0; j < element.table[i].length; j++){
        let cell = element.table[i][j];
        if (cell.isTarget) {
          // dndTarget_tableIndices[cell.text] = [i,j];
          targetTableIndices.push([i,j]);
          targetCellIndices.push([i,j]);
          dgRow.push(dg.empty());
        } else if (!cell.text && !cell.image?.url) {
          dgRow.push(dg.empty());
        } else {
          const alignment = cell.alignment?.split('-')?.[1] as 'left' | 'center' | 'right';
          const multilineTextAligned = (s: string) => multilineText(s, undefined, alignment as CONTENT_JUSTIFY);
          const textDiagramGenerator = cell.isMath ? mathExpression : multilineTextAligned;
          const cellDiagram = generateImageTextDiagram(
            dg, imageManager, cell.text, cell.image,
            textDiagramGenerator, cell.imageLabelPosition, padding, alignment
          );
          dgRow.push(cellDiagram);
        }
      }
      dgContent.push(dgRow);
    }
  }
  
  const options = flattenArray(dndTargets.map(t => t.content));
  const idToIndexMap = new Map(options.map((option,i) => [option.id, i]));
  const extraHomeWidth = isDnDTargetsContainMathOption(dndTargets) ? 1 : 0;
  
  let maxOptionInnerWidthCandidates : number[] = [
    homeMaxWidth - (3+extraHomeWidth)*EM_DND_ELEMENT_PADDING - 2*padding[1],
    optionMaxWidth
  ];
  const [originalRowSizes, originalColSizes] = calcTableSize(dgContent, padding, minRowsize, minColsize);
  
  {
    // calculate the maximum width for the option based on the table size
    const colWithTarget = new Set(targetTableIndices.map(([_,j]) => j));
    const targetColSizes = originalColSizes.filter((_,j) => colWithTarget.has(j));
    const totalTableWidth = originalColSizes.reduce((a,b) => a+b, 0);
    const totalTargetCellWidth = targetColSizes.reduce((a,b) => a+b, 0);
    const totalNonTargetCellWidth = totalTableWidth - totalTargetCellWidth;
    const targetColCount = targetColSizes.length;
    const availableWidth = maxTotalWidth - totalNonTargetCellWidth;
    const maxTargetCellWidth = availableWidth / targetColCount;
    const maxOptionWidth = maxTargetCellWidth - 2*padding[1];
    const maxOptionInnerWidth = maxOptionWidth - 2*padding[1];
    maxOptionInnerWidthCandidates.push(maxOptionInnerWidth);
  }
  
  {
    // calculate the maximum width for the option if the home is horizontal single column
    if (homeConfig.isGroupHome && homeConfig.layout == HOME_CONFIG_LAYOUT.HORIZONTAL_SINGLE_COLUMN) {
      const pad = EM_DND_ELEMENT_PADDING;
      const homeGroupCount = homeConfig.element.length ?? 1;
      const maxSingleHomeWidth = (homeMaxWidth - pad*(homeGroupCount-1)) / homeGroupCount;
      const maxOptionWidth = maxSingleHomeWidth - (3+extraHomeWidth)*pad;
      const maxOptionInnerWidth = maxOptionWidth - 2*padding[1];
      maxOptionInnerWidthCandidates.push(maxOptionInnerWidth);
    }
  }
  
  let maxOptionInnerWidth = Math.min(...maxOptionInnerWidthCandidates);
  if (element.config.optionFixedWidth) maxOptionInnerWidth = element.config.optionFixedWidth*20/100 - 2*padding[1];
  const multilineTextForOption = (s : string) => multilineText(s, maxOptionInnerWidth, contentJustify);
  
  const optionInnerDiagrams: DgDiagram[] = options.map((s) => { 
    let textDiagramGenerator = multilineTextForOption;
    if (s.textMode == DND_OPTION_TEXT_MODE.MATH) textDiagramGenerator = mathExpression;
    return generateImageTextDiagram(
      dg, imageManager, s.value, s.image,
      textDiagramGenerator, s.imageLabelPosition, padding)
  });
  const optionsBB = optionInnerDiagrams.map(o => o.bounding_box());
  
  let choicesMaxwidth  = Math.max(...optionsBB.map(bb => bb[1].x - bb[0].x)) + 2*padding[1];
  let choicesMaxheight = Math.max(...optionsBB.map(bb => bb[1].y - bb[0].y)) + 2*padding[0];
  choicesMaxwidth  = Math.max(choicesMaxwidth, minColsize);
  choicesMaxheight = Math.max(choicesMaxheight, minRowsize);
  
  if (isDnDBlockFillCell){
    const targetCellWidths = targetCellIndices.map(i => originalColSizes[i[1]]);
    const targetCellHeights = targetCellIndices.map(i => originalRowSizes[i[0]]);
    const maxTargetCellWidth = Math.max(...targetCellWidths);
    const maxTargetCellHeight = Math.max(...targetCellHeights);
    let minTargetWidth = maxTargetCellWidth;
    let minTargetHeight = maxTargetCellHeight;
    if (!isDnDBlockNoCellPadding){
      minTargetWidth -= 2*padding[1];
      minTargetHeight -= 2*padding[0];
    }
    choicesMaxwidth = Math.max(choicesMaxwidth, minTargetWidth);
    choicesMaxheight = Math.max(choicesMaxheight, minTargetHeight);
  }
  
  if (element.config.optionFixedWidth) choicesMaxwidth = element.config.optionFixedWidth * 20/100;
  const dndBlock = generateDnDBlock(dg, choicesMaxwidth, choicesMaxheight, styleProfiles);
  const optionDiagrams = optionInnerDiagrams.map((o,i) => {
    const id = options[i].id;
    const source = o.move_origin('center-center').append_tags(TAG_DND_DRAGGABLE);
    let bg: DgDiagram   = dndBlock.position(source.origin).fill(draggableBgColor)
      .append_tags(TAG_DND_DRAGGABLE);
    if (elementColors[id]) bg = bg.fill(elementColors[id]);
    if (element.styleConfig.isUseCustomBorderColors) {
      bg = bg.stroke(element.styleConfig.commonDraggableBorderColor);
      if (borderColors[id]) bg = bg.stroke(borderColors[id]);
    }
    const combined = assembleDraggableBlock(dg, source, bg, padding, contentJustify);
    return dg.style.applyStyleProfiles(combined, styleProfiles);
  });
  
  {
    // setup target placeholders
    let targetPlaceholder = dg.rectangle(choicesMaxwidth, choicesMaxheight).opacity(0);
    if (isDnDBlockNoCellPadding){
      targetPlaceholder = dg.rectangle(choicesMaxwidth - 2*padding[1], choicesMaxheight - 2*padding[0]).opacity(0);
    }
    for (let index of targetCellIndices){
      dgContent[index[0]][index[1]] = targetPlaceholder;
    }
  }
  
  const alignmentConfig = element.table.map(row => row.map(cell => cell.alignment));
  const dgTable = dg.table.advanced_table(dgContent, {
    padding,
    min_rowsize : minRowsize,
    min_colsize : minColsize,
    alignment : alignmentConfig
  })
  const cellRects = dg.table.get_padded_cells(dgTable, padding as any);
  
  let diagram = dgTable.append_tags(TAG_MAIN_DIAGRAM);
  
  // apply custom styling
  if (styleParam["table_dnd_merged_cell_style"]) {
    diagram = generateMergedCellStyle(dg, dgTable).append_tags(TAG_MAIN_DIAGRAM);
  }
  
  diagram = generatePaddedDiagram(dg, diagram, element.diagramPadding);
  if (element.label?.text){
    const textDiagramGenerator = element.label.isMath ? mathExpression : multilineText;
    const labelDiagram = textDiagramGenerator(element.label.text, maxTotalWidth);
    diagram = attachLabelDiagram(dg, diagram, labelDiagram, element.label.position, EM_DND_ELEMENT_PADDING)
  }
  
  const groupedOptionDiagrams = groupByHome(optionDiagrams, homeConfig.element, idToIndexMap);
  let [homeDiagrams, staticHomeDiagram] = generateHomeTargetDiagramsWithLabel(
    dg, homePosition, homeConfig,
    groupedOptionDiagrams, diagram, 
    homeMaxWidth, EM_DND_ELEMENT_PADDING, multilineText, mathExpression, imageManager,
    extraHomeWidth, styleParam);
  if (isAlignHomeToFirstRow){
    const offset = dg.V2(0, -padding[0]);
    homeDiagrams = homeDiagrams.map(d => d.translate(offset));
    staticHomeDiagram = staticHomeDiagram.translate(offset);
  }
  const homeGroupBg = !isVisibleHome ? [] : 
    generateHomeGroupBg(dg, homeDiagrams, homeConfig, styleParam, idToIndexMap, EM_DND_ELEMENT_PADDING, staticHomeDiagram, element.config.homePosition)
      .map(d => d.fill(targetBgColor));
  
  let homeIdList: string[];
  const sortedOptionIdList = flattenArray(homeConfig.element);
  if (isConfigIndividualHome(element.homeConfig)){
    homeIdList = sortedOptionIdList.map(id => appendHomeSuffix(id));
  } else {
    homeIdList = homeDiagrams.map((_, i) => unusedId(i));
  }
  
  const idToDiagramMap = new Map<string, DgDiagram>();
  {
    // setup diagram for dnd elements
    // dnd target
    for (let i = 0; i < dndTargets.length; i++) {
      let target = dndTargets[i];
      if (target.label === DND_UNUSED_ID) continue;
      const [r,c] = targetTableIndices[i];
      const dgAlignment = alignmentConfig[r][c] ?? 'center-center';
      let pos = cellRects[r][c].get_anchor(dgAlignment);
      
      if (isDnDBlockNoCellPadding) {
        if (dgAlignment.endsWith('left')){
          pos = pos.add(dg.V2(-padding[1], 0));
        }
        else if (dgAlignment.endsWith('right')){
          pos = pos.add(dg.V2(padding[1], 0));
        }
      }
      
      let block = dndBlock.move_origin(dgAlignment)
        .position(pos).fill(targetBgColor).append_tags(TAG_DND_TARGET)
      if (elementColors[target.id]) block = block.fill(elementColors[target.id]);
      if (element.styleConfig.isUseCustomBorderColors) {
        block = block.stroke(element.styleConfig.commonTargetBorderColor);
        if (borderColors[target.id]) block = block.stroke(borderColors[target.id]);
      }
      block = dg.style.applyStyleProfiles(block, styleProfiles);
      idToDiagramMap.set(target.id, block);
    }
    // dnd home
    for (let i in homeDiagrams){
      const homeDiagram = isVisibleHome ? homeDiagrams[i].fill(targetBgColor) : homeDiagrams[i];
      const homeId = homeIdList[i];
      idToDiagramMap.set(homeId, homeDiagram);
    }
    // dnd draggable
    for (let i in options){
      const optionDiagram = optionDiagrams[i];
      idToDiagramMap.set(options[i].id, optionDiagram);
    }
  }
  
  const idToGroupMap = new Map<string,number>();
  dndTargets.forEach((target, i) => {
    idToGroupMap.set(target.id, target.answerGroup ?? 0);
    target.content.forEach(content => {
      idToGroupMap.set(content.id, content.answerGroup ?? 0);
    })
  });
  const moveValidationFunction = (draggableId:string, targetId:string) : boolean =>  {
    if (isUnusedId(targetId)) return true;
    const baseDraggableId = getDnDElementBaseId(draggableId);
    const baseTargetId = getDnDElementBaseId(targetId);
    const draggableGroup = idToGroupMap.get(baseDraggableId);
    const targetGroup = idToGroupMap.get(baseTargetId);
    return draggableGroup == targetGroup;
  }
  
  const idToLabelMap = new Map<string, string>();
  {
    for (let target of dndTargets){
      idToLabelMap.set(target.id, target.label);
      for (let option of target.content){
        idToLabelMap.set(option.id, getOptionLabel(option));
      }
    }
  }
  
  if (element.dndIsFilledMode == undefined){ // backward compatibility
    element.dndIsFilledMode = 'auto';
    element._autoDnDIsFilledMode = determineDnDIsFilledMode(
      element.dndTargets, element.altAnswers, 
      element.config.isUsingReusableDraggable, element._isAlwaysSeparateAnswerSetup || element.config.isUsingReusableDraggable);
  }
  const isUsingReusableDraggable = element.config.isUsingReusableDraggable ?? false;
  const dgIntConfig: IDgIntDnDConfig = {
    idList: {
      options: options.map(o => o.id),
      targets: dndTargets.filter(t => !isUnusedId(t.label)).map(t => t.id),
      homes: homeIdList,
    },
    label: {
      idToLabelMap,
    },
    diagram: {
      staticDiagram: dg.diagram_combine(diagram, ...homeDiagrams, staticHomeDiagram, ...homeGroupBg),
      idToDiagramMap,
    },
    home: {
      config: homeConfig,
      position: element.config.homePosition,
    },
    styleProfiles,
    svgElement,
    functions: {
      moveValidation: element.config.isUsingAnswerGroup ? moveValidationFunction : undefined,
      callbacks,
    },
    voiceover: {
      idToVoiceoverMap: generateVoiceoverDataMap(dndTargets)
    },
    config: {
      isAllowGrouping: false,
      isUsingReusableDraggable,
      dndIsFilledMode: element.dndIsFilledMode == 'auto' ? element._autoDnDIsFilledMode : element.dndIsFilledMode,
    },
    scoring: {
      isAllowMultipleAnswers: element.config.isAllowMultipleAnswer,
      answerSets,
      nonScoringTargetIds: element.config.isAllowNonScoringTarget ? element.dndTargets.filter(t => t.isNonScoring).map(t => t.id) : [],
      ...scoringParam
    },
    _showAnswer: {
      isShowAnswer: element._isShowAnswer ?? false,
      activeAnswerSetIndex: element._activeAnswerIndex ?? 0,
    },
    stateManager: isUsingReusableDraggable ? ReusableDnDStateManager : DnDStateManager,
  };
  setupInteractiveDnD(dg, int, dgIntConfig);
}

// copied from the library
// TODO: clean this up
function calcTableSize(diagrams: DgDiagram[][], padding: [number,number], min_rowsize = 0, min_colsize = 0
) : [number[], number[]] {
    // if the orientation is columns, then we just transpose the rows and columns
    let diagram_rows = diagrams;
    function f_size(d: DgDiagram | undefined) {
        if (d == undefined)
            return [min_colsize, min_rowsize];
        let [bottomleft, topright] = d.bounding_box();
        let width = topright.x - bottomleft.x + 2*padding[1];
        let height = topright.y - bottomleft.y + 2*padding[0];
        return [width, height];
    }
    let row_count = diagram_rows.length;
    let col_count = Math.max(...diagram_rows.map(row => row.length));
    let rowsizes = Array(row_count).fill(min_rowsize);
    let colsizes = Array(col_count).fill(min_colsize);
    // find the maximum size of each row and column
    for (let r = 0; r < row_count; r++) {
        for (let c = 0; c < col_count; c++) {
            let [w, h] = f_size(diagram_rows[r][c]);
            rowsizes[r] = Math.max(rowsizes[r], h);
            colsizes[c] = Math.max(colsizes[c], w);
        }
    }
    return [rowsizes, colsizes];
}

export function isPossibleToAlignHomeLabelToFirstRow(element: IDgIntElementDnDTable): boolean {
  return !element.label.text 
    && !!element.homeConfig.label
    && (element.homeConfig.labelPosition == HOME_LABEL_POSITION.TOP 
      || element.homeConfig.labelPosition == undefined)
    && (element.config.homePosition == LAYOUT_HOME_POSITION.LEFT_TOP
      || element.config.homePosition == LAYOUT_HOME_POSITION.RIGHT_TOP)
}

function generateMergedCellStyle(dg:DgLib, tableDiagram: DgDiagram): DgDiagram {
  const tableBBox = tableDiagram.bounding_box();
  const totalRegion = dg.rectangle_corner(tableBBox[0], tableBBox[1]);
  const negativeRegions = tableDiagram.get_tagged_elements([dg.TAG.TABLE_CELL, dg.TAG.EMPTY_CELL])
  const contentDiagram = dg.diagram_combine(...tableDiagram.get_tagged_elements([dg.TAG.TABLE_CONTENT]));
  if (negativeRegions.length == 0) {
    return dg.diagram_combine(totalRegion, contentDiagram);
  } else {
    let combinedNegativeRegions = negativeRegions[0];
    for (let i = 1; i < negativeRegions.length; i++) {
      combinedNegativeRegions = dg.boolean.union(combinedNegativeRegions, negativeRegions[i]);
    }
    const positiveRegion = dg.boolean.difference(totalRegion, combinedNegativeRegions);
    return dg.diagram_combine(positiveRegion, contentDiagram);
  }
}
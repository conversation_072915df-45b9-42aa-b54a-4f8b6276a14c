import { IDgIntConstructionElement, DgIntConstructionType, IDgIntStateManager, IDgIntMcqState } from "../common";
import type { DgLib, DgInt, DgDiagram, DgVector2 } from "src/assets/lib/diagramatics/config";
import { DgIntImageManager } from "../image-manager";
import { IContentElementImage } from "../../element-render-image/model";
import { IDgIntScoringParam, resizeSVGElementAndDraw, IdGenerator, numberToAlphabet, arraysEqual, getStyle, formattedStringToBBCode, multilineTextWithSize } from "./common";
import { DgStyleProfile, getStyleParam } from "../style-profile";
import { cloneDeep } from 'lodash';
import { update } from "@tweenjs/tween.js";
import { ScoringTypes } from "../../models";
import { attachLabelDiagram, DG_INT_MATCHING_TYPE, HOME_LABEL_POSITION } from "./dnd-common";
import { PubSubTypes } from "../../element-render-frame/pubsub/types";
import { Subject } from "rxjs";
import { DgIntCallbacks } from "../renderer";

const EM_LINE_HEIGHT = 1.3;
const EM_ELEMENT_PADDING = 0.5;
const TAG_HOTSPOT_POLYGON = 'hotspot_polygon';
const FILTER_OUTER_SHADOW_ID = 'outer-shadow';

export enum presetHotspotShape {
  SQUARE = 'square',
  CIRCLE = 'circle',
}
function generateCirclePoints(r: number, n: number): {x: number, y: number}[] {
  const points: {x: number, y: number}[] = [];
  for (let i = 0; i < n; i++){
    const angle = i * 2 * Math.PI / n;
    const x = r * Math.cos(angle);
    const y = r * Math.sin(angle);
    points.push({x, y});
  }
  return points;
}
export const presetHotspotShapePoints: { [key in presetHotspotShape]: {x: number, y: number}[]} = {
  [presetHotspotShape.SQUARE] : [
    { x:-1, y:-1},
    { x: 1, y:-1},
    { x: 1, y: 1},
    { x:-1, y: 1},
  ],
  [presetHotspotShape.CIRCLE] : generateCirclePoints(1, 50),
}
export const isPreventEditVertex: { [key in presetHotspotShape]: boolean } = {
  [presetHotspotShape.SQUARE] : false,
  [presetHotspotShape.CIRCLE] : true,
}

interface IDgIntHotspot {
  id?: string;
  points: {x: number, y: number}[];
  position?: {x: number, y: number};
  isCorrect: boolean;
  _isPreventEditVertex?: boolean;
  voiceover?: {url?: string, script?: string};
}

export enum DgIntPolygonEditType {
  BBOX = 'bbox',
  VERTEX = 'vertex',
}
export enum DgIntMcqMode {
  MCQ = 'mcq',
  MSEL = 'msel',
}

export interface IDgIntDataUpdateParamMcqHotspot {
  hotspotIndex: number;
  editType: DgIntPolygonEditType;
  isLockAspectRatio?: boolean;
  isAllowAddNewVertex?: boolean;
}
export interface IDgIntDataUpdateCallbackObjectMcqHotspot {
  hotspotIndex: number;
  hotspotPoints: {x: number, y: number}[];
}
export interface IDgIntMcqHotspotStyle {
  normal:{
    off:{ color: string; opacity: number; }
    on:{ color: string; opacity: number; }
  }
  hover:{
    off:{ color?: string; opacity: number; }
    on:{ color?: string; opacity: number; }
  }
  isSeparateHoverColor?: boolean;
}

export interface IDgIntElementMcqHotspot extends IDgIntConstructionElement {
  constructionType: DgIntConstructionType.MCQ_HOTSPOT;
  hotspots: IDgIntHotspot[];
  backgroundImage?: {
    image?: IContentElementImage;
    _cachedImageSize?: {width: number, height: number};
  },
  mode: DgIntMcqMode;
  isSelectToggle?: boolean;
  maxNumberOfSelection?: number,
  styleConfig: IDgIntMcqHotspotStyle;
  label? : {
    text?: string;
    position?: HOME_LABEL_POSITION;
  },
  _hoveredIndex?: number;
}

export function generateDefaultDgIntElementMcqHotspot(): IDgIntElementMcqHotspot {
  return {
    constructionType: DgIntConstructionType.MCQ_HOTSPOT,
    hotspots: [],
    label: {
      text: '',
      position: HOME_LABEL_POSITION.TOP
    },
    mode: DgIntMcqMode.MCQ,
    styleConfig: {
      normal: {
        off: { color: '#c7c7c7', opacity: 0.5 },
        on: { color: '#1f77b4', opacity: 0.5 },
      },
      hover: {
        off: { color: '#c7c7c7', opacity: 0.7 },
        on: { color: '#1f77b4', opacity: 0.7 },
      },
      isSeparateHoverColor: false,
    },
    backgroundImage: {
      image: undefined,
    }
  }
}

export function generateIdOnHotspots(hotspots : IDgIntHotspot[]) : void{
  for (let i = 0; i < hotspots.length; i++){
    hotspots[i].id = numberToAlphabet(i);
  }
}

type updateCallback_t = (param: IDgIntDataUpdateCallbackObjectMcqHotspot) => void;
export function renderDgIntMcqHotspot(
  element: IDgIntElementMcqHotspot, svgElement: SVGSVGElement,
  dg: DgLib, int: DgInt, imageManager: DgIntImageManager, styleProfiles: Array<DgStyleProfile>,
  scoringParam: IDgIntScoringParam,
  updateParams: IDgIntDataUpdateParamMcqHotspot,
  updateCallback?: updateCallback_t,
  callbacks?: DgIntCallbacks,
) {
  if (element.hotspots.length > 0 && element.hotspots[0].id == undefined){
    element = cloneDeep(element);
    generateIdOnHotspots(element.hotspots);
  }
  
  const styleParam = getStyleParam(styleProfiles);
  const isDropShadow = styleParam['drop_shadow'] ?? false;
  const isColorBorder = styleParam['hotspot_border_only'] ?? false;

  const bgImage = getBackgroundImage(dg, imageManager, element);
  
  let diagram = bgImage;
  const canvasStyle = getStyle(svgElement);
  const multilineText = (s : string, isBBCode = false) => {
    const bbcode = isBBCode ? s : formattedStringToBBCode(s);
    return multilineTextWithSize(bbcode, dg, canvasStyle.pxFontSize, canvasStyle.fontFamily, EM_LINE_HEIGHT);
  }
  if (element.label?.text){
    const labelDiagram = multilineText(element.label.text);
    diagram = attachLabelDiagram(dg, diagram, labelDiagram, element.label.position, EM_ELEMENT_PADDING)
  }

  const hotspotDiagramGenerator = (points: DgVector2[]) => {
    return dg.polygon(points).fill('lightblue').opacity(0.5).move_origin('center-center');
  }
  let hotspotVPoints = element.hotspots.map(h => pointAraryToV2Array(dg, h.points));
  let hotspotDiagrams = hotspotVPoints.map(points => hotspotDiagramGenerator(points));

  const outerShadowFilterStr = dg.filter.string_filter.outer_shadow(
    1, 1, 1, 3, "rgba(0,0,0,0.6)", FILTER_OUTER_SHADOW_ID, 3, 3, 1/10)
  resizeSVGElementAndDraw(dg, diagram, svgElement, {
    filter_strings: [outerShadowFilterStr],
  });
  
  let voiceoverDataList = [];
  for (let hotspot of element.hotspots){
    const voiceover = hotspot.voiceover;
    if (voiceover && voiceover.url && voiceover.script) {
      const trigger = new Subject<boolean>();
      voiceoverDataList.push({url: voiceover.url, trigger});
    }
  }
  callbacks.registerVoiceoverData(voiceoverDataList);
  
  if (updateParams?.hotspotIndex != undefined && updateCallback){
    renderEditHotspot(
      dg, int, diagram, svgElement,
      element, hotspotVPoints, hotspotDiagrams, hotspotDiagramGenerator, updateParams, updateCallback);
  } else {
    renderInputHotspot(
      dg, int, element, hotspotDiagrams, isDropShadow, isColorBorder, scoringParam, voiceoverDataList, callbacks.showMessage);
  }
}

function renderInputHotspot(
  dg: DgLib, 
  int: DgInt, 
  element: IDgIntElementMcqHotspot,
  hotspotDiagrams: DgDiagram[],
  isDropShadow: boolean,
  isColorBorder: boolean,
  scoringParam: IDgIntMcqScoringParam,
  voiceoverDataList: {url: string, trigger: Subject<boolean>}[],
  showMessageCallback?: (msg: string) => void,
){
  const idList = element.hotspots.map(e => e.id);
  const correctAnswer = element.hotspots.filter(e => e.isCorrect).map(e => e.id);
  const styleConfig = element.styleConfig;
  const hoveredIndex = element._hoveredIndex;
  const mode = element.mode ?? DgIntMcqMode.MCQ;
  const maxNumberOfSelections = element.maxNumberOfSelection ?? Infinity;
  let isSelectToggle = element.isSelectToggle ?? true;
  if (mode == DgIntMcqMode.MSEL) isSelectToggle = true;
  
  const stateManager = new McqStateManager(
    int, mode, maxNumberOfSelections, idList, correctAnswer, scoringParam, isSelectToggle, showMessageCallback
  );
  
  for (let i = 0; i < element.hotspots.length; i++){
    const id = element.hotspots[i].id;
    let base: DgDiagram;
    let diagram_off: DgDiagram;
    let diagram_on: DgDiagram;
    let diagram_off_pressed: DgDiagram;
    let diagram_on_pressed: DgDiagram;
    let diagram_off_hover: DgDiagram;
    let diagram_on_hover: DgDiagram;
    
    if (!isColorBorder){
      base = hotspotDiagrams[i];
      diagram_off = base
        .opacity(styleConfig?.normal?.off?.opacity ?? 0.5)
        .fill(styleConfig?.normal?.off?.color ?? 'lightgray');
      diagram_on = base
        .opacity(styleConfig?.normal?.on?.opacity ?? 0.5)
        .fill(styleConfig?.normal?.on?.color ?? 'lightblue');
      
      diagram_off_pressed = diagram_off.opacity(0.2);
      diagram_on_pressed = diagram_on.opacity(0.2);
      
      diagram_off_hover = diagram_off
        .opacity(styleConfig?.hover?.off?.opacity ?? 0.7);
      diagram_on_hover = diagram_on
        .opacity(styleConfig?.hover?.on?.opacity ?? 0.7);
      if (styleConfig?.isSeparateHoverColor){
        diagram_off_hover = diagram_off_hover.fill(styleConfig?.hover?.off?.color ?? 'lightgray');
        diagram_on_hover = diagram_on_hover.fill(styleConfig?.hover?.on?.color ?? 'lightblue');
      }
    } else {
      base = hotspotDiagrams[i].fill('white').stroke('white').strokewidth(4).opacity(0);
      const border = base.opacity(1).fill('none');
      
      const border_off = border
        .opacity(styleConfig?.normal?.off?.opacity ?? 0.5)
        .stroke(styleConfig?.normal?.off?.color ?? 'lightgray')
      const border_on = border
        .opacity(styleConfig?.normal?.on?.opacity ?? 0.5)
        .stroke(styleConfig?.normal?.on?.color ?? 'lightblue')
      diagram_on = base.combine(border_on);
      diagram_off = base.combine(border_off);
      
      const border_off_pressed = border_off.opacity(0.2);
      const border_on_pressed = border_on.opacity(0.2);
      diagram_off_pressed = base.combine(border_off_pressed);
      diagram_on_pressed = base.combine(border_on_pressed);
      
      let border_off_hover = border_off
        .opacity(styleConfig?.hover?.off?.opacity ?? 0.7);
      let border_on_hover = border_on
        .opacity(styleConfig?.hover?.on?.opacity ?? 0.7);
      if (styleConfig?.isSeparateHoverColor){
        border_off_hover = border_off_hover.stroke(styleConfig?.hover?.off?.color ?? 'lightgray');
        border_on_hover = border_on_hover.stroke(styleConfig?.hover?.on?.color ?? 'lightblue');
      }
      diagram_off_hover = base.combine(border_off_hover);
      diagram_on_hover = base.combine(border_on_hover);
    }
    
    if (isDropShadow){
      const shadow = base.opacity(1).filter(FILTER_OUTER_SHADOW_ID);
      diagram_off = diagram_off.combine(shadow);
      diagram_on = diagram_on.combine(shadow);
      diagram_off_pressed = diagram_off_pressed.combine(shadow);
      diagram_on_pressed = diagram_on_pressed.combine(shadow);
      diagram_off_hover = diagram_off_hover.combine(shadow);
      diagram_on_hover = diagram_on_hover.combine(shadow);
    }
    
    if (i == hoveredIndex){
      let offOpacity = styleConfig?.normal?.off?.opacity ?? 0.5;
      let onOpacity = styleConfig?.normal?.on?.opacity ?? 0.5;
      offOpacity = Math.max(offOpacity, 0.5);
      onOpacity = Math.max(onOpacity, 0.5);
      diagram_on = diagram_on.opacity(onOpacity).strokewidth(10).stroke('blue');
      diagram_off = diagram_off.opacity(offOpacity).strokewidth(10).stroke('blue');
    }
    
    if (!isSelectToggle){
      diagram_on_pressed = diagram_on;
      diagram_on_hover = diagram_on;
    }
    
    stateManager.initButton(
      id, int, voiceoverDataList[i],
      diagram_off, diagram_off_pressed, diagram_off_hover,
      diagram_on, diagram_on_pressed, diagram_on_hover
    );
  }
  int.draw();
  
  const thisQuestionState = scoringParam.questionState[scoringParam.entryId];
  const isInitialState = thisQuestionState == undefined || !thisQuestionState.isStarted;
  if (!isInitialState) {
    stateManager.setState(thisQuestionState);
  } else {
    stateManager.publishState();
  }
}


function renderEditHotspot(
  dg: DgLib, 
  int: DgInt, 
  bgImage: DgDiagram,
  svgElement: SVGSVGElement,
  element: IDgIntElementMcqHotspot,
  hotspotVPoints: DgVector2[][],
  hotspotDiagrams: DgDiagram[],
  hotspotDiagramGenerator: (points: DgVector2[]) => DgDiagram,
  updateParams: IDgIntDataUpdateParamMcqHotspot,
  updateCallback: updateCallback_t,
){
  const updateIndex = updateParams.hotspotIndex;
  const otherDiagrams = hotspotDiagrams.filter((_, i) => i != updateIndex).map(d => d.opacity(0.5).fill('lightgray'));
  const diagram = dg.diagram_combine(bgImage, ...otherDiagrams);
  resizeSVGElementAndDraw(dg, diagram, svgElement);
  
  switch (updateParams.editType) {
    case DgIntPolygonEditType.VERTEX: {
      renderEditHotspotPolygon(
        dg, int, element, hotspotVPoints, hotspotDiagrams, hotspotDiagramGenerator, updateParams, updateCallback)
    } break;
    case DgIntPolygonEditType.BBOX:
    default: {
      renderEditHotspotBBox(
        dg, int, element, hotspotVPoints, hotspotDiagrams, hotspotDiagramGenerator, updateParams, updateCallback)
    } break;
  }
  registerKeyboardEvent(dg, int, svgElement, element.hotspots[updateParams.hotspotIndex].id);
}

function registerKeyboardEvent(dg: DgLib, int: DgInt, svgElement: SVGSVGElement, hotspotId: string) {
  // we register using `int.registerEventListener` because the event listener registered this way
  // will be cleaned up for each Interactive Diagram rerendering
  
  int.registerEventListener(document, 'keydown', (e: KeyboardEvent) => {
    const locatorHandler = int.locatorHandler!;
    if (!locatorHandler) return;
    const pos = int.get(hotspotId) as DgVector2; if (!pos) return;
    
    e.preventDefault();
    const setter = locatorHandler.setter[hotspotId];
    const callback = locatorHandler.callbacks[hotspotId];
    let step = e.shiftKey? 0.01 : 0.1;
    if (e.ctrlKey) step = 1;
    
    if (e.key == 'ArrowRight') {
      const newPos = pos.add(dg.V2(step, 0));
      setter(newPos); callback(newPos);
    }
    if (e.key == 'ArrowLeft') {
      const newPos = pos.add(dg.V2(-step, 0));
      setter(newPos); callback(newPos);
    }
    if (e.key == 'ArrowUp') {
      const newPos = pos.add(dg.V2(0, step));
      setter(newPos); callback(newPos);
    }
    if (e.key == 'ArrowDown') {
      const newPos = pos.add(dg.V2(0, -step));
      setter(newPos); callback(newPos);
    }
  });
}

function verticesFromBBox(dg: DgLib, bbox: [DgVector2, DgVector2]): DgVector2[] {
  const [botLeft, topRight] = bbox;
  const vertices = [
      botLeft,
      dg.V2(topRight.x, botLeft.y),
      topRight,
      dg.V2(botLeft.x, topRight.y),
  ];
  return vertices;
}

function transformToBbox(dg: DgLib, diagram: DgDiagram, newBBox: [DgVector2, DgVector2]): DgDiagram {
  const prevBBox = diagram.bounding_box() as [DgVector2, DgVector2];
  const scaleX = (newBBox[1].x - newBBox[0].x) / (prevBBox[1].x - prevBBox[0].x);
  const scaleY = (newBBox[1].y - newBBox[0].y) / (prevBBox[1].y - prevBBox[0].y);
  const vOffset = newBBox[0].sub(prevBBox[0]);
  const newDiagram = diagram.translate(vOffset).scale(dg.V2(scaleX, scaleY), newBBox[0]);
  return newDiagram;
}
function getNewBBoxFromMovedPoint(
  dg: DgLib, prevPoints: DgVector2[], pointIndex: number, pos: DgVector2, isLockAspectRatio: boolean,
): {bbox: [DgVector2, DgVector2], pos: DgVector2} {
  
  if (isLockAspectRatio){
    const oppositeIndex = {0: 2, 1: 3, 2: 0, 3: 1}
    const oppositePoint = prevPoints[oppositeIndex[pointIndex]];
    const prevSpan = prevPoints[pointIndex].sub(oppositePoint);
    const newSpan = pos.sub(oppositePoint);
    const scaleX = newSpan.x / prevSpan.x;
    const scaleY = newSpan.y / prevSpan.y;
    const scale = Math.abs(scaleX) > Math.abs(scaleY) ? scaleY : scaleX;
    const correctedSpan = prevSpan.scale(scale);
    const correctedPos = oppositePoint.add(correctedSpan);
    pos = correctedPos;
  }
  
  let bbox: [DgVector2, DgVector2];
  switch (pointIndex){
    // bottom left
    case 0: bbox = [pos, prevPoints[2]]; break;
    // bottom right
    case 1: bbox = [
      dg.V2(prevPoints[0].x, pos.y),
      dg.V2(pos.x, prevPoints[2].y)
    ]; break;
    // top right
    case 2: bbox = [prevPoints[0], pos]; break;
    // top left
    case 3: bbox = [
      dg.V2(pos.x, prevPoints[0].y),
      dg.V2(prevPoints[2].x, pos.y)
    ]; break;
  }
  return {bbox, pos};
} 

function renderEditHotspotBBox(
  dg: DgLib, 
  int: DgInt, 
  element: IDgIntElementMcqHotspot,
  hotspotVPoints: DgVector2[][],
  hotspotDiagrams: DgDiagram[],
  hotspotDiagramGenerator: (points: DgVector2[]) => DgDiagram,
  updateParams: IDgIntDataUpdateParamMcqHotspot,
  updateCallback: updateCallback_t,
){
  const SCALE = 0.5;
  const polygonVertexCircle = dg.regular_polygon(20, 0.5 * SCALE).fill('black');
  const updateIndex = updateParams.hotspotIndex;
  const isLockAspectRatio = updateParams.isLockAspectRatio ?? true;
  
  function callCallback() {
    updateCallback({
      hotspotIndex: updateIndex,
      hotspotPoints: v2ArrayToPointArray(hotspotVPoints[updateIndex]),
    });
  }
  function updateBBoxLocator(hotspotIndex: number, skipndex?: number) {
    const hotspot = element.hotspots[hotspotIndex];
    const diagram = hotspotDiagrams[hotspotIndex];
    const bbox = diagram.bounding_box() as [DgVector2, DgVector2];
    const corners = verticesFromBBox(dg, bbox);
    
    corners.forEach((point, pointIndex) => {
      if (pointIndex == skipndex) return;
      const locatorName = `${hotspot.id}:${pointIndex}`;
      int.locator_custom(
        locatorName, point, polygonVertexCircle.copy(), undefined, false,
        (_, pos: DgVector2) => {
          const newBBox = getNewBBoxFromMovedPoint(dg, corners, pointIndex, pos, isLockAspectRatio);
          const newDiagram = transformToBbox(dg, diagram, newBBox.bbox);
          hotspotDiagrams[hotspotIndex] = newDiagram;
          const vPoints = newDiagram.path.points;
          hotspotVPoints[hotspotIndex] = vPoints;
          
          if (isLockAspectRatio) int.set(locatorName, newBBox.pos);
          
          updateHotspotLocator(hotspotIndex)
          updateBBoxLocator(hotspotIndex, pointIndex);
          callCallback();
        },
        undefined
      );
    });
  }
  function updateHotspotLocator(hotspotIndex: number){
    const i = hotspotIndex;
    const hotspot = element.hotspots[i];
    const hotspotDiagram = hotspotDiagrams[i];
    const bbox = hotspotDiagram.bounding_box() as [DgVector2, DgVector2];
    const rect = dg.rectangle_corner(bbox[0], bbox[1]).stroke('gray');
    int.locator_custom(
      hotspot.id, hotspotDiagram.origin, hotspotDiagram.combine(rect), undefined, false, 
      (_,pos: DgVector2) => {
        const prevDiagram = hotspotDiagrams[i];
        const offset = pos.sub(prevDiagram.origin);
        hotspotDiagrams[i] = hotspotDiagrams[i].translate(offset);
        hotspotVPoints[i] = hotspotVPoints[i].map(v => v.add(offset));
        updateBBoxLocator(i); 
        callCallback();
      },
      undefined
    );
  }
  
  updateHotspotLocator(updateIndex);
  updateBBoxLocator(updateIndex);
  int.locator_initial_draw();
}

function renderEditHotspotPolygon(
  dg: DgLib, 
  int: DgInt, 
  element: IDgIntElementMcqHotspot,
  hotspotVPoints: DgVector2[][],
  hotspotDiagrams: DgDiagram[],
  hotspotDiagramGenerator: (points: DgVector2[]) => DgDiagram,
  updateParams: IDgIntDataUpdateParamMcqHotspot,
  updateCallback: updateCallback_t,
){
  const SCALE = 0.5;
  const polygonVertexCircle = dg.regular_polygon(20, 0.5 * SCALE).fill('black');
  const newVertexSymbol = dg.diagram_combine(
    dg.rectangle(0.2 * SCALE, 1 * SCALE),
    dg.rectangle(1 * SCALE, 0.2 * SCALE),
  ).fill('black');
  const updateIndex = updateParams.hotspotIndex;
  const isAllowAddNewVertex = updateParams.isAllowAddNewVertex ?? true;
  const isPreventEditVertex = element.hotspots[updateIndex]._isPreventEditVertex ?? false;
  
  function callCallback() {
    updateCallback({
      hotspotIndex: updateIndex,
      hotspotPoints: v2ArrayToPointArray(hotspotVPoints[updateIndex]),
    });
  }
  function updateVerticesLocator(hotspotIndex: number) {
    if (isPreventEditVertex) return;
    const hotspot = element.hotspots[hotspotIndex];
    hotspotVPoints[hotspotIndex].forEach((point, pointIndex) => {
      int.locator_custom(
        `${hotspot.id}:${pointIndex}`, point, polygonVertexCircle.copy(), undefined, false,
        (_, pos: DgVector2) => {
          hotspotVPoints[hotspotIndex][pointIndex] = pos;
          hotspotDiagrams[hotspotIndex] = hotspotDiagramGenerator(hotspotVPoints[hotspotIndex]);
          updateHotspotLocator(hotspotIndex);
          updateNewVertexButton(hotspotIndex)
          callCallback();
        },
        () => {
          // remove the vertex
          if (hotspotVPoints[hotspotIndex].length <= 2) return;
          hotspotVPoints[hotspotIndex].splice(pointIndex, 1);
          hotspotDiagrams[hotspotIndex] = hotspotDiagramGenerator(hotspotVPoints[hotspotIndex]);
          
          int.remove_locator(`${hotspot.id}:${hotspotVPoints[hotspotIndex].length}`);
          int.remove_button(`${hotspot.id}:new:${hotspotVPoints[hotspotIndex].length}`)
          
          updateHotspotLocator(hotspotIndex);
          updateVerticesLocator(hotspotIndex);
          updateNewVertexButton(hotspotIndex)
          callCallback();
        }
      );
    });
  }
  function updateNewVertexButton(hotspotIndex: number){
    if (isPreventEditVertex) return;
    if (!isAllowAddNewVertex) return;
    const hotspot = element.hotspots[hotspotIndex];
    let midpoints = hotspotVPoints[hotspotIndex].map((point, i) => {
      const nextPoint = hotspotVPoints[hotspotIndex][(i+1)%hotspotVPoints[hotspotIndex].length];
      const midPoint = point.add(nextPoint).scale(0.5);
      return midPoint;
    });
    midpoints.forEach((point, i) => {
      const name = `${hotspot.id}:new:${i}`;
      const symbol = newVertexSymbol.position(point)
      int.button_click_hover(name, symbol, symbol, symbol.opacity(0.5), () => {
        hotspotVPoints[hotspotIndex].splice(i+1, 0, point);
        updateNewVertexButton(hotspotIndex);
        updateHotspotLocator(hotspotIndex);
        updateVerticesLocator(hotspotIndex);
        callCallback();
      })
    });
  }
  function updateHotspotLocator(hotspotIndex: number){
    const i = hotspotIndex;
    const hotspot = element.hotspots[i];
    const hotspotDiagram = hotspotDiagrams[i];
    int.locator_custom(
      hotspot.id, hotspotDiagram.origin, hotspotDiagram, undefined, false, 
      (_,pos: DgVector2) => {
        const prevDiagram = hotspotDiagrams[i];
        const offset = pos.sub(prevDiagram.origin);
        hotspotDiagrams[i] = hotspotDiagrams[i].translate(offset);
        hotspotVPoints[i] = hotspotVPoints[i].map(v => v.add(offset));
        updateVerticesLocator(i); 
        updateNewVertexButton(i);
        callCallback();
      },
      undefined
    );
  }
  
  updateHotspotLocator(updateIndex);
  updateVerticesLocator(updateIndex);
  updateNewVertexButton(updateIndex);
  int.locator_initial_draw();
}


export function getHotspotPosition(hotspot: IDgIntHotspot, forceRecalculate: boolean = false): {x: number, y: number} {
  if (hotspot.position && !forceRecalculate) return hotspot.position;
  
  let minX = Infinity;
  let minY = Infinity;
  let maxX = -Infinity;
  let maxY = -Infinity;
  for (let i = 0; i < hotspot.points.length; i++){
    const point = hotspot.points[i];
    minX = Math.min(minX, point.x);
    minY = Math.min(minY, point.y);
    maxX = Math.max(maxX, point.x);
    maxY = Math.max(maxY, point.y);
  }
  return {
    x: (minX + maxX) / 2,
    y: (minY + maxY) / 2,
  }
}

export function moveHotspotToPosition(hotspot: IDgIntHotspot, position: {x: number, y: number}): void{
  const currentPosition = getHotspotPosition(hotspot, true);
  const dx = position.x - currentPosition.x;
  const dy = position.y - currentPosition.y;
  const points = hotspot.points.map(point => ({
    x: point.x + dx,
    y: point.y + dy,
  }));
  hotspot.position = position;
  hotspot.points = points;
}

function pointAraryToV2Array(dg: DgLib, points: {x: number, y: number}[]): DgVector2[] {
  return points.map(({x,y}) => dg.V2(x,y));
}
function v2ArrayToPointArray(v2Array: DgVector2[]): {x: number, y: number}[] {
  return v2Array.map(v => ({x:v.x,y:v.y}));
}

function getBackgroundImage(
  dg: DgLib, imageManager: DgIntImageManager, element: IDgIntElementMcqHotspot
): DgDiagram {
  const background = element.backgroundImage;
  if (!background) return dg.empty();
  
  const imageElement = background.image;
  if (!imageElement) return dg.empty();
  
  // _cachedImageSize used to be [number, number]
  const bgImage = element.backgroundImage;
  if (Array.isArray(bgImage?._cachedImageSize) && bgImage._cachedImageSize.length == 2){
    bgImage._cachedImageSize = {width: bgImage._cachedImageSize[0], height: bgImage._cachedImageSize[1]};
  }
  const url = imageElement.url;
  if (url) imageManager.queueSizeCalculation(url, background);
  
  const ratio = background._cachedImageSize? background._cachedImageSize.width / background._cachedImageSize.height : 1;
  const scale = imageElement.scale;
  // 100unit -> 20em (1unit = 1/5em)
  const widthEm = scale / 5; 
  const sz = imageManager.getSizeOrDefault(url, widthEm, ratio);
  return dg.image(url, sz.width, sz.height);
}

export interface IDgIntMcqScoringParam extends IDgIntScoringParam {
}

export class McqStateManager implements IDgIntStateManager {
  
  isStarted   = false;
  isResponded = false;
  state: { [id: string]: boolean } = {};
  initializer: { [id: string]: { on: CallableFunction, off: CallableFunction } } = {};
  
  constructor(
    public int: DgInt,
    public mode: DgIntMcqMode,
    public maxNumberOfSelections: number,
    public idList: string[],
    public correctAnswer: string[],
    public scoringParam: IDgIntScoringParam,
    public isSelectToggle: boolean,
    public showMessageCallback?: (msg: string) => void,
  ) { }
  
  
  setButtonState(id: string, state: boolean){
    this.state[id] = state;
    const initializer = this.initializer[id];
    if (!initializer) return;
    if (state) initializer.on(); else initializer.off();
  }
  initButton(
    id: string, int: DgInt, voiceoverData: {url: string, trigger: Subject<boolean>},
    diagram_off: DgDiagram, diagram_off_pressed: DgDiagram, diagram_off_hover: DgDiagram,
    diagram_on: DgDiagram, diagram_on_pressed: DgDiagram, diagram_on_hover: DgDiagram,
    initialState: boolean = false,
  ){
    const init_button_off = () => {
      int.button_click_hover(id, diagram_off, diagram_off_pressed, diagram_off_hover, () => { 
        const numberOfSelections = this.getNumberOfSelections();
        if (this.mode == DgIntMcqMode.MSEL && numberOfSelections >= this.maxNumberOfSelections) {
          this.showMessageCallback('slug:mcq_max_msg')
          return;
        }
        
        this.state[id] = true;
        if (this.mode == DgIntMcqMode.MCQ) this.turnOffOtherButtons(id);
        this.interact();
        init_button_on();
        voiceoverData.trigger.next(true);
      })
    }
    const init_button_on = () => {
      int.button_click_hover(id, diagram_on, diagram_on_pressed, diagram_on_hover, () => { 
        // clicking the `on`` button will not turn off the button in MCQ mode
        if (this.mode == DgIntMcqMode.MCQ && !this.isSelectToggle) return;
        this.state[id] = false;
        this.interact();
        init_button_off();
        voiceoverData.trigger.next(true);
      })
    }
    
    this.initializer[id] = { on: init_button_on, off: init_button_off };
    this.setButtonState(id, initialState);
  }
  
  interact() {
    this.isResponded = true;
    this.isStarted = true;
    this.showMessageCallback('') // reset/turn off the message
    this.publishState();
  }
  turnOffOtherButtons(selfId: string) {
    for (let id of this.idList) {
      if (id == selfId) continue;
      this.setButtonState(id, false)
    }
  }
  publishState() {
    this.scoringParam.questionState[this.scoringParam.entryId] = this.getState();
    this.scoringParam.questionPubSub?.allPub({entryId: this.scoringParam.entryId, type: PubSubTypes.UPDATE_VALIDATOR, data: {}})
  }
  getState(): IDgIntMcqState {
    const normalizedScore = this.getNormalizedScore();
    return {
      type: DG_INT_MATCHING_TYPE.MCQ,
      isCorrect: normalizedScore == 1,
      isStarted: this.isStarted,
      isFilled: this.isFilled(),
      isResponded: this.isResponded,
      formattedResponse: this.getFormattedResponse(),
      score: normalizedScore * this.scoringParam.scoreWeight,
      weight: this.scoringParam.scoreWeight,
      scoring_type: ScoringTypes.AUTO,
      selected: this.getSelected(),
    }
  }
  setState(state: IDgIntMcqState) {
    const selected = state.selected;
    for (let id of Object.keys(this.state)){
      this.setButtonState(id, selected.includes(id));
    }
  }
  isFilled() {
    return this.getSelected().length >= 1;
  }
  getFormattedResponse(): string {
    const selected = this.getSelected();
    selected.sort();
    return selected.join(',');
  }
  getSelected(): string[] {
    const selected = Object.keys(this.state).filter(id => this.state[id]);
    return selected;
  }
  getNumberOfSelections() {
    return this.getSelected().length;
  }
  getNormalizedScore() {
    switch (this.mode) {
      case DgIntMcqMode.MCQ: return this.getNormalizedScoreMcq();
      case DgIntMcqMode.MSEL: return this.getNormalizedScoreMsel();
    }
    return 0;
  }
  getNormalizedScoreMcq() {
    const selected = this.getSelected();
    if (selected.length != 1) return 0;
    if (this.correctAnswer.includes(selected[0])) return 1;
    else return 0;
  }
  getNormalizedScoreMsel() {
    if (arraysEqual(this.getSelected(), this.correctAnswer)){
      return 1;
    } else {
      return 0;
    }
  }
  
}

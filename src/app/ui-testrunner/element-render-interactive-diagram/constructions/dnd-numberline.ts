import { IDgIntConstructionElement, DgIntConstructionType, IDgIntStateManager, IDgIntDnDState } from "../common"
import type { DgLib, DgInt, DgDiagram } from "src/assets/lib/diagramatics/config";
import { QuestionState, ScoringTypes } from "../../models";
import { 
  DND_UNUSED_ID, LAYOUT_HOME_POSITION, DG_INT_MATCHING_TYPE, isUnusedId,
  generateDnDBlock,
  TAG_DND_DRAGGABLE,
  TAG_DND_TARGET,
  IDgIntHomeConfig,
  HOME_CONFIG_LAYOUT,
  generateDnDHomeConfigFromOrdering,
  resolveDnDHomeConfig,
  generateHomeTargetDiagramsWithLabel,
  groupByHome,
  unusedId,
  IDgIntDnDConfig,
  setupInteractiveDnD,
  IDgIntElementDnDAnswerSet,
  ReusableDnDStateManager,
  DnDStateManager,
  isConfigIndividualHome,
  appendHomeSuffix,
  DND_ISFILLED_MODE,
  generateHomeGroupBg,
  DgIntVoiceoverData,
} from "./dnd-common";
import { formattedStringToBBCode, multilineTextWithSize, getStyle, IdGenerator, IDgIntScoringParam, flattenArray, EX_TO_EM, } from "./common";
import { DgIntImageManager } from "../image-manager";
import { cloneDeep } from 'lodash';
import { tex2dgMathExpression } from "../mathjax"
import { DgStyleProfile, getStyleParam } from "../style-profile";
import { mathExpressionDg } from "../math-expression";
import { Subject } from "rxjs";
import { DgIntCallbacks } from "../renderer";

const EM_LINE_HEIGHT = 1.3;
const EM_DND_ELEMENT_PADDING = 0.5;

export interface IDgIntElementNumberline {
  min: number,
  max: number,
  isDrawArrow: boolean, // drawArrow: boolean,
  overshoot : {
    isInPercent: boolean,
    left: number,
    right: number,
  },
  isManualTickStep? : boolean,
  tickStep : {
    isShowMajorTicks: boolean,
    major: number,
    isShowMinorTicks: boolean,
    minor: number,
  },
  isCustomTicks? : boolean,
  customTicks?: {
    position: number,
    symbol: string,
  }[]
}

export enum LAYOUT_NUMBERLINE_TARGET_POSITION {
  TOP = "top",
  BOTTOM = "bottom",
  CENTER = "center",
}

export interface IDgIntElementDnDNumberlineElement {
  positionLabel : string; // user can write "3/4" and we want to parse it to 3/4 = 0.75
  isTarget : boolean;
  targetConfig?: {
    layoutPosition : LAYOUT_NUMBERLINE_TARGET_POSITION;
    layoutPositionArgument? : number;
  }
  isOption : boolean;
  optionConfig?: {
    customLabel? : string;
  }
  id? : string;
  voiceover?: {url?: string, script?: string};
}

export enum DND_NUMBERLINE_EXTRA_ELEMENT_TYPE {
  LABELED_TICK = "labeled_tick",
}
export interface IDgIntElementDnDNumberlineExtraElement {
  xpos: number; 
  ypos?: number;
  type: string;
  label?: string;
}


export interface IDgIntElementDnDNumberline extends IDgIntConstructionElement {
  constructionType: DgIntConstructionType.DND_NUMBERLINE;
  numberline: IDgIntElementNumberline;
  elements: IDgIntElementDnDNumberlineElement[];
  homeConfig: IDgIntHomeConfig;
  extraElements?: IDgIntElementDnDNumberlineExtraElement[]
  config: {
    isShowExtraElements: boolean;
    isHideTickOnTarget: boolean;
    isUsingReusableDraggable: boolean;
    numberlineWidth : number;
    homeMaxWidth: number,
    homePosition : LAYOUT_HOME_POSITION;
    padding : [number, number];
    draggableBgColor: string;
    targetBgColor: string;
  };
  _showValuesInEditor?: boolean;
  //
  ordering? : number[]; //deprecated use `homeConfig` instead
  dndIsFilledMode?: 'auto' | DND_ISFILLED_MODE; // user specified value or auto
  _autoDnDIsFilledMode?: DND_ISFILLED_MODE; // auto calculated value
}

export function generateDefaultDgIntElementDnDNumberline(): IDgIntElementDnDNumberline {
  return {
    constructionType: DgIntConstructionType.DND_NUMBERLINE,
    numberline: {
      min: 0,
      max: 10,
      isDrawArrow: true,
      overshoot : {
        isInPercent: true,
        left: 10,
        right: 10,
      },
      isManualTickStep: false,
      tickStep : {
        isShowMajorTicks: true, major: 1,
        isShowMinorTicks: true, minor: 0.5,
      },
    },
    elements : [
      {
        positionLabel: "1.5", 
        isTarget: true,
        targetConfig: {
          layoutPosition : LAYOUT_NUMBERLINE_TARGET_POSITION.TOP,
          layoutPositionArgument : 2
        },
        isOption: false,
      },
      {
        positionLabel: "\\frac{8}{3}", 
        isTarget: true,
        targetConfig: {
          layoutPosition : LAYOUT_NUMBERLINE_TARGET_POSITION.BOTTOM,
          layoutPositionArgument : 2
        },
        isOption: true,
        optionConfig: {
        },
      }
    ],
    homeConfig: {
      isGroupHome: false,
      layout: HOME_CONFIG_LAYOUT.DEFAULT,
      element: [[]]
    },
    config: {
      isShowExtraElements: false,
      isHideTickOnTarget: false,
      isUsingReusableDraggable: false,
      numberlineWidth: 150,
      homeMaxWidth: 150,
      homePosition: LAYOUT_HOME_POSITION.TOP,
      padding: [0.5,0.75],
      draggableBgColor: '#C8E6FA',
      targetBgColor: '#e0e0e0',
    }
  }
}

function handleCommonPattern(label: string) : string {
  // \\frac12 -> \\frac{1}{2}
  const fractionPattern = /\\frac(\d)(\d)/;
  if (fractionPattern.test(label)) {
    label = label.replace(fractionPattern, "\\frac{$1}{$2}");
  }
  
  // pi: "\\pi" -> "PI" 
  // for some reason evaluatex doesn't support "\\pi"
  const piPattern = /\\pi/g;
  if (piPattern.test(label)) {
    label = label.replace(piPattern, "PI");
  }
  // mixed fraction: "A\\frac{B}{C}" -> "(A+\\frac{B}{C})"
  const mixedFractionPattern = /(\d+)\\frac{(\d+)}{(\d+)}/;
  if (mixedFractionPattern.test(label)) {
    label = label.replace(mixedFractionPattern, "($1+\\frac{$2}{$3})");
  }
  return label.trim();
}
function handleTryCommaAsDecimalSeparator(label: string) : string {
  const commaPattern = /,/g;
  if (commaPattern.test(label)) {
    label = label.replace(commaPattern, ".");
  }
  return label.trim();
}
export function evalTexValue(evaluatex: any, str: string) : number{
  let s = str;
  try {
    s = handleCommonPattern(s);
    const val = evaluatex(s)() as number;
    return val;
  } catch (e) {
    // try to replace comma with dot
    try {
      s = handleTryCommaAsDecimalSeparator(s);
      const val = evaluatex(s)() as number;
      return val
    } catch (innerE) {
      console.warn(`Failed to parse the expression "${str}"`);
      return NaN;
    }
  }
}

export function resolveDnDNumberline(element : IDgIntElementDnDNumberline, blockVersion: number|undefined|null) : void {
  if (element.homeConfig == undefined) 
    element.homeConfig = {
      isGroupHome: false,
      layout: HOME_CONFIG_LAYOUT.DEFAULT,
      element: [[]]
    }
  
  const [idChangeMap, newOptionIds] = regenerateIdOnDnDNumberline(element);
  const allOptionIds = element.elements.filter(e => e.isOption).map(e => e.id);
  element.homeConfig = resolveDnDHomeConfig(element.homeConfig, idChangeMap, newOptionIds, allOptionIds);
  
  if (blockVersion >= 2) {
    if (element.dndIsFilledMode == undefined) { element.dndIsFilledMode = 'auto'; }
    if (element.dndIsFilledMode == 'auto') {
      element._autoDnDIsFilledMode = determineNumberlineDnDIsFilledMode(element.elements);
    }
  }
  
}
export function regenerateIdOnDnDNumberline(element : IDgIntElementDnDNumberline) : 
  [Map<string,string>, string[]]
{
  let changeMap = new Map<string,string>();
  let newElementIds = [];
  const idGenerator = new IdGenerator();
  for (let e of element.elements){
    let id = idGenerator.generate('elem_');
    if (e.id) changeMap.set(e.id, id); else newElementIds.push(id);
    e.id = id;
  }
  return [changeMap, newElementIds];
}

export function renderDgIntDnDNumberline(
  element : IDgIntElementDnDNumberline, svgElement : SVGSVGElement, 
  dg: DgLib, int: DgInt, imageManager: DgIntImageManager, styleProfiles: Array<DgStyleProfile>,
  scoringParam: IDgIntScoringParam, callbacks: DgIntCallbacks, blockVersion: number|undefined|null
) {
  let evaluatex = window['evaluatex'];
  
  // backward compatibility
  if ((element.elements.length > 0 && element.elements[0].id == undefined) || (element.homeConfig == undefined )){
    element = cloneDeep(element) as IDgIntElementDnDNumberline;
    resolveDnDNumberline(element, blockVersion);
  }
  //
  
  const homeConfig = cloneDeep(element.homeConfig) as typeof element.homeConfig;
  for (let row of homeConfig.element){
    for (let i in row) row[i] = row[i] + ":option";
  }
  
  let targets = cloneDeep(element.elements.filter(e => e.isTarget)) as IDgIntElementDnDNumberlineElement[];
  let options = cloneDeep(element.elements.filter(e => e.isOption)) as IDgIntElementDnDNumberlineElement[];
  targets.forEach((t) => { t.id += ":target" });
  options.forEach((o) => { o.id += ":option" });
  const styleParam = getStyleParam(styleProfiles);
  const answerSet: IDgIntElementDnDAnswerSet = element.elements
    .filter(e => e.isTarget)
    .map(e => ({
      targetId: e.id + ":target",
      optionIds: e.isOption ? [e.id + ":option"] : []
    }));
  const idToIndexMap = new Map(options.map((option,i) => [option.id, i]));
  
  const isInsideRange = (x: number) => {
    return x >= element.numberline.min && x <= element.numberline.max;
  }
  
  const padding = element.config.padding;
  const homePosition = element.config.homePosition ?? LAYOUT_HOME_POSITION.BOTTOM;
  const homeMaxWidth = element.config.homeMaxWidth * 20/100;
  const targetBgColor = element.config.targetBgColor;
  const draggableBgColor = element.config.draggableBgColor;
  const isVisibleHome = styleParam.visible_home_target ?? false;
  const canvasStyle = getStyle(svgElement);
  const multilineText = (s : string, isBBCode = false) => {
    const bbcode = isBBCode ? s : formattedStringToBBCode(s);
    return multilineTextWithSize(bbcode, dg, canvasStyle.pxFontSize, canvasStyle.fontFamily, EM_LINE_HEIGHT);
  }
  const mathStyleConfig = {svgFontSize: canvasStyle.pxFontSize, mathFontFamily: styleParam.math_font_family};
  const mathExpression = (s: string) => mathExpressionDg(dg, s, mathStyleConfig);
  
  const svgWidthEm = element.config.numberlineWidth * 20/100;
  const numberlineInnerWidth = element.numberline.max - element.numberline.min;
  const leftOvershoot = element.numberline.overshoot.isInPercent ? 
    numberlineInnerWidth * element.numberline.overshoot.left/100 : element.numberline.overshoot.left;
  const rightOvershoot = element.numberline.overshoot.isInPercent ?
    numberlineInnerWidth * element.numberline.overshoot.right/100 : element.numberline.overshoot.right;
  const leftOriginal = element.numberline.min - leftOvershoot;
  const rightOriginal = element.numberline.max + rightOvershoot;
  const numberlineWidth = rightOriginal - leftOriginal;
  const scale = svgWidthEm / numberlineWidth;
  
  let hiddenTicks = [];
  if (element.config.isHideTickOnTarget) {
    for (let t of targets) {
      let x = evalTexValue(evaluatex, t.positionLabel);
      if (!isNaN(x)) hiddenTicks.push(x);
    }
  }
  
  let diagrams : DgDiagram[] = [];
  const extraElements = element.config.isShowExtraElements ? element.extraElements : [];
  const numberlineDiagram = generateNumberlineDiagram(
    dg, element.numberline, hiddenTicks, extraElements,
    svgWidthEm, mathExpression, svgElement)
  diagrams.push(numberlineDiagram);
  
  const optionInnerDiagrams = options.map(option => {
    let text = option.positionLabel;
    if (option.optionConfig?.customLabel) text = option.optionConfig.customLabel;
    // return multilineText(text);
    return mathExpression(text);
  });
  const optionDiagramSizes = optionInnerDiagrams.map(d => dg.geometry.size(d));
  const optionMaxWidth = Math.max(...optionDiagramSizes.map(s => s[0])) + 2*padding[1];
  const optionMaxHeight = Math.max(...optionDiagramSizes.map(s => s[1])) + 2*padding[0];
  const dndBlock = generateDnDBlock(dg, optionMaxWidth, optionMaxHeight, styleProfiles)
  const optionDiagrams = optionInnerDiagrams.map((d) => {
    const source = d.append_tags(TAG_DND_DRAGGABLE);
    const bg = dndBlock.fill(draggableBgColor).append_tags(TAG_DND_DRAGGABLE);
    const combined = bg.combine(source);
    return dg.style.applyStyleProfiles(combined, styleProfiles);
  })
  
  let targetBlocks : DgDiagram[] = [];
  let validBlocks : boolean[] = [];
  // setup target diagram
  for (let target of targets){
    const [yPos, anchor] = getTargetLayoutPositionEm(target);
    let xPosOriginal = evalTexValue(evaluatex, target.positionLabel);
    let isNotValid = (isNaN(xPosOriginal) || !isInsideRange(xPosOriginal))
    if (isNotValid) xPosOriginal = element.numberline.min;
    
    const xPos = xPosOriginal * scale;
    const vBot = dg.V2(xPos, 0);
    const vTop = dg.V2(xPos, yPos);
    const circ = dg.circle(0.2).position(vBot).fill('black');
    const line = dg.line(vBot, vTop);
    let block = dndBlock.copy().move_origin(anchor).position(vTop).move_origin('center-center');
    diagrams.push(circ, line, block);
    validBlocks.push(!isNotValid);
    targetBlocks.push(block);
  }
  
  const diagram0 = dg.diagram_combine(...diagrams).move_origin('center-center');
  const groupedOptionDiagrams = groupByHome(optionDiagrams, homeConfig.element, idToIndexMap);
  const [homeDiagrams, labelDiagram] = generateHomeTargetDiagramsWithLabel(
    dg, homePosition, homeConfig,
    groupedOptionDiagrams, diagram0, 
    homeMaxWidth, EM_DND_ELEMENT_PADDING, multilineText, mathExpression, imageManager,
    1);
  const homeGroupBg = !isVisibleHome ? [] : 
    generateHomeGroupBg(dg, homeDiagrams, homeConfig, styleParam, idToIndexMap, EM_DND_ELEMENT_PADDING).map(d => d.fill(targetBgColor));
  
  let homeIdList: string[];
  const sortedOptionIdList = flattenArray(homeConfig.element);
  if (isConfigIndividualHome(element.homeConfig)){
    homeIdList = sortedOptionIdList.map(id => appendHomeSuffix(id));
  } else {
    homeIdList = homeDiagrams.map((_, i) => unusedId(i));
  }
  
  const idToDiagramMap = new Map<string, DgDiagram>();
  {
    // setup diagram for dnd elements
    // setup dnd target
    for (let i = 0; i < targets.length; i++) {
      const target = targets[i];
      let block = targetBlocks[i].fill(targetBgColor).append_tags(TAG_DND_TARGET);
      block = dg.style.applyStyleProfiles(block, styleProfiles);
      if (!validBlocks[i]) block = block.fill('lightred');
      idToDiagramMap.set(target.id, block);
    }
    
    // dnd home
    for (let i in homeDiagrams){
      const homeDiagram = isVisibleHome ? homeDiagrams[i].fill(targetBgColor) : homeDiagrams[i];
      const homeId = homeIdList[i];
      idToDiagramMap.set(homeId, homeDiagram);
    }
    // dnd draggable
    for (let i in options){
      const optionDiagram = optionDiagrams[i];
      idToDiagramMap.set(options[i].id, optionDiagram);
    }
  }
  
  const idToLabelMap = new Map<string, string>();
  {
    for (let e of element.elements)
      idToLabelMap.set(e.id, e.positionLabel);
    for (let target of targets)
      idToLabelMap.set(target.id, target.positionLabel);
    for (let option of options)
      idToLabelMap.set(option.id, option.positionLabel);
  }
  
  const isUsingReusableDraggable = element.config.isUsingReusableDraggable ?? false;
  const dgIntConfig: IDgIntDnDConfig = {
    idList: {
      options: options.map(o => o.id),
      targets: targets.map(t => t.id),
      homes: homeIdList, 
    },
    label: {
      idToLabelMap,
    },
    diagram: {
      staticDiagram: dg.diagram_combine(diagram0, ...homeDiagrams, labelDiagram, ...homeGroupBg),
      idToDiagramMap,
    },
    home: {
      config: homeConfig,
      position: element.config.homePosition,
    },
    styleProfiles,
    svgElement,
    functions: {
      callbacks,
    },
    voiceover: {
      idToVoiceoverMap: generateVoiceoverDataMapNumberlineDnD(element)
    },
    config: {
      isAllowGrouping: false,
      isUsingReusableDraggable,
      dndIsFilledMode: getIsfilledMode(element, element.elements, blockVersion)
    },
    scoring: {
      isAllowMultipleAnswers: false,
      answerSets: [answerSet],
      ...scoringParam
    },
    _showAnswer: {
      isShowAnswer: element._isShowAnswer ?? false,
      activeAnswerSetIndex: 0,
    },
    stateManager: isUsingReusableDraggable ? ReusableDnDStateManager : DnDStateManager,
  };
  setupInteractiveDnD(dg, int, dgIntConfig);
}

function getIsfilledMode(element: IDgIntElementDnDNumberline, targets: IDgIntElementDnDNumberlineElement[], blockVersion: number|undefined|null): DND_ISFILLED_MODE {
  if (!!blockVersion) {
    let isAllowEmptyTarget = false;
    for (let target of targets){
      if (target.isTarget && !target.isOption) { 
        isAllowEmptyTarget = true;
        break;
      }
    }
    return isAllowEmptyTarget ? DND_ISFILLED_MODE.FILL_ANY_TARGET : DND_ISFILLED_MODE.FILL_ALL_TARGET;
  } else if (blockVersion >= 2) {
    if (element.dndIsFilledMode == undefined){ // backward compatibility
      element.dndIsFilledMode = 'auto';
      element._autoDnDIsFilledMode = determineNumberlineDnDIsFilledMode(element.elements);
    }
    return element.dndIsFilledMode == 'auto' ? element._autoDnDIsFilledMode : element.dndIsFilledMode;
  }
}

function getTargetLayoutPositionEm(target: IDgIntElementDnDNumberlineElement) : [number, string] {
  const layoutPosition = target.targetConfig?.layoutPosition ?? LAYOUT_NUMBERLINE_TARGET_POSITION.TOP;
  const arg = target.targetConfig?.layoutPositionArgument ?? 2;
  switch (layoutPosition){
    case LAYOUT_NUMBERLINE_TARGET_POSITION.TOP: return [arg, 'bottom-center'];
    case LAYOUT_NUMBERLINE_TARGET_POSITION.BOTTOM: return [-arg, 'top-center'];
    case LAYOUT_NUMBERLINE_TARGET_POSITION.CENTER: return [0, 'center-center'];
  }
}

function calcNegSignSize(svgElement: SVGSVGElement): number {
  // Get computed style to match the font
  const style = getComputedStyle(svgElement);
  const fontSizePx = parseFloat(style.fontSize);
  const fontFamily = style.fontFamily;
  const fontWeight = style.fontWeight;
  const fontStyle = style.fontStyle;

  // Create a temporary span with the same font
  const span = document.createElement("span");
  span.textContent = "−";
  span.style.fontSize = fontSizePx + "px";
  span.style.fontFamily = fontFamily;
  span.style.fontWeight = fontWeight;
  span.style.fontStyle = fontStyle;
  span.style.position = "absolute";
  span.style.visibility = "hidden";
  span.style.whiteSpace = "nowrap";

  document.body.appendChild(span);
  const widthPx = span.getBoundingClientRect().width;
  document.body.removeChild(span);

  // Convert to em
  return widthPx / fontSizePx;
}

const ARROW_HEADSIZE = 0.3;
const TICK_SIZE_EM = 0.3;
const TICK_LABEL_OFFSET_EM = 0.3;
export function generateNumberlineDiagram(
  dg: DgLib, 
  numberline: IDgIntElementNumberline,
  hiddenTicks: number[],
  extraElements: IDgIntElementDnDNumberlineExtraElement[],
  widthEm: number,
  textDiagramGenerator: (text: string) => DgDiagram,
  svgElement: SVGSVGElement,
) : DgDiagram {
  
  const negSize = calcNegSignSize(svgElement);
  console.log('negSize', negSize);
  const numberlineInnerWidth = numberline.max - numberline.min;
  const leftOvershoot = numberline.overshoot.isInPercent ? 
    numberlineInnerWidth * numberline.overshoot.left/100 : numberline.overshoot.left;
  const rightOvershoot = numberline.overshoot.isInPercent ?
    numberlineInnerWidth * numberline.overshoot.right/100 : numberline.overshoot.right;
  
  const leftOriginal = numberline.min - leftOvershoot;
  const rightOriginal = numberline.max + rightOvershoot;
  const numberlineWidth = rightOriginal - leftOriginal;
  const scale = widthEm / numberlineWidth;
  
  const left = leftOriginal * scale;
  const right = rightOriginal * scale;
  const ax = numberline.isDrawArrow ?
    dg.arrow2(dg.V2(left, 0), dg.V2(right, 0), ARROW_HEADSIZE) :
    dg.line(dg.V2(left, 0), dg.V2(right, 0));
  let diagrams = [ax.append_tags(dg.TAG.GRAPH_AXIS).stroke('black').fill('black')];

    
  let majorTicks = [];
  if (numberline.tickStep.isShowMajorTicks || !numberline.isManualTickStep) {
    
    if (numberline.isManualTickStep) {
      let majorTickStep = numberline.tickStep.major;
      if (numberlineWidth/majorTickStep > 30) majorTickStep = numberline.max - numberline.min;
      majorTicks = dg.range_inc(numberline.min, numberline.max, majorTickStep);
    } else {
      let majorTickStep = calcTicksInterval(numberline.min, numberline.max);
      majorTicks = dg.range_inc(numberline.min, numberline.max, majorTickStep);
    }
    majorTicks = majorTicks.filter(x => !hiddenTicks.includes(x));
    
    const majorTicksDiagram = majorTicks.map(xOriginal => {
      const x = xOriginal * scale;
      const xLabel = xOriginal < 0 ? x - negSize/2 : x;
      const line = dg.line(dg.V2(x, -TICK_SIZE_EM), dg.V2(x, TICK_SIZE_EM))
        .opacity(0.5).append_tags(dg.TAG.GRAPH_TICK);
      const label = textDiagramGenerator(xOriginal.toString())
        .append_tags(dg.TAG.GRAPH_TICK_LABEL)
        .move_origin('top-center')
        .position(dg.V2(xLabel, -TICK_SIZE_EM-TICK_LABEL_OFFSET_EM));
      return dg.diagram_combine(line, label);
    })
    const majorTicksDiagramGroup = dg.diagram_combine(...majorTicksDiagram);
    diagrams.push(majorTicksDiagramGroup);
  }
  
  if (numberline.tickStep.isShowMinorTicks && numberline.isManualTickStep) {
    let minorTicks = dg.range_inc(numberline.min, numberline.max, numberline.tickStep.minor);
    minorTicks = minorTicks.filter(x => !majorTicks.includes(x));
    const minorTicksDiagram = minorTicks.map(xOriginal => {
      const x = xOriginal * scale;
      return dg.line(dg.V2(x, -TICK_SIZE_EM), dg.V2(x, TICK_SIZE_EM))
        .opacity(0.5).append_tags(dg.TAG.GRAPH_TICK);
    });
    const minorTicksDiagramGroup = dg.diagram_combine(...minorTicksDiagram);
    diagrams.push(minorTicksDiagramGroup);
  }
  
  if (extraElements != undefined && extraElements.length > 0) {
    const extraElementsDiagrams = extraElements.map(e => generateExtraElement(dg, e, scale, textDiagramGenerator));
    const extraElementsDiagramGroup = dg.diagram_combine(...extraElementsDiagrams);
    diagrams.push(extraElementsDiagramGroup);
  }
  
  const numberlineDiagram = dg.diagram_combine(...diagrams);
  return numberlineDiagram;
}

function generateExtraElement(
  dg: DgLib, element: IDgIntElementDnDNumberlineExtraElement, 
  scale: number,
  textDiagramGenerator: (text: string) => DgDiagram,
) : DgDiagram {
  switch (element.type){
    case DND_NUMBERLINE_EXTRA_ELEMENT_TYPE.LABELED_TICK: {
      const x = element.xpos * scale;
      const line = dg.line(dg.V2(x, -TICK_SIZE_EM), dg.V2(x, TICK_SIZE_EM))
        .opacity(0.5).append_tags(dg.TAG.GRAPH_TICK);
      const label = textDiagramGenerator(element.label)
        .append_tags(dg.TAG.GRAPH_TICK_LABEL)
        .move_origin('top-center')
        .position(dg.V2(x, -TICK_SIZE_EM-TICK_LABEL_OFFSET_EM));
      return dg.diagram_combine(line, label);
    }
    default: return dg.empty();
  }
}

function tweakInterval(interval: number) : number {
  if (0.1 < interval && interval < 2) return 1;
  return interval;
}
function calcTicksInterval(min : number, max : number) : number {
    let range = max-min;
    let range_order = Math.floor(Math.log10(range));
    let interval_to_try = [0.1, 0.15, 0.2, 0.5, 1.0].map(x => x*Math.pow(10,range_order));
    let tick_counts = interval_to_try.map(x => Math.floor(range/x));
    // choose the interval so that the number of ticks is between the biggest one but less than 10
    for (let i = 0; i < tick_counts.length; i++) {
        if (tick_counts[i] <= 10) {
            return tweakInterval(interval_to_try[i]);
        }
    }
    return tweakInterval(interval_to_try.slice(-1)[0]);
}


function generateIdOnDnDNumberline(targets : IDgIntElementDnDNumberlineElement[], options : IDgIntElementDnDNumberlineElement[]) : void{
  const idGenerator = new IdGenerator();
  for (let target of targets) target.id = idGenerator.generate('target_');
  for (let option of options) option.id = idGenerator.generate('option_');
}

export function sortNumberlineElement(evaluatex: any, numberline: IDgIntElementDnDNumberline){
  numberline.elements.sort((a, b) => {
    const aPos = evalTexValue(evaluatex, a.positionLabel);
    const bPos = evalTexValue(evaluatex, b.positionLabel);
    return aPos - bPos;
  })
}

function generateVoiceoverDataMapNumberlineDnD(element: IDgIntElementDnDNumberline): Map<string, DgIntVoiceoverData> {
  const map = new Map<string, DgIntVoiceoverData>();
  for (const el of element.elements) {
    if (!el.isOption) continue;
    const id = el.id + ":option";
    const voiceover = el.voiceover;
    if (voiceover && voiceover.url && voiceover.script) {
      const trigger = new Subject<boolean>();
      map.set(id, {url: voiceover.url, trigger});
    }
  }
  return map;
}

function determineNumberlineDnDIsFilledMode(elements: IDgIntElementDnDNumberlineElement[]): DND_ISFILLED_MODE {
  // logic table
  // case | isOtherOptionsPresent | isEmptyTargetPresent  | mode
  // --------------------------------------------------------------------------------
  // 1    | false                 | false                 | place all draggables
  // 2    | false                 | true                  | palce all draggables
  // 3    | true                  | false                 | fill all targets
  // 4    | true                  | true                  | fill any target
  const isOtherOptionsPresent = elements.some((e) => e.isOption && !e.isTarget);
  const isEmptyTargetPresent = elements.some((e) => !e.isOption && e.isTarget);
  if (!isOtherOptionsPresent) {
    return DND_ISFILLED_MODE.PLACE_ALL_DRAGGABLE;
  } else if (!isEmptyTargetPresent) {
    return DND_ISFILLED_MODE.FILL_ALL_TARGET;
  } else {
    return DND_ISFILLED_MODE.FILL_ANY_TARGET;
  }
}
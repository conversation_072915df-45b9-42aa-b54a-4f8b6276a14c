import { IDgIntConstructionElement, DgIntConstructionType } from "../common"
import { LAYOUT_HOME_POSITION, DND_UNUSED_ID, isUnusedId, generateDnDBlock, TAG_DND_DRAGGABLE, TAG_DND_TARGET, IDgIntHomeConfig, HOME_CONFIG_LAYOUT, resolveDnDHomeConfig, IDgIntDnDConfig, unusedId, generateHomeTargetDiagramsWithLabel, groupByHome, setupInteractiveDnD, DnDStateManager, ReusableDnDStateManager, getDnDElementBaseId, isConfigIndividualHome, appendHomeSuffix, DND_ISFILLED_MODE, generateHomeGroupBg, DgIntVoiceoverData } from "./dnd-common";
import type { DgLib, DgInt, DgDiagram } from "src/assets/lib/diagramatics/config";
import { formattedStringToBBCode, multilineTextWithSize, flattenArray, IdGenerator, IDgIntStyle, getStyle, getPxFontSize, getFontFamily, IDgIntScoringParam } from "./common";
import { IContentElementImage } from "../../element-render-image/model";
import { DgIntImageManager } from "../image-manager";
import { DgStyleProfile, getStyleParam } from "../style-profile";
import { cloneDeep } from 'lodash';
import { Subject } from "rxjs";
import { DgIntCallbacks } from "../renderer";

const EM_LINE_HEIGHT = 1.3;
const EM_DND_ELEMENT_PADDING = 0.5;

enum BOX_LAYOUT {
  HORIZONTAL = 'horizontal',
  VERTICAL = 'vertical',
  GRID = 'grid',
}

export interface IDgIntElementDnDTallyTarget {
  label : string;
  targetValue : number;
  isShowLabel? : boolean;
  id? : string;
}
export interface IDgIntElementDnDTallyOption {
  label : string;
  value : number;
  id? : string;
  image? : IContentElementImage;
  _cachedImageSize? : {width: number, height: number};
  voiceover?: {url?: string, script?: string};
}

export interface IDgIntElementDnDTally extends IDgIntConstructionElement {
  constructionType: DgIntConstructionType.DND_TALLY;
  targets : IDgIntElementDnDTallyTarget[];
  options : IDgIntElementDnDTallyOption[];
  homeConfig: IDgIntHomeConfig;
  config: {
    isUseImage: boolean;
    isUsingReusableDraggable: boolean;
    boxLayout: BOX_LAYOUT;
    boxLayoutParam?: number;
    boxMinSize: number;
    homeMaxWidth: number,
    homePosition : LAYOUT_HOME_POSITION;
    padding : [number, number];
    draggableBgColor?: string;
    targetBgColor?: string;
  };
}

export function generateDefaultDgIntElementDnDTally(): IDgIntElementDnDTally {
  return {
    constructionType: DgIntConstructionType.DND_TALLY,
    targets : [
      {label: "A", targetValue: 20},
    ],
    options : [
      {label: "$1", value:1 },
      {label: "$2", value:2 },
      {label: "$5", value:5 },
      {label: "$10", value:10 },
      {label: "$20", value:20 },
    ],
    homeConfig: {
      isGroupHome: false,
      layout: HOME_CONFIG_LAYOUT.DEFAULT,
      element: [[]]
    },
    config: {
      isUseImage: true,
      isUsingReusableDraggable: true,
      boxLayout: BOX_LAYOUT.HORIZONTAL,
      boxMinSize: 80,
      homeMaxWidth: 200,
      homePosition: LAYOUT_HOME_POSITION.TOP,
      padding: [0.5,0.75],
      draggableBgColor: '#ffffff',
      targetBgColor: '#e0e0e0',
    }
  }
}

export function resolveDnDTally(element : IDgIntElementDnDTally) : void {
  if (element.homeConfig == undefined) 
    element.homeConfig = {
      isGroupHome: false,
      layout: HOME_CONFIG_LAYOUT.DEFAULT,
      element: [[]]
    }
  
  const [idChangeMap, newOptionIds, _] = regenerateIdOnDnDTally(element);
  const allOptionIds = element.options.map(option => option.id);
  element.homeConfig = resolveDnDHomeConfig(element.homeConfig, idChangeMap, newOptionIds, allOptionIds);
}

function regenerateIdOnDnDTally(element : IDgIntElementDnDTally) : 
  [Map<string,string>, string[], string[]]
{
  let changeMap = new Map<string,string>();
  let newOptionIds = [];
  let newTargetIds = [];
  const idGenerator = new IdGenerator();
  for (let target of element.targets){
    let id = idGenerator.generate('target_');
    if (target.id) changeMap.set(target.id, id); else newTargetIds.push(id);
    target.id = id;
  }
  for (let option of element.options){
    let id = idGenerator.generate('option_');
    if (option.id) changeMap.set(option.id, id); else newOptionIds.push(id);
    option.id = id;
  }
  return [changeMap, newOptionIds, newTargetIds];
}

export function renderDgIntDnDTally(
  element: IDgIntElementDnDTally, svgElement: SVGSVGElement, 
  dg: DgLib, int: DgInt, imageManager: DgIntImageManager, styleProfiles: Array<DgStyleProfile>,
  scoringParam: IDgIntScoringParam, callbacks: DgIntCallbacks
) {
  
  // backward compatibility
  if ((element.targets.length > 0 && element.targets[0].id == undefined) || 
    (element.options.length > 0 && element.options[0].id == undefined) ||
    (element.homeConfig == undefined )
  ){
    element = cloneDeep(element) as IDgIntElementDnDTally;
    resolveDnDTally(element);
  }
  //
  
  const targets = element.targets;
  const draggables = element.options;
  const homeConfig = element.homeConfig;
  const styleParam = getStyleParam(styleProfiles);
  
  const pxFontSize = getPxFontSize(svgElement);
  const fontFamily = getFontFamily(svgElement);
  const multilineText = (s : string, isBBCode = false) => {
    const bbcode = isBBCode ? s : formattedStringToBBCode(s);
    return multilineTextWithSize(bbcode, dg, pxFontSize, fontFamily, EM_LINE_HEIGHT);
  }
  
  // queue the image size calculation
  for (let draggable of draggables){
    const url = draggable.image?.url;
    if (url) { imageManager.queueSizeCalculation(url, draggable) }
  }
  
  const homePosition = element.config.homePosition ?? LAYOUT_HOME_POSITION.BOTTOM;
  const targetBgColor = element.config.targetBgColor;
  const draggableBgColor = element.config.draggableBgColor;
  const homeMaxWidth = element.config.homeMaxWidth * 20/100;
  const isVisibleHome = styleParam.visible_home_target ?? false;
  const style = getStyle(svgElement);
  let padding = element.config.padding;
  
  const options = draggables;
  const idToIndexMap = new Map(options.map((option,i) => [option.id, i]));
  const idToValueMap = new Map(options.map(option => [option.id, option.value]));
  let optionDiagrams = generateOptionDiagrams(dg, options, element.config, style, imageManager, styleProfiles);
  const groupedOptionDiagrams = groupByHome(optionDiagrams, homeConfig.element, idToIndexMap);
  
  let homeIdList: string[];
  const sortedOptionIdList = flattenArray(homeConfig.element);
  if (isConfigIndividualHome(element.homeConfig)){
    homeIdList = sortedOptionIdList.map(id => appendHomeSuffix(id));
  } else {
    homeIdList = homeConfig.element.map((_, i) => unusedId(i));
  }
  const getDiagramUpdate = () => {
    const boxDiagrams = generateBoxDiagrams(dg, int, targets, element.config, style);
    const alignedBoxDiagram = dg.distribute_horizontal_and_align(boxDiagrams, padding[1], 'top');
    
    const [homeDiagrams, labelDiagram] = generateHomeTargetDiagramsWithLabel(
      dg, homePosition, homeConfig,
      groupedOptionDiagrams, alignedBoxDiagram, 
      homeMaxWidth, EM_DND_ELEMENT_PADDING, multilineText, multilineText, imageManager, 0.5);
    const homeGroupBg = !isVisibleHome ? [] : 
      generateHomeGroupBg(dg, homeDiagrams, homeConfig, styleParam, idToIndexMap, EM_DND_ELEMENT_PADDING).map(d => d.fill(targetBgColor));
      
    const idToDiagramMap = new Map<string, DgDiagram>();
    {
      // setup diagram for dnd elements
      // dnd target
      for (let i = 0; i < targets.length; i++) {
        const target = targets[i];
        if (target.label === DND_UNUSED_ID) continue;
        const targetBox = alignedBoxDiagram.children[i].children[1];
        let block = targetBox.fill(targetBgColor).append_tags(TAG_DND_TARGET);
        block = dg.style.applyStyleProfiles(block, styleProfiles);
        idToDiagramMap.set(target.id, block);
      }
      // dnd home
      for (let i in homeDiagrams){
        const homeDiagram = isVisibleHome ? homeDiagrams[i].fill(targetBgColor) : homeDiagrams[i];
        const homeId = homeIdList[i];
        idToDiagramMap.set(homeId, homeDiagram);
      }
    }
    
    return {
      staticDiagram: dg.diagram_combine(alignedBoxDiagram, ...homeDiagrams, labelDiagram, ...homeGroupBg),
      idToDiagramMap
    }
  }
  
  const {staticDiagram, idToDiagramMap} = getDiagramUpdate();
  // dnd draggable
  for (let i in options){
    const optionDiagram = optionDiagrams[i];
    idToDiagramMap.set(options[i].id, optionDiagram);
  }
  
  const idToLabelMap = new Map<string, string>();
  {
    for (let target of targets) idToLabelMap.set(target.id, target.label);
    for (let option of options) idToLabelMap.set(option.id, option.label);
  }
  
  const answerSet = targets.map(target => ({
    targetId: target.id,
    optionIds: [],
    targetValue: target.targetValue,
  }));
  const isUsingReusableDraggable = element.config.isUsingReusableDraggable ?? true;
  const dgIntConfig: IDgIntDnDConfig = {
    idList: {
      options: options.map(o => o.id),
      targets: targets.map(t => t.id),
      homes: homeIdList,
    },
    label: {
      idToLabelMap,
    },
    diagram: {
      staticDiagram,
      idToDiagramMap,
    },
    getDiagramUpdate,
    dndElementConfig: {
      targetMaxCapacity: Infinity,
      globalTargetConfig: {
        type: "flex-row",
        horizontal_alignment: "center",
        padding: padding[0],
        sorting_function: (idA:string, idB:string) => 
          idToValueMap.get(getDnDElementBaseId(idA)) - idToValueMap.get(getDnDElementBaseId(idB))
      }
    },
    home: {
      config: homeConfig,
      position: element.config.homePosition,
    },
    styleProfiles,
    svgElement,
    functions: {
      callbacks,
    },
    voiceover: {
      idToVoiceoverMap: generateVoiceoverDataMapTallyDnD(element)
    },
    config: {
      isAllowGrouping: false,
      isUsingReusableDraggable,
      dndIsFilledMode: DND_ISFILLED_MODE.FILL_ANY_TARGET,
    },
    scoring: {
      isAllowMultipleAnswers: false,
      answerSets: [answerSet],
      idToValueMap,
      ...scoringParam
    },
    _showAnswer: {
      isShowAnswer: element._isShowAnswer ?? false,
      activeAnswerSetIndex: 0,
    },
    stateManager: ReusableTallyDnDStateManager,
  };
  setupInteractiveDnD(dg, int, dgIntConfig);
}

function generateOptionDiagrams(
  dg : DgLib, 
  options : IDgIntElementDnDTallyOption[],
  config : IDgIntElementDnDTally['config'],
  style : IDgIntStyle,
  imageManager : DgIntImageManager,
  styleProfiles: Array<DgStyleProfile>
) : DgDiagram[] {
  const padding = config.padding;
  const multilineText = (s : string, isBBCode = false) => {
    const bbcode = isBBCode ? s : formattedStringToBBCode(s);
    return multilineTextWithSize(bbcode, dg, style.pxFontSize, style.fontFamily, EM_LINE_HEIGHT);
  }
  if (config.isUseImage){
    return options.map(s => {
      const url = s.image?.url;
      if (url){
        const scale = s.image?.scale ?? 25;
        // 100unit -> 20em (1unit = 1/5em)
        const widthEm = scale / 5; 
        const sz = imageManager.getSize(url, widthEm);
        return dg.image(url, sz.width, sz.height);
      }else{
        const value  = s.value ?? 0;
        let source = multilineText(value.toString()).move_origin('center-center')
          .append_tags(TAG_DND_DRAGGABLE);
        const sourceSize = dg.geometry.size(source);
        const bg = generateDnDBlock(
          dg, sourceSize[0] + 2*padding[1], sourceSize[1] + 2*padding[0], 
          styleProfiles, true)
          .append_tags(TAG_DND_DRAGGABLE);
        const combined = dg.diagram_combine(bg.fill(config.draggableBgColor), source);
        return dg.style.applyStyleProfiles(combined, styleProfiles);
      }
    });
  } else {
    return options.map(s =>  {
      let source = multilineText(s.label).move_origin('center-center').append_tags(TAG_DND_DRAGGABLE);
      const sourceSize = dg.geometry.size(source);
      const bg = generateDnDBlock(
        dg, sourceSize[0] + 2*padding[1], sourceSize[1] + 2*padding[0], 
        styleProfiles, true)
        .append_tags(TAG_DND_DRAGGABLE);
      const combined = dg.diagram_combine(bg.fill(config.draggableBgColor), source);
      return dg.style.applyStyleProfiles(combined, styleProfiles);
    });
  }
}

function generateBoxDiagrams(
  dg : DgLib, 
  int : DgInt,
  targets : IDgIntElementDnDTallyTarget[],
  config : IDgIntElementDnDTally['config'],
  style : IDgIntStyle,
) : DgDiagram[] {
  const padding = config.padding;
  const multilineText = (s : string, isBBCode = false) => {
    const bbcode = isBBCode ? s : formattedStringToBBCode(s);
    return multilineTextWithSize(bbcode, dg, style.pxFontSize, style.fontFamily, EM_LINE_HEIGHT);
  }
  return targets.map(target => {
    const labelText = multilineText(target.label);
    const labelTextSize = dg.geometry.size(labelText);
    // TODO: width config
    const boxWidth = Math.max(labelTextSize[0], config.boxMinSize * 20/100) + 2*padding[1];
    const contentHeight = int.dragAndDropHandler?.get_container_content_size(target.id)[1] ?? 0
    const boxHeight = Math.max(9, contentHeight);
    
    const labelRect = dg.rectangle(boxWidth, labelTextSize[1] + 2*padding[0]);
    const label = dg.diagram_combine(labelRect, labelText);
    const box = dg.rectangle(boxWidth, boxHeight);
    return dg.distribute_vertical_and_align([label, box])
  });
}

export enum DG_INT_TALLY_TYPE {
  SINGLE = 'diagram-interactive-tally',
  MULTIPLE = 'diagram-interactive-tally-multi',
}
class ReusableTallyDnDStateManager extends ReusableDnDStateManager {
  constructor(
    public int: DgInt,
    public config: IDgIntDnDConfig,
  ) {
    super(int, config);
  }
  
  getType() : string {
    if (this.config.idList.targets.length === 1) return DG_INT_TALLY_TYPE.SINGLE;
    return DG_INT_TALLY_TYPE.MULTIPLE;
  }
  
  getNormalizedScore() : number {
    const data = this.int.get_dnd_data();
    const idToTargetValueMap = new Map(this.config.scoring.answerSets[0].map(t => [t.targetId, t.targetValue]));
    for (let container of data){
      if (isUnusedId(container.container)) continue;
      if (idToTargetValueMap.get(container.container) !== this.getValueOfContent(container.content)) 
        return 0;
    }
    return 1;
  }
  
  getFormattedResponse() : string {
    switch (this.getType()){
      case DG_INT_TALLY_TYPE.SINGLE: return this.getFormattedResponseSingleBox();
      case DG_INT_TALLY_TYPE.MULTIPLE: return this.getFormattedResponseMultipleBox();
      default: return '';
    }
  }
  
  getFormattedResponseMultipleBox() : string {
    let response = '';
    // containerLabel=value;containerLabel=value;...
    const data = this.int.get_dnd_data();
    for (let container of data){
      if (isUnusedId(container.container)) continue;
      const value = this.getValueOfContent(container.content);
      const containerLabel = this.config.label.idToLabelMap.get(container.container);
      response += `${containerLabel}=${value};`;
    }
    if (response) response = response.slice(0, -1);
    return response;
  }
  
  getFormattedResponseSingleBox() : string {
    const data = this.int.get_dnd_data();
    const container = data[0];
    if (!container) return '';
    const value = this.getValueOfContent(container.content);
    return value.toString();
  }
  
  getValueOfContent(content : string[]) : number {
    let res = 0;
    for (let name of content){
      const optionId = getDnDElementBaseId(name);
      const value = this.config.scoring.idToValueMap.get(optionId);
      if (value) res += value;
    }
    return res;
  }
  
  isFilled() : boolean {
    let state = this.int.get_dnd_data()
      .filter(container => !isUnusedId(container.container));
    // return true if all container is filled
    for (let container of state) {
      if (container.content.length === 0) return false;
    }
    return true;
  }
  
}

function generateVoiceoverDataMapTallyDnD(element: IDgIntElementDnDTally): Map<string, DgIntVoiceoverData> {
  const map = new Map<string, DgIntVoiceoverData>();
  for (const options of element.options) {
    const id = options.id;
    const voiceover = options.voiceover;
    if (voiceover && voiceover.url && voiceover.script) {
      const trigger = new Subject<boolean>();
      map.set(id, {url: voiceover.url, trigger});
    }
  }
  return map;
}

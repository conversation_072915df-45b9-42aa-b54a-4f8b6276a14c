import { IDgIntConstructionElement, DgIntConstructionType } from "../common"
import type { DgLib, DgInt, DgDiagram, DgVector2 } from "src/assets/lib/diagramatics/config";
import { 
  IDgIntElementDnDTarget, DND_UNUSED_ID, LAYOUT_HOME_POSITION, HOME_CONFIG_LAYOUT,
  generateIdOnDnDTargets, resolveDnDTargetOptions, resolveDnDTargetLabelChange,
  IDgIntHomeConfig, IDgIntElementDnDAnswerSet,
  generateDnDHomeConfigFromOrdering, regenerateIdOnDnDTargets, resolveDnDHomeConfig,
  resolveDnDAltAnswers, generateDefaultAnswerSet, groupByHome,
  unusedId,
  isUnusedId,
  generateDnDBlock,
  TAG_DND_DRAGGABLE,
  TAG_DND_TARGET,
  IDgIntStyleConfig,
  resolveDnDStyleConfig,
  generateHomeTargetDiagramsWithLabel,
  IDgIntDnDConfig,
  setupInteractiveDnD,
  getAllOptionIdsFromDnDTargets,
  ReusableDnDStateManager,
  DnDStateManager,
  isConfigIndividualHome,
  appendHomeSuffix,
  resolveDnDTargetOptions2,
  DND_OPTION_TEXT_MODE,
  isDnDTargetsContainMathOption,
  HOME_LABEL_POSITION,
  attachLabelDiagram,
  DND_STYLE_MODE,
  generateElementColorsObject,
  getOptionLabel,
  assembleDraggableBlock,
  resolveTargetAndAnswersForCombinedOptions,
  generatePaddedDiagram,
  DND_ISFILLED_MODE,
  determineDnDIsFilledMode,
  generateHomeGroupBg,
  generateVoiceoverDataMap,
  resolveDnDAnswerGroup,
  getDnDElementBaseId,
} from "./dnd-common";
import { flattenArray, formattedStringToBBCode, multilineTextWithSize, getStyle, generateImageTextDiagram, numberDuplicateString, IDgIntScoringParam, EX_TO_EM, CONTENT_JUSTIFY, } from "./common";
import { IContentElementImage } from "../../element-render-image/model";
import { DgIntImageManager } from "../image-manager";
import { cloneDeep } from 'lodash';
import { DgStyleProfile, getStyleParam } from "../style-profile";
import { tex2dgMathExpression } from "../mathjax";
import { mathExpressionDg } from "../math-expression";
import { DgIntCallbacks } from "../renderer";

const EM_LINE_HEIGHT = 1.3;
const EM_DND_ELEMENT_PADDING = 0.5;

export interface IDgIntElementDnDFreeform extends IDgIntConstructionElement {
  constructionType: DgIntConstructionType.DND_FREEFORM;
  backgroundImage?: {
    image?: IContentElementImage;
    _cachedImageSize?: {width: number, height: number};
    padding?: {
      isEnabled?: boolean;
      top?: number;
      bottom?: number;
      left?: number;
      right?: number;
    }
  },
  label? : {
    text?: string;
    position?: HOME_LABEL_POSITION;
    isMath?: boolean;
  },
  targetData : IDgIntElementDnDFreeTargetData[];
  dndTargets: IDgIntElementDnDTarget[];
  homeConfig: IDgIntHomeConfig;
  styleConfig: IDgIntStyleConfig;
  altAnswers?: IDgIntElementDnDAnswerSet[];
  _activeAnswerIndex?: number;
  _isAlwaysSeparateAnswerSetup?: boolean;
  config: {
    isAutoPadding?: boolean;
    isAllowMultipleAnswer: boolean,
    isAllowGrouping: boolean;
    isUseImage: boolean;
    isUsingAnswerGroup?: boolean;
    isUsingReusableDraggable: boolean;
    isAllowEmptyTarget?: boolean;
    isAllowNonScoringTarget?: boolean;
    homePosition : LAYOUT_HOME_POSITION;
    padding : [number, number];
    optionMaxWidth?: number;
    homeMaxWidth: number;
    draggableBgColor: string;
    targetBgColor: string;
    isFixedOptionWidth: boolean;
    contentJustify?: CONTENT_JUSTIFY,
  };
  _hoveredTargetIndex?: number;
  dndIsFilledMode?: 'auto' | DND_ISFILLED_MODE; // user specified value or auto
  _autoDnDIsFilledMode?: DND_ISFILLED_MODE; // auto calculated value
  //
  ordering? : number[]; //deprecated use `homeConfig` instead
}

interface IDgIntElementDnDFreeTargetData {
  label: string;
  position: {
    x: number,
    y: number
  }
}
export function generateDefaultDgIntElementDnDFreeform(): IDgIntElementDnDFreeform {
  return {
    constructionType: DgIntConstructionType.DND_FREEFORM,
    targetData: [],
    dndTargets: [],
    homeConfig: {
      isGroupHome: false,
      layout: HOME_CONFIG_LAYOUT.DEFAULT,
      element: [[]]
    },
    styleConfig: {
      styleMode: DND_STYLE_MODE.DEFAULT,
    },
    config: {
      isAllowMultipleAnswer: false,
      isAllowGrouping: false,
      isUseImage: false,
      isUsingReusableDraggable: false,
      homePosition: LAYOUT_HOME_POSITION.BOTTOM,
      padding : [0.5, 0.75],
      homeMaxWidth: 200,
      draggableBgColor: '#ffffff',
      targetBgColor: '#e0e0e0',
      isFixedOptionWidth: true,
    },
  };
}

export function resolveDnDFreeformTargets(element : IDgIntElementDnDFreeform) : void {
  const targetNames = numberDuplicateString(element.targetData.map(t => t.label));
  
  // _cachedImageSize used to be [number, number]
  const bgImage = element.backgroundImage;
  if (Array.isArray(bgImage?._cachedImageSize) && bgImage._cachedImageSize.length == 2){
    bgImage._cachedImageSize = {width: bgImage._cachedImageSize[0], height: bgImage._cachedImageSize[1]};
  }
  
  if (element.homeConfig == undefined) 
    element.homeConfig = generateDnDHomeConfigFromOrdering(element.dndTargets, element.ordering);
  if (element.altAnswers == undefined) element.altAnswers = [];
  if (element.backgroundImage == undefined) element.backgroundImage = {};
  if (element.backgroundImage.padding == undefined) 
    element.backgroundImage.padding = { isEnabled: false, top: 0, bottom: 0, left: 0, right: 0 };
  if (element.label == undefined) element.label = {text: '', position: HOME_LABEL_POSITION.TOP};
  
  const isCombineOptionContainer = element._isAlwaysSeparateAnswerSetup || element.config.isUsingReusableDraggable;
  element.dndTargets = resolveDnDTargetLabelChange(element.dndTargets, targetNames);
  element.dndTargets = resolveDnDTargetOptions2(element.dndTargets, targetNames, element.config.isAllowGrouping || isCombineOptionContainer);
  const [idChangeMap, newOptionIds, newTargetIds] = regenerateIdOnDnDTargets(element.dndTargets);
  const allOptionIds = getAllOptionIdsFromDnDTargets(element.dndTargets);
  element.homeConfig = resolveDnDHomeConfig(element.homeConfig, idChangeMap, newOptionIds, allOptionIds);
  
  const homeCount = element.homeConfig.element.length;
  const targetIds = element.dndTargets.map(t => t.id);
  const optionIds = flattenArray(element.dndTargets.map(t => t.content.map(c => c.id)));
  const homeIds = Array.from({length: homeCount}, (_, i) => unusedId(i));
  element.styleConfig = resolveDnDStyleConfig(
    element.styleConfig, idChangeMap, newOptionIds, newTargetIds,
    targetIds, optionIds, homeIds
  );
  
  element.altAnswers = resolveDnDAltAnswers(element.altAnswers, idChangeMap, element.dndTargets);
  resolveTargetAndAnswersForCombinedOptions(isCombineOptionContainer, element.dndTargets, element.altAnswers);
  resolveDnDAnswerGroup(element.config.isUsingAnswerGroup, element.dndTargets, element.homeConfig, isCombineOptionContainer, element.altAnswers);
  
  if (element.dndIsFilledMode == undefined) { element.dndIsFilledMode = 'auto'; }
  if (element.dndIsFilledMode == 'auto') {
    element._autoDnDIsFilledMode = determineDnDIsFilledMode(
      element.dndTargets, element.altAnswers, 
      element.config.isUsingReusableDraggable, isCombineOptionContainer);
  }
}

export function renderDgIntDnDFreeform(
  element: IDgIntElementDnDFreeform, svgElement: SVGSVGElement, 
  dg: DgLib, int: DgInt, imageManager: DgIntImageManager, styleProfiles: Array<DgStyleProfile>,
  scoringParam: IDgIntScoringParam, callbacks: DgIntCallbacks
) {
  // backward compatibility
  if (element.styleConfig == undefined) element.styleConfig = {};
  let dndTargets = element.dndTargets;
  if (dndTargets.length > 0 && dndTargets[0].id == undefined){
    dndTargets = cloneDeep(element.dndTargets) as typeof element.dndTargets;
    generateIdOnDnDTargets(dndTargets);
  }
  let homeConfig = element.homeConfig;
  if (homeConfig == undefined) {
    homeConfig = generateDnDHomeConfigFromOrdering(dndTargets, element.ordering);
  }
  //
  
  let answerSets = []
  if (!element.config.isUsingReusableDraggable && !element._isAlwaysSeparateAnswerSetup)
    answerSets.push(generateDefaultAnswerSet(dndTargets));
  if (element.config.isAllowMultipleAnswer || element.config.isUsingReusableDraggable || element._isAlwaysSeparateAnswerSetup){
    answerSets = answerSets.concat(element.altAnswers);
  }
  const styleParam = getStyleParam(styleProfiles);
  
  // queue the image size calculation
  if (element.config.isUseImage) {
    for (let target of dndTargets){
      for (let c of target.content){
        const url = c.image?.url;
        if (url) {
          imageManager.queueSizeCalculation(url, c);
        }
      }
    }
  }
  
  
  const homeMaxWidth = element.config.homeMaxWidth * 20/100;
  const optionMaxWidth = (element.config.optionMaxWidth  ?? 200 ) * 20 / 100;
  const homePosition = element.config.homePosition ?? LAYOUT_HOME_POSITION.BOTTOM;
  const targetBgColor = element.config.targetBgColor;
  const draggableBgColor = element.config.draggableBgColor;
  const isShowAnswer = element._isShowAnswer ?? false;
  const padding = element.config.padding ?? [0.5, 0.75];
  const isUseDraggableBackground = true;
  const hoveredTargetIndex = element._hoveredTargetIndex ?? NaN;
  const isVisibleHome = styleParam.visible_home_target ?? false;
  const isFixedElementWidth = Boolean(element.config.optionMaxWidth) && element.config.isFixedOptionWidth;
  const contentJustify = element.config.contentJustify ?? CONTENT_JUSTIFY.CENTER;
  const [elementColors, borderColors] = generateElementColorsObject(element.styleConfig, homeConfig)
  
  const canvasStyle = getStyle(svgElement);
  const multilineText = (s : string, maxWidth?: number, justification: CONTENT_JUSTIFY = CONTENT_JUSTIFY.CENTER) => {
    const bbcode = formattedStringToBBCode(s);
    return multilineTextWithSize(bbcode, dg, canvasStyle.pxFontSize, canvasStyle.fontFamily, EM_LINE_HEIGHT, maxWidth, justification);
  }
  const mathStyleConfig = {svgFontSize: canvasStyle.pxFontSize, mathFontFamily: styleParam.math_font_family};
  const mathExpression = (s: string) => mathExpressionDg(dg, s, mathStyleConfig);
  
  const options = flattenArray(dndTargets.map(t => t.content));
  const idToIndexMap = new Map(options.map((option,i) => [option.id, i]));
  const extraHomeWidth = isDnDTargetsContainMathOption(dndTargets) ? 1 : 0;
  
  let optionInnerDiagrams : DgDiagram[] = options.map((s) => {
    let textDiagramGenerator = (s: string) => multilineText(s, optionMaxWidth, contentJustify);
    if (s.textMode == DND_OPTION_TEXT_MODE.MATH) textDiagramGenerator = mathExpression;
    return generateImageTextDiagram(
      dg, imageManager, s.value, s.image,
      textDiagramGenerator, s.imageLabelPosition, padding);
  });
  const optionsBB = optionInnerDiagrams.map(o => o.bounding_box());
  const targetPositions = element.targetData.map(t => dg.V2(t.position.x, t.position.y));
  
  const choicesMaxwidth  = Math.max(...optionsBB.map(bb => bb[1].x - bb[0].x)) + 2*padding[1];
  const choicesMaxheight = Math.max(...optionsBB.map(bb => bb[1].y - bb[0].y)) + 2*padding[0];
  const dndBlockWidth = isFixedElementWidth ? optionMaxWidth : choicesMaxwidth;
  const dndBlock: DgDiagram = generateDnDBlock(dg, dndBlockWidth, choicesMaxheight, styleProfiles).fill('white');
  const optionDiagrams = optionInnerDiagrams.map((o,i) => {
    const id = options[i].id;
    const source = o.move_origin('center-center').append_tags(TAG_DND_DRAGGABLE);
    let bg = dndBlock.position(source.origin).append_tags(TAG_DND_DRAGGABLE)
      .fill(draggableBgColor);
    if (elementColors[id]) bg = bg.fill(elementColors[id]);
    if (element.styleConfig.isUseCustomBorderColors){
      bg = bg.stroke(element.styleConfig.commonDraggableBorderColor);
      if (borderColors[id]) bg = bg.stroke(borderColors[id]);
    }
    const combined = assembleDraggableBlock(dg, source, bg, padding, contentJustify);
    return dg.style.applyStyleProfiles(combined, styleProfiles);
  });
  const groupedOptionDiagrams = groupByHome(optionDiagrams, homeConfig.element, idToIndexMap);
  
  let bgImage = getBackgroundImage(dg, imageManager, element);
  
  {
    // apply paddings
    if (element.config.isAutoPadding){
      const autoPad = calcAutoPad(dg, bgImage, targetPositions, dndBlock);
      bgImage = generatePaddedDiagram(dg, bgImage, autoPad, 1);
    }
    if (element.backgroundImage?.padding?.isEnabled){
      const paddingConfig = element.backgroundImage.padding;
      bgImage = generatePaddedDiagram(dg, bgImage, paddingConfig);
    }
  }
  
  let diagram = bgImage;
  if (element.label?.text){
    const textDiagramGenerator = element.label.isMath ? mathExpression : multilineText;
    const labelDiagram = textDiagramGenerator(element.label.text);
    diagram = attachLabelDiagram(dg, diagram, labelDiagram, element.label.position, EM_DND_ELEMENT_PADDING)
  }
  
  const [homeDiagrams, labelDiagram] = generateHomeTargetDiagramsWithLabel(
    dg, homePosition, homeConfig,
    groupedOptionDiagrams, diagram, 
    homeMaxWidth, EM_DND_ELEMENT_PADDING, multilineText, mathExpression, imageManager, extraHomeWidth);
  const homeGroupBg = !isVisibleHome ? [] : 
    generateHomeGroupBg(dg, homeDiagrams, homeConfig, styleParam, idToIndexMap, EM_DND_ELEMENT_PADDING).map(d => d.fill(targetBgColor));
  
  let answerDiagrams: DgDiagram[] = [];
  if (isShowAnswer) {
    for (let i = 0; i < dndTargets.length; i++) {
      let target = dndTargets[i];
      if (target.label === DND_UNUSED_ID) continue;
      const pos = targetPositions[i];
      
      const optionId = target.content[0]?.id;
      const optionIndex = idToIndexMap.get(optionId);
      if (optionIndex == undefined) continue;
      
      const ans = optionDiagrams[optionIndex].position(pos);
      answerDiagrams.push(ans);
    }
  }
  
  let homeIdList: string[];
  const sortedOptionIdList = flattenArray(homeConfig.element);
  if (isConfigIndividualHome(element.homeConfig)){
    homeIdList = sortedOptionIdList.map(id => appendHomeSuffix(id));
  } else {
    homeIdList = homeDiagrams.map((_, i) => unusedId(i));
  }
  const idToDiagramMap = new Map<string, DgDiagram>();
  {
    // setup diagram for dnd elements
    // dnd target
    for (let i = 0; i < dndTargets.length; i++) {
      let target = dndTargets[i];
      if (target.label === DND_UNUSED_ID) continue;
      const pos = targetPositions[i];
      let block = dndBlock.position(pos).fill(targetBgColor).append_tags(TAG_DND_TARGET);
      if (elementColors[target.id]) block = block.fill(elementColors[target.id]);
      if (element.styleConfig.isUseCustomBorderColors){
        block = block.stroke(element.styleConfig.commonTargetBorderColor);
        if (borderColors[target.id]) block = block.stroke(borderColors[target.id]);
      }
      block = dg.style.applyStyleProfiles(block, styleProfiles);
      if (isShowAnswer || hoveredTargetIndex == i) block = block.opacity(0.5);
      idToDiagramMap.set(target.id, block);
    }
    // dnd home
    for (let i in homeDiagrams){
      const homeDiagram = isVisibleHome ? homeDiagrams[i].fill(targetBgColor) : homeDiagrams[i];
      const homeId = homeIdList[i];
      idToDiagramMap.set(homeId, homeDiagram);
    }
    // dnd draggable
    for (let i in options){
      const optionDiagram = optionDiagrams[i];
      idToDiagramMap.set(options[i].id, optionDiagram);
    }
  }
  
  const idToGroupMap = new Map<string,number>();
  dndTargets.forEach((target, i) => {
    idToGroupMap.set(target.id, target.answerGroup ?? 0);
    target.content.forEach(content => {
      idToGroupMap.set(content.id, content.answerGroup ?? 0);
    })
  });
  const moveValidationFunction = (draggableId:string, targetId:string) : boolean =>  {
    if (isUnusedId(targetId)) return true;
    const baseDraggableId = getDnDElementBaseId(draggableId);
    const baseTargetId = getDnDElementBaseId(targetId);
    const draggableGroup = idToGroupMap.get(baseDraggableId);
    const targetGroup = idToGroupMap.get(baseTargetId);
    return draggableGroup == targetGroup;
  }
  
  const idToLabelMap = new Map<string, string>();
  {
    for (let target of dndTargets){
      idToLabelMap.set(target.id, target.label);
      for (let option of target.content){
        idToLabelMap.set(option.id, getOptionLabel(option));
      }
    }
  }
  
  if (element.dndIsFilledMode == undefined){ // backward compatibility
    element.dndIsFilledMode = 'auto';
    element._autoDnDIsFilledMode = determineDnDIsFilledMode(
      element.dndTargets, element.altAnswers, 
      element.config.isUsingReusableDraggable, element._isAlwaysSeparateAnswerSetup || element.config.isUsingReusableDraggable);
  }
  const isUsingReusableDraggable = element.config.isUsingReusableDraggable ?? false;
  const dgIntConfig: IDgIntDnDConfig = {
    idList: {
      options: options.map(o => o.id),
      targets: dndTargets.filter(t => !isUnusedId(t.label)).map(t => t.id),
      homes: homeIdList,
    },
    label: {
      idToLabelMap,
    },
    diagram: {
      staticDiagram:  dg.diagram_combine(diagram, ...homeDiagrams, labelDiagram, ...homeGroupBg),
      idToDiagramMap,
    },
    home: {
      config: homeConfig,
      position: element.config.homePosition,
    },
    styleProfiles,
    svgElement,
    functions: {
      moveValidation: element.config.isUsingAnswerGroup ? moveValidationFunction : undefined,
      callbacks,
    },
    voiceover: {
      idToVoiceoverMap: generateVoiceoverDataMap(dndTargets)
    },
    config: {
      isAllowGrouping: false,
      isUsingReusableDraggable,
      dndIsFilledMode: element.dndIsFilledMode == 'auto' ? element._autoDnDIsFilledMode : element.dndIsFilledMode,
    },
    scoring: {
      isAllowMultipleAnswers: element.config.isAllowMultipleAnswer,
      answerSets,
      nonScoringTargetIds: element.config.isAllowNonScoringTarget ? element.dndTargets.filter(t => t.isNonScoring).map(t => t.id) : [],
      ...scoringParam
    },
    _showAnswer: {
      isShowAnswer: element._isShowAnswer ?? false,
      activeAnswerSetIndex: element._activeAnswerIndex ?? 0,
    },
    stateManager: isUsingReusableDraggable ? ReusableDnDStateManager : DnDStateManager,
  };
  setupInteractiveDnD(dg, int, dgIntConfig);
}

function getBackgroundImage(
  dg: DgLib, imageManager: DgIntImageManager, element: IDgIntElementDnDFreeform
): DgDiagram {
  const background = element.backgroundImage;
  if (!background) return dg.empty();
  
  const imageElement = background.image;
  if (!imageElement) return dg.empty();
  
  // _cachedImageSize used to be [number, number]
  const bgImage = element.backgroundImage;
  if (Array.isArray(bgImage?._cachedImageSize) && bgImage._cachedImageSize.length == 2){
    bgImage._cachedImageSize = {width: bgImage._cachedImageSize[0], height: bgImage._cachedImageSize[1]};
  }
  const url = imageElement.url;
  if (url) imageManager.queueSizeCalculation(url, background);
  
  const ratio = background._cachedImageSize? background._cachedImageSize.width / background._cachedImageSize.height : 1;
  const scale = imageElement.scale;
  // 100unit -> 20em (1unit = 1/5em)
  const widthEm = scale / 5; 
  const sz = imageManager.getSizeOrDefault(url, widthEm, ratio);
  return dg.image(url, sz.width, sz.height);
}

function firefox_calcCTM(svgelem: SVGSVGElement) : DOMMatrix {
    let ctm = svgelem.getScreenCTM() as DOMMatrix;
    let screenWidth  = svgelem.width.baseVal.value;
    let screenHeight = svgelem.height.baseVal.value;
    let viewBox      = svgelem.viewBox.baseVal;
    let scalex       = screenWidth/viewBox.width;
    let scaley       = screenHeight/viewBox.height;
    let scale        = Math.min(scalex, scaley);

    let translateX = (screenWidth/2 ) - (viewBox.width/2  + viewBox.x) * scale;
    let translateY = (screenHeight/2) - (viewBox.height/2 + viewBox.y) * scale;
    return DOMMatrix.fromMatrix(ctm).translate(translateX, translateY).scale(scale);
}

export function clientPos_to_svgPos(clientPos: {x : number, y : number}, svgelem: SVGSVGElement) : 
{x : number, y : number} {
    let CTM : DOMMatrix;
    if (navigator.userAgent.toLowerCase().indexOf('firefox') > -1) {
        CTM = firefox_calcCTM(svgelem);
    } else {
        CTM = svgelem.getScreenCTM() as DOMMatrix;
    }
    return {
        x : (clientPos.x - CTM.e) / CTM.a,
        y : - (clientPos.y - CTM.f) / CTM.d
    }
}

// export function getTargetSize(dg: DgLib, svgElement: SVGSVGElement, element: IDgIntElementDnDFreeform, imageManager: DgIntImageManager) {
//   const canvasStyle = getStyle(svgElement);
//   const padding = element.config.padding;
//   const multilineText = (s : string, isBBCode = false) => {
//     const bbcode = isBBCode ? s : formattedStringToBBCode(s);
//     return multilineTextWithSize(bbcode, dg, canvasStyle.pxFontSize, canvasStyle.fontFamily, EM_LINE_HEIGHT);
//   }
  
//   const unorderedOptions = flattenArray(element.dndTargets.map(t => t.content));
//   const options = element.ordering.map(i => unorderedOptions[i]);
//   let optionInnerDiagrams : DgDiagram[] = options.map((s) => generateImageTextDiagram(
//     dg, imageManager, s.value, s.image,
//     multilineText, s.imageLabelPosition, padding
//   ));
//   const optionsBB = optionInnerDiagrams.map(o => o.bounding_box());
  
//   const choicesMaxwidth  = Math.max(...optionsBB.map(bb => bb[1].x - bb[0].x)) + 2*padding[1];
//   const choicesMaxheight = Math.max(...optionsBB.map(bb => bb[1].y - bb[0].y)) + 2*padding[0];
// }

/**
Calculate automatic padding if there are targets that was positioned outside the image
*/ 
function calcAutoPad(dg: DgLib, diagram: DgDiagram, targetPositions: DgVector2[], dndBlock: DgDiagram): {
  top?: number; bottom?: number; left?: number; right?: number } 
{
  const [diagramWidth, diagramHeight] = dg.geometry.size(diagram);
  const [targetWidth, targetHeight] = dg.geometry.size(dndBlock);
  
  let leftMostPos = 0;
  let rightMostPos = 0;
  let topMostPos = 0;
  let bottomMostPos = 0;
  targetPositions.forEach(pos => {
    const left = pos.x - targetWidth / 2;
    const right = pos.x + targetWidth / 2;
    const top = pos.y + targetHeight / 2;
    const bottom = pos.y - targetHeight / 2;
    leftMostPos = Math.min(leftMostPos, left);
    rightMostPos = Math.max(rightMostPos, right);
    topMostPos = Math.max(topMostPos, top);
    bottomMostPos = Math.min(bottomMostPos, bottom);
  })
  
  let pad: any = {};
  if (leftMostPos < -diagramWidth/2) pad.left = -diagramWidth/2 - leftMostPos;
  if (rightMostPos > diagramWidth/2) pad.right = rightMostPos - diagramWidth/2;
  if (topMostPos > diagramHeight/2) pad.top = topMostPos - diagramHeight/2;
  if (bottomMostPos < -diagramHeight/2) pad.bottom = -diagramHeight/2 - bottomMostPos;
  return pad;
}
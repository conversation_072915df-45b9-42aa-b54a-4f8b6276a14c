import { IDgIntConstructionElement, DgIntConstructionType } from "../common";
import type { DgLib, DgInt, DgDiagram, DgVector2 } from "src/assets/lib/diagramatics/config";
import { 
  IDgIntElementDnDTarget, DND_UNUSED_ID, generateIdOnDnDTargets, 
  LAYOUT_HOME_POSITION, unusedId, HOME_CONFIG_LAYOUT,
  isUnusedId, AnswerGroup, resolveDnDTargetOptions, resolveDnDTargetLabelChange, generateSourceZone,
  IDgIntHomeConfig, IDgIntElementDnDAnswerSet,
  regenerateIdOnDnDTargets, resolveDnDHomeConfig, resolveDnDAltAnswers,
  generateDefaultAnswerSet, generateHomeTargetDiagrams, groupByHome,
  generateDnDBlock,
  TAG_DND_DRAGGABLE,
  TAG_DND_TARGET,
  generateHomeTargetDiagramsWithLabel,
  IDgIntDnDConfig,
  setupInteractiveDnD,
  getAllOptionIdsFromDnDTargets,
  ReusableDnDStateManager,
  DnDStateManager,
  isConfigIndividualHome,
  appendHomeSuffix,
  DND_OPTION_TEXT_MODE,
  IDgIntStyleConfig,
  resolveDnDStyleConfig,
  DND_STYLE_MODE,
  generateElementColorsObject,
  isHomeId,
  getDnDElementBaseId,
  assembleDraggableBlock,
  generateDnDHomeConfigFromOrdering,
  resolveDnDTargetOptions2,
  resolveTargetAndAnswersForCombinedOptions,
  isDnDTargetsContainMathOption,
  generatePaddedDiagram,
  DND_ISFILLED_MODE,
  determineDnDIsFilledMode,
  IDgIntElementDnDOption,
  resolveDnDAnswerGroup,
  AnswerGroupOf,
  generateHomeGroupBg,
  generateVoiceoverDataMap
} from "./dnd-common";
import { getPxFontSize, flattenArray, formattedStringToBBCode, getFontFamily, multilineTextWithSize, formattedStringToBBCodeWithTarget, IDgIntScoringParam, EX_TO_EM, formatBBCodeTaggedPlaceholder, CONTENT_JUSTIFY, getStyle, DgBookmarkData, generateBookmarkButton } from "./common";
import { DgIntImageManager } from "../image-manager";
import { DgStyleProfile, getStyleParam } from "../style-profile";
import { cloneDeep } from 'lodash';
import { tex2dgMathExpression } from "../mathjax";
import { mathExpressionDg } from "../math-expression";
import { DgIntCallbacks } from "../renderer";

const EM_LINE_HEIGHT = 1.3;
const EM_DND_ELEMENT_PADDING = 0.5;
const EM_MAX_LINE_WIDTH = 40;

export enum DND_INLINE_2_CONTENT_TYPE {
  TEXT = 'TEXT',
  TARGET = 'TARGET',
  MATH = 'MATH',
  BOOKMARK_LINK = 'BOOKMARK_LINK',
}
export interface IDgIntDnDInline2ContentElement {
  type: DND_INLINE_2_CONTENT_TYPE;
  value: string;
  // bookmark specific
  bookmarkId?: string;
  targetItemLabel?: string;
}

export interface IDgIntElementDnDInline2 extends IDgIntConstructionElement {
  constructionType: DgIntConstructionType.DND_INLINE_2;
  content: string;
  contentArray?: IDgIntDnDInline2ContentElement[];
  styleConfig: IDgIntStyleConfig;
  dndTargets: IDgIntElementDnDTarget[];
  homeConfig: IDgIntHomeConfig;
  altAnswers?: IDgIntElementDnDAnswerSet[];
  _activeAnswerIndex?: number;
  _isAlwaysSeparateAnswerSetup?: boolean;
  diagramPadding?: {
    isEnabled?: boolean;
    top?: number;
    bottom?: number;
    left?: number;
    right?: number;
  }
  config: {
    isUseArrayEditor?: boolean;
    // isSyncHomeGroupAndAnswerGroup: boolean;
    isAllowMultipleAnswer: boolean;
    isUsingReusableDraggable: boolean;
    isAllowEmptyTarget?: boolean;
    isAllowNonScoringTarget?: boolean;
    homePosition: LAYOUT_HOME_POSITION,
    maxHomeWidth: number,
    padding : [number, number];
    draggableBgColor: string;
    targetBgColor: string;
    isUsingAnswerGroup?: boolean,
    isUniformLineSpacing: boolean,
    contentJustify?: CONTENT_JUSTIFY,
  },
  dndIsFilledMode?: 'auto' | DND_ISFILLED_MODE; // user specified value or auto
  _autoDnDIsFilledMode?: DND_ISFILLED_MODE; // auto calculated value
}

export function generateDefaultDgIntElementDnDInline2(): IDgIntElementDnDInline2 {
  return {
    constructionType: DgIntConstructionType.DND_INLINE_2,
    // content: 'x = [short1] + 2\ny = 3 + [short2]\n \nf(x) = [long1]\ng(x) = [long2]',
    content: 'x = [target1] + 2\ny = 3 + [target2]',
    contentArray: [
      {
        type: DND_INLINE_2_CONTENT_TYPE.TEXT,
        value: 'x = '
      },
      {
        type: DND_INLINE_2_CONTENT_TYPE.TARGET,
        value: 'target1'
      },
      {
        type: DND_INLINE_2_CONTENT_TYPE.TEXT,
        value: ' + 2\ny = 3 + '
      },
      {
        type: DND_INLINE_2_CONTENT_TYPE.TARGET,
        value: 'target2'
      }
    ],
    styleConfig: {
      styleMode: DND_STYLE_MODE.DEFAULT,
    },
    dndTargets: [
      { label : 'target1', content: [{value : 'a'}] },
      { label : 'target2', content: [{value : 'b'}] },
      {
        label : DND_UNUSED_ID,
        content: [{value: 'c'}, {value:'d'}]
      },
      // {
      //   label : DND_UNUSED_ID,
      //   group : 2,
      //   content: [{value: 'x+3'}]
      // }
    ],
    homeConfig: {
      isGroupHome: false,
      layout: HOME_CONFIG_LAYOUT.DEFAULT,
      element: [[]]
    },
    config: {
      isUseArrayEditor: true,
      // isSyncHomeGroupAndAnswerGroup: true,
      isAllowMultipleAnswer: false,
      isUsingReusableDraggable: false,
      padding : [0.05, 0.75],
      maxHomeWidth : 200,
      draggableBgColor: '#ffffff',
      targetBgColor: '#e0e0e0',
      homePosition: LAYOUT_HOME_POSITION.BOTTOM,
      // isUsingAnswerGroup: false,
      isUniformLineSpacing: false,
    }
  }
}

function contentArrayToContent(contentArray: IDgIntDnDInline2ContentElement[]): string {
  let str = '';
  for (let content of contentArray){
    switch (content.type){
      case DND_INLINE_2_CONTENT_TYPE.TEXT: {
        str += content.value;
      } break;
      case DND_INLINE_2_CONTENT_TYPE.TARGET: {
        str += `[${content.value}]`;
      } break;
      case DND_INLINE_2_CONTENT_TYPE.MATH: {
        str += `\\{${content.value}\\}`;
      } break;
      case DND_INLINE_2_CONTENT_TYPE.BOOKMARK_LINK: {
        str += `\\{bookmark:${content.value};;${content.bookmarkId};;${content.targetItemLabel}\\}`;
      } break;
    }
  }
  return str;
}

export function resolveDnDInline2Targets(element : IDgIntElementDnDInline2) : void {
  if (element.config.isUseArrayEditor){
    if (element.contentArray == undefined) element.contentArray = [];
    element.content = contentArrayToContent(element.contentArray);
  }
  
  const regex = /\[(.*?)\]/g;
  const match = element.content.match(regex);
  let tags = match?.map(m => m.slice(1,-1)) ?? [];
  const targetNames = tags;
  
  if (element.homeConfig == undefined) 
    element.homeConfig = generateDnDHomeConfigFromOrdering(element.dndTargets, []);
  if (element.altAnswers == undefined) element.altAnswers = [];
  if (element.diagramPadding == undefined) element.diagramPadding = {left: 0, right: 0, top: 0, bottom: 0};
  
  const isCombineOptionContainer = element._isAlwaysSeparateAnswerSetup || element.config.isUsingReusableDraggable;
  element.dndTargets = resolveDnDTargetLabelChange(element.dndTargets, targetNames);
  element.dndTargets = resolveDnDTargetOptions2(element.dndTargets, targetNames, isCombineOptionContainer);
  const [idChangeMap, newOptionIds, newTargetIds] = regenerateIdOnDnDTargets(element.dndTargets);
  const allOptionIds = getAllOptionIdsFromDnDTargets(element.dndTargets);
  element.homeConfig = resolveDnDHomeConfig(element.homeConfig, idChangeMap, newOptionIds, allOptionIds);
  
  const homeCount = element.homeConfig.element.length;
  const targetIds = element.dndTargets.map(t => t.id);
  const optionIds = flattenArray(element.dndTargets.map(t => t.content.map(c => c.id)));
  const homeIds = Array.from({length: homeCount}, (_, i) => unusedId(i));
  element.styleConfig = resolveDnDStyleConfig(
    element.styleConfig, idChangeMap, newOptionIds, newTargetIds,
    targetIds, optionIds, homeIds
  );
  
  element.altAnswers = resolveDnDAltAnswers(element.altAnswers, idChangeMap, element.dndTargets);
  // if (element.config.isSyncHomeGroupAndAnswerGroup && element.homeConfig.isGroupHome) 
  //   element.homeConfig = syncDnDHomeConfig(element.dndTargets, element.homeConfig);
  resolveTargetAndAnswersForCombinedOptions(isCombineOptionContainer, element.dndTargets, element.altAnswers);
  resolveDnDAnswerGroup(element.config.isUsingAnswerGroup, element.dndTargets, element.homeConfig, isCombineOptionContainer, element.altAnswers);
  
  if (element.dndIsFilledMode == undefined) { element.dndIsFilledMode = 'auto'; }
  if (element.dndIsFilledMode == 'auto') {
    element._autoDnDIsFilledMode = determineDnDIsFilledMode(
      element.dndTargets, element.altAnswers, 
      element.config.isUsingReusableDraggable, isCombineOptionContainer);
  }
}

export function renderDgIntDndInline2(
  element : IDgIntElementDnDInline2, svgElement : SVGSVGElement,
  dg: DgLib, int: DgInt, imageManager: DgIntImageManager, styleProfiles: Array<DgStyleProfile>,
  scoringParam: IDgIntScoringParam, callbacks: DgIntCallbacks
) {
  // backward compatibility
  if (element.styleConfig == undefined) element.styleConfig = {};
  let dndTargets = element.dndTargets;
  if (dndTargets.length > 0 && dndTargets[0].id == undefined){
    dndTargets = cloneDeep(element.dndTargets) as typeof element.dndTargets;
    generateIdOnDnDTargets(dndTargets);
  }
  let homeConfig = element.homeConfig;
  if (homeConfig == undefined) {
    homeConfig = generateDnDHomeConfigFromOrdering(dndTargets, []);
  }
  //
  
  let answerSets = []
  if (!element.config.isUsingReusableDraggable && !element._isAlwaysSeparateAnswerSetup)
    answerSets.push(generateDefaultAnswerSet(dndTargets));
  if (element.config.isAllowMultipleAnswer || element.config.isUsingReusableDraggable || element._isAlwaysSeparateAnswerSetup){
    answerSets = answerSets.concat(element.altAnswers);
  }
  const styleParam = getStyleParam(styleProfiles);
  
  const padding = element.config.padding ?? [0.05, 0.75];
  const homePosition = element.config.homePosition ?? LAYOUT_HOME_POSITION.BOTTOM;
  const homeMaxWidth = (element.config.maxHomeWidth ?? 150) * 20/100;
  const targetBgColor = element.config.targetBgColor;
  const draggableBgColor = element.config.draggableBgColor;
  const isUniformLineSpacing = element.config.isUniformLineSpacing;
  const isVisibleHome = styleParam.visible_home_target ?? false;
  const contentJustify = element.config.contentJustify ?? CONTENT_JUSTIFY.CENTER;
  const [elementColors, borderColors] = generateElementColorsObject(element.styleConfig, homeConfig)
  
  let emLineHeight = EM_LINE_HEIGHT;
  
  const canvasStyle = getStyle(svgElement);
  const multilineText = (
    s : string,  justification: CONTENT_JUSTIFY = CONTENT_JUSTIFY.CENTER,
    isBBCode = false, emLineHeight = EM_LINE_HEIGHT, 
    idToSizeMap?: Map<string, [number,number]>
  ) => {
    const bbcode = isBBCode ? s : formattedStringToBBCode(s);
    return multilineTextWithSize(
      bbcode, dg, canvasStyle.pxFontSize, canvasStyle.fontFamily, emLineHeight, EM_MAX_LINE_WIDTH, justification, idToSizeMap
    );
  }
  const mathStyleConfig = {svgFontSize: canvasStyle.pxFontSize, mathFontFamily: styleParam.math_font_family};
  const mathExpression = (s: string) => mathExpressionDg(dg, s, mathStyleConfig);
  
  const options = flattenArray(dndTargets.map(t => t.content));
  const idToIndexMap = new Map(options.map((option,i) => [option.id, i]));
  const dndTargetsLabelMap = new Map<string, IDgIntElementDnDTarget>();
  for (const target of dndTargets) dndTargetsLabelMap.set(target.label, target);
  const extraHomeWidth = isDnDTargetsContainMathOption(dndTargets) ? 1 : 0;
  
  const optionInnerDiagrams : DgDiagram[] = options.map((s) => {
    switch (s.textMode){
      case DND_OPTION_TEXT_MODE.MATH: return mathExpression(s.value);
      case DND_OPTION_TEXT_MODE.TEXT: 
      default: return multilineText(s.value, contentJustify);
    }
  });
  
  const groupBlockSize = getAnswerGroupBlockSize(options, optionInnerDiagrams, padding);
  const groupDnDBlock = getDnDBlock(dg, groupBlockSize, styleProfiles);
  
  const optionDiagrams: DgDiagram[] = optionInnerDiagrams.map((o,i) => {
    const id = options[i].id;
    const group = options[i].answerGroup ?? 0;
    const dndBlock = groupDnDBlock[group];
    const source = o.move_origin('center-center').append_tags(TAG_DND_DRAGGABLE);
    let bg = dndBlock.position(source.origin).append_tags(TAG_DND_DRAGGABLE)
      .fill(draggableBgColor);
    if (elementColors[id]) bg = bg.fill(elementColors[id]);
    if (element.styleConfig.isUseCustomBorderColors){
      bg = bg.stroke(element.styleConfig.commonDraggableBorderColor);
      if (borderColors[id]) bg = bg.stroke(borderColors[id]);
    }
    const combined = assembleDraggableBlock(dg, source, bg, padding, contentJustify);
    return dg.style.applyStyleProfiles(combined, styleProfiles);
  });
  const groupedOptionDiagrams = groupByHome(optionDiagrams, homeConfig.element, idToIndexMap);
  
  let contentText = element.content;
  let idToSizeMap = new Map<string, [number, number]>();
  let idToObjectDiagramMap = new Map<string, DgDiagram>();
  let bookmarkDataList: DgBookmarkData[] = [];
  // collect objects (math, link, target)
  contentText = handleObjectsAndGetContentText(
    dg, contentText, idToSizeMap, idToObjectDiagramMap, dndTargetsLabelMap, bookmarkDataList, groupBlockSize, padding, 
    multilineText, mathExpression
  );
  
  let bbtext = contentTextToBBCode(contentText);
  bbtext = formatBBCodeTaggedPlaceholder(bbtext, idToSizeMap);
  
  if (isUniformLineSpacing){
    const maxTaggedSize = Math.max(1,...Array.from(idToSizeMap.values()).map(([_,h]) => h));
    emLineHeight = maxTaggedSize + (EM_LINE_HEIGHT-1);
    // emLineHeight = EM_LINE_HEIGHT + 2*padding[0] + (EM_LINE_HEIGHT-1);
  }
  const mm = multilineText(bbtext, CONTENT_JUSTIFY.LEFT, true, emLineHeight, isUniformLineSpacing ? undefined : idToSizeMap);
  const diagram = generatePaddedDiagram(dg, mm, element.diagramPadding);
  const [homeDiagrams, labelDiagram] = generateHomeTargetDiagramsWithLabel(
    dg, homePosition, homeConfig,
    groupedOptionDiagrams, diagram, 
    homeMaxWidth, EM_DND_ELEMENT_PADDING, multilineText, mathExpression, imageManager);
  const homeGroupBg = !isVisibleHome ? [] : 
    generateHomeGroupBg(dg, homeDiagrams, homeConfig, styleParam, idToIndexMap, EM_DND_ELEMENT_PADDING).map(d => d.fill(targetBgColor));
  
  const targetPositionOrigin = mm.get_anchor('top-left');
  const taggedPositionMap = getTaggedPosition(mm, canvasStyle.pxFontSize, canvasStyle.fontFamily, targetPositionOrigin, emLineHeight);
  let positionedObjectDiagrams: DgDiagram[] = [];
  for (const [id, objectDiagram] of idToObjectDiagramMap.entries()) {
      const pos = taggedPositionMap.get(id);
      const vPos = dg.V2(pos.x, pos.y);
      positionedObjectDiagrams.push(objectDiagram.position(vPos));
  }
  
  const bookmarkButtons = bookmarkDataList.map(data => {
    const pos = taggedPositionMap.get(data.id);
    const buttonData = generateBookmarkButton(dg, data, canvasStyle, dg.V2(pos.x, pos.y), callbacks.openBookmarkLink);
    return buttonData;
  });
  
  
  let homeIdList: string[];
  const sortedOptionIdList = flattenArray(homeConfig.element);
  if (isConfigIndividualHome(element.homeConfig)){
    homeIdList = sortedOptionIdList.map(id => appendHomeSuffix(id));
  } else {
    homeIdList = homeDiagrams.map((_, i) => unusedId(i));
  }
  const idToDiagramMap = new Map<string, DgDiagram>();
  {
    // setup diagram for dnd elements
    // dnd target
    const dy = -padding[0] - (EM_LINE_HEIGHT-1)/2;
    for (let i = 0; i < dndTargets.length; i++) {
      const target = dndTargets[i];
      const group = target.answerGroup ?? 0;
      const dndBlock = groupDnDBlock[group];
      if (isUnusedId(target.label)) continue;
      const targetPos = taggedPositionMap.get(target.label);
      if (!targetPos) continue;
      const vTargetPos = dg.V2(targetPos.x, targetPos.y);
      let block = dndBlock.position(vTargetPos).fill(targetBgColor).append_tags(TAG_DND_TARGET);
      if (elementColors[target.id]) block = block.fill(elementColors[target.id]);
      if (element.styleConfig.isUseCustomBorderColors) {
        block = block.stroke(element.styleConfig.commonTargetBorderColor);
        if (borderColors[target.id]) block = block.stroke(borderColors[target.id]);
      }
      block = dg.style.applyStyleProfiles(block, styleProfiles);
      idToDiagramMap.set(target.id, block);
    }
    // dnd home
    for (let i in homeDiagrams){
      const homeDiagram = isVisibleHome ? homeDiagrams[i].fill(targetBgColor) : homeDiagrams[i];
      const homeId = homeIdList[i];
      idToDiagramMap.set(homeId, homeDiagram);
    }
    // dnd draggable
    for (let i in options){
      const optionDiagram = optionDiagrams[i];
      idToDiagramMap.set(options[i].id, optionDiagram);
    }
  }
  
  const idToGroupMap = new Map<string,number>();
  dndTargets.forEach((target, i) => {
    idToGroupMap.set(target.id, target.answerGroup ?? 0);
    target.content.forEach(content => {
      idToGroupMap.set(content.id, content.answerGroup ?? 0);
    })
  });
  const moveValidationFunction = (draggableId:string, targetId:string) : boolean =>  {
    if (isUnusedId(targetId)) return true;
    const baseDraggableId = getDnDElementBaseId(draggableId);
    const baseTargetId = getDnDElementBaseId(targetId);
    const draggableGroup = idToGroupMap.get(baseDraggableId);
    const targetGroup = idToGroupMap.get(baseTargetId);
    return draggableGroup == targetGroup;
  }
  
  const idToLabelMap = new Map<string, string>();
  {
    for (let target of dndTargets){
      idToLabelMap.set(target.id, target.label);
      for (let option of target.content){
        idToLabelMap.set(option.id, option.value);
      }
    }
  }
  
  if (element.dndIsFilledMode == undefined){ // backward compatibility
    element.dndIsFilledMode = 'auto';
    element._autoDnDIsFilledMode = determineDnDIsFilledMode(
      element.dndTargets, element.altAnswers, 
      element.config.isUsingReusableDraggable, element._isAlwaysSeparateAnswerSetup || element.config.isUsingReusableDraggable);
  }
  const isUsingReusableDraggable = element.config.isUsingReusableDraggable ?? false;
  const dgIntConfig: IDgIntDnDConfig = {
    idList: {
      options: options.map(o => o.id),
      targets: dndTargets.filter(t => !isUnusedId(t.label)).map(t => t.id),
      homes: homeIdList, 
    },
    label: {
      idToLabelMap,
    },
    diagram: {
      staticDiagram: dg.diagram_combine(diagram, ...homeDiagrams, labelDiagram, ...positionedObjectDiagrams, ...homeGroupBg),
      idToDiagramMap,
      buttons: bookmarkButtons,
    },
    home: {
      config: homeConfig,
      position: element.config.homePosition,
    },
    styleProfiles,
    svgElement,
    config: {
      isAllowGrouping: false,
      isUsingReusableDraggable,
      dndIsFilledMode: element.dndIsFilledMode == 'auto' ? element._autoDnDIsFilledMode : element.dndIsFilledMode,
    },
    functions: {
      moveValidation: element.config.isUsingAnswerGroup ? moveValidationFunction : undefined,
      callbacks,
    },
    voiceover: {
      idToVoiceoverMap: generateVoiceoverDataMap(dndTargets)
    },
    scoring: {
      isAllowMultipleAnswers: element.config.isAllowMultipleAnswer,
      answerSets,
      nonScoringTargetIds: element.config.isAllowNonScoringTarget ? element.dndTargets.filter(t => t.isNonScoring).map(t => t.id) : [],
      ...scoringParam
    },
    _showAnswer: {
      isShowAnswer: element._isShowAnswer ?? false,
      activeAnswerSetIndex: element._activeAnswerIndex ?? 0,
    },
    stateManager: isUsingReusableDraggable ? ReusableDnDStateManager : DnDStateManager,
  };
  setupInteractiveDnD(dg, int, dgIntConfig);
}

function contentTextToBBCode(contentText: string): string {
  // split on "\{...\}" to make sure the styling is not applied to target labels
  // making sure "test \{target_name\}" is not converted to "test \{target[sub]name[/sub]\}" which is not valid
  let parts = contentText.split(/(\\\{.*?\\\})/);
  for (let i = 0; i < parts.length; i++) {
    if (parts[i].startsWith('\\{') && parts[i].endsWith('\\}')) { continue }
    parts[i] = formattedStringToBBCode(parts[i]);
  }
  return parts.join('');
}

function handleObjectsAndGetContentText(
  dg: DgLib,
  contentText: string, 
  idToSizeMap: Map<string, [number, number]>,
  idToDiagramMap: Map<string, DgDiagram>,
  dndTargetsLabelMap: Map<string, IDgIntElementDnDTarget>,
  bookmarkDataList: DgBookmarkData[],
  groupBlockSize: AnswerGroupOf<[number,number]>,
  padding: [number, number],
  textGenerator: (s: string) => DgDiagram,
  mathExpressionGenerator: (s: string) => DgDiagram,
): string 
{
  
  // collect objects `\{...\}`
  const objectRegex = /\\\{(.*?)\\\}/g;
  const objectMatch = contentText.match(objectRegex);
  const objectInnerText = objectMatch?.map(m => m.slice(2, -2)) ?? [];
  
  const bookmarkStrings = [];
  const mathStrings = [];
  for (const text of objectInnerText) {
    if (text.startsWith('bookmark:')) {
      bookmarkStrings.push(text);
    } else {
      mathStrings.push(text);
    }
  }
  
  // bookmark
  // caption;;bookmarkId;;targetItemLabel
  bookmarkStrings.forEach((str, i) => {
    const id = `bookmark${i}`;
    contentText = contentText.replace(`\\{${str}\\}`, `\\{${id}\\}`);
    const [caption, bookmarkId, targetItemLabel] = str.slice(9).split(';;');
    bookmarkDataList.push({id, caption, bookmarkId, targetItemLabel});
    const size = dg.geometry.size(textGenerator(caption));
    idToSizeMap.set(id, size as [number, number]);
  })
  
  // math 
  const mathTex = mathStrings;
  const mathIds = mathTex.map((_, i) => `math${i}`);
  const mathDiagrams = mathTex.map(tex => mathExpressionGenerator(tex));
  for (let i = 0; i < mathIds.length; i++) {
    contentText = contentText.replace(`\\{${mathTex[i]}\\}`, `\\{${mathIds[i]}\\}`);
    idToDiagramMap.set(mathIds[i], mathDiagrams[i]);
    idToSizeMap.set(mathIds[i], dg.geometry.size(mathDiagrams[i]) as [number,number]);
  }
  
  // target
  const targetRegex = /\[(.*?)\]/g;
  const match = contentText.match(targetRegex);
  const tags = match?.map(m => m.slice(1, -1)) ?? [];
  const matchlen = match?.length ?? 0;
  // replace "[target]" with "\{target\}"
  for (let i = 0; i < matchlen; i++) {
    contentText = contentText.replace(match[i], `\\{${tags[i]}\\}`);
  }
  
  for (let i = 0; i < matchlen; i++) {
    const targetLabel = tags[i];
    const target = dndTargetsLabelMap.get(targetLabel);
    if (!target) continue;
    const group = target.answerGroup ?? 0;
    const dx = groupBlockSize[group][0];
    idToSizeMap.set(target.label, [dx, EM_LINE_HEIGHT + 2*padding[0]]);
  }
  
  return contentText;
}

function getAnswerGroupBlockSize(
  options: IDgIntElementDnDOption[],
  optionInnerDiagrams: DgDiagram[],
  padding: [number, number],
):  AnswerGroupOf<[number,number]>
{
  const groupOptionBBs: {[group: string]: DgVector2[][]} = {};
  optionInnerDiagrams.forEach((diagram, i) => {
    const group = options[i].answerGroup ?? 0;
    const bb = diagram.bounding_box();
    if (!groupOptionBBs[group]) groupOptionBBs[group] = [];
    groupOptionBBs[group].push(bb)
  });
  const groupBlockSize: {[group: string]: [number, number]} = {0: [0, 0]};
  for (let group in groupOptionBBs){
    const bbs = groupOptionBBs[group];
    const maxWidth = Math.max(...bbs.map(bb => bb[1].x - bb[0].x));
    const maxHeight = Math.max(...bbs.map(bb => bb[1].y - bb[0].y));
    groupBlockSize[group] = [maxWidth + 2*padding[1], maxHeight + 2*padding[0]];
  }
  return groupBlockSize;
}
function getDnDBlock(dg: DgLib, groupBlockSize: AnswerGroupOf<[number,number]>, styleProfiles: DgStyleProfile[]): AnswerGroupOf<DgDiagram> {
  const groupDnDBlock = {};
  for (let group in groupBlockSize){
    const [width, height] = groupBlockSize[group];
    const dndBlock = generateDnDBlock(dg, width, height, styleProfiles)
    groupDnDBlock[group] = dndBlock;
  }
  return groupDnDBlock;
}


type TextSpanData = Array<{text:string, style:any}>;
const textMeasureCanvas = document.createElement('canvas');
const textMeasureContext = textMeasureCanvas.getContext('2d');
function getTaggedPosition(
  mm: DgDiagram, pxFontSize: number, fontFamily: string, origin: {x:number, y:number}, emLineHeight: number
) : Map<string, {x:number, y:number}> {
  const textSpanData = mm.children?.[1]?.multilinedata?.content as TextSpanData ?? [];
  let taggedPositionMap = new Map<string, {x:number,y:number}>();
  let x = 0;
  let y = 0;
  let lastDy = 0;
  for (let tspanData of textSpanData) {
    let fontData = `${pxFontSize}px ${fontFamily}`
    if (tspanData?.style?.['font-weight'] == 'bold') fontData = `bold ${fontData}`;
    if (tspanData?.style?.['font-style'] == 'italic') fontData = `italic ${fontData}`;
    textMeasureContext.font = fontData;
    
    if (tspanData.text == '\n') {
      lastDy = parseFloat(tspanData.style?.dy);
      y += lastDy;
      x = 0;
    } else {
      if (tspanData?.style?.tag){
        const dx = parseFloat(tspanData.style?.dx) ?? 0;
        taggedPositionMap.set(tspanData.style.tag, {x: origin.x + x + dx/2, y: origin.y - y - emLineHeight/2});
      }
      let tspanWidth = textMeasureContext.measureText(tspanData.text).width / pxFontSize;
      if (tspanData?.style?.dx) tspanWidth += parseFloat(tspanData.style.dx);
      x += tspanWidth;
    }
  }
  return taggedPositionMap;
}

// function groupByIndexToGroupMap<T>(arr: T[], indexToGroupMap: Map<number, number>) : {[group:number] : T[]} {
//   let result = {};
//   for (let i = 0; i < arr.length; i++){
//     const group = indexToGroupMap.get(i) ?? 1;
//     if (result[group] == undefined) result[group] = [];
//     result[group].push(arr[i]);
//   }
//   return result;
// }

// function generateGroupDnDBlock(
//   dg: DgLib, groupedDraggableSize: { [group: number]: [number, number] },
//   styleProfiles: Array<DgStyleProfile>
// ): { [group: number]: DgDiagram } {
//   let result = {};
//   for (let group in groupedDraggableSize){
//     const [width, height] = groupedDraggableSize[group];
//     const dndBlock = generateDnDBlock(dg, width, height, styleProfiles)
//     result[group] = dndBlock;
//   }
//   return result;
// }

export function contentToContentArray(content: string): IDgIntDnDInline2ContentElement[] {
  const contentArray: IDgIntDnDInline2ContentElement[] = [];
  let currentIndex = 0;

  while (currentIndex < content.length) {
    if (content[currentIndex] === '[') {
      // Finding the closing bracket for a TARGET type
      const closeIndex = content.indexOf(']', currentIndex);
      if (closeIndex !== -1) {
        contentArray.push({
          type: DND_INLINE_2_CONTENT_TYPE.TARGET,
          value: content.substring(currentIndex + 1, closeIndex)
        });
        currentIndex = closeIndex + 1;
      }
    } else if (content.startsWith('\\{', currentIndex)) {
      // Finding the closing curly brace for a MATH or BOOKMARK_LINK type
      const closeIndex = content.indexOf('\\}', currentIndex);
      if (closeIndex !== -1) {
        const value = content.substring(currentIndex + 2, closeIndex);
        if (value.startsWith('bookmark:')) {
          const [caption, bookmarkId, targetItemLabel] = value.slice(9).split(';;');
          contentArray.push({
            type: DND_INLINE_2_CONTENT_TYPE.BOOKMARK_LINK,
            value: caption ?? '',
            bookmarkId: bookmarkId ?? '',
            targetItemLabel: targetItemLabel ?? ''
          });
        } else {
          contentArray.push({
            type: DND_INLINE_2_CONTENT_TYPE.MATH,
            value
          });
        }
        currentIndex = closeIndex + 2;
      }
    } else {
      // Handling TEXT type
      let nextSpecialIndex = content.length;
      const nextTargetIndex = content.indexOf('[', currentIndex);
      const nextMathIndex = content.indexOf('\\{', currentIndex);

      if (nextTargetIndex !== -1) {
        nextSpecialIndex = nextTargetIndex;
      }
      if (nextMathIndex !== -1 && nextMathIndex < nextSpecialIndex) {
        nextSpecialIndex = nextMathIndex;
      }

      contentArray.push({
        type: DND_INLINE_2_CONTENT_TYPE.TEXT,
        value: content.substring(currentIndex, nextSpecialIndex)
      });
      currentIndex = nextSpecialIndex;
    }
  }

  return contentArray;
}
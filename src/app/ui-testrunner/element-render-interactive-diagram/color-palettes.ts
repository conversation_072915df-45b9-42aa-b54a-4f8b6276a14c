export interface IDgColor {
  name: string;
  color: string;
}
export interface IDgColorPalette {
  name: string
  colors: Array<IDgColor>
};

// export const colorPaletteMap: { [id: string]: IDgColorPalette } = {
//   "abed_diploma" : {
//     name: "ABED Diploma",
//     colors: [
//       {name: "White", color: "#FFFFFF"},
//       {name: "Transparent", color: "#00000000"},
//       {name: "Yellow", color: "#F0FAAA"},
//       {name: "Darker Yellow", color: "#D2D232"},
//       {name: "Blue", color: "#C8DCFA"},
//       {name: "Darker Blue", color: "#8CB4FA"},
//       {name: "Grey", color: "#E6E6E6"},
//       {name: "Darker Grey", color: "#C8C8C8"},
//       {name: "Green", color: "#96C86E"},
//       {name: "Darker Green", color: "#5AB414"}
//     ]
//   }
// }

import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class SharedObjectMapService {
  private map: WeakMap<Object, any> = new WeakMap();

  public set(obj: Object, value: any) {
    this.map.set(obj, value);
  }
  
  public update(obj: Object, value: any){
    const objValue = this.map.get(obj) ?? {};
    this.map.set(obj, {...objValue, ...value});
  }

  public get(obj: Object): any {
    return this.map.get(obj);
  }

  public remove(obj: string) {
    this.map.delete(obj);
  }
}

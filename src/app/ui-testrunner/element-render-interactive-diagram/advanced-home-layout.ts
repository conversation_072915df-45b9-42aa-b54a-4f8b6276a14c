import type { DgLib, DgInt, DgDiagram } from "src/assets/lib/diagramatics/config";
import { flattenArray, formattedStringToBBCode, getStyle, IdGenerator, multilineTextWithSize, resizeSVGElementAndDraw } from "./constructions/common";
const EM_LINE_HEIGHT = 1.3;
const TAG_DND_HOME_PLACEHOLDER = 'dnd_home_placeholder';
const TARGET_ID_PREFIX = 'target_';

export enum DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE {
  VERTICAL_CONTAINER = 'vertical_container',
  GRID_CONTAINER = 'grid_container',
  TARGET_ROW = 'target_row',
  TEXT_ROW = 'text_row',
  LEFT_LABEL = 'left_label',
  // CENTER_LABEL = 'center_label',
}

export interface IDgIntAdvancedHomeLayoutElement {
  type: DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE;
}
export interface IDgIntAdvancedHomeLayoutVerticalContainerElement extends IDgIntAdvancedHomeLayoutElement {
  type: DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.VERTICAL_CONTAINER,
  contents: IDgIntAdvancedHomeLayoutElement[];
  verticalGap: number;
}
type GridContainerElement = 
  DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.TARGET_ROW |
  DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.TEXT_ROW |
  DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.LEFT_LABEL
export interface IDgIntAdvancedHomeLayoutGridContainerElement extends IDgIntAdvancedHomeLayoutElement {
  type: DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.GRID_CONTAINER,
  contents: any[];
  // contents: IDgIntAdvancedHomeLayoutElement[];
  verticalGap: number;
  horizontalGap: number;
}

export interface IDgIntAdvancedHomeLayoutLeftLabelElement extends IDgIntAdvancedHomeLayoutElement {
  type: DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.LEFT_LABEL,
  text: string;
}

export interface IDgIntAdvancedHomeLayoutTextRowElement extends IDgIntAdvancedHomeLayoutElement {
  type: DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.TEXT_ROW,
  delimiter: string;
  texts: string[];
  fontSize?: number;
}

export interface IDgIntAdvancedHomeLayoutTargetRowElement extends IDgIntAdvancedHomeLayoutElement {
  type: DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.TARGET_ROW,
  count: number,
  delimiter: string;
}

export const defaultElement: IDgIntAdvancedHomeLayoutGridContainerElement = {
  type: DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.GRID_CONTAINER,
  verticalGap: 0,
  horizontalGap: 0,
  contents: [
  ]
}
// export const testElement: IDgIntAdvancedHomeLayoutGridContainerElement = {
//   type: DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.GRID_CONTAINER,
//   verticalGap: 0,
//   horizontalGap: 0,
//   contents: [
//     {
//       type: DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.LEFT_LABEL,
//       text: '**Order of Dominance of Alleles**'
//     },
//     {
//       type: DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.TARGET_ROW,
//       count: 1,
//       delimiter: '>',
//     },
//     {
//       type: DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.LEFT_LABEL,
//       text: '**Phenotypes**'
//     },
//     {
//       type: DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.TEXT_ROW,
//       texts: [
//         'Black', 'Birchen', 'Wild-type', 'Dominant\nwheaten',
//         'Partridge', 'Speckled', 'Buttercup', 'Recessive\nwheaten'
//       ],
//       delimiter: ''
//     },
//   ],
// }


export function generateAdvancedHomeLayoutDefaultElement(
  type: DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE
): IDgIntAdvancedHomeLayoutElement {
  switch (type) {
    case DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.VERTICAL_CONTAINER:
      return {
        type: DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.VERTICAL_CONTAINER,
        contents: [],
        verticalGap: 0,
      } as IDgIntAdvancedHomeLayoutVerticalContainerElement;
    case DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.GRID_CONTAINER:
      return {
        type: DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.GRID_CONTAINER,
        contents: [],
        verticalGap: 0,
        horizontalGap: 0,
      } as IDgIntAdvancedHomeLayoutGridContainerElement;
    case DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.TARGET_ROW:
      return {
        type: DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.TARGET_ROW,
        count: 1,
        delimiter: '',
      } as IDgIntAdvancedHomeLayoutTargetRowElement;
    case DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.TEXT_ROW:
      return {
        type: DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.TEXT_ROW,
        delimiter: '',
        texts: [
          'text'
        ],
      } as IDgIntAdvancedHomeLayoutTextRowElement;
    case DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.LEFT_LABEL:
      return {
        type: DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.LEFT_LABEL,
        text: '',
      } as IDgIntAdvancedHomeLayoutLeftLabelElement;
    default:
      throw new Error('Unknown element type: ' + type);
  }
}

type DgIntDnDHomeLayoutDiagram = {static: DgDiagram, homes: DgDiagram[][]}
export interface IHomeLayoutGenerationContext {
  targetPlaceholder: DgDiagram;
  textDiagramGenerator: (s: string) => DgDiagram;
}

// this function is used for the preview
// for the actual item, use each construction's render function
export function renderAdvancedHomeDiagramLayout(
  dg: DgLib,  element: IDgIntAdvancedHomeLayoutElement, svgElement: SVGSVGElement
): void {
  const canvasStyle = getStyle(svgElement);
  const multilineText = (s : string, maxWidth?: number) => {
    const bbcode = formattedStringToBBCode(s);
    return multilineTextWithSize(bbcode, dg, canvasStyle.pxFontSize, canvasStyle.fontFamily, EM_LINE_HEIGHT, maxWidth);
  }
  const targetPlaceholder = dg.rectangle(2, 1);
  const ctx: IHomeLayoutGenerationContext = {
    targetPlaceholder,
    textDiagramGenerator: multilineText,
  }
  
  const diagramLayout = generateAdvancedHomeDiagramLayout(dg, element, ctx);
  resizeSVGElementAndDraw(dg, diagramLayout.static, svgElement)
}

export function generateAdvancedHomeDiagramLayout(
  dg: DgLib,  element: IDgIntAdvancedHomeLayoutElement, ctx: IHomeLayoutGenerationContext
): DgIntDnDHomeLayoutDiagram {
  switch (element.type) {
    // case DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.VERTICAL_CONTAINER: {
    //   return generateVerticalContainer(dg, element as IDgIntAdvancedHomeLayoutVerticalContainerElement, ctx);
    // } break;
    case DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.GRID_CONTAINER: {
      return generateGridContainer(dg, element as IDgIntAdvancedHomeLayoutGridContainerElement, ctx);
    } break;
    default:
      throw new Error('Unknown element type');
  }
}

function generateGridContainer(
  dg: DgLib, element: IDgIntAdvancedHomeLayoutGridContainerElement, ctx: IHomeLayoutGenerationContext
): DgIntDnDHomeLayoutDiagram {
  const rows: DgDiagram[][] = [];
  
  let isRenderDelimiter = false;
  for (let content of element.contents) {
    switch (content.type) {
      case DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.TARGET_ROW: {
        if (content.delimiter) isRenderDelimiter = true;
      } break;
      case DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.TEXT_ROW: {
        if (content.delimiter) isRenderDelimiter = true;
      } break;
    }
  }
  
  let homeRowIndex: number[] = [];
  let leftLabelData: { row: number, diagram: DgDiagram }[] = [];
  for (let i = 0; i < element.contents.length; i++) {
    const content = element.contents[i];
    switch (content.type) {
      case DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.TARGET_ROW: {
        homeRowIndex.push(i);
        rows.push(generateTargetRow(
          dg, content as IDgIntAdvancedHomeLayoutTargetRowElement, ctx, isRenderDelimiter,
        ));
      } break;
      case DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.TEXT_ROW: {
        rows.push(generateTextRow(
          dg, content as IDgIntAdvancedHomeLayoutTextRowElement, ctx, isRenderDelimiter
        ));
      } break;
      case DG_INT_ADVANCED_HOME_LAYOUT_ELEMENT_TYPE.LEFT_LABEL: {
        rows.push([dg.rectangle(1, EM_LINE_HEIGHT).opacity(0)]);
        const diagram = generateLeftLabel(dg, content as IDgIntAdvancedHomeLayoutLeftLabelElement, ctx)
        leftLabelData.push({row: i, diagram});
      } break;
    }
  }
  
  const padding = [element.verticalGap, element.horizontalGap];
  if (rows.length == 0) rows.push([]);
  const table = dg.table.table(rows, padding as any)
    .apply_to_tagged_recursive(dg.TAG.TABLE_CELL, c => c.opacity(0));
  const labelDiagrams = leftLabelData.map(({row, diagram}) => {
    const tags = [dg.TAG.TABLE_CELL, dg.TAG.ROW_ + row.toString(), dg.TAG.COL_ + "0"];
    const cell = table.get_tagged_elements(tags)[0];
    return diagram.move_origin('center-left').position(cell.get_anchor('center-left'));
  });
  
  const homeRowGroups: DgDiagram[][] = homeRowIndex.map(i => {
    const tags = [dg.TAG.TABLE_CONTENT, dg.TAG.ROW_ + String(i), TAG_DND_HOME_PLACEHOLDER];
    return table.get_tagged_elements(tags)
  })
  
  return {
    static: dg.diagram_combine(table, ...labelDiagrams),
    homes: homeRowGroups,
  }
  
}

function generateLeftLabel(
  dg: DgLib, element: IDgIntAdvancedHomeLayoutLeftLabelElement, ctx: IHomeLayoutGenerationContext
): DgDiagram {
  const text = element.text;
  return ctx.textDiagramGenerator(text);
}

function generateTextRow(
  dg: DgLib, element: IDgIntAdvancedHomeLayoutTextRowElement, ctx: IHomeLayoutGenerationContext,
  isRenderDelimiter: boolean
): DgDiagram[] {
  const delimiter = element.delimiter;
  
  let texts: string[] = [];
  if (isRenderDelimiter) {
    for (let i = 0; i < element.texts.length; i++) {
      texts.push(element.texts[i]);
      if (i < element.texts.length - 1) texts.push(delimiter);
    }
  } else {
    texts = element.texts;
  }
  const fontSize = element.fontSize;
  return texts.map(text => { 
    const textDiagram = ctx.textDiagramGenerator(text);
    if (!fontSize) return textDiagram;
    return textDiagram.scale(fontSize, undefined).scaletext(fontSize);
  });
}

function generateTargetRow(
  dg: DgLib, element: IDgIntAdvancedHomeLayoutTargetRowElement, ctx: IHomeLayoutGenerationContext,
  isRenderDelimiter: boolean,
): DgDiagram[] {
  const targetPlaceholder = ctx.targetPlaceholder.append_tags(TAG_DND_HOME_PLACEHOLDER);
  const delimiter = element.delimiter;
  const delimiterDg = ctx.textDiagramGenerator(delimiter);
  const count = element.count ?? 1;
  
  let diagrams: DgDiagram[] = [];
  for (let i = 0; i < count; i++) {
    diagrams.push(targetPlaceholder);
    if (isRenderDelimiter && i < count - 1) diagrams.push(delimiterDg);
  }
  
  return diagrams;
}

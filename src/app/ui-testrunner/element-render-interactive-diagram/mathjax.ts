// export function utf8ToBase64(str: string) {
//   const utf8Bytes = new TextEncoder().encode(str);
//   const binaryStr = Array.from(utf8Bytes).map(b => String.fromCharCode(b)).join('');
//   return btoa(binaryStr);
// }

// function removeNonLatin1Chars(str: string): string {
//   return str.replace(/[^\x00-\xFF]/g, '');
// }
export function svgToDataURL(svgElement: SVGSVGElement) {
    var serializer = new XMLSerializer();
    var svgString = serializer.serializeToString(svgElement);
    // var encodedData = btoa(svgString);
    var encodedData = btoa(unescape(encodeURIComponent(svgString)));
    // var encodedData = utf8ToBase64(svgString);
    // var encodedData = btoa(removeNonLatin1Chars(svgString));
    return 'data:image/svg+xml;base64,' + encodedData;
}

export interface IDgIntMathExpression {
  dataURL: string;
  widthEx: number; // for some reason, MathJax returns the dimension in ex units
  heightEx: number;
}

// const mj = mathJaxInit();
export function tex2svg(MathJax: any, tex: string, display = true) : SVGSVGElement {
  try {
    const node = MathJax.tex2svg(tex, {display: display});
    return node.firstChild;
  } catch (error) {
    console.error(error);
    console.log('Error processing tex2svg.')
    const node = MathJax.tex2svg("\\error", {});
    return node.firstChild;
  }
}
export function tex2dgMathExpression(MathJax: any, tex: string, display = true) : IDgIntMathExpression {
  const svg = tex2svg(MathJax, normalizeTex(tex), display);
  return {
    dataURL: svgToDataURL(svg),
    widthEx: svg.width.baseVal.valueInSpecifiedUnits,
    heightEx: svg.height.baseVal.valueInSpecifiedUnits
  }
}

export function normalizeTex(texstr: string) {
  let str = texstr;
  // convert "^a" to "^{a}"
  str = str.replace(/^\^([a-zA-Z0-9])$/, "^{$1}");
  // convert "_a" to "_{a}
  str = str.replace(/^_([a-zA-Z0-9])$/, "_{$1}");
  // convert "^{...}" to "\scriptstyle{...}"
  str = str.replace(/^\^\{(.*)\}$/, "\\scriptstyle{$1}");
  // convert "_{...}" to "\scriptstyle{...}"
  str = str.replace(/^_\{(.*)\}$/, "\\scriptstyle{$1}");
  return str;
}

import { IContentElementInteractiveDiagram } from './model'
import { DgIntConstructionType, IDgIntConstructionElement } from './common'

import { generateDefaultDgIntElementDnDTable } from './constructions/dnd-table'
import { generateDefaultDgIntElementDnDInline } from './constructions/dnd-inline'
import { generateDefaultDgIntElementDnDInline2 } from './constructions/dnd-inline-2'
import { generateDefaultDgIntElementDnDGroup } from './constructions/dnd-group'
import { generateDefaultDgIntElementDnDSorting } from './constructions/dnd-sorting'
import { generateDefaultDgIntElementDnDTally } from './constructions/dnd-tally'
import { generateDefaultDgIntElementDnDNumberline } from './constructions/dnd-numberline'
import { generateDefaultDgIntElementDnDFreeform } from './constructions/dnd-freeform'
import { generateDefaultDgIntElementDnDVenn } from './constructions/dnd-venn'
import { generateDefaultDgIntElementDnDClozeMath } from './constructions/dnd-cloze-math'
import { generateDefaultDgIntElementMcqHotspot } from './constructions/mcq-hotspot'
import { ElementType } from '../models'
import { getCurrentVersion } from 'src/app/ui-item-maker/services/util'

export function generateDefaultElementInteractiveDiagram(elementType : string) : IContentElementInteractiveDiagram {
  const defaultConstructionType = DgIntConstructionType.DND_TABLE;
  return {
    elementType,
    styleProfiles: [],
    constructionType: defaultConstructionType,
    constructionElement: generateDefaultDgIntElement(defaultConstructionType),
    block_version_id: getCurrentVersion(ElementType.INTERACTIVE_DIAGRAM)
  }
}

export function generateDefaultDgIntElement(constructionType : DgIntConstructionType)  : IDgIntConstructionElement {
  switch (constructionType) {
    case DgIntConstructionType.DND_TABLE: return generateDefaultDgIntElementDnDTable();
    case DgIntConstructionType.DND_INLINE: return generateDefaultDgIntElementDnDInline();
    case DgIntConstructionType.DND_INLINE_2: return generateDefaultDgIntElementDnDInline2();
    case DgIntConstructionType.DND_CLOZE_MATH: return generateDefaultDgIntElementDnDClozeMath();
    case DgIntConstructionType.DND_GROUP: return generateDefaultDgIntElementDnDGroup();
    case DgIntConstructionType.DND_SORTING: return generateDefaultDgIntElementDnDSorting();
    case DgIntConstructionType.DND_TALLY: return generateDefaultDgIntElementDnDTally();
    case DgIntConstructionType.DND_NUMBERLINE: return generateDefaultDgIntElementDnDNumberline();
    case DgIntConstructionType.DND_FREEFORM: return generateDefaultDgIntElementDnDFreeform();
    case DgIntConstructionType.DND_VENN: return generateDefaultDgIntElementDnDVenn();
    case DgIntConstructionType.MCQ_HOTSPOT: return generateDefaultDgIntElementMcqHotspot();
    default: throw new Error(`Unknown interactive diagram construction type: ${constructionType}`);
  }
}

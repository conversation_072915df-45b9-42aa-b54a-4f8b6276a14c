import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class DynamicScriptLoaderService {

  public async loadScripts(scripts: {src: string, id: string}[]): Promise<void[]> {
    const promises = scripts.map(script => this.loadScript(script.src, script.id));
    return Promise.all(promises);
  }

  public async loadScript(scriptUrl: string, id: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (document.getElementById(id)) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = scriptUrl;
      script.id = id;
      script.onload = () => {
        resolve();
      }
      script.onerror = () => {
        document.head.removeChild(script);
        reject(`Failed to load the script at url: ${scriptUrl}`);
      }
      document.head.appendChild(script);
    });
  }
}

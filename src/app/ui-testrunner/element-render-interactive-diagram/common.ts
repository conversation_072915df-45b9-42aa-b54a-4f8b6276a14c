export { loadLatestDiagramaticsLibrary, DgLib } from 'src/assets/lib/diagramatics/config';
import { IEntryStateScored, IEntryState } from "../models";

export interface IDgIntConstructionElement {
  constructionType: DgIntConstructionType;
  _isShowAnswer?: boolean;
  isShowAdvancedOptions?: boolean;
}

export enum DgIntConstructionType {
  DND_TABLE = 'dnd_table',
  DND_INLINE_2 = 'dnd_inline_2',
  DND_CLOZE_MATH = 'dnd_cloze_math',
  DND_GROUP = 'dnd_group',
  DND_SORTING = 'dnd_sorting',
  DND_TALLY = 'dnd_tally',
  DND_NUMBERLINE = 'dnd_numberline',
  DND_FREEFORM = 'dnd_freeform',
  DND_VENN = 'dnd_venn',
  MCQ_HOTSPOT = 'mcq_hotspot',
  DND_INLINE = 'dnd_inline', // deprecated, the structure is too different from the other construction types
}

export const disabledConstructionTypes = []

export const DgIntConstructionTypeCaption = {
  [DgIntConstructionType.DND_TABLE]: 'Table Drag and Drop',
  [DgIntConstructionType.DND_INLINE_2]: 'Inline Drag and Drop',
  [DgIntConstructionType.DND_CLOZE_MATH]: 'Math Cloze Drag and Drop',
  [DgIntConstructionType.DND_GROUP]: 'Group Drag and Drop',
  [DgIntConstructionType.DND_SORTING]: 'Sorting Drag and Drop',
  [DgIntConstructionType.DND_TALLY]: 'Tally Drag and Drop',
  [DgIntConstructionType.DND_NUMBERLINE]: 'Numberline Drag and Drop',
  [DgIntConstructionType.DND_FREEFORM]: 'Freeform Drag and Drop',
  [DgIntConstructionType.DND_VENN]: 'Venn Diagram Drag and Drop',
  [DgIntConstructionType.MCQ_HOTSPOT]: 'MCQ Hotspot',
  [DgIntConstructionType.DND_INLINE]: '[Old Version] Inline Drag and Drop',
}

export interface IDgIntState extends IEntryStateScored {
  formattedResponse: string;
};

export interface IDgIntDnDState extends IDgIntState {
  dndState?: any;
}
export interface IDgIntMcqState extends IDgIntState {
  selected: string[];
}

export interface IDgIntStateManager {
    getState(): IDgIntState;
    setState(state: IDgIntState): void;
    publishState(): void;
    interact(): void;
}

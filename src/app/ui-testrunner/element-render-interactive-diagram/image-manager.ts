type objWithImageSizeCache = {
  _cachedImageSize? : {width:number, height:number};
}

export class DgIntImageManager {
  
  onImgLoadCallback : Function | undefined;
  imageSizeMap : Map<string, {width:number, height:number}>;
  
  constructor() {
    this.imageSizeMap = new Map();
  }
  
  setOnImgLoadCallback(callback : Function) {
    this.onImgLoadCallback = callback;
  }
  clear() {
    this.imageSizeMap.clear();
  }
  
  // name and src can be different, but if src is not provided, name will be used
  // obj: container object that has a field _cachedImageSize
  queueSizeCalculation(name : string, obj? : objWithImageSizeCache, src? : string, recalculate : boolean = false) : void {
    this.queueSizeCalculationWithCallback(name, undefined, src, obj, recalculate);
  }
  
  queueSizeCalculationWithCallback(
    name: string, callback?: (img: HTMLImageElement) => void, 
    src? : string, obj? : objWithImageSizeCache, recalculate : boolean = false
  ) : void {
    src = src ?? name;
    if (!recalculate && this.imageSizeMap.has(name)) return;
    
    if (obj && obj._cachedImageSize == undefined) {
      obj._cachedImageSize = {width: 1, height: 1};
    }
    this.imageSizeMap.set(name, {width: obj._cachedImageSize.width, height: obj._cachedImageSize.height});
    
    const img = new Image();
    img.onload = () => {
      if (obj && obj._cachedImageSize) {
        // only call the callback if the size is different from the cached size
        if (obj._cachedImageSize.width == img.width && obj._cachedImageSize.height == img.height) return;
        obj._cachedImageSize.width = img.width;
        obj._cachedImageSize.height = img.height;
      }
      
      this.imageSizeMap.set(name, {width: img.width, height: img.height});
      this.onImgLoadCallback?.();
      if (callback) callback(img);
    };
    img.src = src;
  }
  
  getSize(name : string, width : number) : {width:number, height:number} {
    return this.getSizeOrDefault(name, width, 1);
  }
  
  getSizeOrDefault(name: string, width: number, defaultRatio: number) : {width:number, height:number} {
    const size = this.imageSizeMap.get(name);
    if (size){
      const ratio = size.width / size.height;
      return { width: width, height: width / ratio };
    } else {
      return { width, height: width / defaultRatio };
    }
  }
  
}

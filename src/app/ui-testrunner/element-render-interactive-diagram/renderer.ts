import { DgLib, DgInt, loadDiagramaticsLibrary, DiagramaticsVersion } from "src/assets/lib/diagramatics/config";
import { IContentElementInteractiveDiagram, IDgIntSharedObject } from './model';
import { DgIntConstructionType } from './common';
import type { DgIntImageManager } from './image-manager';
import { DgStyleProfile, getStyleParam, styleProfilesMap } from './style-profile'
import { DynamicScriptLoaderService } from './dynamic-script-loader.service';

import { renderDgIntDndTable, IDgIntElementDnDTable } from './constructions/dnd-table';
import { renderDgIntDndInline, IDgIntElementDnDInline } from './constructions/dnd-inline';
import { renderDgIntDndInline2, IDgIntElementDnDInline2 } from './constructions/dnd-inline-2';
import { renderDgIntDndGroup, IDgIntElementDnDGroup } from './constructions/dnd-group';
import { renderDgIntDndSorting, IDgIntElementDnDSorting } from './constructions/dnd-sorting';
import { renderDgIntDnDTally, IDgIntElementDnDTally } from './constructions/dnd-tally';
import { renderDgIntDnDNumberline, IDgIntElementDnDNumberline } from './constructions/dnd-numberline';
import { renderDgIntDnDFreeform, IDgIntElementDnDFreeform } from './constructions/dnd-freeform';
import { renderDgIntDndVenn, IDgIntElementDnDVenn } from './constructions/dnd-venn';
import { renderDgIntDndClozeMath, IDgIntElementDnDClozeMath } from "./constructions/dnd-cloze-math";
import { renderDgIntMcqHotspot, IDgIntElementMcqHotspot, IDgIntDataUpdateParamMcqHotspot } from "./constructions/mcq-hotspot";
import { USE_MATHJAX } from "./math-expression";
import type { QuestionState } from "../models";
import { displayLoadingText, DgBookmarkData, IDgIntScoringParam } from "./constructions/common";
import { QuestionPubSub } from "../question-runner/pubsub/question-pubsub";
import { StyleprofileService } from "src/app/core/styleprofile.service";
import { isSupportedVersion } from "src/app/ui-item-maker/services/util";
import { Subject } from "rxjs";

const diagramaticsVersionConfig : {[type in DgIntConstructionType] : DiagramaticsVersion}= {
  [DgIntConstructionType.DND_TABLE]      : DiagramaticsVersion.V1_7_3_e0,
  [DgIntConstructionType.DND_INLINE]     : DiagramaticsVersion.V1_7_3_e0,
  [DgIntConstructionType.DND_INLINE_2]   : DiagramaticsVersion.V1_7_3_e0,
  [DgIntConstructionType.DND_CLOZE_MATH] : DiagramaticsVersion.V1_7_3_e0,
  [DgIntConstructionType.DND_GROUP]      : DiagramaticsVersion.V1_7_3_e0,
  [DgIntConstructionType.DND_SORTING]    : DiagramaticsVersion.V1_7_3_e0,
  [DgIntConstructionType.DND_TALLY]      : DiagramaticsVersion.V1_7_3_e0,
  [DgIntConstructionType.DND_NUMBERLINE] : DiagramaticsVersion.V1_7_3_e0,
  [DgIntConstructionType.DND_FREEFORM]   : DiagramaticsVersion.V1_7_3_e0,
  [DgIntConstructionType.DND_VENN]       : DiagramaticsVersion.V1_7_3_e0,
  [DgIntConstructionType.MCQ_HOTSPOT]    : DiagramaticsVersion.V1_7_3_e0,
}
export const advancedHomeLayoutPreviewRendererVersion = DiagramaticsVersion.V1_7_3_e0;

export type DgIntCallbacks = {
  showMessage?: (msg: string) => void,
  registerVoiceoverData: (voiceoverDataList: {url: string, trigger: Subject<any>}[]) => void,
  openBookmarkLink?: (bookmarkData: DgBookmarkData) => void,
}

export async function renderDgIntConstruction(
  element : IContentElementInteractiveDiagram, 
  imageManager : DgIntImageManager,
  svgElement : SVGSVGElement,
  questionState : QuestionState,
  sharedObject?: IDgIntSharedObject,
  questionPubSub?: QuestionPubSub,
  otherStyles?: {[key: string]: any},
  callbacks?: DgIntCallbacks,
) : Promise<DgInt> {
  const dgVersion = diagramaticsVersionConfig[element.constructionElement.constructionType];
  const dgLib = await loadDiagramaticsLibrary(dgVersion) as unknown as DgLib;
  const hidden_div = document.createElement('div');
  const int = new dgLib.Interactive(hidden_div, svgElement, undefined, 'document');
  int.single_int_mode = true;
  int.set_focus_padding(0.2);
  const scoringParam: IDgIntScoringParam = {
    questionState: questionState,
    entryId: element.entryId,
    scoreWeight: element.scoreWeight,
    enableProportionalScoring: element.enableProportionalScoring,
    questionPubSub: questionPubSub,
  }
  
  try {
    
    // initial empty draw (hack to make sure the size is correct)
    dgLib.draw_to_svg_element(svgElement, dgLib.square().stroke('none').fill('none')); 
    const sp = (element.styleProfiles ?? []).map(sp => styleProfilesMap[sp]?.styleProfile);
    sp.push(<DgStyleProfile>{style_param: otherStyles});
    handleCentering(svgElement, sp);
    // this `render` function is needed for imageManager onImgLoadCallback
    const render = async () => {
      // only load evaluatex if it's needed
      if (element.constructionElement.constructionType == DgIntConstructionType.DND_NUMBERLINE && !window['evaluatex']){
        await DynamicScriptLoaderService.prototype.loadScripts([
          {src:"https://d5kn4fwmij1og.cloudfront.net/evaluatex@2.2.0/dist/evaluatex.min.js", id:"evaluatex"}
        ]);
      }
      
      if (USE_MATHJAX){
        const isMathjaxReady = !!(window['MathJax']?.tex2svg);
        if (!isMathjaxReady) {
          displayLoadingText(dgLib, svgElement);
        }
        
        if (!window['MathJax']) {
          window.localStorage.setItem('MathJax-Menu-Settings', '{}');
          window['MathJax'] = {
              loader: {load: ['[tex]/mhchem']},
              tex: {packages: {'[+]': ['mhchem']}},
              startup: {
                pageReady: () => {
                  return window['MathJax'].startup.defaultPageReady();
                }
              },
            };
          await DynamicScriptLoaderService.prototype.loadScripts([
            {src:"https://d5kn4fwmij1og.cloudfront.net/mathjax@3.2.2/es5/tex-svg.js", id:"MathJax"},
          ]);
          await window['MathJax'].startup.promise;
        }
        await waitForCondition(() => !!(window['MathJax']?.tex2svg), 10);
      }
      
      switch (element.constructionElement.constructionType) {
        case DgIntConstructionType.DND_TABLE: {
          const constructionElement = element.constructionElement as IDgIntElementDnDTable;
          renderDgIntDndTable(constructionElement, svgElement,  dgLib, int, imageManager, sp, scoringParam, callbacks); 
        } break;
        case DgIntConstructionType.DND_INLINE: {
          const constructionElement = element.constructionElement as IDgIntElementDnDInline;
          renderDgIntDndInline(constructionElement, svgElement, dgLib, int, imageManager, sp, scoringParam, callbacks);
        } break;
        case DgIntConstructionType.DND_INLINE_2: {
          const constructionElement = element.constructionElement as IDgIntElementDnDInline2;
          renderDgIntDndInline2(constructionElement, svgElement, dgLib, int, imageManager, sp, scoringParam, callbacks);
        } break;
        case DgIntConstructionType.DND_CLOZE_MATH: {
          const constructionElement = element.constructionElement as IDgIntElementDnDClozeMath;
          renderDgIntDndClozeMath(constructionElement, svgElement, dgLib, int, imageManager, sp, scoringParam, callbacks);
        } break;
        case DgIntConstructionType.DND_GROUP: {
          const constructionElement = element.constructionElement as IDgIntElementDnDGroup;
          renderDgIntDndGroup(constructionElement, svgElement, dgLib, int, imageManager, sp, scoringParam, callbacks);
        } break;
        case DgIntConstructionType.DND_SORTING: {
          const constructionElement = element.constructionElement as IDgIntElementDnDSorting;
          const isLabelWide = isSupportedVersion(1, element);
          renderDgIntDndSorting(constructionElement, svgElement, dgLib, int, imageManager, sp, scoringParam, callbacks, isLabelWide);
        } break;
        case DgIntConstructionType.DND_TALLY: {
          const constructionElement = element.constructionElement as IDgIntElementDnDTally;
          renderDgIntDnDTally(constructionElement, svgElement, dgLib, int, imageManager, sp, scoringParam, callbacks); 
        } break;
        case DgIntConstructionType.DND_NUMBERLINE: {
          const constructionElement = element.constructionElement as IDgIntElementDnDNumberline;
          renderDgIntDnDNumberline(constructionElement, svgElement, dgLib, int, imageManager, sp, scoringParam, callbacks); 
        } break;
        case DgIntConstructionType.DND_FREEFORM: {
          const constructionElement = element.constructionElement as IDgIntElementDnDFreeform;
          renderDgIntDnDFreeform(constructionElement, svgElement, dgLib, int, imageManager, sp, scoringParam, callbacks); 
        } break;
        case DgIntConstructionType.DND_VENN: {
          const constructionElement = element.constructionElement as IDgIntElementDnDVenn;
          renderDgIntDndVenn(constructionElement, svgElement, dgLib, int, imageManager, sp, scoringParam, callbacks);
        } break;
        case DgIntConstructionType.MCQ_HOTSPOT: {
          const constructionElement = element.constructionElement as IDgIntElementMcqHotspot;
          const dataChangeParams = sharedObject?.dataChangeParams as IDgIntDataUpdateParamMcqHotspot;
          renderDgIntMcqHotspot(
            constructionElement, svgElement, dgLib, int, imageManager, sp, scoringParam,
            dataChangeParams, sharedObject?.dataChangeCallback as any, callbacks
          );
        } break;
        default: {
          console.error(`Unsupported construction type: ${element.constructionElement.constructionType}`);
        }
      }
    }
    imageManager.setOnImgLoadCallback(() => {
      if (svgElement.isConnected) render();
    });
    await render();
  } catch(e) {
    console.error(e);
  } finally {
    return int;
  }
}

function handleCentering(svgelem: SVGSVGElement, sp: DgStyleProfile[]){
  const styleParam = getStyleParam(sp);
  const svgContainer = svgelem?.parentElement;
  if (svgContainer == null) return;
  if (styleParam.center_diagram){
    svgContainer.style.display = 'flex';
    svgContainer.style.justifyContent = 'center';
  } else {
    svgContainer.style.display = 'block';
    svgContainer.style.justifyContent = 'initial';
  }
}

async function waitForCondition(checkFunction: () => boolean, interval: number) {
    while (!checkFunction()) {
        await new Promise(resolve => setTimeout(resolve, interval));
    }
}

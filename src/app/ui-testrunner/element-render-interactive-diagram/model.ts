import { IContentElement, IScoredResponse } from "src/app/ui-testrunner/models";
import { DgIntConstructionType, IDgIntConstructionElement } from './common'

export interface IDgIntSharedObject {
  svgElement?: SVGSVGElement,
  dataChangeCallback?: CallableFunction,
  dataChangeParams?: any,
  _resetState?: boolean,
}

export const interactiveDiagramEditInfo = {
  editExcludeFields: []
}

export interface IContentElementInteractiveDiagram extends IContentElement, IScoredResponse {
  styleProfiles: Array<string>,
  constructionType: DgIntConstructionType,
  constructionElement: IDgIntConstructionElement,
  _colorPalette?: string,
}

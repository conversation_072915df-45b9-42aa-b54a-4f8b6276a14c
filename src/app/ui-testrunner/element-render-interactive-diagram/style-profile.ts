import { DgIntConstructionType } from './common'
import center_diagram from "./style-profiles/center-diagram.json";
import table_hidden_border from "./style-profiles/table-hidden-border.json";
import table_carroll from "./style-profiles/table-carroll.json";
import table_hide_empty_cell from "./style-profiles/table-hide-empty-cell.json";
import table_dnd_target_fill_cell from "./style-profiles/table-dnd-target-fill-cell.json";
import table_dnd_target_no_cell_padding from "./style-profiles/table-dnd-target-no-cell-padding.json";
import dnd_block_sharp_corner from "./style-profiles/dnd-block-sharp-corner.json";
import dnd_draggable_solid_border from "./style-profiles/dnd-draggable-solid-border.json";
import dnd_draggable_dashed_border from "./style-profiles/dnd-draggable-dashed-border.json";
import dnd_draggable_no_border from "./style-profiles/dnd-draggable-no-border.json";
import dnd_target_solid_border from "./style-profiles/dnd-target-solid-border.json";
import dnd_target_dashed_border from "./style-profiles/dnd-target-dashed-border.json";
import dnd_target_no_border from "./style-profiles/dnd-target-no-border.json";
import dnd_visible_home from "./style-profiles/dnd-visible-home.json";
import drop_shadow from "./style-profiles/drop-shadow.json";
import hotspot_border_only from "./style-profiles/hotspot-border-only.json";
import dnd_sort_box_style from "./style-profiles/dnd-sort-box-style.json";
import table_dnd_merged_cell_style from "./style-profiles/table-dnd-merged-cell-style.json";
import dnd_extend_home_group from "./style-profiles/dnd-extend-home-group.json";


// export type DiagramStyle = typeof dg.default_diagram_style
// export type TextData = typeof dg.default_textdata
// export type Style = {
//     tags: string[],
//     diagram? : Partial<DiagramStyle>,
//     text? : Partial<TextData> & Partial<DiagramStyle>
// }
// export type StyleParams = {[param in STYLE_PARAM]? : boolean}
// export type StyleProfile = {
//     style_param?: StyleParams,
//     style_list?: Style[],
// }
export type DgStyleProfile = {
  style_param?: {[key: string]: any},
  style_list?: Array<{
    tags: string[],
    diagram?: {[key: string]: any},
    text?: {[key: string]: any},
  }>
};

export type DgStyleProfileMap = {[key: string]:{
  styleProfile: DgStyleProfile,
  caption: string,
}};

export const styleProfilesMap : DgStyleProfileMap = {
  'center_diagram': {
    styleProfile: center_diagram,
    caption: 'Center Diagram',
  },
  'table_hidden_border': {
    styleProfile: table_hidden_border,
    caption: 'Hidden Table Border',
  },
  'table_carroll': {
    styleProfile: table_carroll,
    caption: 'Carroll Diagram',
  },
  'table_hide_empty_cell': {
    styleProfile: table_hide_empty_cell,
    caption: 'Hidden Empty Cell',
  },
  'table_dnd_target_fill_cell': {
    styleProfile: table_dnd_target_fill_cell,
    caption: 'DnD Target Fill Cell',
  },
  'table_dnd_target_no_cell_padding': {
    styleProfile: table_dnd_target_no_cell_padding,
    caption: 'DnD Target No Cell Padding',
  },
  'dnd_block_sharp_corner': {
    styleProfile: dnd_block_sharp_corner,
    caption: 'DnD Element Sharp Corner',
  },
  'dnd_draggable_solid_border': {
    styleProfile: dnd_draggable_solid_border,
    caption: 'DnD Draggable Solid Border',
  },
  'dnd_draggable_dashed_border': {
    styleProfile: dnd_draggable_dashed_border,
    caption: 'DnD Draggable Dashed Border',
  },
  'dnd_draggable_no_border': {
    styleProfile: dnd_draggable_no_border,
    caption: 'DnD Draggable No Border',
  },
  'dnd_target_solid_border': {
    styleProfile: dnd_target_solid_border,
    caption: 'DnD Target Solid Border',
  },
  'dnd_target_dashed_border': {
    styleProfile: dnd_target_dashed_border,
    caption: 'DnD Target Dashed Border',
  },
  'dnd_target_no_border': {
    styleProfile: dnd_target_no_border,
    caption: 'DnD Target No Border',
  },
  'visible_home': {
    styleProfile: dnd_visible_home,
    caption: 'Visible Home Target',
  },
  'drop_shadow': {
    styleProfile: drop_shadow,
    caption: 'Drop Shadow',
  },
  'hotspot_border_only': {
    styleProfile: hotspot_border_only,
    caption: 'Hotspot Border Only',
  },
  'dnd_sort_box_style': {
    styleProfile: dnd_sort_box_style,
    caption: 'DnD Sorting Box Style',
  },
  'table_dnd_merged_cell_style': {
    styleProfile: table_dnd_merged_cell_style,
    caption: 'Merged Table Cell Style',
  },
  'dnd_extend_home_group': {
    styleProfile: dnd_extend_home_group,
    caption: 'Extend Home Group',
  },
}

const generalStyle : Array<string> = [
  'center_diagram',
]
const generalDnDStyle : Array<string> = [
  'dnd_block_sharp_corner',
  'dnd_draggable_solid_border',
  'dnd_draggable_dashed_border',
  'dnd_draggable_no_border',
  'dnd_target_solid_border',
  'dnd_target_dashed_border',
  'dnd_target_no_border',
  'visible_home',
  ...generalStyle
  // TODO: bring dnd_extend_home_group here (need to implement the behaviour for all constructions)
]
export const constructionTypeStyle: { [key in DgIntConstructionType]: Array<string> } = {
  [DgIntConstructionType.DND_TABLE]: [
    'table_hidden_border', 'table_carroll', 'table_hide_empty_cell',
    'table_dnd_target_fill_cell', 'table_dnd_target_no_cell_padding',
    'table_dnd_merged_cell_style', 'dnd_extend_home_group',
    ...generalDnDStyle
  ],
  [DgIntConstructionType.DND_INLINE]: [...generalDnDStyle],
  [DgIntConstructionType.DND_INLINE_2]: [...generalDnDStyle],
  [DgIntConstructionType.DND_CLOZE_MATH]: [...generalDnDStyle],
  [DgIntConstructionType.DND_GROUP]: [...generalDnDStyle],
  [DgIntConstructionType.DND_SORTING]: [...generalDnDStyle, 'dnd_sort_box_style'],
  [DgIntConstructionType.DND_TALLY]: [...generalDnDStyle],
  [DgIntConstructionType.DND_NUMBERLINE]: [...generalDnDStyle],
  [DgIntConstructionType.DND_FREEFORM]: [...generalDnDStyle],
  [DgIntConstructionType.DND_VENN]: [...generalDnDStyle],
  [DgIntConstructionType.MCQ_HOTSPOT]: [
    'hotspot_border_only', 'drop_shadow',
    ...generalStyle
  ],
}

export interface DgIntStyleParam {
  center_diagram?: boolean,
  dnd_block_border_radius?: number,
  visible_home_target?: boolean,
  table_dnd_target_fill_cell?: boolean,
  table_dnd_target_no_cell_padding?: boolean,
  table_dnd_merged_cell_style?: boolean,
  drop_shadow?: boolean,
  hotspot_border_only?: boolean,
  dnd_content_alignment?: 'left' | 'center' | 'right',
  math_font_family?: string,
  dnd_sort_outline_target_label?: boolean,
  dnd_extend_home_group?: boolean,
}
export function getStyleParam(arr: Array<DgStyleProfile>): DgIntStyleParam {
  let styleParam: DgIntStyleParam = {};
  for (let i = 0; i < arr.length; i++) {
    if (arr[i]?.style_param)  styleParam = {...styleParam, ...arr[i].style_param};
  }
  return styleParam as DgIntStyleParam;
}

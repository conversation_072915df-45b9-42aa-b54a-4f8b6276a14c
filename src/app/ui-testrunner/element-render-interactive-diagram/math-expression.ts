import type { DgLib, DgInt, DgDiagram, DgVector2 } from "src/assets/lib/diagramatics/config";
import { EX_TO_EM, IDgIntStyle } from "./constructions/common";
import { IDgIntMathExpression, tex2dgMathExpression } from "./mathjax";
import { RenderMathComponent } from "src/app/ui-testrunner/render-math/render-math.component";

// use mathjax if the browser is safari
// https://bubo.vretta.com/vea/project-management/abed/abed-support/-/issues/1287
export const USE_MATHJAX = navigator.userAgent.includes('Safari') && !navigator.userAgent.includes('Chrome');

import katex from 'katex';
import 'katex/contrib/mhchem/mhchem.js';

type mathStyleConfig = {
  svgFontSize: number,
  mathFontFamily?: string,
}

export function mathExpressionDg(dg: DgLib, s : string, style: mathStyleConfig): DgDiagram {
  if (USE_MATHJAX && window['MathJax']) {
    const mathExpressionData = tex2dgMathExpression(window['MathJax'], s);
    
    // -- original code for default mathjax rendering for reference
    // const widthEm = mathExpressionData.widthEx * EX_TO_EM;
    // const heightEm = mathExpressionData.heightEx * EX_TO_EM;
    // const dataURL = mathExpressionData.dataURL;
    // return dg.image(dataURL, widthEm, heightEm);
    
    // -- use katex width and height
    // ensure consistent width and height between mathjax and katex for more consistent layout (important for freeform diagram)
    // caveat: might stretch the rendered expression a bit
    const katexData = getKaTeXElementData(s, style, true);
    return generateMathjaxWithKatexSize(dg, mathExpressionData, katexData);
  } else {
    const data = getKaTeXElementData(s, style, true);
    return dg.foreign_object(data.innerHTML, data.width, data.height, 1/style.svgFontSize);
  }
}

function generateMathjaxWithKatexSize(dg: DgLib, mathjaxExpressionData: IDgIntMathExpression, katexData: {width: number, height: number}) : DgDiagram {
  const mjaxWidth = mathjaxExpressionData.widthEx * EX_TO_EM;
  const mjaxHeight = mathjaxExpressionData.heightEx * EX_TO_EM;
  
  // avoid overflow
  const renderedWidth = Math.min(katexData.width, mjaxWidth);
  const renderedHeight = Math.min(katexData.height, mjaxHeight);
  
  // invisible rect to make the width and height consistent
  const mjaxImg = dg.image(mathjaxExpressionData.dataURL, renderedWidth, renderedHeight);
  const rect = dg.rectangle(katexData.width, katexData.height).opacity(0);
  return dg.diagram_combine(mjaxImg, rect);
}



const hiddenContainer = document.createElement('div');
hiddenContainer.id = 'hidden-container-int-dg-math-expression';
hiddenContainer.style.position = 'absolute';
hiddenContainer.style.left = '-100%';
hiddenContainer.style.top = '-100%';
hiddenContainer.style.visibility = 'hidden';
document.body.appendChild(hiddenContainer);
function getKaTeXElementData(texStr: string, style: mathStyleConfig, displayStyle = false):
  {width: number, height: number, innerHTML: string } 
{
  if (displayStyle) texStr = '\\displaystyle ' + texStr;
  hiddenContainer.innerHTML = '';
  const katexContainer = document.createElement('div');
  hiddenContainer.appendChild(katexContainer);
  katex.render(texStr, katexContainer, {throwOnError: false});
  applyStyle(katexContainer, style);
  const rect = katexContainer.getBoundingClientRect();
  const widthPx = rect.width;
  const heightPx = rect.height;
  // const fontSize = parseFloat(getComputedStyle(katexContainer).fontSize);
  const widthEm = widthPx / style.svgFontSize;
  const heightEm = heightPx / style.svgFontSize;
  hiddenContainer.removeChild(katexContainer)
  return { width: widthEm, height: heightEm, innerHTML: katexContainer.innerHTML };
}

function applyStyle(katexContainer: HTMLElement, style: mathStyleConfig) {
  const fontStyle = {
    'font-size': `${style.svgFontSize}px`,
    'font-family': style.mathFontFamily
  }
  RenderMathComponent.updateFontStyle(katexContainer, fontStyle);
}
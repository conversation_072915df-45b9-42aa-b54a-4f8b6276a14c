import { Component, Input, OnInit } from '@angular/core';
import { IContentElementPassage } from './model';
import { DEFAULT_WIDTH_EM, QuestionState } from '../models';
import { QuestionPubSub } from '../question-runner/pubsub/question-pubsub';
import { StyleprofileService, StylingProcess, processText } from 'src/app/core/styleprofile.service';
import { AuthScopeSettingsService } from 'src/app/ui-item-maker/auth-scope-settings.service';
import { LangService } from 'src/app/core/lang.service';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { BackgroundFillService } from '../background-fill.service';
import { EditSelectionService } from '../edit-selection.service';
import { Subscription } from 'rxjs';

const PARARGRAPH_SPACING = 1;
type ILine = {
  // identSpans:{
  //   str: string,
  //   width: number,
  // }[]
}
type ISegment = {
  // lines:ILine[], 
  str:string,
  isComplete?:boolean,
  lineCount?:number,
  isColumnBreakNext?:boolean
}
@Component({
  selector: 'element-render-passage',
  templateUrl: './element-render-passage.component.html',
  styleUrls: ['./element-render-passage.component.scss']
})
export class ElementRenderPassageComponent implements OnInit {

  @Input() element:IContentElementPassage;
  @Input() isLocked:boolean;
  @Input() isShowSolution:boolean;
  @Input() questionState:QuestionState;
  @Input() changeCounter:number;
  @Input() questionPubSub?: QuestionPubSub;
  isLineNumberItalic = false;
  transforms: StylingProcess[] = undefined;

  constructor(
    private lang:LangService,
    private styleProfile:StyleprofileService,
    private authScope: AuthScopeSettingsService,
    private sanitizer: DomSanitizer,
    public bgFillService: BackgroundFillService,
    public editSelection: EditSelectionService
  ) { }

  styleProfileChangeSub: Subscription;
  ngOnInit() {
    // this.updateRender();
    this.styleProfileChangeSub = this.styleProfile.getStyleProfileChanges().subscribe((hasStyleProfile) => {
      if(hasStyleProfile) {
        this.isLineNumberItalic = 
          this.styleProfile.getStyleProfile()[this.lang.c()]?.renderStyling?.passage?.italicLineNumbers ?? false;
        this.transforms = 
          this.styleProfile.getStyleProfile()[this.lang.c()]?.renderStyling?.passage?.transforms;
        this.isStyleProfileHasChanged = true;
      }
    })
  }

  ngOnDestroy(): void {
    if(this.styleProfileChangeSub) {
      this.styleProfileChangeSub.unsubscribe();
    }
  }

  getImageRefs(){
    const imageRef:Map<number, {url:string, width_em:number, alignment?:string}> = new Map()
    if (this.element.images){
      for (let image of this.element.images){
        const alignment = image.alignment
        const imageEl = image.el?.images?.default?.image || {}; // todo:bad backwards compatibility
        const url = imageEl.url;
        const width_em = DEFAULT_WIDTH_EM * (imageEl.scale /100)
        imageRef.set(+image.id, { url, alignment, width_em })
      }
    }
    return imageRef
  }

  processPStyle(htmlContent: string): string {
    const htmlContentSplit = htmlContent.split('\n')

    return htmlContentSplit.map(line => {
      let paragraphStyle:{tabs:{sizeEm:number}[]};
      line = line.replace(/<pstyle id="(\d+)"\/?>/g, (match:string, id:string) => {
        for (let ps of this.element.paragraphStyles){
          if (+ps.id == +id){
            paragraphStyle = ps;
          }
        }
        return '';
      });
      if (paragraphStyle){
        const lineIndentSpans = line.split('<t/>');
        line = `${ lineIndentSpans.map((str, i) => {
          paragraphStyle.tabs
          const tab = paragraphStyle.tabs[i]
          const widthStyle = (tab && tab.sizeEm) ? ' width:'+tab.sizeEm+'em' : '';
          return `<span style="display:inline-block;${widthStyle}">${str}</span>`
        }).join('') }`
      }

      return line;
    }).join('\n');
  }

  lastTextSnapshot:string;
  segments:{html?:SafeHtml, isComplete?:boolean}[];
  isStyleProfileHasChanged = false;
  renderTextSegments(){
    const getBookmarkTag = (i, body: string, isParagraph?: boolean): string => {
      return `<div class="bookmark id-${isParagraph ? 'paragraph' : 'line'}_${i+1}">${body}</div>`
    }
    const isTwoColumn = this.isTwoColumn();
    const snapshot = this.element._changeCounter+this.element.text
    if (this.lastTextSnapshot != snapshot || this.isStyleProfileHasChanged){
      this.isStyleProfileHasChanged = false;
      this.lastTextSnapshot = snapshot
      let htmlContent = this.element.text || '';
      if (this.transforms) htmlContent = processText(htmlContent, this.transforms);
      const imageRef = this.getImageRefs()
      // use custom small caps
      htmlContent = htmlContent.replace(/<sc\b[^>]*>(.*?)<\/sc>/g, '<span class="small-caps">$1</span>');
      // center
      htmlContent = htmlContent.replace(/<c\b[^>]*>(.*?)<\/c>/g, '<div class="center">$1</div><nonewline/>');
      // apply bookmark tags (and other style profile transforms)
      htmlContent = this.processPStyle(htmlContent);
      htmlContent = this.processBookmark(htmlContent);
      htmlContent = this.styleProfile.processBookmark(htmlContent);
      htmlContent = this.styleProfile.processBoldedMarkup(htmlContent);
      htmlContent = this.styleProfile.processItalicsMarkup(htmlContent);
      htmlContent = this.styleProfile.processTooltip(htmlContent);
      // apply image tags
      htmlContent = htmlContent.replace(/<img id="(\d+)">/g, (match:string, id:string) => {
        const imageData = imageRef.get(+id);
        if (imageData) {
          const alignmentClass = imageData.alignment ? ` align-${imageData.alignment} ` : ''
          let replacement = `<div class="img-container ${alignmentClass}"><img id="${id}" style="width:${imageData.width_em}em" src="${imageData.url}" `;
          replacement += '></div>';
          return replacement;
        }
        return match; // Return the original match if no image data is found
      })
      // split the lines
      
      const segments:ISegment[] = [];
      let currentSegment:{chunks:string[], numLines:number};
      const resetCurrentSegment = (nextNumLines=0) => currentSegment = {chunks:[], numLines:nextNumLines};
      resetCurrentSegment();

      const htmlContentSplit = htmlContent.split('\n')
      let lineCount = 0;
      if (this.element.counterType === 'LINE'){
        for (let i in htmlContentSplit){
          let line = htmlContentSplit[i];
          // strippedLine holds the version of the line without bookmark tags to check for emptyness
          let strippedLine = line.replace(/<div class="bookmark id-[^>]+>/g, '');
          strippedLine = strippedLine.replace(/<\/div>/g, '');
          // bookmarkTag holds the original bookmark tag before it was replaced.
          const bookmarkTag = line.match(/<div class="bookmark id-[^>]+>/g)

          const isLineFilled = ((strippedLine || ' ').trim() != '');
          let isLineSkipped = !(isLineFilled || !this.element.isLineCountSkipBlank);
          line = line.replace(/<skip\/>/g, (match:string, id:string) => {
            isLineSkipped = true
            return '';
          })

          // If the line is empty, an empty space needs to be added to be parsed by the HTML
          if(!isLineFilled && bookmarkTag.length > 0) {
            currentSegment.chunks.push(`${bookmarkTag[0]} </div>`)
          } else {
            currentSegment.chunks.push(line)
          }
          if (!isLineSkipped){
            currentSegment.numLines += 1
            lineCount += 1
          }
          if (isTwoColumn && +i == this.element.columnBreakPos-1){
            const remainder = currentSegment.numLines % this.element.lineCountInterval;
            segments.push({ str: currentSegment.chunks.join('\n'), isComplete:remainder == 0, isColumnBreakNext: true, lineCount })
            resetCurrentSegment(remainder);
          }
          if (currentSegment.numLines >= this.element.lineCountInterval){
            segments.push({ str: currentSegment.chunks.join('\n'), isComplete:true, lineCount })
            resetCurrentSegment();
          }
        }
      }
      else if (this.element.counterType === 'PARAGRAPH'){
        let paragraphCount = 1;
        for (let i in htmlContentSplit){
          let line = htmlContentSplit[i];
          let strippedLine = line.replace(/<div class="bookmark id-[^>]+>/g, '');
          strippedLine = strippedLine.replace(/<\/div>/g, '');

          const isBlankLine = (
            strippedLine.trim() == ''
          )

          if (isTwoColumn && +i == this.element.columnBreakPos-1){
            if (!isBlankLine){
              currentSegment.chunks.push(line);
              segments.push({ str: currentSegment.chunks.join('\n'), isComplete:true, isColumnBreakNext: true, lineCount:paragraphCount })
              resetCurrentSegment();
              
              const nextLine = htmlContentSplit[(+i)+1];
              if (nextLine != undefined){
                let strippedNextLine = nextLine.replace(/<div class="bookmark id-[^>]+>/g, '');
                strippedNextLine = strippedNextLine.replace(/<\/div>/g, '');
                const isNextLineBlank = strippedNextLine.trim() == '';
                if (isNextLineBlank) paragraphCount += 1
              }
            }
            if (isBlankLine && currentSegment.chunks.length > 0){
              segments.push( {str:currentSegment.chunks.join('\n'), isComplete:true, isColumnBreakNext: true, lineCount:paragraphCount} )
              resetCurrentSegment();
              paragraphCount += 1
            }
            if (isBlankLine && currentSegment.chunks.length == 0){
              if (segments[segments.length-1]) segments[segments.length-1].isColumnBreakNext = true
            }
          } else {
            if (!isBlankLine){
              currentSegment.chunks.push(line);
            }
            if (isBlankLine && currentSegment.chunks.length > 0){
              segments.push( {str:currentSegment.chunks.join('\n'), isComplete:true, lineCount:paragraphCount} )
              resetCurrentSegment();
              paragraphCount += 1
            }
          }
        }
        lineCount = paragraphCount;
        if (isTwoColumn){
          for (let i = 0; i < segments.length-1 ; i++){
            if (segments[i].lineCount == segments[i + 1].lineCount) {
              segments[i+1].isComplete = false
            }
          }
        }
      }
      else if (this.element.counterType === 'NONE'){
        if (isTwoColumn){
          segments.push( {str:htmlContentSplit.slice(0, this.element.columnBreakPos).join('\n'), isColumnBreakNext: true} )
          segments.push( {str:htmlContentSplit.slice(this.element.columnBreakPos).join('\n')} )
        } else {
          segments.push( {str:htmlContentSplit.join('\n')} )
        }
      }
      // push whatever is left
      if (currentSegment.chunks.length){
        segments.push( {
          str: currentSegment.chunks.join('\n'),
          isComplete: (this.element.counterType === 'PARAGRAPH'), // if paragraph mode, then whereever the text ends is considered the end of the paragraph (dont need another set of spaces afterwards)
          lineCount,
        })
      }
      this.segments = segments.map((segment, i) =>{
        const {str, isComplete, isColumnBreakNext, lineCount} = segment;
        const strFixnewline = str.replace(/<nonewline\/><\/div>\n?/g,'</div>')
        return {
          html: this.sanitizer.bypassSecurityTrustHtml(strFixnewline),
          isComplete, isColumnBreakNext, lineCount,
        }
      });
      // console.log('this.segments', this.segments)
    }
    return this.segments
  }

  paragraphSpacing(){
    if (this.element.counterType == 'PARAGRAPH'){
      return PARARGRAPH_SPACING;
    }
    return 0
  }

  isCounterAlignRight(){
    return (this.element.counterAlignment == 'right')
  }
  isCounterAlignLeft(){
    return ! this.isCounterAlignRight()
  }

  isLinesMode(){
    return (this.element.counterType == 'LINE')
  }
  isParagraphMode(){
    return (this.element.counterType == 'PARAGRAPH')
  }

  isShowCounter(){
    return (this.element.counterType !== 'NONE') && (+this.element.lineCountInterval > 0);
  }
  
  isTwoColumn(){
    return !!this.element.columnBreakPos;
  }
  
  getLeftTextSegments(textSegments:ISegment[]): ISegment[]{
    const columnBreakIndex = textSegments.findIndex(segment => segment.isColumnBreakNext) + 1
    return textSegments.slice(0, columnBreakIndex)
  }
  getRightTextSegments(textSegments:ISegment[]): ISegment[]{
    const columnBreakIndex = textSegments.findIndex(segment => segment.isColumnBreakNext) + 1
    return textSegments.slice(columnBreakIndex)
  }

  processBookmark(html: string): string {
    const passage = html.split('\n')
    let processedPassage = passage.map((line, i) => {
      return `<bookmark id="line_${i+1}">${this.balanceTags(line)}</bookmark>`;
    });
    return processedPassage.join('\n');
  }

  // we represent a stack. We push every opening tag we see onto the 
  // stack and pop the most recent tag when we see a closing tag
  openTags = []
  balanceTags(line: string) : string {

    const regex = /<\/?bookmark[^>]*>/g;
    const matches = line.match(regex) || [];

    let preLine = this.openTags.join("")

    for (const match of matches) {
      if (match.startsWith('</')) {
        // Closing tag
        if (this.openTags.length === 0) {
          // break if there is a closing tag without any open tags. This is invalid bookmark tag structure
          break;
        }
        this.openTags.pop();
      } else {
        this.openTags.push(match);
      }
    }

    // close any unclosed opening tags
    const postLine = Array.from({ length: this.openTags.length }, (_, index) => `</bookmark>`).join("");


    return preLine + line + postLine

  }

  getPassageContainerBgColor(){
    return this.element.isTransparentBackground ? 'transparent' : this.bgFillService.getFillColor();
  }
  getLineCount(){
    const text = this.element.text || '';
    let lineCount = 1;
    for (let i = 0; i < text.length; i++) {
      if (text[i] == '\n') lineCount++;
    }
    return lineCount;
  }
}

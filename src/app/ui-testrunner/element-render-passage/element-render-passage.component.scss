@import './../element-render/element-render.component.scss';
.two-column-container {
  display: flex;
  align-items: flex-start;
  flex-direction: row;
}
.two-column-container > div {
  width: 50%;
}

.width-fit-content {
  width: fit-content !important;
}
.italic {
  font-style: italic;
}

.passage.is-drop-cap-dropped::first-letter {
  float: left;
  font-size: calc(1.5em * var(--drop-cap-size, 3));
  font-weight: bold;
  line-height: 1em;
  margin-right: 0.1em;
}

.passage.is-drop-cap-in-margin::first-letter {
  float: left;
  font-size: calc(1.5em * var(--drop-cap-size, 3));
  font-weight: bold;
  line-height: 1em;
  margin-right: 0.1em;
  margin-bottom: calc((1em * var(--line-count, 1) / var(--drop-cap-size, 3)) - 1em);
}
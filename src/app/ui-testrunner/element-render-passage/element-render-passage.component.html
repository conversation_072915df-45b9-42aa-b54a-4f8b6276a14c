<div 
    class="passage-container"
    [class.numbers-align-left]="isCounterAlignLeft()"
    [class.numbers-align-right]="isCounterAlignRight()"
    [style.backgroundColor]="getPassageContainerBgColor()"
    [class.is-selected-edit]="editSelection.isSelectedEdit(element, questionPubSub)"
    [style.border]="editSelection.getBorder(element, questionPubSub)"
>
    <div class="passage" [class.is-colored-image]="!bgFillService.isFillWhite()" [class.width-fit-content]="element.isFitContentWidth" [class.is-no-line-height]="element.isNoLineHeight"
      [class.is-drop-cap-dropped]="element.isDropCap && element.dropCapStyle == 'dropped'"
      [class.is-drop-cap-in-margin]="element.isDropCap && element.dropCapStyle == 'in-margin'"
      [style.--drop-cap-size]="element.dropCapSize"
      [style.--line-count]="element.isDropCap && element.dropCapStyle == 'in-margin' ? getLineCount() : ''"
    >
        <!-- todo:OPTIMIZE we should only re-render the text segments when there is a change to the text -->
        <ng-container *ngIf="!isTwoColumn()">
          <div 
              *ngFor="let textSegment of renderTextSegments(); let i = index" 
              style="position:relative"
              [style.margin-bottom.em]="paragraphSpacing()"
          >
              <div [innerHtml]="textSegment.html"></div>
              <div 
                  *ngIf="isShowCounter() && textSegment.isComplete" 
                  class="passage-numbering-placer"
                  [class.is-lines]="isLinesMode()"
                  [class.is-paragraph]="isParagraphMode()"
                  [class.align-left]="isCounterAlignLeft()"
                  [class.align-right]="isCounterAlignRight()"
              >
                  <span [class.italic]="isLineNumberItalic" class="passage-numbering">{{textSegment.lineCount}}</span>
              </div>
          </div>
        </ng-container>
        
        <ng-container *ngIf="isTwoColumn()">
          <div class="two-column-container" *ngIf="renderTextSegments() as textSegments">
            <div class="two-column-left">
              <div 
                  *ngFor="let textSegment of getLeftTextSegments(textSegments); let i = index"
                  style="position:relative"
                  [style.margin-bottom.em]="paragraphSpacing()"
              >
                  <div [innerHtml]="textSegment.html"></div>
                  <div 
                      *ngIf="isShowCounter() && textSegment.isComplete" 
                      class="passage-numbering-placer column-left"
                      [class.is-lines]="isLinesMode()"
                      [class.is-paragraph]="isParagraphMode()"
                      [class.align-left]="isCounterAlignLeft()"
                      [class.align-right]="isCounterAlignRight()"
                  >
                      <span [class.italic]="isLineNumberItalic" class="passage-numbering">{{textSegment.lineCount}}</span>
                  </div>
              </div>
            </div>
            <div class="two-column-right" style="padding-left: 2em;">
              <div 
                  *ngFor="let textSegment of getRightTextSegments(textSegments); let i = index"
                  style="position:relative"
                  [style.margin-bottom.em]="paragraphSpacing()"
              >
                  <div [innerHtml]="textSegment.html"></div>
                  <div 
                      *ngIf="isShowCounter() && textSegment.isComplete" 
                      class="passage-numbering-placer"
                      [class.is-lines]="isLinesMode()"
                      [class.is-paragraph]="isParagraphMode()"
                      [class.align-left]="isCounterAlignLeft()"
                      [class.align-right]="isCounterAlignRight()"
                  >
                      <span [class.italic]="isLineNumberItalic" class="passage-numbering">{{textSegment.lineCount}}</span>
                  </div>
              </div>
            </div>
          </div>
        </ng-container>
    </div>    
</div>

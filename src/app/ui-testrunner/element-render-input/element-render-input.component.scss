.remaining-chars {
    font-size: 0.7em;
    color: #777;
    display: flex;
    flex-direction: row;
    align-items: center;
    &.is-complete {
        color: #ccc;
    }
    &.is-overflow {
        color: #ff0000;
    }
}

.left-pad-stack {
    margin-left: 0.5em;
}

.ratio {
    padding: 0;
}

.input {
    font-family: inherit;
}

.input-fraction {
    width: 4em;
    // font-size: 1em !important;
    text-align:center;
    // border: 3px solid green;
    // box-shadow: 0px 0px 7px green;
    margin-bottom: 0.3em;
    margin-right: 0.3em;
    margin-left: 0.3em;
}

.high-contrast {
    filter: invert(1);
}


.default-text-container {
    position:absolute;
    // top:39px; // toolbar of ckeditor
    top:0em;
    bottom:0em;
    left:0em;
    right:0em;
    pointer-events:none;
    background-color: rgba(0,0,0,0.1);
    color:#696969;
    display:flex;
    // justify-content: center;
    align-items: center;
    padding:1em;
    padding-top:39px;
    &.is-hide-toolbar{
        padding-top:0px;
    }
    &.is-in-fixed-height {
        padding-top:0px;
    }
}

.default-text{
    margin-top: 1em;
    padding-left: 0.2em;
    padding-right: 0.2em;
    // margin-bottom: 0.5em;
    line-height: 1.1em;
}

.keyboard-container {
    // background-color: rgb(169, 169, 243);
    // display: flex;
    flex-direction: column;
    align-items: center;
    .keyboard {
        display:flex;
        flex-direction: row;
        flex-wrap: wrap;
        .character {
            // background-color: rgb(175, 175, 175);
            margin: 0.15em;
            width: 2em;
            font-size:0.6em;
        }
    }
}

#number-keyboard{
    .keyboard{
        flex-wrap: nowrap;
    }
}

mark strong {
    color: inherit !important;
}

.is-compact-input {
  padding: 0 0.2em;
  height: fit-content;
}

.is-no-right-padding-input {
  padding-right: 0;
}


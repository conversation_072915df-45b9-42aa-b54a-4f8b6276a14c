import { IContentElement, IEntryStateScored, INumberTolerance, IScoredResponse, IVoiceover } from "../models";

export interface IContentElementInput extends IContentElement, IScoredResponse {
  acceptEnFrNumericFormats?: boolean;
  algebraGroups?: AlgebraList[][];
  algebraInputMode?: string;
  algebraList?: AlgebraList[];
  alternativeValues?: IContentElementInput[];
  defaultText?: string;
  defaultTextAlignment?: string;
  defaultTextColour?: string;
  fixedHeight?: number;
  isFixedFullHeight?: boolean;
  format: InputFormat;
  formElements?: IContentElement[];
  fracDenominator?: string;
  fracNumerator?: string;
  fracWholeNumber?: string;
  inputNumFormat?: string;
  isAllowEquivalent?: boolean;
  isAnswerNotNecessary?: boolean;
  isDecimalsNotAllowed?: boolean;
  isNegativeSignBlocked?: boolean;
  isDisabled?:boolean; 
  isFixedHeight?: boolean;
  isMixedNumber?: boolean;
  isReductionForced?: boolean;
  isSetHardLimitToZero?: boolean;
  isShowingFrenchKeys?: boolean;
  isShowingNumericKeyboard?: boolean;
  isShowingNumericKeyboardWithDecimal?: boolean;
  isCaseSensitive?: boolean;  
  isShowingWordCount?: boolean;  
  isShowingAdvLayoutPreview?: boolean;  
  isStrictLowestTerms?: boolean;
  isStrictSimplified?: boolean;
  isToolbarAutoHide?: boolean;
  latex?: string;
  latexExtraCasesMultiline?: string;
  mathCharLimit?: number;
  maxCharDenominator?: number;
  maxCharFraction?: number;
  maxCharNumerator?: number;
  maxChars?: number;
  maxRatioChar?: number;
  maxWords?: number;
  minChars?: number;
  minWords?: number;
  allowedWordAnswers?: {val:string, weight?:number}[];
  numberGroups?: NumberList[][];
  numberList?: NumberList[];
  numberListMode?: string;
  prefix?: string;
  ratioTerms?: string[];
  roundingTolerance?: string;
  roundingTolerancePercentageBased?: boolean;
  enableWordOrCharLimit?: boolean;
  selectedLimitType?: string;
  startingLatex?: string;
  suffix?: string;
  tolerances?: INumberTolerance[];
  value?: string;
  width?: number;
  fontFamily?: string;
  isDual?: boolean;
  defaultText_Paper?: string;
  defaultTextAlignment_Paper?: string;
  defaultTextColour_Paper?: string;
  buttonText?: string;
  buttonVoiceOver?: IVoiceover;
  enableSpellCheck?: boolean;
  enableSpellCheckSettings?: boolean;
  numberInputAlign?: 'left' | 'right' | 'center';
  isCompact?: boolean;
  isNoRightPadding?: boolean;
}

  export interface IEntryStateInputNumber extends IEntryStateScored {
    value: string;
  }
  export interface IEntryStateInputMath extends IEntryStateScored {
    latex: string;
  }
  export interface IEntryStateInputText extends IEntryStateScored {
    str: string;
    isPaperFormat: boolean;
  }
  export interface IEntryStateInputFraction extends IEntryStateScored {
    wholenumber: number;
    numerator: number;
    denominator: number;
    reducedValue?: number;
  }
  export interface IEntryStateInputRatio extends IEntryStateScored {
    terms: string[];
  }

  export interface IEntryStateInputNumberList extends IEntryStateScored {
    values: string[];
  }

  export enum InputFormat {
    ALGEBRA = "algebra",
    FORM = "form",
    FRACTION = "fraction",
    NUMBER = "number",
    NUMBER_LIST = "number_list",
    RATIO = "ratio",
    TEXT = "text",
    WORDS = "words",
  }

  export enum AlgebraInputModes {
    ALGEBRA_INPUT = "algebraInput",
    ALGEBRA_LIST = "algebraList",
    ALGEBRA_GROUPS = "algebraGroups",  
  }

  export enum NumberListModes {
    NUMBER_LIST = "numberList",
    NUMBER_GROUPS = "numberGroups",
  }

  export const defaultTexts = {
    paper: "Paper instructions",
    buttonText: "I have finished this question"
  }

  export interface NumberList {
    value?: string;
    suffix?: string;
    values?: string[];
  }
  
  export interface AlgebraList {
    value?: string;
    suffix?: string;
  }

  export const inputEditInfo = {
    editExcludeFields: ['alternativeValues'],
    editTextFields: ['defaultText', 'buttonText', 'defaultText_Paper'],
    editKeyFieldsToShow: ['defaultText', 'buttonText', 'defaultText_Paper', 'format', 'isDual'],
  }

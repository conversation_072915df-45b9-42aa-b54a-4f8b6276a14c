import * as _ from 'lodash';
import { Component, OnInit, Input, OnChanges, ElementRef, OnDestroy, ViewChild, SimpleChanges, AfterViewInit, Renderer2 } from '@angular/core';
import { FormControl } from '@angular/forms';
import { LangService } from '../../core/lang.service';
import { StyleprofileService } from '../../core/styleprofile.service';
import * as ClassicEditor from 'ckeditor-custom';
import { of, Subject, Subscription } from 'rxjs';
// import * as ClassicEditor from '@ckeditor/ckeditor5-build-classic';
import { QuestionPubSub } from '../question-runner/pubsub/question-pubsub';
import { threadId } from 'worker_threads';
import { WhitelabelService } from '../../domain/whitelabel.service';
import { EInsertType, insertAtCursor } from './util/insert-at-cursor';
import { TextToSpeechService } from '../text-to-speech.service';
import { INPUT_NUMBER_FORMATS } from '../../ui-item-maker/services/input-format.service';
import { CKEditor5, ChangeEvent } from '@ckeditor/ckeditor5-angular';
import { ZoomService } from '../zoom.service';
import { PubSubTypes } from '../element-render-frame/pubsub/types';
import { ScoringTypes, QuestionState, getElementWeight, IEntryStateScored, IEntryState, IVoiceover, MetaItemState } from '../models';
import { IContentElementInput, InputFormat, NumberListModes, AlgebraInputModes, IEntryStateInputText, IEntryStateInputNumber, IEntryStateInputFraction, IEntryStateInputRatio, IEntryStateInputMath, IEntryStateInputNumberList } from './model';
import { CustomInteractionType } from '../element-render-custom-interaction/model';
import { OnlineOrPaperService } from '../online-or-paper.service';
import "ckeditor-custom/build/translations/fr";
import { AccessibilityService } from '../accessibility.service';
import { checkFindAndReplaceLanguage, ckEditorUiFindAndReplaceFormLangMap, findAndReplaceDomElementInnerText, overwriteCKEditorToolIcons } from './util/ck-editor-fr';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { AuthService } from 'src/app/api/auth.service';
import { RoutesService } from 'src/app/api/routes.service';

const wproofreaderServiceId = 'M0srRnp083NCwxs';
const wproofreaderSrcUrl = 'https://svc.webspellchecker.net/spellcheck31/wscbundle/wscbundle.js';

const SCORING_TYPE = ScoringTypes.REVIEW;
const IS_IGNORE_SPECIAL_CHAR = true;

const DEFAULT_MAX_RATIO_CHAR = 5;

enum LineSpaceOptionId {
  SINGLE = 1,
  DOUBLE = 2,
}

const parseNumber = (num:string ,lang:string) =>{
  num = ''+num
  if (lang === 'fr'){
    num = num.replace( /\,/g, '.' )
  }
  return +(num.replace( /[^\-0-9.]/g, '' ));
}

const checkBlankValue = (val:any) => (val === null || val === undefined || val === '');
@Component({
  selector: 'element-render-input',
  templateUrl: './element-render-input.component.html',
  styleUrls: ['./element-render-input.component.scss']
})
export class ElementRenderInputComponent implements OnInit, AfterViewInit, OnChanges, OnDestroy {

  @Input() element:IContentElementInput;
  @Input() isLocked:boolean;
  @Input() isShowSolution:boolean;
  @Input() questionState:QuestionState;
  @Input() questionPubSub?: QuestionPubSub;
  @Input() changeCounter:number;
  @Input() metaItemState: MetaItemState;

  @ViewChild('textArea') textArea:ElementRef<HTMLElement>;
  @ViewChild('defaultText')defaultText: ElementRef<HTMLElement>;
  @ViewChild('defaultText_Paper')defaultText_Paper: ElementRef<HTMLElement>;
  @ViewChild("input") firstInput: ElementRef;
  @ViewChild("input_box") inputBox: ElementRef;

  clickTriggers:Map<IContentElementInput, Subject<boolean>> = new Map();
  isShowingCapitals:boolean = false;
  frenchAccent = ["à","â","æ","ç","é","è","ê","ë","î","ï","ô","œ","ù","û","ü","«","»"]
  frenchAccentCapital = ["À",'Â','Æ','Ç','É','È','Ê','Ë','Ï','Î','Ô','Œ','Ù','Û','Ü',"«","»"]
  lineSpacingSelectionOptions = [
    {id:LineSpaceOptionId.SINGLE, caption:'lbl_auth_linespacing_1', },
    {id:LineSpaceOptionId.DOUBLE, caption:'lbl_auth_linespacing_2',}
  ]
  currentLineSpacingSelection = LineSpaceOptionId.DOUBLE;
  numberInputRect;
  digits = ["1","2","3","4","5","6","7","8","9","0"];
  keyboard = {
    en: {
      digits: this.digits,
      signs: {
        decimal: '.'
      }
    },
    fr: {
      digits: this.digits,
      signs: {
        decimal: ','
      }
    }
  }
  keyboardLocal = null;
  focusedFormEl = null;

  isAlive:boolean = true;
  lastTrackedQuestionState;
  numberInput = new FormControl('');
  textInput = new FormControl('');
  fractionWholeInput = new FormControl('');
  fractionNumeInput = new FormControl('');
  fractionDenoInput = new FormControl('');
  isStarted:boolean;
  isInFocus:boolean = false;
  latexCapture = {latexInput:''};
  ratioTerms:FormControl[];
  previousInput = '';
  filterRegEx = /(<p>|<\p>|\s)/
  filterChars = ['',' ','&nbsp;','\t', '<p>','&nbsp;</p>', '&nbsp;&nbsp;</p>']
  ckEditorConfig;
  ckEditorConfig_Paper;
  Editor;
  Editor_Paper;
  displayKeyboard = 'none';
  displayNumericKeyboard = 'flex';
  numberListInput:FormControl[];
  suffixListInput:FormControl[];
  numberGroupsInput:FormControl[];
  suffixGroupsInput:FormControl[];
  algebraListGiven = {};
  algebraListInput:FormControl[];
  algebraSuffixListInput:FormControl[];
  algebraGroupsGiven = {};
  algebraGroupsInput:FormControl[];
  algebraSuffixGroupsInput:FormControl[];
  isResponded: boolean;
  isConfirmFinished: boolean;
  ckEditorFocusId;
  isCkEditorErrorShown: boolean = false;
  
  digitCharWidths: number[]; // in em
  maxDigitCharWidth: number; // in em

  constructor(
    private lang: LangService,
    private profile: StyleprofileService,
    private elRef: ElementRef,
    public whitelabel: WhitelabelService,
    private zoom: ZoomService,
    private accessibility: AccessibilityService,
    private text2Speech: TextToSpeechService,
    public onlineOrPaper: OnlineOrPaperService,
    private renderer: Renderer2,
    private loginGuard: LoginGuardService,
    private auth: AuthService,
    private routes: RoutesService
  ) { 
  }
  
  ngOnInit() {
    // this.ckEditorFocusId = this.zoom.getNewCkEditorId()
    if (this.element.startingLatex){
      this.latexCapture.latexInput = this.element.startingLatex
    }
    /* math element has change even in dom */
    if (this.element.format === InputFormat.RATIO){
      this.ensureRatioTerms(); // subscription is done here
    }
    if (this.element.format === InputFormat.NUMBER_LIST){
      this.prepareNumberListMode(() => this.ensureNumberList(), () => this.ensureNumberGroups());
    }
    if (this.element.format === InputFormat.ALGEBRA){
      this.prepareAlgebraListMode(() => {}, () => this.ensureAlgebraList(), () => this.ensureAlgebraGroups());
    }
    if (!this.element.width) {
      this.element.width = 24;
    }
    if (!this.element.fixedHeight) {
      this.element.fixedHeight = 8;
    }
    if (!this.element.enableSpellCheck){
      this.element.enableSpellCheck = false;
      this.element.enableSpellCheckSettings = false;
    }
    this.initWordCountToggle()
    this.handleNewState(); 

    if (this.isDualPaper()) {this.initCkEditor_Paper();}
    else {this.initCkEditor()};  

    this.numberInput.valueChanges.subscribe( () => this.onNumberInputChange() );
    this.textInput.valueChanges.subscribe( () => this.onTextAreaInputChange() );
    this.fractionWholeInput.valueChanges.subscribe( () => this.updateState() );
    this.fractionNumeInput.valueChanges.subscribe( () => this.onNumeratorChange() );
    this.fractionDenoInput.valueChanges.subscribe( () => this.onDenominatorChange() );
    this.fractionWholeInput.valueChanges.subscribe( () => this.onFractionWholeInputChange() );
    this.updateState();

    this.keyboardLocal = this.keyboard[this.lang.c()];
    this.initElementSub()
    
    this.previousInput = this.textInput.value;
    this.isConfirmFinished = false;
  }

  ngAfterViewInit() {
    this.numberInputRect = this.elRef.nativeElement.querySelector('.numeric-inputs')?.getBoundingClientRect();
    if (this.checkElementFocus()) this.firstInput?.nativeElement.focus();
    window.sessionStorage.setItem("numericKeyBoardShown", ""+this.element.entryId);
  }

  isPubsubElResponded: boolean //Keeps track of user interaction where default init of input is done by another Element
  isPubsubResponseTracked: boolean

  isWordCountToggleActive:boolean = false;
  isWordCountStudentActivated:boolean = true;
  initWordCountToggle(){
    this.isWordCountToggleActive = !!this.whitelabel.getSiteFlag('IS_STU_WORDCOUNT_TOGGLE');
    if (this.isWordCountToggleActive){
      this.isWordCountStudentActivated = false
    }
  }
  toggleWordCount(){
    this.isWordCountStudentActivated = !this.isWordCountStudentActivated
  }
  isSimpleWordCount(){
    return !!this.whitelabel.getSiteFlag('IS_STU_WORDCOUNT_SIMPLE') && !this.element.enableWordOrCharLimit;
  }
  isDoubleLineDisplay() {
    return (this.currentLineSpacingSelection == LineSpaceOptionId.DOUBLE)
  }
  
  printPreview?:{strHtml:string, isDoubleLine:boolean}
  startPrintPreview(){
    this.printPreview = {
      strHtml:this.textInput.value || '', 
      isDoubleLine: this.isDoubleLineDisplay(),
    }
  }
  closePrintPreview(){
    this.printPreview = null;
  }

  /*
    For Number Input: Check and remove decimal (for current language) if isDecimalsNotAllowed is checked
    For Ratio Input: Check and remove decimal (for all langauges) if isDecimalsNotAllowed is checked
  */
  checkAndRemoveDecimal(val: string, isLangDependant?: boolean): string {
    const decimalChar = this.lang.getDecimalPointForCurrentLanguage();
    const decimalCharForOtherLang = this.lang.getDecimalPointForOtherLanguages();

    let decimalsToRemove = [...decimalChar];
    if (isLangDependant) {
      decimalsToRemove = [...decimalsToRemove, ...decimalCharForOtherLang];
    }

    decimalsToRemove.forEach(decimal => {
      val = this.checkAndRemoveChar(val, decimal);
    })

    return val;
  }

  /*
    Removes negative sign from val if the isNegativeSignBlocked option is checked
  */
  checkAndRemoveNegativeSign(val: string): string {
    const negativeSign = '-';
    if (this.element.isNegativeSignBlocked) {
      while (val && val.indexOf(negativeSign) !== -1) {
        const index = val.indexOf(negativeSign);
        val = val.substring(0, index) + val.substring(index + 1);
      }
    }
    return val;
  }

  /*
    Removes a given character from val completely
  */
  checkAndRemoveChar(val: string, char: string) {
    while (val && val.indexOf(char) !== -1) {
      const index = val.indexOf(char);
      val = val.substring(0, index) + val.substring(index + 1);
    }

    return val;
  }

  elementSubscrpition: Subscription;
  initElementSub(){
    if (this.questionPubSub){
      const elementSub = this.questionPubSub.initElementSub(this.element.entryId);
      if (this.isLocked) {
        return
      }
      this.elementSubscrpition = elementSub.subscribe((payload)=>{
        switch(this.element.format) {
          case InputFormat.NUMBER:
            if(typeof payload.data === 'object'){
              if('elementType' in payload.data){
                switch (payload.data.elementType) {
                  case CustomInteractionType.PIE_CHART:
                    const {isResponded, value} = payload.data
                    this.isPubsubResponseTracked = true;
                    this.isPubsubElResponded = isResponded;
                    this.numberInput.setValue(Number(value))     
                    break;              
                  default:
                    break;
                }
              }
            } else {
              this.numberInput.setValue(Number(payload.data))
            }
            break;
          case InputFormat.TEXT:
            if (payload.type==PubSubTypes.INPUT) {
              this.textInput.setValue(payload.data)
            } 
            // else if (payload.type==PubSubTypes.TIMER) {
            //   this.transcriptTimer = payload.data
            // }
            break;
          default:
            break
        }
      })
    }
  }

  getFractionWidth() {
    const fracDenom = String(this.element.fracNumerator).length
    const fracNumer = String(this.element.fracDenominator).length
    const buffer = this.element.isCompact ? 0 : 1;
    let width = Number(this.element.maxCharFraction)
    if (width) {
      return width+buffer
    }
    width = (fracDenom)
    if (!width || (fracNumer && width<fracNumer)) {
      width = fracNumer
    }
    if (width) {
      return width+buffer
    }
    return width
  }

  prepareNumberListMode(numberListFunc, numberGroupsFunc) {
    switch (this.element.numberListMode) {
      case NumberListModes.NUMBER_LIST:
        return numberListFunc();
      case NumberListModes.NUMBER_GROUPS:
        return numberGroupsFunc();
      default:
        return this.element.numberGroups ? numberGroupsFunc() : numberListFunc();
    }
  }

  prepareAlgebraListMode(algebraFunc, algebraListFunc, algebraGroupsFunc) {
    switch (this.element.algebraInputMode) {
      case AlgebraInputModes.ALGEBRA_LIST:
        return algebraListFunc();
      case AlgebraInputModes.ALGEBRA_GROUPS:
        return algebraGroupsFunc();
      default:
        return algebraFunc();
    }
  }

  ngOnDestroy(){
    const els = Array.from(document.getElementsByClassName('ML__keyboard is-visible'));
    if (els){
      els.forEach(e => e.classList.remove('is-visible'));
    }

    if (this.elementSubscrpition) this.elementSubscrpition.unsubscribe();

    this.isAlive = false;
  }

  isInsertDown;
  isEditorInited = false;
  ngAfterViewChecked() {
    if (this.isTextEntryShort()){
      // this.resizeCkEditor();
    }

    if (this.isEditorInited) return;

    if(this.editorObj ){
      this.isEditorInited = true;
      const editor = this.editorObj;
		  const view = editor.editing.view;
	    const viewDocument = view.document;

      editor.ui.focusTracker.on( 'change:isFocused', ( evt, name, isFocused ) => {
        if ( isFocused && this.element.isShowingFrenchKeys ) {
          this.displayKeyboard = 'flex'
        } else{
          this.displayKeyboard = 'none'
        }
      });

		  viewDocument.on( 'keydown', ( evt, data ) => {
        if( (data.keyCode == 9) && viewDocument.isFocused ){    
          if (!this.isInsertDown) { 
            // with white space setting to pre   
            // editor.execute('input', { text: "\t" });
            editor.execute( 'input', { text: "    " } );
            evt.stop(); // Prevent executing the default handler.
            data.preventDefault();
            view.scrollToTheSelection();
          } else {
            this.isInsertDown = false;
            evt.stop();
          }
        } else if (data.keyCode==45 || data.keyCode==18) {
          this.isInsertDown = true
        }
		  });

      viewDocument.on('keyup', ( evt, data) => {
        if (data.keyCode==45 || data.keyCode==18) {
          this.isInsertDown = false
        }
      })
      
      if (this.isDualPaper()){
        if(this.element.defaultText_Paper) this.resizeCkEditorOnDefaultText_Paper()
      } else {
        if(this.element.defaultText) this.resizeCkEditorOnDefaultText()
      }

    }
  }

  isCountdownMode(){
    return !(this.whitelabel.getSiteFlag('IS_BCED') || this.whitelabel.getSiteFlag('IS_NBED') || this.whitelabel.getSiteFlag('IS_ABED'));
  }

  isPlainTextArea(){
    return !this.whitelabel.getSiteFlag('IS_CKEDITOR')
  }

  isDualPaper(){
    return this.onlineOrPaper.isPaper && this.element.isDual
  }

  getPlaceholderDefaultPaperText() {
    if (!this.element.defaultText_Paper) return '';
    return this.element.defaultText_Paper;
  }

  getPlaceholderDefaultText() {
    if (!this.element.defaultText) return '';
    return this.element.defaultText;
  }

  initCkEditor(){
    // console.log('ckEditorData', this.ckEditorData)
    let toolbar = ['Bold', 'Italic', 'Underline',  'Highlight', 'bulletedList', 'numberedList', 'alignment', 'findAndReplace'] ;
    // toolbar = toolbar.concat(['|' ,'InsertTable']); // 'FontSize'
    if (this.element.enableSpellCheckSettings) toolbar = toolbar.concat('WProofreader');
    toolbar = toolbar.concat(['|' ,'Undo' ,'Redo']);

    // For WProodReader Plugin
    let removePlugins = ["MediaEmbedToolbar", "Link", "CKFinder"];
    let wproofreaderSettings;
    let wproofreaderFullSettings = ['dictionaries', 'languages', 'general', 'options']
    let lang = this.lang.getCurrentLanguage() == 'en' ? 'en_CA' : 'fr_FR';
    if (this.whitelabel.getSiteFlag('CKEDITOR_REMOVE_CODE_PLUGIN')){
      removePlugins.push("Code");
    }
    if (!this.element.enableSpellCheck){
      removePlugins.push('WProofreader');
    } else {
      wproofreaderSettings = {
        autoStartup: true,
        lang: lang,
        disableStyleGuide: true,
        styleGuideSuggestions: false,
        autocorrect: false,
        autocomplete: false,
				serviceId: wproofreaderServiceId,
				srcUrl: wproofreaderSrcUrl,
        syncOptions: false,
        settingsSections: this.element.enableSpellCheckSettings? wproofreaderFullSettings : []
			}
    }
    
    this.ckEditorConfig = {
      removePlugins: removePlugins,
      alignment: {
        options: [ 'left', 'center', 'right', 'justify']
      },
      toolbar,
      language: this.lang.c(),
      wproofreader: wproofreaderSettings
    };
    this.Editor = ClassicEditor; // CkEditor.BalloonEditor;
    window['Editor'] = this.Editor
  }

  initCkEditor_Paper(){
    let toolbar = ['Bold', 'Italic', 'Underline',  'Highlight', 'bulletedList', 'numberedList' ] ;
    toolbar = toolbar.concat(['|' ,'Undo' ,'Redo']);
    let removePlugins = ["MediaEmbedToolbar", "Link", "CKFinder"];
    this.ckEditorConfig_Paper = {
      toolbar,
      language: this.lang.c(),
      removePlugins: removePlugins
    };
    this.Editor_Paper = ClassicEditor; 
    window['Editor_Paper'] = this.Editor_Paper
  }

  confirmFinish(){
    this.isConfirmFinished = !this.isConfirmFinished
    this.updateState_ConfirmFinish();    
  }

  addFrenchCharacter(ch, el, e) {
    e.preventDefault();
    if (this.isPlainTextArea()){
      const textArea = <HTMLInputElement>this.textArea.nativeElement;
      textArea.focus();
      const {value, newPos} = insertAtCursor(textArea, ch, {type: EInsertType.BEG_END});
      this.textInput.setValue(value);
      textArea.setSelectionRange(newPos, newPos);
    }
    else{
      if (this.editorObj) {
        const viewFragment = this.editorObj.data.processor.toView(ch);
        const modelFragment = this.editorObj.data.toModel(viewFragment);
        this.editorObj.model.insertContent(modelFragment, this.editorObj.model.document.selection);
        this.editorObj.editing.view.focus()
      }
      // el.blur()
    }
  }

  onNumericInputFocusIn = (el) => {
    this.focusedFormEl = el;
    if(this.isLocked) return
    window.sessionStorage.setItem("numericKeyBoardShown", ""+this.element.entryId);
    this.displayNumericKeyboard = 'flex';
  }
  onNumericInputFocusOut = () => this.displayNumericKeyboard = 'none';
  
  isThisKeyboardInputSelected(){
    return window.sessionStorage.getItem("numericKeyBoardShown") === ""+this.element.entryId;
  }

  onRatioInputBlur() {
    this.onNumericInputFocusOut();
    this.ratioTerms.forEach(nFc => {
      const obj = this.onFormatNumberValue(nFc.value);
      if (Number.isNaN(obj.val) || !obj.val) obj.val = '' 
      if (obj.changed) nFc.setValue(obj.val);
      this.updateState();
    });
  }

  /*
    Is subscribed to ratio terms form control. Whenever input value
    is changed, we ensure that value conforms to the rules.
  */
  onRatioInputChange(): void {
    this.ratioTerms.forEach((term: FormControl) => {
      let changed: boolean = false;
      let val: string = term.value;

      if (!val) {
        return;
      }

      // ensure is a number
      const valIsNum = val.replace(/[^0-9.,-]/g, '');
      if (valIsNum !== val) {
        changed = true;
        val = valIsNum;
      }

      // are decimals allowed
      if (this.element.isDecimalsNotAllowed) {
        const ValWithoutDecimal = this.checkAndRemoveDecimal(val, true);
        if (ValWithoutDecimal !== val) {
          changed = true;
          val = ValWithoutDecimal;
        }
      }

      // are negative numbers not allowed
      const valWithoutNegativeSign = this.checkAndRemoveNegativeSign(val);
      if (valWithoutNegativeSign !== val) {
        changed = true;
        val = valWithoutNegativeSign;
      }

      if (changed) {
        term.setValue(val);
      }
    })
  }

  addKeyboardCharacter(e, ch) {
    e.preventDefault();
    
    const val = this.focusedFormEl.value ? this.focusedFormEl.value : '';
    this.focusedFormEl.setValue(`${val}${ch}`);
  }

  removeLastCharacter(){
    const val = this.focusedFormEl.value?.length > 1 ? this.focusedFormEl.value.slice(0, -1) : '';
    this.focusedFormEl.setValue(`${val}`);
  }

  getGCD(num1:number, num2:number) {
    let larger = num1;
    let smaller = num2;
    if (num2>num1) {
      larger = num2;
      smaller = num1;
    }

    if (larger%smaller==0) {
      return smaller;
    }

    return this.getGCD(smaller, larger%smaller)
  }

  previousNumberInput_mathCharLimit = ""
  alreadyTriggered = false

  toggleNumberInputSign(val: String){
    if(val.charAt(0)==='-'){
      val = val.substring(1)
    } else {
      val = "-" + val;
    }
    return val;
  }

  previousNumberInputVal
  onNumberInputChange() {

    if (this.alreadyTriggered) {
      this.alreadyTriggered = false
      return
    }
    let changed = false;
    const originalVal = this.numberInput.value;
    let val = originalVal

    let validationRegEx = /^-?[0-9,. ]*$/mg;
    let validationRegEx_negativeToggle = /^-?[0-9,. ]*-$/mg;

    if (this.element.inputNumFormat == 'Restricted'){
      switch (this.lang.getCurrentLanguage()){
        case 'en':
          validationRegEx = /^-?[0-9]*(?:[.][0-9]*?)?$/mg;
          validationRegEx_negativeToggle = /^-?[0-9]*(?:[.][0-9]*?)?-$/mg;
          break;
        case 'fr':
          validationRegEx = /^-?[0-9]*(?:[,][0-9]*?)?$/mg;
          validationRegEx_negativeToggle = /^-?[0-9]*(?:[,][0-9]*?)?-$/mg;
          break
        default:
          validationRegEx = /^-?[0-9]*(?:[.,][0-9]*?)?$/mg;
          validationRegEx_negativeToggle = /^-?[0-9]*(?:[.,][0-9]*?)?-$/mg;
      }
    }

    // Check for valid input 
    let isValid = validationRegEx.test(val);
    if(!isValid){
      // Check for minus sign for toggling negative
      isValid = validationRegEx_negativeToggle.test(val);
      if(isValid){
        val = val.slice(0, -1)
        val = this.toggleNumberInputSign(val)
      } else {

        val = (this.element.mathCharLimit ? this.previousNumberInput_mathCharLimit : this.previousNumberInputVal) || "";
      }
      changed = true
    } 

    // remove any spaces from input
    const valWithoutSpace = val.replace(' ', '');
    if (valWithoutSpace !== val) {
      changed = true;
      val = valWithoutSpace;
    }

    if (this.element.isDecimalsNotAllowed) {
      const newVal = this.checkAndRemoveDecimal(val, false);
      // set changed to true if we have removed the decimal
      if (newVal !== val) {
        changed = true;
        val = newVal;
      }
    }

    // remove negative sign from the input
    const numWithoutNegative = this.checkAndRemoveNegativeSign(val);
    if (numWithoutNegative !== val) {
      changed = true;
      val = numWithoutNegative;
    }

    if (this.element.mathCharLimit) {
      let lenOfValue = 0
      if (IS_IGNORE_SPECIAL_CHAR){
        for (let ch of val) {
          if(ch !== ',' && ch !== '.' && ch!==' ') lenOfValue++
        }
      } else {
        lenOfValue = val.length;
      }
      
      if (val!=undefined && val!=null && lenOfValue > this.element.mathCharLimit) {
        if (val == this.previousNumberInput_mathCharLimit) {
          val = val.toString().substring(0,this.element.mathCharLimit)
        } else {
          val = this.previousNumberInput_mathCharLimit;
        }
        changed = true;
      } else {
        this.previousNumberInput_mathCharLimit = val;
      }
    }
    if (changed) 
      this.numberInput.setValue(val);
    
    if (this.previousNumberInputVal && this.previousNumberInputVal.length <  this.numberInput.value.length){
      const obj = this.onFormatNumberValue(this.numberInput.value);
      if (Number.isNaN(obj.val) || obj.val=='NaN' || !obj.val) obj.val = '' 
      if (obj.changed && this.numberInput.value!=obj.val) {
        this.alreadyTriggered = true
        val = obj.val
        this.numberInput.setValue(obj.val);
      }
    }

    try {
      if (this.whitelabel.getSiteFlag('IS_STU_NUMBER_INPUT_NOISY')){
        if (originalVal != val){
          this.accessibility.log.next({eventType:'input-number-formatted', originalVal, val})
        }
      }
    }
    catch(e) {}

    this.updateState();
    this.previousNumberInputVal = val;
  }

  onNumberInputBlur() {
    this.onNumericInputFocusOut();
    this.updateState();
  }

  onNumberListInputBlur() {
    this.onNumericInputFocusOut();
    this.numberListInput.forEach(nFc => {
      const obj = this.onFormatNumberValue(nFc.value);
      if (Number.isNaN(obj.val) || !obj.val) obj.val = '' 
      if (obj.changed) nFc.setValue(obj.val);
      this.updateState();
    });
  }

  onNumberGroupsInputBlur() {
    this.onNumericInputFocusOut();
    this.numberGroupsInput.forEach(nFc => {
      const { val, changed } = this.onFormatNumberValue(nFc.value);
      if (changed) nFc.setValue(val);
      this.updateState();
    });
  }

  onFormatNumberValue(val) {
    let changed = true;
    // let val = value ? value.replace(/[^0-9.]/g, '') : null;  #reformatting in input-format service.
    // in case the first character is the minus sign for negative numbers
    if (val == '-' && (this.element.acceptEnFrNumericFormats || this.lang.c()=='en' || this.lang.c()=='fr')) return {val: '-', changed};
    if (isNaN(parseFloat(val))) return { val: '', changed };
    if (this.element.inputNumFormat && !this.element.acceptEnFrNumericFormats) {
      const formattedVal = INPUT_NUMBER_FORMATS[this.element.inputNumFormat](val, this.isFrench());
      if (formattedVal != val) {
        val = String(formattedVal);
      }
    }
    // When accepting multiple formats => formatting is disabled => remove the leading zeros.
    if(this.element.acceptEnFrNumericFormats && val.length > 1){      
      if(val[0] === '0'){
        val = !isNaN(val[1]) ? val.substring(1) : val;
      }
    }
    return { val, changed };
  }

  getWidthNum(field:string, valField:string) {
    let width = 0
    if (this.element[field] && this.element[field]>0) {
      width = Number(this.element[field])
    } else if (this.element[valField] != undefined && this.element[valField] != null){
      width =  Number(this.element[valField].length)
    } else {
      if (field=='value') return 5;
      else return 4
    }
    
    if (this.element.isCompact) {
      this.calcNumberCharWidths();
      const buffer = 0.5;
      return width * this.maxDigitCharWidth + buffer;
    } else {
      const buffer = 1;
      return width + buffer;
    }
  }
  
  previousNumerator = ''
  previousDenominator = ''
  
  onFractionWholeInputChange() {
    if (this.element.isNegativeSignBlocked) {
      const valWithoutNegativeSign = this.checkAndRemoveNegativeSign(this.fractionWholeInput.value);
      if (valWithoutNegativeSign !== this.fractionWholeInput.value) {
        this.fractionWholeInput.setValue(valWithoutNegativeSign);
      }
    }
    this.updateState();
  }

  onNumeratorChange() {
    this.onFractionChange(true)
    this.updateState();
  }

  onDenominatorChange() {
    this.onFractionChange(false)
    this.updateState();
  }

  onFractionChange(isNumerator: boolean) {
    // macChar logic mostly deprecated
    let maxChar = this.element.maxCharFraction;
    let val = this.fractionNumeInput
    let prevVal = this.previousNumerator
    if (!isNumerator) {
      maxChar = this.element.maxCharFraction
      val = this.fractionDenoInput
      prevVal = this.previousDenominator
    }
    // Check for validation + Charlimit
    if (!this.isFractionInputValid(val, maxChar)) val.setValue(prevVal);
    
    if (this.element.isNegativeSignBlocked) {
      const valWithoutNegativeSign = this.checkAndRemoveNegativeSign(val.value);
      if (valWithoutNegativeSign !== val.value) {
        val.setValue(valWithoutNegativeSign);
      }
    }
    
    if (isNumerator) {
      this.previousNumerator = val.value
    } else {
      this.previousDenominator = val.value
    }
  }

  isFractionInputValid(input: FormControl, maxChar){
    let isValid = !input.value.includes('.');
    if (isValid) {
      let strVal = String(input.value)
      let isNagative = /^-?[0-9]*$/mg.test(strVal);
      // Allow negative sign as the first character, if first char is "-", remove it from the string and proceed with normal validation
      if (isNagative && !this.element.isMixedNumber) // We only allow negative signs in fractions if there is no whole number in front
        strVal = strVal.substring(1)
      // might be redundant, the regex above is also checking the numbers.
      for (let i = 0;i<strVal.length;i++) {
        const c = strVal.charAt(i)
        if (c<'0' || c>'9') {
          isValid = false
          break;
        }
      }
    }
    if(isValid && maxChar) return input.value.length <= maxChar;
    return isValid;
  }

  onTextAreaInputChange(){
    if (this.element.enableWordOrCharLimit && this.isLimitByWords(true)) {
      const str = this.textInput.value || '';
      const words = str.split(this.filterRegEx);
      let numWords = 0;
      let maxWords = +this.element.maxWords+this.getExtraWordsOverLimit();
      words.forEach((word)=>{
        if (!this.filterChars.includes(word)) numWords++;
      })
      if (numWords>maxWords){
        this.textInput.setValue(this.previousInput);
      }
      else{ 
        this.previousInput = this.textInput.value
      }
    } 
    else if(this.element.enableWordOrCharLimit && this.element.selectedLimitType == 'char'){
      let charArray = this.getCharArray();
      if(charArray.length > this.element.maxChars){
        this.textInput.setValue(this.previousInput)
      } else {
        this.previousInput = this.textInput.value
      }
    }
    this.updateState();
  }

  getCharArray() {
    let str = this.textInput.value || '';
    if(str) str = str.toString()
    let charArray = str.replace(/<[^>]*(>|$)|&nbsp;|&zwnj;|&raquo;|&laquo;|&gt;/g, '');
    return charArray;
  }

  getRemainingChars(){
    return this.element.maxChars - this.getCharArray().length 
  }

  /*
    Get the ratio term with index. If the ratio term is uninitialized,
    then initialize it first.
  */
  getRatioTerm(index: number): FormControl {
    if (index >= this.ratioTerms.length) {
      this.ensureRatioTerms();
    }

    return this.ratioTerms[index];
  }

  ensureRatioTerms(){
    const IsInitRatioTerms = !this.ratioTerms && this.element.ratioTerms;
    const isNewTermAdded = this.ratioTerms && this.element.ratioTerms && this.ratioTerms.length < this.element.ratioTerms.length;
    if (IsInitRatioTerms || isNewTermAdded) {
      this.ratioTerms = [];
      this.element.ratioTerms.forEach(() => {
        const fc = new FormControl()
        this.ratioTerms.push(fc)
        fc.valueChanges.subscribe( () => {
          this.getInputRatioState() 
        });
      });
    }
  }

  ensureNumberList() {
    if (!this.numberListInput && this.element.numberList){
      this.numberListInput = [];
      this.suffixListInput = [];
      this.element.numberList.forEach((v) => {
        const fc = new FormControl()
        this.numberListInput.push(fc)
        this.suffixListInput.push(new FormControl(v.suffix));
        fc.valueChanges.subscribe( () => this.getInputNumberListState());
      });
    }
  }

  ensureNumberGroups() {
    if (!this.numberGroupsInput && this.element.numberGroups){
      this.numberGroupsInput = [];
      this.suffixGroupsInput = [];
      const [ group ] = this.element.numberGroups;
      group.forEach(v => {
        const fc = new FormControl()
        this.numberGroupsInput.push(fc)
        this.suffixGroupsInput.push(new FormControl(v.suffix));
        fc.valueChanges.subscribe( () => this.getInputNumberGroupsState());
      })
    }
  }

  ensureAlgebraList() {
    if (!this.algebraListInput && this.element.algebraList){
      this.algebraListInput = [];
      this.algebraSuffixListInput = [];
      this.element.algebraList.forEach((v) => {
        const fc = new FormControl()
        this.algebraListInput.push(fc)
        this.algebraSuffixListInput.push(new FormControl(v.suffix));
        fc.valueChanges.subscribe( () => this.getInputAlgebraListState());
      });
    }
  }

  ensureAlgebraGroups() {
    if (!this.algebraGroupsInput && this.element.algebraGroups){
      this.algebraGroupsInput = [];
      this.algebraSuffixGroupsInput = [];
      const [ group ] = this.element.algebraGroups;
      group.forEach(v => {
        const fc = new FormControl()
        this.algebraGroupsInput.push(fc)
        this.algebraSuffixGroupsInput.push(new FormControl(v.suffix));
        fc.valueChanges.subscribe( () => this.getInputAlgebraGroupsState());
      })
    }
  }
  updateAlgebraGroups() {
    if(this.element.algebraGroups && this.element.algebraGroups.length == 0) {
      this.algebraGroupsInput = [];
      this.algebraSuffixGroupsInput = [];
    }
    if(this.element.algebraGroups && this.element.algebraGroups.length > 0) {
      this.algebraGroupsInput = [];
      this.algebraSuffixGroupsInput = [];
      //if it is nested groups
      if(Array.isArray(this.element.algebraGroups) && this.element.algebraGroups.every(Array.isArray)) {
        //flatten groups
        const group  = this.element.algebraGroups.reduce((acc, val) => acc.concat(val), []);
        group.forEach(v => {
          const fc = new FormControl()
          this.algebraGroupsInput.push(fc)
          this.algebraSuffixGroupsInput.push(new FormControl(v.suffix));
          fc.valueChanges.subscribe( () => this.getInputAlgebraGroupsState());
        })
      }
    }
  }

  updateAlgebraList() {
    if(this.element.algebraList && this.element.algebraList.length == 0) {
      this.algebraListInput = [];
      this.algebraSuffixListInput = [];
    }
    if (this.element.algebraList && this.element.algebraList.length > 0){
      this.algebraListInput = [];
      this.algebraSuffixListInput = [];
      this.element.algebraList.forEach((v) => {
        const fc = new FormControl()
        this.algebraListInput.push(fc)
        this.algebraSuffixListInput.push(new FormControl(v.suffix));
        fc.valueChanges.subscribe( () => this.getInputAlgebraListState());
      });
    }
  }

  ngOnChanges(changes: SimpleChanges){
    if (this.lastTrackedQuestionState !== this.questionState){
      this.lastTrackedQuestionState = this.questionState;
      if (this.questionState){
        this.handleNewState();
      }
    }
    if(changes.changeCounter) {
      this.updateAlgebraGroups();
      this.updateAlgebraList();
    }
  }

  isFormatTypeNumber(){ return this.element.format === InputFormat.NUMBER }
  isFormatTypeFraction(){ return this.element.format === InputFormat.FRACTION }
  isFormatTypeAlgebra(){ return this.element.format === InputFormat.ALGEBRA && this.element.algebraInputMode === AlgebraInputModes.ALGEBRA_INPUT || this.element.latex && !this.element.algebraList }
  isFormatTypeAlgebraList(){ return this.element.format === InputFormat.ALGEBRA && this.element.algebraInputMode === AlgebraInputModes.ALGEBRA_LIST || this.element.algebraList }
  isFormatTypeAlgebraGroups(){ return this.element.format === InputFormat.ALGEBRA && this.element.algebraInputMode === AlgebraInputModes.ALGEBRA_GROUPS || this.element.algebraGroups }
  isFormatTypeRatio(){ return this.element.format === InputFormat.RATIO }
  isFormatTypeTextShort(){ return (this.element.format === InputFormat.WORDS) }
  isFormatTypeTextLong(){ return (this.element.format === InputFormat.TEXT)   }
  isFormatTypeForm() { return (this.element.format === InputFormat.FORM); }
  isFormatNumberListTypeForm() { return (this.element.format === InputFormat.NUMBER_LIST && this.element.numberListMode === NumberListModes.NUMBER_LIST || this.element.numberList); }
  isFormatNumberGroupsTypeForm() { return (this.element.format === InputFormat.NUMBER_LIST && this.element.numberListMode === NumberListModes.NUMBER_GROUPS || this.element.numberGroups); }
  isNumericKeyBoardType(){
    return this.isFormatTypeRatio() || this.isFormatTypeFraction() || this.isFormatTypeNumber() || this.element.format === InputFormat.NUMBER_LIST
  }
  isTextEntryShort(){
    return this.isFormatTypeTextShort();
  }

  getEntryState(){
    let state = undefined
    switch (this.element.format){
      case InputFormat.TEXT:     state = this.getInputTextState();break;
      case InputFormat.WORDS:    state = this.getInputTextShortState();break;
      case InputFormat.NUMBER:   state = this.getInputNumberState();break;
      case InputFormat.FRACTION: state = this.getInputFractionState();break;
      case InputFormat.RATIO:    state = this.getInputRatioState();break;
      case InputFormat.ALGEBRA:  
        state = this.prepareAlgebraListMode(() => this.getInputAlgebraState(), () => this.getInputAlgebraListState(), () => this.getInputAlgebraGroupsState());break;
      case InputFormat.NUMBER_LIST:
        state = this.prepareNumberListMode(() => this.getInputNumberListState(), () => this.getInputNumberGroupsState());break;
      default:
        state = <IEntryStateInputText>{
          type: 'input-longtext',
          isCustomGrading: true,
          isStarted: false,
          isFilled: !!this.element.isOptional,
          isResponded: false,
          str: '{ERROR}',
        }
    }
    if (this.element.isDisabled) {
      state["isScoringDisabled"] = true
    }
    return state
  }

  getEntryState_ConfirmFinish(){
    let state = undefined
    switch (this.element.format){
      case InputFormat.TEXT: state = this.getInputTextState_ConfirmFinish();break;
      default:
        state = <IEntryStateInputText>{
          type: 'input-longtext',
          isCustomGrading: true,
          isStarted: false,
          isFilled: !!this.element.isOptional,
          isResponded: false,
          str: '{ERROR}',
        }
    }
    if (this.element.isDisabled) {
      state["isScoringDisabled"] = true
    }
    return state
  }

  getMaxRatioChar() {
    return this.element.maxRatioChar || DEFAULT_MAX_RATIO_CHAR
  }

  isLimitByWords(maxWord?:boolean) {
    let isInputLimit = this.profile.getTextInputLimit();
    if(maxWord) return isInputLimit && this.element.maxWords && this.getExtraWordsOverLimit()>=0 && this.isLimitTypeWord();
    return isInputLimit;
  }

  isLimitTypeWord() {
    return this.element.selectedLimitType == 'word' || !this.element.selectedLimitType;
  }

  getExtraWordsOverLimit() {
    if (this.element.isSetHardLimitToZero) {
      return 0;
    }
    const wordsOverLimit = this.profile.getExtraWordsOverLimit();
    return wordsOverLimit;
  }

  getInputNumberState(){
    const value = this.numberInput.value;
    const isFilled = !checkBlankValue(value);

    if(isFilled) 
      this.isStarted = true
    
    const weight = getElementWeight(this.element);
    let isCorrect = this.element.acceptEnFrNumericFormats 
      ? this.isEnFrFormattedNumberCorrect(this.numberInput.value, this.element.value) 
      : this.isNumberCorrect(this.numberInput.value, this.element.value); 
    this._isResponded(isFilled);
    return <IEntryStateInputNumber>{
      type: 'input-number',
      isCorrect,
      isStarted: this.isStarted,
      isFilled: !!this.element.isOptional || isFilled,
      isResponded: this.isResponded,
      value,
      score: isCorrect ? weight : 0,
      weight,
      scoring_type: ScoringTypes.REVIEW, 
    }
  }

  private _isResponded(isFilled: boolean) {
    if (!this.isResponded && isFilled)
      if(this.isPubsubResponseTracked){
        this.isResponded = !!this.isPubsubElResponded;
      } else {
        this.isResponded = true;
      }
  }

  /**
   * Helper function to determine if a fraction is negative
   * @param wholenumber 
   * @param numerator 
   * @param denominator 
   * @returns true or false
   */
  private isNegativeFraction(wholenumber: number, numerator: number, denominator: number){
    let negativeSignCounter = 0;
    if (wholenumber < 0)
      negativeSignCounter++
    
    if (numerator < 0)
      negativeSignCounter++
    
    if (denominator < 0)
      negativeSignCounter++
    
    if (negativeSignCounter % 2 == 0)
      return 0
    
    return 1;
  }

  getInputFractionState(){
    let wholenumber = this.fractionWholeInput.value;
    let numerator = this.fractionNumeInput.value;
    let denominator = this.fractionDenoInput.value;
    let isFilled = true;
    if (this.element.isMixedNumber && checkBlankValue(wholenumber)){ isFilled = false; }

    if(isFilled)
      this.isStarted = true
    
    if (checkBlankValue(numerator)){ isFilled = false; }
    if (checkBlankValue(denominator)){ isFilled = false; }
    let isCorrect = false;
    const weight = getElementWeight(this.element);
    let wholeGiven:number = 0;
    let wholeInput:number = 0;
    const lang = this.lang.c();
    let numeInput:number = parseNumber(numerator, lang);
    let denoInput:number = parseNumber(denominator, lang);
    let numeGiven:number = parseNumber(this.element.fracNumerator, lang);
    let denoGiven:number = parseNumber(this.element.fracDenominator, lang);
    if (this.element.isMixedNumber){
      wholeGiven = parseNumber(this.element.fracWholeNumber, lang);
      wholeInput = parseNumber(wholenumber, lang);
    }
    if (this.element.isStrictSimplified){

      const isInputFractionNegative = this.isNegativeFraction(wholeInput, numeInput, denoInput);
      const isGivenFractionNegative = this.isNegativeFraction(wholeGiven, numeGiven, denoGiven);

      isCorrect = (Math.abs(wholeGiven) === Math.abs(wholeInput)) 
               && (Math.abs(numeGiven) === Math.abs(numeInput))
               && (Math.abs(denoGiven) === Math.abs(denoInput))
               && (isInputFractionNegative === isGivenFractionNegative);
    }
    else{
      if (denominator === 0 && denoGiven === 0){
        isCorrect = true;
      }
      else {
        const approx = (wholeGiven + (numeGiven/denoGiven)) - (wholeInput + (numeInput / denoInput));
        if (Math.abs(approx) < 0.000001){
          isCorrect = true;
        }
      }
    }
    if (this.element.isReductionForced && numeInput && denoInput && this.getGCD(numeInput, denoInput) > 1) {
      isCorrect = false;
    }

    this._isResponded(isFilled);
    return <IEntryStateInputFraction>{
      type: 'input-fraction',
      isCorrect,
      isStarted: this.isStarted,
      isFilled: !!this.element.isOptional || isFilled,
      isResponded: this.isResponded,
      wholenumber,
      numerator,
      denominator,
      score: isCorrect ? weight : 0,
      weight,
      scoring_type: ScoringTypes.REVIEW, 
    }
  }
  getInputRatioState(){
    this.onRatioInputChange();
    let isFilled = true;
    this.ensureRatioTerms();
    const lang = this.lang.c();
    const terms = this.ratioTerms ? this.ratioTerms.map(termFc => {
      const value = termFc.value;
      if (checkBlankValue(value)){
        isFilled = false;
      }
      return termFc.value;
    }) : [];
    
    if(isFilled) 
      this.isStarted = true
    
    let isCorrect = false;
    const weight = getElementWeight(this.element);
    
    const given = this.element.ratioTerms.map(val => parseNumber(val, lang) ) || [];
    const input = terms || [];
    if (given.length === 1){ // rare case?
      if (given[0] === input[0]){
        isCorrect = true;
      }
    }
    else {
      let scalingFactor = 1;
      if (!this.element.isStrictSimplified){
        scalingFactor = input[0] / given[0];
      }
      let isAllMatched = true; // until contra case found
      for (let i=0; i<given.length; i++){
        if (Math.abs((given[i] * scalingFactor) - input[i]) > 0.00001){
          isAllMatched = false;
        }
      }
      isCorrect = isAllMatched;
    }

    this._isResponded(isFilled);
    return <IEntryStateInputRatio>{
      type: 'input-ratio',
      isCorrect,
      isStarted: this.isStarted,
      isFilled: !!this.element.isOptional || isFilled,
      isResponded: this.isResponded,
      terms,
      score: isCorrect ? weight : 0,
      weight,
      scoring_type: ScoringTypes.REVIEW, 
    }
  }

  findParentWithClass(element: HTMLElement|Element, className: string): HTMLElement | null {
    while (element.parentElement) {
      if (element.parentElement.classList.contains(className)) {
        return element.parentElement;
      }
      element = element.parentElement;
    }
    return null;
  }
  centeredForNumericKB:boolean = null;
  originalMarginBottom: string | null = null;
  originalVerticalAlign: string | null = null;
  originalDisplay: string | null = null;
  isCentered():boolean{
    const SPACING_AFTER_KEYBOARD = 40;
    const isCentered = this.element.isShowingNumericKeyboard && this.isThisKeyboardInputSelected() && this.isNumericKeyBoardType();
    if(this.centeredForNumericKB !== isCentered){
      const parentElement = this.elRef.nativeElement.parentElement;
      // Check if the grandparent element exists
      if (isCentered) {
        // Use Renderer2 to set the margin-bottom style
        if(parentElement){
          this.originalMarginBottom = window.getComputedStyle(parentElement).getPropertyValue('margin-bottom');
          this.originalDisplay = window.getComputedStyle(parentElement).getPropertyValue('display');
          this.renderer.setStyle(parentElement, 'margin-bottom',(this.numberInputRect?.height  + SPACING_AFTER_KEYBOARD) + 'px');
          this.renderer.setStyle(parentElement, 'display','inline-block');
          this.originalVerticalAlign = window.getComputedStyle(parentElement).getPropertyValue('vertical-align');
          this.renderer.setStyle(parentElement, 'vertical-align', 'baseline');
        }
      } else {
        // Reset All Changed Elements
        if(parentElement){
          if(this.originalVerticalAlign){this.renderer.setStyle(parentElement, 'vertical-align', this.originalVerticalAlign);}
          else {this.renderer.removeAttribute(parentElement, 'vertical-align');}

          if(this.originalMarginBottom){this.renderer.setStyle(parentElement, 'margin-bottom', this.originalMarginBottom);}
          else {this.renderer.removeAttribute(parentElement, 'margin-bottom');}

          if(this.originalDisplay){this.renderer.setStyle(parentElement, 'display', this.originalDisplay);}
          else {this.renderer.removeAttribute(parentElement, 'display');}
        }
      }
      this.centeredForNumericKB = isCentered;
    }
    return isCentered;
  }

  getStyleForNumericKeyBoard(elementType:('input-container' | 'input' )):string{
    if(!this.isCentered()){
      return'';
    }
    const questionContainerRect = this.findParentWithClass(this.elRef.nativeElement,'question-container')?.getBoundingClientRect();
    const input = this.elRef.nativeElement.querySelector('.input');
    if(!input){
      return ''
    }
    switch(elementType){
      
      case 'input-container':
        const width = this.isFormatNumberListTypeForm() || this.isFormatTypeRatio()?  
          this.numberInputRect.width
          : input.getBoundingClientRect().width;
        // If Number list we gotta set width to all the inputs
        return `
        height: 0px;
        width: ${width}px;
        `;
      case 'input':
        let alignItems = 'center';
        const inputRect = this.elRef.nativeElement.parentElement.getBoundingClientRect();
        const keyboardWidth = this.element.isShowingNumericKeyboardWithDecimal? 373.75 : 342.65;
        if (keyboardWidth > inputRect.width && questionContainerRect){
          const keyboardOverflow = (keyboardWidth - inputRect.width)/2
          if((inputRect.left - keyboardOverflow ) < questionContainerRect.left){
            alignItems = 'flex-start';
          } else if((inputRect.right + keyboardOverflow ) > questionContainerRect.right){
            alignItems = 'flex-end';
          }
        }
        return `display: flex; flex-direction: column; align-items: ${alignItems}; gap: 2px;`;
      default:
        return '';
    }
  }

  getInputAlgebraState(){
    const latex = this.latexCapture.latexInput;
    const isFilled = !checkBlankValue(latex);
    
    if(isFilled) 
      this.isStarted = true
    
    this._isResponded(isFilled);

    const stripSpaces = (str:string) => str.replace(/\s/g, '');

    let score = 0;
    const weight = getElementWeight(this.element);

    if (isFilled){
      const inputLatex = stripSpaces(latex)
      const correctOptions = [];
      if (this.element.latexExtraCasesMultiline){
        const options = this.element.latexExtraCasesMultiline.split('\n')
        for (let latexOption of options){
          correctOptions.push( stripSpaces(latexOption) )
        }
      }
      if (this.element.latex){
        correctOptions.push( stripSpaces(this.element.latex) )
      }
      for (let latexOption of correctOptions){
        if (latexOption == inputLatex){
          score = weight;
        }
      }
      
    }

    let isCorrect = (score == weight);

    return <IEntryStateInputMath>{
      type: 'input-algebra',
      isCustomGrading: true,
      isStarted: this.isStarted,
      isFilled: !!this.element.isOptional || isFilled,
      isCorrect,
      isResponded: this.isResponded,
      latex,
      score,
      weight,
      scoring_type: ScoringTypes.MANUAL, 
    }
  }

  getInputAlgebraListState(){
    let isFilled = true;
    this.ensureAlgebraList();

    const givenValues = this.algebraListInput ? this.algebraListInput.map(nFc => {
      if (checkBlankValue(nFc.value)) isFilled = false;
      return nFc.value ? nFc.value : '';
    }) : [];

    if(isFilled) 
      this.isStarted = true

    this.algebraListGiven = { algebraListInput: this.algebraListInput };
    this._isResponded(isFilled)
    return <IEntryStateInputNumberList>{
      type: 'input-algebra_list',
      isCustomGrading: true,
      isStarted: this.isStarted,
      isFilled: !!this.element.isOptional || isFilled,
      isResponded: this.isResponded,
      values: givenValues,
      score: 0,
      weight: getElementWeight(this.element),
      scoring_type: ScoringTypes.MANUAL, 
    }
  }

  getInputAlgebraGroupsState(){
    let isFilled = true;
    this.ensureAlgebraGroups();

    const givenValues = this.algebraGroupsInput ? this.algebraGroupsInput.map(nFc => {
      if (checkBlankValue(nFc.value)) isFilled = false;
      return nFc.value ? nFc.value : '';
    }) : [];
    
    if(isFilled) 
      this.isStarted = true
    
    this.algebraGroupsGiven = { algebraGroupsInput: this.algebraGroupsInput };
    this._isResponded(isFilled);
    return <IEntryStateInputNumberList>{
      type: 'input-algebra_list',
      isStarted: this.isStarted,
      isFilled: !!this.element.isOptional || isFilled,
      isResponded: this.isResponded,
      values: givenValues,
      score: 0,
      weight: getElementWeight(this.element),
      scoring_type: ScoringTypes.MANUAL, 
    }
  }

  isTextInputValueEmpty() {
    if (this.textInput && !this.textInput.value) return true;
    return this.textInput.value.length === 0;
  }

  getInputTextState(){
    const str = this.textInput.value;
    const isFilled = this.element.isAnswerNotNecessary || !checkBlankValue(str);
    
    if(isFilled) 
      this.isStarted = true
    
      this._isResponded(isFilled);
    return <IEntryStateInputText>{
      type: 'input-longtext',
      isCustomGrading: true,
      isStarted: this.isStarted,
      isResponded: this.isResponded,
      isFilled: !!this.element.isOptional || isFilled,
      str,
      score: 0,
      weight: getElementWeight(this.element),
      scoring_type: ScoringTypes.MANUAL, 
    }
  }
  getInputTextShortState(){
    const str = (this.textInput.value || '').trim();
    const isFilled = this.element.isAnswerNotNecessary || !checkBlankValue(str);
    
    if(isFilled) 
      this.isStarted = true
    
    this._isResponded(isFilled);
    let isCorrect = false;
    let score = 0;
    const weight = getElementWeight(this.element);
    if (this.element.allowedWordAnswers){
      for (let option of this.element.allowedWordAnswers){
        let isMatch = false;
        if (this.element.isCaseSensitive){
          isMatch = (option.val.trim() == str)
        }
        else {
          isMatch = (option.val.trim().toLowerCase() == str.toLowerCase())
        }
        if (isMatch){
          isCorrect = true;
          score = option.weight || weight;
          break;
        }
      }
    }
    return <IEntryStateInputText>{
      type: 'input-shorttext',
      isCustomGrading: true,
      isCorrect,
      isStarted: this.isStarted,
      isResponded: this.isResponded,
      isFilled: !!this.element.isOptional || isFilled,
      str,
      score,
      weight,
      scoring_type: ScoringTypes.REVIEW, 
    }
  }
  getInputTextState_ConfirmFinish(){
    const str = this.textInput.value;
    let isFilled: boolean;
    this.isConfirmFinished? isFilled = true : isFilled = false;
    
    if(isFilled) 
      this.isStarted = true
    
    this._isResponded(isFilled);

    if(this.isResponded && !isFilled)
      this.isResponded = false

    return <IEntryStateInputText>{
      type: 'input-longtext',
      isCustomGrading: true,
      isStarted: this.isStarted,
      isResponded: this.isResponded,
      isFilled: !!this.element.isOptional || isFilled,
      str,
      score: 0,
      weight: getElementWeight(this.element),
      scoring_type: ScoringTypes.MANUAL, 
    }
  }
  getInputNumberListState(){
    let isFilled = true;
    this.ensureNumberList();

    const givenValues = this.numberListInput ? this.numberListInput.map(nFc => {
      if (checkBlankValue(nFc.value)) isFilled = false;
      return nFc.value ? nFc.value : '';
    }) : [];
    
    if(isFilled) 
      this.isStarted = true

    const weight = getElementWeight(this.element);
    const correctValues = this.element.numberList.map(n => n.value);
    const givenValuesSorted = this.sortArray1ByArray2(givenValues, correctValues);
    const isCorrect = givenValuesSorted ? !!givenValuesSorted.every((givenV, i) => {
      return this.element.acceptEnFrNumericFormats 
            ? this.isEnFrFormattedNumberCorrect(givenV, correctValues[i]) 
            : this.isNumberCorrect(givenV, correctValues[i]); 
    }) : false;

    this._isResponded(isFilled);
    return <IEntryStateInputNumberList>{
      type: 'input-number_list',
      isCorrect,
      isStarted: this.isStarted,
      isFilled: !!this.element.isOptional || isFilled,
      isResponded: this.isResponded,
      values: givenValues,
      score: isCorrect ? weight : 0,
      weight,
      scoring_type: ScoringTypes.AUTO, 
    }
  }

  getInputNumberGroupsState(){
    let isFilled = true;
    this.ensureNumberGroups();

    const givenValues = this.numberGroupsInput ? this.numberGroupsInput.map(nFc => {
      if (checkBlankValue(nFc.value)) isFilled = false;
      return nFc.value ? nFc.value : '';
    }) : [];
    
    if(isFilled) 
      this.isStarted = true

    const weight = getElementWeight(this.element);
    let isCorrect = false;
    if (givenValues) {
      isCorrect = !!this.element.numberGroups.find(group => {
        return !!group.every((set, setIndex) => {
          return this.element.acceptEnFrNumericFormats 
            ? this.isEnFrFormattedNumberCorrect(givenValues[setIndex], set.value) 
            : this.isNumberCorrect(givenValues[setIndex], set.value); 
        })
      })
    }

    this._isResponded(isFilled);
    return <IEntryStateInputNumberList>{
      type: 'input-number_list',
      isCorrect,
      isStarted: this.isStarted,
      isFilled: !!this.element.isOptional || isFilled,
      isResponded: this.isResponded,
      values: givenValues,
      score: isCorrect ? weight : 0,
      weight,
      scoring_type: ScoringTypes.AUTO, 
    }
  }

  sortArray1ByArray2 = (array1, array2) => {
    if (!array1 || !array2) return null;
    return array1.slice().sort((a, b) => {
      a = a.replace(/[^0-9.]/g, '');
      b = b.replace(/[^0-9.]/g, '');
      return array2.indexOf(a) - array2.indexOf(b);
    });
  };

  getStyle() {
    const style = {}
    if (!this.element.isNoInvertOnHiContrast && this.text2Speech.isHiContrast) {
      style["filter"] = "invert(0.8)"
    }
    return style
  }

  isHighContrast() {
    if (!this.element.isNoInvertOnHiContrast && this.text2Speech.isHiContrast) {
      return true;
    }
    return false;
  }

  getRemainingCharacters(){
    const input = this.textInput.value || '';
    return this.element.maxChars - input.length;
  }

  resizeCkEditorOnDefaultText(){
    let height,minWidth;

    const resizeEditor = (height,minWidth) => {
      this.editorObj.editing.view.change( writer => {
        writer.setStyle( 'height', height, this.editorObj.editing.view.document.getRoot() );
        writer.setStyle( 'min-width', minWidth, this.editorObj.editing.view.document.getRoot() );
      });
    }

    if(!this.textInput.value && this.defaultText){
      height = this.defaultText.nativeElement.offsetHeight + 55 +'px'
      minWidth = this.defaultText.nativeElement.offsetWidth + 16 +'px'  
    } else {
      height = '';
      minWidth = ''
    }
    
    resizeEditor(height,minWidth)    
  }

  resizeCkEditorOnDefaultText_Paper(){
    let height,minWidth;
    const resizeEditor = (height,minWidth) => {
      this.editorObj.editing.view.change( writer => {
        writer.setStyle( 'height', height, this.editorObj.editing.view.document.getRoot() );
        writer.setStyle( 'min-width', minWidth, this.editorObj.editing.view.document.getRoot() );
      });
    }

    if(!this.textInput.value && this.defaultText_Paper){
      height = this.defaultText_Paper.nativeElement.offsetHeight + 55 +'px'
      minWidth = this.defaultText_Paper.nativeElement.offsetWidth + 16 +'px'  
    } else {
      height = '';
      minWidth = ''
    }
    
    resizeEditor(height,minWidth)    
  }

  resizeCkEditor = _.throttle(() => {
    const ckeditor = this.elRef.nativeElement.getElementsByTagName("ckeditor")[0];
    if (ckeditor) {
      const el = ckeditor.getElementsByTagName('div').item(12)
      if (el) {
        if (this.element.fixedHeight && this.element.isFixedHeight) {
          el.style.height = this.element.fixedHeight + 'em';
        } else {
          el.style.height = '';
        }
      }
    }
  }, 5000);

  getWords() {
    const input = this.textInput.value || '';
    const words = input.split(this.filterRegEx);
    // console.log(words)
    let length = words.length;
    
    words.forEach((word)=>{
      if (this.filterChars.includes(word)) length--;
    })
    return length;
  }
  getRemainingWords() {

    return this.element.maxWords - this.getWords();
  }

  isNumberCorrect(givenValue, correctValue){
    if (givenValue !== null && givenValue !== undefined && givenValue != ''){
      const lang = this.lang.c();
      let valInput = parseNumber(givenValue, lang);
      let valExpected = parseNumber(correctValue, lang);
      let tolerance = this.element.roundingTolerance || 0.00000001
      if (this.element.roundingTolerancePercentageBased) {
        tolerance = valExpected * (parseFloat(this.element.roundingTolerance) / 100);
      }
      if (Math.abs(valExpected - valInput) <= +tolerance){
        return true
      }
    }
    return false;
  }

  isEnFrFormattedNumberCorrect = (givenValue, correctValue) =>{
    let isCorrect = false;
    if (givenValue !== null && givenValue !== undefined && givenValue != ''){
      const lang = this.lang.c();
      let valInputs = this.getGivenVariations(givenValue);
      let valExpected = parseNumber(correctValue, lang);
      let tolerance = this.element.roundingTolerance || 0.00000001
      if (this.element.roundingTolerancePercentageBased) {
        tolerance = valExpected * (parseFloat(this.element.roundingTolerance) / 100);
      }
      isCorrect = valInputs.some(v => Math.abs(valExpected - v) <= +tolerance)
    }
    return isCorrect;
  }

  getGivenVariations = (num) => {
    const isNegative = num.charAt(0) === '-';
    num = num.replace( /[^0-9.,]/g, '' );

    if (isNegative){
      num = '-' + num;
    }

    let variations = [num];
    let updatedNum = num;

    if (num.match(/\,/g) || num.match(/\./g)) {
      if (num.match(/\./g)) {
        updatedNum = num.replace( /\,/g, '' );
        variations = [...variations, updatedNum];

        updatedNum = num.replace( /\,/g, '' );
        updatedNum = updatedNum.replace( /\./g, ',' );
        variations = [...variations, updatedNum];
      }

      if (num.match(/\,/g)) {
        updatedNum = num.replace( /\./g, '' );
        variations = [...variations, updatedNum];

        updatedNum = num.replace( /\./g, '' );
        updatedNum = updatedNum.replace( /\,/g, '.' );
        variations = [...variations, updatedNum];
      }
    }

    return variations;
  }

  updateState = _.throttle(() => {
    if (!this.isAlive){ return; }
    if (this.isLocked){ return; }
    if (!this.questionState || !this.questionState[this.element.entryId]){ return; }
    this.questionState[this.element.entryId] = this.getEntryState();
    // console.log('input pubsub', this.questionPubSub)
    if(this.questionPubSub){
      this.questionPubSub.allPub({entryId: this.element.entryId, type: PubSubTypes.UPDATE_VALIDATOR, data: {}})
    }
  }, 500);

  updateState_ConfirmFinish = _.throttle(() => {
    if (!this.isAlive){ return; }
    if (this.isLocked){ return; }
    if (!this.questionState || !this.questionState[this.element.entryId]){ return; }
    this.questionState[this.element.entryId] = this.getEntryState_ConfirmFinish();
    if(this.questionPubSub){
      this.questionPubSub.allPub({entryId: this.element.entryId, type: PubSubTypes.UPDATE_VALIDATOR, data: {}})
    }
  }, 500);

  onCkEditorUpdate({editor}:ChangeEvent ){
    let editorData: any;
    try {
      // The ckeditor does not only fire the change event when the user updates the main input, it can also be triggered
      // by interactions with tools such as the Find and Replace, including focus in out of the find & replace input, 
      // in which the editor object will be undefined, and we should ignore.
      if (editor){
        editorData = editor.getData();
        this.textInput.setValue(editorData);
        //this.onTextAreaInputChange();
        if (editorData != this.textInput.value) {
          // Setting the data reset caret position to startnode we don't want that
          // editor.data.set(this.textInput.value)
          editor.execute('delete')
        }
        this.updateState();
      }
    } catch(e) {
      if(!this.isCkEditorErrorShown) { // Log only once per question
        this.auth.apiCreate(this.routes.LOG, {
            slug: `CKEDITOR_UPDATE_ERR`,
            data: {
              uid: this.auth.getUid(),
              questionState: this.questionState,
              element: this.element,
              textInput: this?.textInput?.value,
              editorData,
              error: {
                code: e.code,
                message: e.message,
                stack: e.stack,
                data: e?.data
              }
            }
          }
        )
      }

      this.isCkEditorErrorShown = true;

      if(!this.auth.userIsStudent) { // Show the pop-up only to students
        return;
      }

      this.loginGuard.confirmationReqActivate({
        caption: 'msg_save_question_ckeditor_err',
        btnCancelConfig: {
          hide: true
        }
      })
    }
  }

  editorObj: CKEditor5.Editor
  onCkEditorReady(e:any){
    try {
    
      //disable spellcheck
      if (!this.editorObj) this.editorObj = e;
      // All the changes to the Ck-editor should go in _onCkEditorReadyUpdate()
      try {
        this._onCkEditorReadyUpdate(e);        
      } catch (error) {
        const instanceInfo = {
          id: e?.id,
          ckEditorElementId: this.getCkEditorElementId()
        }
        this.auth.apiCreate(this.routes.LOG, {
          slug: `CKEDITOR_READY_ERR`,
          data: {
            uid: this.auth.getUid(),
            questionState: this.questionState,
            element: this.element,
            instanceInfo,
            error: {
              code: error.code,
              message: error.message,
              stack: error.stack,
              data: error?.data
            }
          }
        });
        console.log('CkEditor changes on ready failed', error)
      }
      
      // State restore 
      if (this.textInput.value) e.data.set(this.textInput.value);
      
    }
    catch(error){
      const instanceInfo = {
        id: e?.id,
        ckEditorElementId: this.getCkEditorElementId()
      }

      this.auth.apiCreate(this.routes.LOG, {
        slug: `CKEDITOR_READY_ERR`,
        data: {
          uid: this.auth.getUid(),
          questionState: this.questionState,
          element: this.element,
          instanceInfo,
          error: {
            code: error.code,
            message: error.message,
            stack: error.stack,
            data: error?.data
          }
        }
      });
      console.warn('state restoration', error)
    }
  }
  
  /**
   * 
   * @param e : EventInfo
   * 
   * This function is responsible to make changes to ckEditor onReady
   */
  private _onCkEditorReadyUpdate = (e: any) => {

    if(this.isLocked) 
      this.editorObj.enableReadOnlyMode(this.getCkEditorElementId());

    this.updateFindAndReplaceMenuPosition();
    this.updateCkEditorUiForLang();
    
    e.editing.view.change(writer => {
      writer.setAttribute( 'spellcheck', this.whitelabel.getSiteFlag('IS_SPELLCHECK') ? 'true' : 'false', e.editing.view.document.getRoot() )
    })

  }

  updateCkEditorUiForLang = () => {
    if(this.lang.c() === 'fr') {
      overwriteCKEditorToolIcons();
      checkFindAndReplaceLanguage(this.lang);
    }
  }

  /**
   * Method to move the Find and Replace tool menu position to below the tool bar strip, so
   * that it opens up in a fixed position regard less of the window size.
   */
  updateFindAndReplaceMenuPosition(){
    // Find all ckeditor toolbar elements
    const toolbarStripEl = Array.from(document.getElementsByClassName('ck ck-editor__top ck-reset_all'));

    toolbarStripEl.forEach(toolbarEl => {
      const fondAndReplaceBtns: Element[] = [];
      const toolbarTools = Array.from(toolbarEl.getElementsByClassName('ck ck-button ck-dropdown__button'));

      // Find the "Find and Replace" tool button in the toolbar
      toolbarTools.forEach(tool => {
        Array.from(tool.children).forEach(child => {
          if (child.innerHTML == 'Find and replace') fondAndReplaceBtns.push(tool) ;
        })
      })

      if(fondAndReplaceBtns && fondAndReplaceBtns.length > 0){
        // The menu is always the sibling of the tool button, so we get the parent of the tool button, and then get the menu
        // element from its second child. The order is fixed, where the toolbar button is the first child, and the menu is the second.
        const toolBtn = fondAndReplaceBtns[0];
        const toolBtnParent = toolBtn?.parentElement;
        const toolBtnParentChilds = toolBtnParent?.children;
        const isDropDownMenuPresent = toolBtnParentChilds?.length > 1; // as per the above comment 2nd child is menu
        if(!isDropDownMenuPresent) return;

        const dropdownMenu = toolBtnParentChilds[1]
        fondAndReplaceBtns[0].parentElement.removeChild(dropdownMenu); // Remove the menu element from here, because we will re-attatch it to the toolbar element

        if(dropdownMenu.children?.length) {
          (<HTMLElement>dropdownMenu.children[0]).style.width = '100%'; // Make the width of the menu the same as the editor toolbar (i.e. the width of the editor)
        }          

        var iDiv = document.createElement('div'); // Create a container div for the menu before attatching to the toolbar
        // iDiv.classList.add('ck')
        // iDiv.classList.add('ck-dropdown');
        iDiv.style.display = 'none';
        iDiv.appendChild(dropdownMenu);
        toolbarEl.appendChild(iDiv); // Attatch the container and the menu to the toolbar

        // Add new event listeners to toggle on and off the menu through the tool button, as the default one will no longer work
        fondAndReplaceBtns[0].addEventListener('click', function(e) {
          if (iDiv.style.display == 'none')
            iDiv.style.display = 'inline';
          else
          iDiv.style.display = 'none';
        })
      }
    })
  }

  getCkEditorElementId() {
    return `ck-editor-instance-${this.element.entryId }`
  }

  isBlankText(){
    const str = this.textInput.value;
    return !str ||  (str === '')
  }

  isFrench() {
    return this.lang.c() === 'fr'
  }

  getZoomVal(){
    return this.zoom.getScreenShrinkZoom();
  }

  getInputFontSize(){
    const zoomVal = this.getZoomVal();
    if (zoomVal > 0 && zoomVal < 1) {
      return zoomVal;
    }
  }

  ensureState(){
    if (this.questionState){
      const entryId = this.element.entryId;
      if (!this.questionState[entryId]){
        let entryState:IEntryStateScored = {
          type: 'input',
          isCorrect: false,
          isStarted: false,
          isFilled: !!this.element.isOptional,
          isResponded: false,
          score: 0,
          weight: getElementWeight(this.element),
          scoring_type: SCORING_TYPE, 
        }
        this.questionState[entryId] = entryState;
      }
    }
  }

  handleNewState(){
    if (this.questionState){
      const entryState:IEntryState = this.questionState[this.element.entryId];
      if (entryState){
        this.isStarted = entryState.isStarted;
        this.injectStateToDom(entryState)
      }
      else{
        this.ensureState();
      }
    }
  }
  injectStateToDom(state:IEntryState){
    switch (this.element.format){
      case InputFormat.TEXT: return this.injectTextState(<IEntryStateInputText> state);
      case InputFormat.NUMBER: return this.injectNumberState(<IEntryStateInputNumber> state);
      case InputFormat.ALGEBRA: 
        return this.prepareAlgebraListMode(
          () => this.injectAlgebraState(<IEntryStateInputMath> state), 
          () => this.injectAlgebraListState(<IEntryStateInputNumberList> state),
          () => this.injectAlgebraGroupsState(<IEntryStateInputNumberList> state),
        );
      case InputFormat.FRACTION: return this.injectFractionState(<IEntryStateInputFraction> state);
      case InputFormat.RATIO: return this.injectRatioState(<IEntryStateInputRatio> state);
      case InputFormat.NUMBER_LIST: 
        return this.prepareNumberListMode(() => this.injectNmberListState(<IEntryStateInputNumberList> state), () => this.injectNmberGroupsState(<IEntryStateInputNumberList> state));
    }
  }
  injectNumberState(state:IEntryStateInputNumber){
    this.numberInput.setValue(state.value);
  }
  injectFractionState(state:IEntryStateInputFraction){
    this.fractionWholeInput.setValue(state.wholenumber);
    this.fractionNumeInput.setValue(state.numerator);
    this.fractionDenoInput.setValue(state.denominator);
  }
  injectRatioState(state:IEntryStateInputRatio){
    this.ensureRatioTerms();
    if (state.terms){
      state.terms.forEach( (value, i) => {
        if(this.ratioTerms[i]){
          this.ratioTerms[i].setValue(value);
        }
      })
    }
  }
  injectAlgebraState(state:IEntryStateInputMath){
    this.latexCapture.latexInput = state.latex;
  }
  injectAlgebraListState(state:IEntryStateInputNumberList){
    this.ensureAlgebraList();
    if (state.values){
      state.values.forEach( (value, i) => {
        if (this.algebraListInput[i]){
          this.algebraListInput[i].setValue(value);
        }
      })
    }
  }
  injectAlgebraGroupsState(state:IEntryStateInputNumberList){
    this.ensureAlgebraGroups();
    if (state.values){
      state.values.forEach( (value, i) => {
        if (this.algebraGroupsInput[i]){
          this.algebraGroupsInput[i].setValue(value);
        }
      })
    }
  }
  injectTextState(state:IEntryStateInputText){
    // console.log('injectTextState', state.str)
    // if (this.whitelabel.getSiteFlag('IS_CKEDITOR')){
    //   // this.ckEditorData = state.str;
    // }
    this.textInput.setValue(state.str);
  }
  injectNmberListState(state:IEntryStateInputNumberList){
    this.ensureNumberList();
    if (state.values){
      state.values.forEach( (value, i) => {
        if (this.numberListInput[i]){
          this.numberListInput[i].setValue(value);
        }
      })
    }
  }
  injectNmberGroupsState(state:IEntryStateInputNumberList){
    this.ensureNumberGroups();
    if (state.values){
      state.values.forEach( (value, i) => {
        if (this.numberGroupsInput[i]){
          this.numberGroupsInput[i].setValue(value);
        }
      })
    }
  }
  isVoiceoverEnabled(){
    return this.text2Speech.isActive;
  }

  getClickTrigger(element){

    let trigger = this.clickTriggers.get(element);
    if (!trigger){
      trigger = new Subject();
      this.clickTriggers.set(element, trigger);
    }

    return trigger;
  }

  getTextVoiceURL(element:IContentElementInput) {
    if (element?.voiceover?.url) {
      return element.voiceover.url
    }
  }

  getButtonVoiceURL(element:IVoiceover) {
    if (element?.url) {
      return element.url
    }
  }
  
  getFontSizeForInput() {
    if (this.element.isCompact) {
      return '1em';
    } else {
      return this.profile.getInputFontSize();
    }
  }
  
  measurementCanvas: HTMLCanvasElement;
  prevFontFamily: string;
  calcNumberCharWidths() {
    if (this.maxDigitCharWidth == undefined || this.prevFontFamily != this.element.fontFamily){
      if (this.measurementCanvas == undefined) this.measurementCanvas = document.createElement('canvas');
      const ctx = this.measurementCanvas.getContext('2d');
      const fontSize = 16;
      let fontFamily = this.element.fontFamily;
      if (!fontFamily) {
        const inputElement = this.firstInput?.nativeElement;
        if (!inputElement) return;
        const computedStyle = window.getComputedStyle(inputElement);
        fontFamily = computedStyle.fontFamily;
      }
      ctx.font = `${fontSize}px ${fontFamily}`;
      this.digitCharWidths = this.digits.map(digit => ctx.measureText(digit).width/fontSize);
      this.maxDigitCharWidth = Math.max(...this.digitCharWidths);
    }
  }
  

  textEditorMouseEnter() {
    this.getClickTrigger(this.element).next(true);
  }

  buttonMouseEnter() {
    this.getClickTrigger(this.element.buttonVoiceOver).next(true);
  }

  getShowCountBtnSlug(){
    if (this.isLimitTypeWord()){
      return 'lbl_auth_word_count_show'
    }
    return 'lbl_auth_char_count_show'
  }

  getRatioInputStyle(){
    const style = {}
    style['font-size'] = this.getFontSizeForInput();
    style['vertical-align'] = 'middle';
    style['text-align'] = 'center';
    
    const width = this.element.maxRatioChar ? this.element.maxRatioChar : 2
    style['width'] = `${width}em`;
    return style;
  }
  
  checkElementFocus(): boolean {
    if (this.metaItemState == undefined) this.metaItemState = {};
    if (this.metaItemState.isInitialFocusDone) {
      return false
    } else {
      this.metaItemState.isInitialFocusDone = true;
      return true;
    }
  }
  
}

import { IContentElementTextLink } from "../element-render-text-link/model";
import { ElementType, IContentElement, IEntryStateScored, IScoredResponse } from "../models";

export interface IContentElementMcq extends IContentElement, IScoredResponse {
    currentSettings?: string;
    defaultDropdownText?: string;
    displayStyle: McqDisplay;
    fontSizeDropdownOptions?: boolean
    gridCellWidth?: number;
    gridNumColumns?: number;
    hasFrame?: boolean;
    HCSelectionBorderColor?: string;
    height?: number;
    isBgClear?: boolean;
    isBgClearAlways?: boolean;
    isBorderColor?: boolean;
    isCanLink?: boolean;
    isColoredLikert?: boolean;
    isHotspot?: boolean;
    isHotspotOnDark?: boolean;
    isContentsCentered?: boolean;
    isContentsCenteredVertically?: boolean;
    isContentsVertPaddDisabled?: boolean;
    isCustomHoverEffect?: boolean
    isDynamicallyCorrect?: boolean;
    isEnablePartialweights?: boolean;
    isHCBorderColor?: boolean
    isHideOptionActivated?: boolean
    isLimittedWidth?: boolean;
    isMultiSelect?: boolean;
    isNoThickBorder?: number;
    isOffsetSelectionBorder?: boolean;
    isOnRightSideOptionIndicator?: boolean;
    isOptionLabelsDisabled?: boolean;
    isOptionsBgColor?: boolean;
    isOptionsBoxShadow?: boolean;
    isOptionsFontColor?: boolean;
    isPolaroidStyle?: boolean;
    isRadioBackgroundHidden?: boolean;
    isRadioBackgroundHiddenAlways?: boolean;
    isRadioDisabled?: boolean;
    isScrambled?: boolean;
    isSelectionBorder?: Boolean;
    isSelectionScale?: Boolean;
    isSelectToggle?: boolean;
    isVerticallyCenterOptionIndicator?: boolean;
    marginBottom?: number;
    marginRight?: number;
    maxOptions?: number;
    mcqAreaHeight?: number;
    minHeight?: number;
    optionDropshadowBlur?: number;
    optionDropshadowColor?: string;
    optionDropshadowX?: number;
    optionDropshadowY?: number;
    optionHeight?: number;
    optionHeightSet?: boolean;
    options: Array<IContentElementMcqOption>;
    optionsBgColor?: string;
    optionsFontColor?: string;
    selectionBorder?: string;
    selectionBorderColor?: string;
    selectionScale?: number;
    width?: number;
    isTextLeftAligned?: boolean;
    maxCustomDDWidth?: number;
    framePopIds?: string;
    dropdownWidth?: number;
    isDropdownFullWidth?: boolean;
    maxCustomDDHeight?: number;
    avoidInversionOnHighContrast?: boolean;
    tableCols?: Array<IContentElementMcqTableColEachCell>;
    isTableHeaderCentred?: boolean;
    isRadioRowHeaderDisabled?: boolean;
    isTableFirstColumnDisabled?: boolean;
    isTableUseNormalLineHeight?: boolean;
    isTableUniformWidth?: boolean;
    hotspotGlobalColor?: string;
    hotspotGlobalOpacity?: number;
    hideCaretOnSelection?: boolean;
    isOptionButtonNoMargin?: boolean;
    isWidthFitContent?: boolean;
  }


  export const mcqEditInfo = {
    editExcludeFields: ['options'],
    editKeyFieldsToShow: ['displayStyle']
  }

  export interface IContentElementOption extends IContentElement {
    voiceover?: any;
    optionId: number;
    optionType: ElementType;
    content: any;
  }

  export const optionEditInfo = {
    editExcludeFields: [],
    editTextFields: ['content']
  }

  export interface IContentElementMcqOption extends IContentElementOption {
    content: string;
    elementType: string;
    frameData?: any;
    frameInteractionId?: number;
    frameInteractionType?: string;
    height?: number;
    isCorrect?: boolean;
    isDisabled?: boolean;
    link?: IContentElementTextLink;
    optionId: number;
    partialWeight?: number;
    url?: string;
    width?: number;
    x?: number;
    y?: number;
    elementMode?: string;
    cols?: any;
    align?: string;
  }
  export interface IContentElementMcqTableCol {
    label:string;
    elementType:string;
  }

  export interface IContentElementMcqTableColEachCell {
    label:string;
    isCellOpen: false,
    cellInfo:'',
    cellType:'text',
    elementType:string;
  }


  export interface IContentElementMcqOptionInTable  extends IContentElementMcqOption {
    cols: {
      content: string;
      elementType?: string;
      cellInfo?: IContentElementMcqOption;
      cellType?: string;
      align?: string;
      alignVertical?: string;
    }[]
  }
  
  export const mcqOptionEditInfo = {
    editExcludeFields: ['voiceover'],
    editKeyFieldsToShow: ['optionType', 'content', 'isCorrect'],
    superType: ElementType.OPTION
  }

  export enum McqDisplay {
    BUBBLE = "bubble",
    DROPDOWN = "dropdown",
    CUSTOM_DROPDOWN = "custom_dropdown",
    FREEFORM = "freeform",
    TABLE = "table",
    GRID = "grid",
    HORIZONTAL = "horizontal",
    LIKERT = "likert",
    VERTICAL = "vertical",
    WRAP = "wraparound",
  }


  export interface IMcqSelection {
    i: number;
    id: number;
    elementType: string;
    content: any;
  }
  export interface IEntryStateMcq extends IEntryStateScored {
    selections: Array<IMcqSelection>;
    selectedOptionId?: Set<number>, // @w
    isPathFollowed?: boolean;
    alreadyScrambled?: boolean;
  }
  
  export interface IMcqSharedObject {
    mcqContainerDiv: HTMLDivElement;
  }

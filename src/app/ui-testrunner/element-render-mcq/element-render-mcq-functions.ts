import { SimpleChanges } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Subject } from 'rxjs';
import { LoginGuardService } from '../../api/login-guard.service';
import { LangService } from '../../core/lang.service';
import { StyleprofileService } from '../../core/styleprofile.service';
import { getRandomInt } from '../../marking/data/data';
import { DrawingLogService } from '../drawing-log.service';
import { PubSubTypes } from '../element-render-frame/pubsub/types';
import { ElementType, getElementWeight, QuestionState, ScoringTypes } from '../models';
import { QuestionPubSub } from '../question-runner/pubsub/question-pubsub';
import { indexOf } from '../services/util';
import { TextToSpeechService } from '../text-to-speech.service';
import { IContentElementMcq, IContentElementMcqOption, IEntryStateMcq, IMcqSelection, McqDisplay } from './model';
import { QuestionRunnerLayoutService } from '../question-runner-layout.service';
import { ElementLogService, ILogResponse } from '../element-log.service';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';

export class McqRenderer {
    
    currentSelections;
    isStarted = false;
    alreadyScrambled;
    clickTriggers:Map<IContentElementMcqOption, Subject<boolean>> = new Map();
    lastTrackedQuestionState
    isShowSolutionApplied:boolean;
    lastDropDownSelection: number | undefined;

    selectionsMap = new Map();

    constructor(
      public element:IContentElementMcq, 
      public questionState:QuestionState, 
      public getMcqContent:Function,
      public isShowSolution,
      public isLocked:boolean, 
      public textToSpeech:TextToSpeechService,
      public profile: StyleprofileService,
      public loginGuard: LoginGuardService,
      public lang: LangService,
      private questionPubSub: QuestionPubSub,
      private elementLog?: ElementLogService,
      private questionRunnerLayout?: QuestionRunnerLayoutService,
      private dropdownSelector?:FormControl,
      private bufferedLog?: DrawingLogService,
    ) {
        this.currentSelections = [];
        // this.alreadyScrambled = this.element.alreadyScrambled;
        this.updateSelectionsMap(this.ensureState().selections);
    }

    checkForChanges(changes?:SimpleChanges) {
      if (changes){
        if (changes.isLocked){ this.isLocked = changes.isLocked.currentValue }
        if (changes.isShowSolution) { this.isShowSolution = changes.isShowSolution.currentValue; }
        if (changes.questionState) { this.questionState = changes.questionState.currentValue; }
      }
      if (this.lastTrackedQuestionState !== this.questionState){
        this.lastTrackedQuestionState = this.questionState;
        this.questionState = this.questionState;
        if (this.questionState){
          this.handleNewState();
        }
      }
      if (this.isShowSolution && !this.isShowSolutionApplied){
        this.showSolution()
        this.isShowSolutionApplied = true;
      }

    }

    getLastTrackedQuestion() {
      return this.lastTrackedQuestionState
    }

    updateSelectionsMap(selections : IMcqSelection[]) {
      this.selectionsMap = new Map();
      for(const selection of (selections || [])) {
        this.selectionsMap.set(selection.i, true);
      }
    }

    updateState() {
      let selectedOptionId: Set<number> = new Set()
      const entryState:IEntryStateMcq = this.ensureState();
      const weight = getElementWeight(this.element);
      let score =  this.element.isOptional ? undefined :weight ;
      let totalPartialScore = 0;
      let isCorrect = false;
      let isOneCorrectOptionSelected = false;

      let selections = this.currentSelections.map( selection => {
        let i = indexOf(this.element.options, selection);
        if (selection.isCorrect){
          isOneCorrectOptionSelected = true
        }
        const content = this.getMcqContent(selection);
        return {
          i,
          id: selection.optionId,
          elementType: selection.elementType,
          content
        }
      });

      this.updateSelectionsMap(selections);

      let isFilled = selections.length > 0;
      if (this.element.isMultiSelect && this.element.maxOptions && selections.length < this.element.maxOptions){
        isFilled = false;
      }
      
      score = 0
      let numCorrect = 0;
      let numLimittedCorrect = 0;
      let numLimittedMax = 0;
      let numTotalMax = 0;
      let numIncorrect = 0;
      this.element.options.forEach((option, i) => {
        const expected = !!option.isCorrect;
        const input = !!this.selectionsMap.has(i);
        const isMatch = (expected === input);
        numTotalMax ++
        if (expected){
          numLimittedMax ++
          if (input){
            numLimittedCorrect ++ 
            numCorrect ++
            totalPartialScore += option.partialWeight || 0
          }
        }
        else if (isMatch){
            numCorrect ++
            totalPartialScore += option.partialWeight || 0
        }

        if (!isMatch){
          numIncorrect ++
        }

      })
        
      // conclude the scoring


      if (this.element.isMultiSelect){
        
        if (this.element.enableProportionalScoring){
          if (this.element.maxOptions){
            if (numLimittedMax){
              score = weight * (numLimittedCorrect/numLimittedMax)
            }
          }
          else{
            if (numTotalMax){
              score = weight * (numCorrect/numTotalMax)
            }
          }
        }
        else if (this.element.isEnablePartialweights){
          let fullMarks = numLimittedMax == numLimittedCorrect && selections.length == numLimittedMax
          score = fullMarks ? weight : totalPartialScore
        }
        else{ // deduction by default
          if (numIncorrect < weight){
            score = weight - numIncorrect
          }
        }
        isCorrect = (score == weight)
      }
      else {
        if (numLimittedCorrect > 0){
          isCorrect = true;
          score = weight;
        }
        else{
          isCorrect = false
          score = 0;
        }
      }

      // console.log({
      //   maxOptions: !!this.element.maxOptions,
      //   isMultiSelect: this.element.isMultiSelect,
      //   enableProportionalScoring: this.element.enableProportionalScoring,
      //   numIncorrect,
      //   numLimittedCorrect,
      //   numLimittedMax,
      //   numCorrect,
      //   numTotalMax,
      //   score,
      //   isCorrect
      // })

      score = Math.max(0, score)

      entryState.isCorrect = this.element.isOptional? undefined :isCorrect;
      entryState.isStarted = this.isStarted;
      entryState.isFilled = isFilled;
      entryState.isResponded = selections.length >= 1;
      entryState.selections = selections;
      entryState.alreadyScrambled = this.alreadyScrambled,
      entryState.score = score;
      entryState.weight = weight;

      if(this.elementLog){
        const data: ILogResponse = {itemType: entryState.type, response: entryState};
        this.elementLog.onResponseChange.next(data);
      }
      if (this.bufferedLog){
        this.bufferedLog.bufferedLog('MCQ_UPDATE', {entryId: this.element.entryId, entryState });
      }
      if(this.questionPubSub){
        this.questionPubSub.allPub({entryId: this.element.entryId, type: PubSubTypes.UPDATE_VALIDATOR, data: {}})
      }
    }

    isMCQCorrect(optionElement, index){
      const state:IEntryStateMcq = this.questionState[this.element.entryId]
      if(state.isCorrect){
        if(this.selectionsMap.has(index)) return true
      }
      return optionElement.isCorrect
    }

    isInputDisabled(){
      const entryState = this.ensureState();
      return (this.isLocked || entryState.isPathFollowed);
    }
    
    ensureState() {
      let entryState:IEntryStateMcq
      if (this.questionState){
          const entryId = this.element.entryId;
          entryState = this.questionState[entryId]
          // console.log('ensureState', entryState)
          if (!entryState){
            entryState  = {
            type: 'mcq',
            isCorrect: false,
            isStarted: false,
            isFilled: false,
            isResponded: false,
            selections: [],
            alreadyScrambled: this.alreadyScrambled,
            score: 0,
            weight: getElementWeight(this.element),
            scoring_type: ScoringTypes.AUTO, 
          }
          // console.log('overwriteState', entryState)
          this.questionState[entryId] = entryState;
        }
      }
      return entryState;
    }

    isMaxAnswerMsgShown;
    checkIfMaxAnswersReached() {
      const selections = this.currentSelections
      if (this.element.isMultiSelect && this.element.maxOptions && selections.length >= this.element.maxOptions && this.element.maxOptions > 0) {
        //if (this.loginGuard) this.loginGuard.quickPopup("You have made the maximum number of selections. Please unselect one to make a new selection.")
        this.isMaxAnswerMsgShown = true;
      } else {
        this.isMaxAnswerMsgShown = false;
      }
      return this.isMaxAnswerMsgShown;
    }

    isHandlingNewState:boolean;
    handleNewState () {
      if (this.questionState){
        const entryState:IEntryStateMcq = this.ensureState();
        this.isStarted = entryState.isStarted;
        this.isHandlingNewState = true;
        if (this.element.isOptional){
          entryState.isFilled = true;
          entryState.isCorrect = undefined;
        }
        if (this.element.displayStyle === McqDisplay.DROPDOWN){
          this.dropdownSelector.setValue(entryState.selections[0])
          if(entryState.selections[0]){
            this.lastDropDownSelection = entryState.selections[0].i
          }          
        }
        else{
          if (!entryState.selections){
            return;
          }
          entryState.selections.forEach(selectionState => {
            let i = selectionState.i;
            this.selectOption(this.element.options[i]);
          })
        }
        this.isHandlingNewState = false;
      }
    }
    
    popOut() {
      if (this.questionRunnerLayout && this.element.framePopIds) {
        this.questionRunnerLayout.clear(this.element.entryId);
        const list = this.element.framePopIds.split(',')
        list.forEach((num, index)=>{
          this.questionRunnerLayout.mapId2ZIndex(parseInt(num), 1000-index, this.element.entryId)
        })
      }
    }
    isDisplayStyleDropdown() { return this.element.displayStyle === McqDisplay.DROPDOWN; }

    initDropDownSelector(){
      const entryState = this.ensureState();
      if (this.isDisplayStyleDropdown()) {
        if (entryState.selections && entryState.selections.length > 0){
          const selection = entryState.selections[0]
          this.dropdownSelector.setValue(selection.i);
        }
        // else if (this.element.defaultDropdownText && this.element.defaultDropdownText.length>0) {
        //   this.dropdownSelector.setValue(0);
        // }
        else{
          this.dropdownSelector.setValue(undefined)
        }
      } 
      this.dropdownSelector.valueChanges.subscribe(change => this.updateDropdownState() );
      this.updateDropdownEnabled();
    }

    updateDropdownEnabled(){
      if (this.isInputDisabled()){
        this.dropdownSelector.disable()
      }
      else{
        this.dropdownSelector.enable()
      }
    }


    isDropdownCorrect(dropdownSelector){
      return this.questionState[this.element.entryId].isCorrect
    }

    updateDropdownState(){
      if (!this.isDisplayStyleDropdown()){
        return;
      }
      if(this.dropdownSelector.value == this.lastDropDownSelection) return
      let isCorrect = false;
      let isFilled = false;
      let i = this.dropdownSelector.value;
      this.lastDropDownSelection = i
      let option;
      if (i>=0){
        // if (this.hasDefaultDropDownText()) option = this.element.options[i-2];
        // else option = this.element.options[i]
        option = this.element.options[i]
        if (option){
          isFilled = true
          isCorrect = option.isCorrect;
        }
      }
  
      const weight = getElementWeight(this.element);
      const entryState:IEntryStateMcq = this.ensureState();
      entryState.isCorrect = isCorrect;
      entryState.isFilled = isFilled;
      entryState.isStarted = true;
      if (isFilled){
        entryState.selections = [{
          i: +i, 
          id: option.optionId, 
          elementType: ElementType.TEXT, 
          content: option.content,
        }]
      }
      else{
        entryState.selections = [];
      }
      entryState.score =  isCorrect ? weight : 0 ;
      entryState.isResponded = entryState.selections.length >= 1;
      if(this.questionPubSub){
        this.questionPubSub.allPub({entryId: this.element.entryId, type: PubSubTypes.UPDATE_VALIDATOR, data: {}})
      }

      if(this.elementLog){
        const data: ILogResponse = {itemType: entryState.type, response: entryState};
        this.elementLog.onResponseChange.next(data);
      }
    }

    initElementSub(questionPubSub: QuestionPubSub){
      if (questionPubSub) {  //some of the old items giving questionPubSub undefined for first time.
        const elementSub = questionPubSub.initElementSub(this.element.entryId);
        elementSub.subscribe(payload => {
          if (this.isLocked) return;
          const optionId = payload.data.option;
          this.element.options.map((option, id) => {
          option.isCorrect = option.optionId === optionId ? true : false;
          });

          switch (payload.type) {
            case McqDisplay.DROPDOWN:
              return this.updateDropdownState();
            default:
              return this.updateState();
            //Default for : BUBBLE, HORIZONTAL, VERTICAL, FREEFORM, GRID, WRAP,LIKERT
          }
        });
      }
      
    }

    getMaxMsgSlug(frameWorkTagsRef:Map<string, boolean>) {
      if(frameWorkTagsRef){
        if(frameWorkTagsRef.get('G9')){
          return 'lbl_max_sel_g9'
        }
      }
      return 'lbl_max_sel';
    }

    static getMaxMcqMsgSlug(profile: StyleprofileService, whiteLabel: WhitelabelService, frameWorkTagsRef?:Map<string, boolean>): string {
      if(frameWorkTagsRef && frameWorkTagsRef.get('G9')){
        return 'lbl_max_sel_g9';
      } 
      return 'lbl_max_sel';
    }

    getClickToDismissMsg() {
      return this.lang.tra('btn_click_dismiss');
    }

    turnMaxMsgOff() {
      this.isMaxAnswerMsgShown = false
    }

    _selectOption (option:IContentElementMcqOption) {
        if (!this.element.isMultiSelect){
          if (this.isSelected(option)){
            if (this.element.isSelectToggle){
              this.currentSelections = [];
            }
          }
          else {
            this.currentSelections = [option];
          }
        }
        else{
          let i = indexOf(this.currentSelections, option);
          let isSelected = (i !== -1);
          if (isSelected){ 
            this.currentSelections.splice(i, 1);
            this.checkIfMaxAnswersReached();
          }
          else{
            if (this.checkIfMaxAnswersReached()) return this.currentSelections
            this.currentSelections.push(option);
          }
        }
        return this.currentSelections;
    }

    selectOption (option:IContentElementMcqOption) {
        this.currentSelections = this._selectOption(option);
        if (!this.isHandlingNewState){
          this.updateState();
          this.getClickTrigger(option).next(true);
        }
    }

    selectOptionManual(option:IContentElementMcqOption){
        if (this.isInputDisabled()){ return; }
        if (option.frameInteractionType && option.frameInteractionId){
          this.questionPubSub.elementPub(option.frameInteractionId, option.frameInteractionType, option.frameData)
        }
        this.isStarted = true;
        this.selectOption(option);  
    }

    isSelected (option:IContentElementMcqOption) {
        if (indexOf(this.currentSelections, option) !== -1){
            return true;
        }
        return false;
    }

    scrambleOrder() {
        //algorithm: Fisher-Yates shuffle
        let options = this.element.options;
        for(let i  = options.length - 1; i > 0; i--) {
          let j = getRandomInt(0, i);
          [options[i], options[j]] = [options[j], options[i]]; //swap them
        }
        this.alreadyScrambled = true;
      }
    
      isVoiceoverEnabled(){
        return this.textToSpeech.isActive;
      }
    
      getClickTrigger(option: IContentElementMcqOption){
        let trigger = this.clickTriggers.get(option);
        if (!trigger){
          trigger = new Subject();
          this.clickTriggers.set(option, trigger);
        }
        return trigger;
      }
    
      deselectAllOptions(){
        this.currentSelections.splice(0, this.currentSelections.length);
      }

      showSolution(){
        // console.log("in show solution")
        this.deselectAllOptions();
        this.element.options.forEach(option => {
          if (option.isCorrect){
            this._selectOption(option);
          }
        })
      }
}

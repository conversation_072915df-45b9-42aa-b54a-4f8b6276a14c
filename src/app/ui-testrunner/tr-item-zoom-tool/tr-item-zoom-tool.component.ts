import { Component, ElementRef, EventEmitter, Input, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { IQuestionConfig } from '../models';
import { ZoomService } from '../zoom.service';
import { LangService } from 'src/app/core/lang.service';

@Component({
  selector: 'tr-item-zoom-tool',
  templateUrl: './tr-item-zoom-tool.component.html',
  styleUrls: ['./tr-item-zoom-tool.component.scss']
})
export class TrItemZoomToolComponent implements OnInit {

  constructor(public lang: LangService, private zoom: ZoomService) {}

  @Input() item: IQuestionConfig;
  @Input() zoomIncrement: number = 0.25;
  @Input() maxZoomLevel: number = 3;
  @Input() minZoomLevel: number = 0.5;
  @Output() itemZoomedIn = new EventEmitter();
  @Output() itemZoomedOut = new EventEmitter();
  @ViewChild ('container', { static: false }) container:ElementRef<HTMLElement>;

  ngOnInit(): void {}

  ngOnChanges(changes:SimpleChanges){
    // Reset the position of the tool when the item has changed
    if (changes.item && this.container?.nativeElement){
      this.container.nativeElement.style.transform = 'translate3d(0px, 0px, 0px)'
    }
  }

  getItemZoom() {
    if (this.zoom.getItemLvlZoom(this.item.id))
      return this.zoom.getItemLvlZoom(this.item.id)

    return undefined;
  }

  zoomInItem() {
    const current_zoom_lvl = this.getItemZoom();
    if (current_zoom_lvl){
      if (current_zoom_lvl + this.zoomIncrement <=  this.maxZoomLevel) {
        this.zoom.updateItemLvlZoom(this.item.id, current_zoom_lvl + this.zoomIncrement);
      }
      this.itemZoomedIn.emit();
    }
  }
  
  zoomOutItem() {
    const current_zoom_lvl = this.getItemZoom();
    if (current_zoom_lvl - this.zoomIncrement >=  this.minZoomLevel) {
      this.zoom.updateItemLvlZoom(this.item.id, current_zoom_lvl - this.zoomIncrement)
    }
    this.itemZoomedOut.emit();
  }

  getItemZoomDisplay(){
    return Math.floor( 100*(this.getItemZoom()/1) ) + '%';
  }

  getZoomInSlug() {
    return 'btn_zoom_in';
  }

  getZoomOutSlug() {
    return 'btn_zoom_out';
  }
}

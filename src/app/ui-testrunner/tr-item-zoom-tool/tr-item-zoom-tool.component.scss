.item-zoom-control-draggable {
    position: relative;
    //transform: translate(-50%, 0%); //this makes sure the item is truly centered horizontally, but causes the drag handle to twitch
    // left: 35%;
    z-index: 100;
    width: 7em;
    opacity: 0.7;
    &:hover {
        opacity: 1;
    }
    .item-zoom-control-container {
        background-color: #2A304B;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 0 1em rgba(0, 0, 0, 0.2);
        border-radius: 0.7em;
        height: 1.5em;

        .item-level-zoom-btn {
            display: flex;
            margin: 0.3em;
            img{
                width: 1.5rem;
                height: 1.5rem;
            }
        }

        .zoom-in-btn {
            padding-right: 0.2em;
        }

        .zoom-out-btn {
            padding-left: 0.2em;
        }
    }
    .zoom-value-display{
        font-size: 0.95em
    }
}
<div cdkDrag #container class="item-zoom-control-draggable">
    <div cdkDragHandle class="item-zoom-control-container">
        <button class="toolbar-icon item-level-zoom-btn zoom-in-btn" (click)="zoomInItem()" [tooltip]="lang.tra(getZoomInSlug())" placement="bottom" [attr.aria-label]="lang.tra(getZoomInSlug())">
            <img aria-hidden="true" src="https://d3azfb2wuqle4e.cloudfront.net/user_uploads/2329038/authoring/zoom-in/1666378095532/zoom-in.png">
        </button>
        <div class="zoom-value-display" style="color:#fff">{{getItemZoomDisplay()}}</div>
        <button class="toolbar-icon item-level-zoom-btn zoom-out-btn" (click)="zoomOutItem()" [tooltip]="lang.tra(getZoomOutSlug())" placement="bottom" [attr.aria-label]="lang.tra(getZoomOutSlug())">
            <img  draggable="FALSE" aria-hidden="true" src="https://d3azfb2wuqle4e.cloudfront.net/user_uploads/2329038/authoring/zoom-out/1666378160109/zoom-out.png">
        </button>
    </div>
</div>
import { Component, Input, OnInit } from '@angular/core';
import { IContentElementTemplate } from './model';
import { ElementType, IContentElement, QuestionState } from '../models';
import { QuestionPubSub } from '../question-runner/pubsub/question-pubsub';
import { StyleprofileService } from 'src/app/core/styleprofile.service';
import { QUESTION_RUNNER_WIDTH } from '../test-runner/test-runner.component';
import { IContentElementCanvas } from '../element-render-canvas/model';
import { IContentElementFrame } from '../element-render-frame/model';

@Component({
  selector: 'element-render-template',
  templateUrl: './element-render-template.component.html',
  styleUrls: ['./element-render-template.component.scss']
})
export class ElementRenderTemplateComponent implements OnInit {

  @Input() element:IContentElementTemplate;
  @Input() isLocked:boolean;
  @Input() isShowSolution:boolean;
  @Input() questionState:QuestionState;
  @Input() changeCounter:number;
  @Input() questionPubSub?: QuestionPubSub;

  constructor(
    
  ) { }

  ngOnInit(): void {
  }

  computeFontScaleByBaseWidth(baseWidth:number){
    let fontScale = 1
    if (baseWidth && QUESTION_RUNNER_WIDTH < baseWidth){
      fontScale = QUESTION_RUNNER_WIDTH / baseWidth;
    }
    return fontScale;
  }

  getFontScale(element:IContentElement){
    let fontScale = 1;
    if (element.elementType === ElementType.CANVAS){
      const elementCanvas = <IContentElementCanvas> element;
      fontScale = this.computeFontScaleByBaseWidth(elementCanvas.width);
    }
    if (element.elementType === ElementType.FRAME){
      const elementFrame = <IContentElementFrame> element;
      fontScale = this.computeFontScaleByBaseWidth(elementFrame.width);
    }
    return fontScale;
  }

  getElementStyle(element:IContentElement){
    const fontScale = this.getFontScale(element);
    const style = {
      'font-size': fontScale+'em',
    }
    return style
  }

}

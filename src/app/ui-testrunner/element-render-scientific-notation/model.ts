import { IContentElement, IEntryStateScored, IScoredResponse } from "../models";

export interface IContentElementScientificNotation extends IContentElement, IScoredResponse {
  whole: string,
  fractional: string,
  exponent: string,
  isNegativeCoefficient?: boolean;
  isNegativeExponent?: boolean;
  isFixedWhole?: boolean;
  isFixedFractional?: boolean;
  isFixedExponent?: boolean;
  isFixedExponentSign?: boolean;
  isFixedCoefficientSign?: boolean;
  isAllowMultipleAnswers?: boolean;
  answerSets?: IScientificNotationAnswer[];
}

export const scientificNotationEditInfo = {
  editExcludeFields: []
}

export interface IScientificNotationAnswer {
  whole: string,
  fractional: string,
  exponent: string,
  isNegativeCoefficient?: boolean;
  isNegativeExponent?: boolean;
}

export interface IEntryStateInputScientificNotation extends IEntryStateScored {
  whole: string,
  fractional: string,
  exponent: string,
  coefficientSign: string,
  exponentSign: string,
  expression: string,
}

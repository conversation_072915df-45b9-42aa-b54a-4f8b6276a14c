import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ElementRenderScientificNotationComponent } from './element-render-scientific-notation.component';

describe('ElementRenderScientificNotationComponent', () => {
  let component: ElementRenderScientificNotationComponent;
  let fixture: ComponentFixture<ElementRenderScientificNotationComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ElementRenderScientificNotationComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ElementRenderScientificNotationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});

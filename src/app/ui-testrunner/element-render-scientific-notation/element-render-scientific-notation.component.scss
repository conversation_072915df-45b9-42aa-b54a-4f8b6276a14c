.digit-input {
  width: 2em;
  &.is-compact {
    width: 1.5em;
    height: 2em;
    padding: 0 0.2em;
  }
}
input {
  font-family: inherit;
}


$selectedColor: #209cee;
.custom-dropdown {
  width: 2em;
  position: relative;  
  border: 1px solid $selectedColor;
  /* border: 1px solid #ccc; */
  border-radius: 4px;
  padding: 10px;

  button {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    width: 100%;
    height: 100%;
    font-size: inherit;
    font-family: inherit;
  }
  
  .chevron {
    color: $selectedColor;
  }
  
  .dropdown-header {
    cursor: pointer;
  }
}

.custom-dropdown-menu {
  
  font-size: 21px;
  width: 2em;
  
  position: absolute;
  top: 100%; 
  left: 0;
  right: 0;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: white;
  z-index: 10;
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1);
  
  button {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    width: 100%;
    height: 100%;
    font-size: inherit;
    font-family: inherit;
  }
  .dropdown-item {
    padding: 10px;
    cursor: pointer;

    &:hover {
      background-color: #f0f0f0;
    }
  }
}
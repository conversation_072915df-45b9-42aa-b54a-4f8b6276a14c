<div>
  
  <ng-container *ngIf="element.isFixedCoefficientSign">
    <span *ngIf="element.isNegativeCoefficient" style="margin-right: 0.3em;">&minus;</span>
  </ng-container>
  <ng-container *ngIf="!element.isFixedCoefficientSign">
    <div style="display:inline-block; margin-right: 0.3em;">
      <div class="custom-dropdown" style="padding: 0; height: 1.5em" [class.no-pointer-events]="isLocked">
        <button class="dropdown-header" (click)="toggleCoefficientSignDropdown()" #coefficientDropdownTrigger="cdkOverlayOrigin" cdkOverlayOrigin>
          <span *ngIf="coefficientSignSelected">{{ coefficientSignSelected === '-' ? '&minus;' : coefficientSignSelected }}</span>
          <span *ngIf="!coefficientSignSelected" style="font-size: 0.6em;" class="chevron">
            <i *ngIf="isCoefficientSignDropdownOpen" class="fas fa-chevron-up" name="dropdown-icon-up"></i>
            <i *ngIf="!isCoefficientSignDropdownOpen" class="fas fa-chevron-down" name="dropdown-icon-down"></i>
          </span>
        </button>
        <ng-template cdkConnectedOverlay
          [cdkConnectedOverlayOrigin]="coefficientDropdownTrigger"
          [cdkConnectedOverlayOpen]="isCoefficientSignDropdownOpen"
          [cdkConnectedOverlayHasBackdrop]="false"
          [cdkConnectedOverlayOffsetY]="4"
        >
          <div class="custom-dropdown-menu">
            <button (click)="selectCoefficientSign('+')" class="dropdown-item" #coefficientDropdownFirstButton>+</button>
            <button (click)="selectCoefficientSign('-')" class="dropdown-item">&minus;</button>
          </div>
        </ng-template>
      </div>
    </div>
  </ng-container>
  
  <ng-container *ngIf="element.isFixedWhole">
    <span>{{element.whole}}</span>
  </ng-container>
  <ng-container *ngIf="!element.isFixedWhole">
    <span *ngFor="let digitInput of wholeDigitInput; let listIndex = index;">
      <input 
        #digitInput
        [readonly]="this.isLocked"
        type="text" 
        class="input digit-input" 
        [class.is-compact]="isSupportCompactInputStyle()"
        [formControl]="digitInput" 
        (focus)="onNumericInputFocusIn(digitInput)"
        (blur)="onNumericInputFocusOut()"
        [style.margin-left]="listIndex == 0 ? '0' : '0.3em'"
        [maxlength]="1"
        (keydown)="onDigitInputKeyDown($event, listIndex, 'whole')"
        [style.font-size]="getFontSizeForInput()" 
        style="vertical-align:middle; text-align:center" >
    </span>
  </ng-container>
  <span style="margin-left:0.3em; margin-right:0.3em;">
    <tra slug="decimal_delim"></tra>
  </span>
  <ng-container *ngIf="element.isFixedFractional">
    <span>{{element.fractional}}</span>
  </ng-container>
  <ng-container *ngIf="!element.isFixedFractional">
    <span *ngFor="let digitInput of fractionalDigitInput; let listIndex = index;">
      <input 
        #digitInput 
        [readonly]="this.isLocked"
        type="text" 
        class="input digit-input" 
        [class.is-compact]="isSupportCompactInputStyle()"
        [formControl]="digitInput" 
        (focus)="onNumericInputFocusIn(digitInput)"
        (blur)="onNumericInputFocusOut()"
        [style.margin-left]="listIndex == 0 ? '0' : '0.3em'"
        [maxlength]="1"
        (keydown)="onDigitInputKeyDown($event, listIndex, 'fractional')"
        [style.font-size]="getFontSizeForInput()" 
        style="vertical-align:middle; text-align:center" >
    </span>
  </ng-container>
  <span style="margin-left: 0.3em; margin-right: 0.3em;">&times; 10</span>
  
  <ng-container *ngIf="element.isFixedExponent">
    <span *ngIf="element.isNegativeExponent"><sup>&minus;</sup></span>
    <span><sup>{{element.exponent}}</sup></span>
  </ng-container>
  <ng-container *ngIf="!element.isFixedExponent">
    
    <span style="vertical-align: 1em;">
      
      <ng-container *ngIf="element.isFixedExponentSign">
        <span *ngIf="element.isNegativeExponent" style="margin-right: 0.3em;">&minus;</span>
      </ng-container>
      <ng-container *ngIf="!element.isFixedExponentSign">
        <div style="display:inline-block; margin-right: 0.3em;" [style.font-size.em]="getExponentFontSizeFactor()">
          <div class="custom-dropdown" style="padding: 0; height: 1.5em" [class.no-pointer-events]="isLocked">
            <button class="dropdown-header" (click)="toggleExponentSignDropdown()" #exponentDropdownTrigger="cdkOverlayOrigin" cdkOverlayOrigin>
              <span *ngIf="exponentSignSelected">{{ exponentSignSelected === '-' ? '&minus;' : exponentSignSelected }}</span>
              <span *ngIf="!exponentSignSelected" style="font-size: 0.6em;" class="chevron">
                <i *ngIf="isExponentSignDropdownOpen" class="fas fa-chevron-up" name="dropdown-icon-up"></i>
                <i *ngIf="!isExponentSignDropdownOpen" class="fas fa-chevron-down" name="dropdown-icon-down"></i>
              </span>
            </button>
            <ng-template cdkConnectedOverlay
              [cdkConnectedOverlayOrigin]="exponentDropdownTrigger"
              [cdkConnectedOverlayOpen]="isExponentSignDropdownOpen"
              [cdkConnectedOverlayHasBackdrop]="false"
              [cdkConnectedOverlayOffsetY]="4"
            >
              <div class="custom-dropdown-menu">
                <button (click)="selectExponentSign('+')" class="dropdown-item" #exponentDropdownFirstButton>+</button>
                <button (click)="selectExponentSign('-')" class="dropdown-item">&minus;</button>
              </div>
            </ng-template>
          </div>
        </div>
      </ng-container>
      
      <span *ngFor="let digitInput of exponentDigitInput; let listIndex = index;" [style.font-size.em]="getExponentFontSizeFactor()">
        <input 
          #digitInput 
          [readonly]="this.isLocked"
          type="text" 
          class="input digit-input" 
          [class.is-compact]="isSupportCompactInputStyle()"
          [formControl]="digitInput" 
          (focus)="onNumericInputFocusIn(digitInput)"
          (blur)="onNumericInputFocusOut()"
          [style.margin-left]="listIndex == 0 ? '0' : '0.3em'"
          [maxlength]="1"
          (keydown)="onDigitInputKeyDown($event, listIndex, 'exponent')"
          [style.font-size]="getFontSizeForInput()" 
          style="vertical-align:middle; text-align:center" >
      </span>
    </span>
  </ng-container>
    
</div>

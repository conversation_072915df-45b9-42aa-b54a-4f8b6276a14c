import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ReactiveFormsModule } from '@angular/forms';
import { UiPartialModule } from '../ui-partial/ui-partial.module';
import { FullCalendarModule } from '@fullcalendar/angular';
import { MarkdownModule } from 'ngx-markdown';
import { ViewTtCreateAccountComponent } from './view-tt-create-account/view-tt-create-account.component';
import { ViewTtDashboardComponent } from './view-tt-dashboard/view-tt-dashboard.component';
import { ViewTtSessionBookerComponent } from './view-tt-session-booker/view-tt-session-booker.component';
import { ViewTtSessionInviteComponent } from './view-tt-session-invite/view-tt-session-invite.component';
import { FusePipe } from './fuse.pipe';
import { ViewTtTestRunnerComponent } from './view-tt-test-runner/view-tt-test-runner.component';
import { ViewTtPastAttemptsComponent } from './view-tt-past-attempts/view-tt-past-attempts.component';
import { ViewTtReportComponent } from './view-tt-report/view-tt-report.component';
import { UiTestrunnerModule } from '../ui-testrunner/ui-testrunner.module';
import { ViewTtShowCamComponent } from './view-tt-show-cam/view-tt-show-cam.component';
import { UiTestTakerRoutingModule } from './ui-testtaker-routing.module';
import { FileAppealComponent } from './file-appeal/file-appeal.component';
import { ViewTtFileAppealComponent } from './view-tt-file-appeal/view-tt-file-appeal.component';
import { ViewTtRemoteInstrModalComponent } from './view-tt-remote-instr-modal/view-tt-remote-instr-modal.component';
@NgModule({
  imports: [
    CommonModule,
    RouterModule, 
    ReactiveFormsModule,
    UiPartialModule,
    MarkdownModule,
    UiTestrunnerModule,
    UiTestTakerRoutingModule
  ],
  declarations: [
    ViewTtCreateAccountComponent, 
    ViewTtDashboardComponent, 
    ViewTtSessionBookerComponent, 
    ViewTtSessionInviteComponent, 
    FusePipe, 
    ViewTtTestRunnerComponent, 
    ViewTtPastAttemptsComponent, 
    ViewTtReportComponent, 
    ViewTtShowCamComponent, 
    FileAppealComponent, 
    ViewTtFileAppealComponent, ViewTtRemoteInstrModalComponent, 
    // ViewStudentG9DashboardComponent, 
    // ViewStudentG9ExerciseComponent, 
    // ViewStudentG9AssessmentComponent
  ],
})
export class UiTesttakerModule { }

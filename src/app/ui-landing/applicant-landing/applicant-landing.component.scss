@import '../../../styles/page-types/standard.scss';
@import '../../../styles/page-types/landing.scss';
@import '../../../styles/partials/_media.scss';
@import '../../../styles/partials/_colors.scss';

.page-body {
    @extend %page-body;
}

.slab-splash {
    @extend %slab-splash;
    background-color: #293282;
    background-image: url('https://d3azfb2wuqle4e.cloudfront.net/user_uploads/2329038/authoring/math_shapes/1666377171667/math_shapes.png');
    .splash-callout-container {
        .splash-callout {
            background-color: #FFD600;
            color: #000;
            strong {
                color: #000;
            }
        }
    }
}

.slab-info {
    @extend %slab-info;
    .main-access-button {
        display:flex;
        flex-direction: row;
        align-items: center; 
        .button {
            margin-right:1em;
        }
        .button.is-success {
            // 
        }
    }
}

a.button {
    white-space: normal;
    height: auto;
}
import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { FormGroup, FormControl } from '@angular/forms';
import { ScrollService } from '../../core/scroll.service';
import { AccountType } from '../../constants/account-types';
import { LoginGuardService } from '../../api/login-guard.service';
import { LangService } from '../../core/lang.service';
import { Subscription } from 'rxjs';
import { RoutesService } from '../../api/routes.service';
import { AuthService } from '../../api/auth.service';
import { WhitelabelService } from 'src/app/domain/whitelabel.service'

@Component({
  selector: 'forgot-password',
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.scss']
})
export class ForgotPasswordComponent implements OnInit, OnDestroy {

  public breadcrumb = [];
  public isFormValidated: boolean;
  public accountType: AccountType;
  private routeSub: Subscription;

  public formGroup = new FormGroup({
    emailAddress: new FormControl(),
  });

  constructor(
    public lang: LangService,
    private loginGuard: LoginGuardService, //
    private breadcrumbsService: BreadcrumbsService,
    private whiteLabel: WhitelabelService,
    private router: Router,
    private route: ActivatedRoute,
    private auth: AuthService,
    private routes: RoutesService,
    private scrollService: ScrollService,
    private whitelabelService: WhitelabelService
  ) { }

  ngOnInit() {
    this.loginGuard.deactivate();
    this.scrollService.scrollToTop();
    this.routeSub = this.route.params.subscribe(routeParams => {
      this.accountType = <AccountType> routeParams['accountType'];
      this.initRouteView();
    });
  }
  initRouteView() {
    this.breadcrumb = [
      this.breadcrumbsService._CURRENT( this.lang.tra(this.getAdminLoginSlug()), `/${this.lang.c()}/login-educator`),
      this.breadcrumbsService._CURRENT( this.lang.tra('title_forgot_password'), this.router.url),
    ];
    //_HEAD(this.accountType),
  }
  getAdminLoginSlug = () => this.whiteLabel.getSiteText( 'login_admins', 'lbl_administrators');

  ngOnDestroy() {
    if (this.routeSub) {
      this.routeSub.unsubscribe();
    }
  }

  getLoginRoute() {
    return `/${this.lang.c()}/general/login`;
  }

  submitForm() {
    if (this.isFormValidated) {
      return;
    }
    const email = this.formGroup.controls.emailAddress.value;
    if (email) {
      this.formGroup.controls.emailAddress.disable();
      const langCode = this.lang.c();
      const domain = this.auth.getDomain();
      const params = {isSMCS: this.isSMCS() ? 1 : 0};
      this.auth
        .apiCreate(
          this.routes.AUTH_RESET_PASSWORD_REQUEST,
          {
            email,
            langCode,
            domain,
          },params
        )
        .then(noInfoRes => {
          this.scrollService.scrollToTop();
          this.isFormValidated = true;
        });
    }
  }

  isSMCS(){
    return this.whitelabelService.getSiteFlag('IS_SMCS');
  }

  isEqaoStyle = () => {
    return this.whitelabelService.getSiteFlag('EQAO_LOGIN')
  }

}

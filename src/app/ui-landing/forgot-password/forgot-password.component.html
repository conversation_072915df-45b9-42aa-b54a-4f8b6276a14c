<div class="page-body" [class.is-eqao-bg]="isEqaoStyle()">
    <div>
      <header
      [breadcrumbPath]="breadcrumb"
      [isLoginPage]="isEqaoStyle()"
      ></header>
      <div class="page-content is-fullpage">
        <div class="form-content">

          <h2 class="strong">
            <tra slug="title_forgot_password"></tra>
          </h2>
          <div class="form-instruction-major">
            <tra-md slug="lbl_fp_instr"></tra-md>
          </div>

          <form [formGroup]="formGroup" (ngSubmit)="submitForm()"> 

            <div class="field">
              <label class="label">
                <tra slug="lbl_fp_email"></tra>
              </label>
              <div class="control">
                <input class="input" [formControl]="formGroup.controls.emailAddress">
              </div>
            </div>
  
            <div>
              <input  
                type="submit" 
                [disabled]="isFormValidated" 
                class="button is-main is-fullwidth"
                [value]="lang.tra('btn_continue')"
              />
            </div>
          </form>

          <div *ngIf="isFormValidated" style="margin-top:2em;">
            <tra-md slug="lbl_fp_instr_post"></tra-md>
            <div style="margin-top:1em;">
              <a [routerLink]="getLoginRoute()">
                <tra slug="title_login"></tra>
              </a>
            </div>
          </div>
  
        </div>
        
      </div>
    </div>
    <footer [hasLinks]="true" *ngIf="!isEqaoStyle()"></footer>
  </div>
  
import {animate, style, transition, trigger} from '@angular/animations';
import {formatDate} from '@angular/common';
import {Component, OnInit, Input} from '@angular/core';
import moment from 'moment';
import { LangService } from 'src/app/core/lang.service';
import { generateSanitizedRecords } from 'src/app/scoring-leader-utils/sanitizeTableRows';
import {IMenuTabConfig} from 'src/app/ui-partial/menu-bar/menu-bar.component';
import {AuthService} from '../../api/auth.service';
import { RoutesService } from 'src/app/api/routes.service';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { PageModalController, PageModalService } from 'src/app/ui-partial/page-modal.service';
import { saveAs } from 'file-saver';


enum IInvalitePageModal {
  DUPLICATE_TASKS = "DUPLICATE_TASKS"
}

const BLANK_FORM = {
    french:false,
    language:null,
    wave_id: null,
    name: '',
    invitation_subject: 'EQAO Scoring Invitation',
    // new_user_content: `email_acct_invite_scor_scor`,
    // existing_user_content: `email_acct_invite_scor_scor_ret`,
    invitation_message:'',
    message: '',
    send_date: null,
    create_deadline: null,
    complete_training_deadline: null,
    start_scoring_deadline: null,
    hours:null,
    minutes:null
};

@Component({
    selector: 'panel-sl-invites',
    templateUrl: './panel-sl-invites.component.html',
    styleUrls: ['./panel-sl-invites.component.scss'],
    animations: [trigger('inOutAnimation', [
            transition(':enter', [
                style({height: 0, opacity: 0}),
                animate('1s ease-out', style({height: 300, opacity: 1}))
            ]),
            transition(':leave', [
                style({height: 300, opacity: 1}),
                animate('1s ease-in', style({height: 0, opacity: 0}))
            ])
        ])]
})
export class PanelSlInvitesComponent implements OnInit {

    @Input() markingWindowId : number;
    @Input() markingWindowGroupId : number;
    @Input() markingWindowEmailInviteMsg;
    @Input() isLocked : boolean;

    views : any[] = [
        {
            id: 'waves',
            caption: 'Waves'
        }, {
            id: 'users',
            caption: 'Users'
        }
    ];

    pageModal: PageModalController;
    IInvalitePageModal = IInvalitePageModal;
    now;
    inviteHourOptions: { value: number; label: string }[] = [];

    selectedView = 'waves'

    invitations : any[];
    invitationWaves : any[];
    sanitizedInvitations: any[] = [];

    showNewWaveForm = false;
    configureDeadlines = false;
    showDefaultMessageContent = false;
    invitationSubject = this.lang.tra('lbl_invit_wave_subject_line');
    newWaveForm = {
        ...BLANK_FORM
    };
    defaultColDef = {
        resizable: true,
        sortable: true,
        filter: true
    }
    isNewInviteSending:boolean = false;

    showRangeFinderModal: boolean = false;

    wavesColumnDefs = [
        {
            headerName: 'ID',
            field: 'id',
            checkboxSelection: true,
            headerCheckboxSelection: true,
            headerCheckboxSelectionFilteredOnly: true,
        }, {
            headerName: 'Name',
            field: 'name',
        }, {
          headerName: 'Type',
          field: 'is_rafi',
          valueGetter: params => params.data.is_rafi ? 'Range Finder' : 'Scorer'
        },
        {
            headerName: 'Message',
            field: 'message',
        }, {
            headerName: 'Participants',
            field: 'num_participants',
        }, {
            headerName: 'Sent?',
            valueGetter: (params) => {
                return params.data.sent
                    ? 'Yes'
                    : 'No';
            },
        }, {
            headerName: 'Send Date',
            valueGetter: (params) => {
                return formatDate(params.data.send_date, 'medium', 'en-US');
            },
        }, {
            headerName: 'Create Account By',
            valueGetter: (params) => {
                return params.data.create_deadline
                    ? formatDate(params.data.create_deadline, 'medium', 'en-US')
                    : 'n/a';
            },
        }, {
            headerName: 'Complete Training By',
            valueGetter: (params) => {
                return params.data.complete_training_deadline
                    ? formatDate(params.data.complete_training_deadline, 'medium', 'en-US')
                    : 'n/a';
            },
        }, {
            headerName: 'Start Scoring By',
            valueGetter: (params) => {
                return params.data.start_scoring_deadline
                    ? formatDate(params.data.start_scoring_deadline, 'medium', 'en-US')
                    : 'n/a';
            },
        }
    ];

    invitesColumnDefs = [
        {
            headerName: 'UID',
            field: 'uid',
            checkboxSelection: true,
            headerCheckboxSelection: true,
            headerCheckboxSelectionFilteredOnly: true,
        }, {
          headerName: 'Profile ID',
          field: 'profile_id'
        }, {
            headerName: 'Name',
            valueGetter: (params) => {
                return (params.data.first_name || "") + " " + (params.data.last_name || "")
            },
        }, {
            headerName: 'Invite Email',
            field: 'invit_email',
        }, {
            headerName: 'Invited On',
            valueGetter: (params) => {
                return formatDate(params.data.send_date, 'medium', 'en-US');
            },
        }, {
            headerName: 'Wave ID',
            field: 'invite_wave_id',
        }, {
            headerName: 'Wave Name',
            field: 'name',
        }, {
            headerName: 'Message',
            field: 'invitation_message',
        }, {
            headerName: 'Has Accepted?',
            field: 'is_claimed',
            valueGetter: (params) => params.data.is_claimed ? "Yes" : "No",
        }, {
          headerName: 'Revoked?',
          field: 'is_not_revoked',
          valueGetter: (params) => !params.data.is_not_revoked ? "Yes" : "No",
        }, {
            headerName: 'Create Account By',
            valueGetter: (params) => {
                return params.data.create_deadline
                    ? formatDate(params.data.create_deadline, 'medium', 'en-US')
                    : 'n/a';
            },
        }, {
            headerName: 'Complete Training By',
            valueGetter: (params) => {
                return params.data.complete_training_deadline
                    ? formatDate(params.data.complete_training_deadline, 'medium', 'en-US')
                    : 'n/a';
            },
        }, {
            headerName: 'Start Scoring By',
            valueGetter: (params) => {
                return params.data.start_scoring_deadline
                    ? formatDate(params.data.start_scoring_deadline, 'medium', 'en-US')
                    : 'n/a';
            },
        }
    ];

    importRecordColumnDefs = [
        {
            headerName: 'Name',
            valueGetter: (params) => {
                return params.data.first_name + " " + params.data.last_name
            }
        }, {
            headerName: 'Email',
            field: 'contact_email'
        }, {
            headerName: 'New user?',
            valueGetter: (params) => {
                return params.data.uid
                    ? 'No'
                    : 'Yes'
            }
        }, {
            headerName: 'Item ID',
            field: 'window_item_id'
        }, {
            headerName: 'Item Slug',
            field: 'slug'
        }, {
            headerName: 'Item Caption',
            field: 'caption'
        }
    ];
    
    constructor(
        private auth : AuthService,
        public lang: LangService,
        private routes: RoutesService,
        private loginGuard: LoginGuardService,
        private pageModalService: PageModalService,
    ) {
        this.now = new Date()
    }

    isLoaded = false;

    configureQueryParams() {
        return {
        query: {
            marking_window_group_id: this.markingWindowGroupId,
            markingWindowId: this.markingWindowId
        }
        }
      }

    async reload() {
        this.isLoaded = false;
        const invitationData = await this
            .auth
            .apiGet(this.routes.SCOR_LEAD_INVITE_WAVE, this.markingWindowId, this.configureQueryParams());

        // Some returned records are just waves with no user invitations inside, exclude those from display
        this.invitations = invitationData.filter(i => i.uid)

        let waves = {};
        for (let i of invitationData) {
            if (!waves[i.wave_id]) {
                waves[i.wave_id] = [];
            }
            const row: any = generateSanitizedRecords(this.invitesColumnDefs, i);
            this.sanitizedInvitations.push(row);
            waves[i.wave_id].push(i);
        }

        let wavesArr = [];
        for (let w in waves) {
            let recordsInWave = waves[w];
            wavesArr.push({
                id: recordsInWave[0].wave_id,
                name: recordsInWave[0].name,
                message: recordsInWave[0].invitation_message,
                num_participants: recordsInWave.length,
                is_rafi: recordsInWave[0].is_rafi,
                sent: recordsInWave[0].sent,
                send_date: recordsInWave[0].send_date,
                create_deadline: recordsInWave[0].create_deadline,
                complete_training_deadline: recordsInWave[0].complete_training_deadline,
                start_scoring_deadline: recordsInWave[0].start_scoring_deadline
            });
        }

        this.invitationWaves = wavesArr;
        this.isLoaded = true;

    }

    ngOnInit() : void {
      this.pageModal = this.pageModalService.defineNewPageModal();
      this.generateHourOptions();
    }

    ngOnChanges(): void {
      if (this.markingWindowId && this.markingWindowGroupId){
        this.reload();
      }
    }

    /**
     * Returns the standard email message for the variant, in the selected language
     * @param target The message variant, either `new_user_content` or `existing_user_content`
     * @returns The message
     */
    getInviteMessage(target:string):string{
      const currLang = this.selectedLang()
      return this.markingWindowEmailInviteMsg[target]?.[currLang]
    }
    
    selectView(viewId : any) {
        this.selectedView = viewId;
    }

    toggleNewWaveForm() {
        this.showNewWaveForm = !this.showNewWaveForm;

        this.formMode = 'New';

        this.newWaveForm = {
            ...BLANK_FORM
        };

        this.importRecord = null;
        this.fileName = '';
        this.showImportRecord = false;
        this.configureDeadlines = false;
        this.showDefaultMessageContent = false;

        this.reload();
    }

    isForcingLangChange:boolean;
    forceLangChange(){
        this.isForcingLangChange = true;
        setTimeout(() => {
            this.isForcingLangChange = false;
        }, 300);
    }

    formMode;

    toggleEditWaveForm() {
        this.showNewWaveForm = !this.showNewWaveForm;

        this.formMode = 'Edit';

        let selectedWave = this.selectedWave();
        if(selectedWave) {
            this.newWaveForm = {
                ...selectedWave
            }
            this.newWaveForm.send_date = new Date(this.newWaveForm.send_date);
            this.newWaveForm.french = false;
            this.newWaveForm.wave_id = selectedWave.id
            this.newWaveForm.hours = this.newWaveForm.send_date.getHours()
            this.newWaveForm.invitation_message = this.newWaveForm.message;
        }
        else {
            this.newWaveForm = {...BLANK_FORM};
        }
       

        this.importRecord = null;
        this.fileName = '';
        this.showImportRecord = false;
        this.configureDeadlines = !!this.newWaveForm.complete_training_deadline || !!this.newWaveForm.create_deadline || !!this.newWaveForm.start_scoring_deadline;
        this.showDefaultMessageContent = false;
        this.reload();
    }

    showImportRecord;
    importRecord;
    fileName;
    
    async fileChange(fileList : FileList) {
      for (let i = 0; i < fileList.length; i++) {
          const file = fileList.item(i);

          const fileExt = file
              .name
              .split('.')
              .pop();

          if (fileExt !== 'xlsx') {
              this.loginGuard.quickPopup("Unsupported file type. Please upload an excel (.xlsx) file.")
              return;
          }

          this.showImportRecord = true;
          this.importRecord = null;
          this.importRecord = await this
              .auth
              .xlsxToInvitation(file, this.markingWindowId, this.markingWindowGroupId)
              .catch(e => {
                  this.loginGuard.quickPopup(e.message)
                  this.importRecord = e;
              });
          this.fileName = file.name
      }
    }

    openInput() {
        document
            .getElementById("fileInput")
            .click();
    }

    selectedLang(){
        return this.newWaveForm.french ? 'fr' : 'en';
    }

    validateNewInvitationWave() {
        return this.newWaveForm.name && (this.isValidImportRecord() || this.newWaveForm.wave_id) && this.newWaveForm.send_date;
    }

    async createInvitationWave() {

        this.isNewInviteSending = true;

        if(this.newWaveForm.french) {
          this.newWaveForm.language = 'FR'
        }
        else {
          this.newWaveForm.language = 'EN'
        }


        //reappending the time
        this.newWaveForm.send_date = new Date(this.newWaveForm.send_date);
        this.newWaveForm.send_date = new Date(this.newWaveForm.send_date.getFullYear(), this.newWaveForm.send_date.getMonth(), this.newWaveForm.send_date.getDate())
        this.newWaveForm.send_date.setHours(this.newWaveForm.hours)


        this.newWaveForm.send_date = this
            .newWaveForm
            .send_date
            .toISOString()
            .slice(0, 19)
            .replace('T', ' ');
        this.newWaveForm.create_deadline = this.newWaveForm.create_deadline
            ? this
                .newWaveForm
                .create_deadline
                .toISOString()
                .slice(0, 19)
                .replace('T', ' ')
            : null;
        this.newWaveForm.complete_training_deadline = this.newWaveForm.complete_training_deadline
            ? this
                .newWaveForm
                .complete_training_deadline
                .toISOString()
                .slice(0, 19)
                .replace('T', ' ')
            : null;
        this.newWaveForm.start_scoring_deadline = this.newWaveForm.start_scoring_deadline
            ? this
                .newWaveForm
                .start_scoring_deadline
                .toISOString()
                .slice(0, 19)
                .replace('T', ' ')
            : null;

        let invalidError = false;
        if (this.newWaveForm.wave_id) {
            await this
                .auth
                .apiPatch(this.routes.SCOR_LEAD_INVITE_WAVE, this.newWaveForm.wave_id, {
                    ...this.newWaveForm,
                    importRecord: this.importRecord
                }, this.configureQueryParams());
        } else {
            await this
                .auth
                .apiCreate(this.routes.SCOR_LEAD_INVITE_WAVE, {
                    ...this.newWaveForm,
                    invitation_subject: this.invitationSubject,
                    importRecord: this.importRecord
                }, this.configureQueryParams())
                .catch((e) => {
                    if (e.message === 'INVALID_WINDOW_ITEM_ID') {
                        this.loginGuard.quickPopup('alert_msg_invalid_window_item_id');
                        invalidError = true;
                    }
                });
        }

        if (!invalidError) this.toggleNewWaveForm();
        this.isNewInviteSending = false;
    }

    gridApi;
    gridColumnApi;

    async onGridReady(params) {
        this.gridApi = params.api;
        this.gridColumnApi = params.columnApi;
    }

    selectedWave() {
        if(!this.gridApi || !this.gridApi.getSelectedNodes()) return null;
        
        let selectedNodes = this
            .gridApi
            .getSelectedNodes();
        let selectedData = selectedNodes.map(node => node.data);

        if (selectedData.length != 1) 
            return null;
        
        return selectedData[0];
    }

    isValidImportRecord() {
        return this.importRecord && Array.isArray(this.importRecord) && this.importRecord.length > 0
    }

    getExportFilename() {
        if (this.selectedView == 'waves') {
            return 'invitations-waves' +  moment().format('YYYY-MM-DD[T]HH_mm_ss');
        } else if (this.selectedView == 'users') {
            return 'invitations-users' +  moment().format('YYYY-MM-DD[T]HH_mm_ss');
        }
    }


    cModal() { return this.pageModal.getCurrentModal(); }
    cmc() { return this.cModal().config; }

    async checkDuplicateMimt(){
      const query = {marking_window_group_id: this.markingWindowGroupId}
      const duplicateTasks = await this.auth.apiGet(this.routes.SCOR_LEAD_DUPLICATE_MARKING_TASKS, this.markingWindowId, {query})
      if (!Object.keys(duplicateTasks).length) {
        this.loginGuard.quickPopup('No duplicate tasks found in this marking window.')
      } else {
        const config = { 
          duplicateTasks,
          closeCaption: 'btn_close',
         };
        this.pageModal.newModal({
          type: IInvalitePageModal.DUPLICATE_TASKS,
          config,
          finish: () => {
            this.pageModal.closeModal();
          }
        })
      }
    }

    removeDuplicateMimt(){
      const query = {marking_window_group_id: this.markingWindowGroupId}
      this.auth.apiRemove(this.routes.SCOR_LEAD_DUPLICATE_MARKING_TASKS, this.markingWindowId, {query})
      .then((res) => {
        this.pageModal.closeModal();
        const numRecordsRemoved = res.unlinkedMimtIds.length;
        this.loginGuard.quickPopup(`${numRecordsRemoved} task records were unlinked`);
      })
      .catch((err) => {
        this.loginGuard.quickPopup(`Error: ${err.message}`)
      })
    }

    downloadDuplicateLog(){
      const { duplicateTasks } = this.cmc();
      const jsonExport = JSON.stringify(duplicateTasks);
      const blob = new Blob([jsonExport], { type: 'application/json' });
      saveAs(blob, `duplicate_mimt_mw${this.markingWindowId}.json`);
    }

    generateHourOptions() {
      this.inviteHourOptions = Array.from({ length: 24 }, (_, i) => ({
        value: i,
        label: moment({ hour: i }).format('h:mm a')
      }));
    }
  

}

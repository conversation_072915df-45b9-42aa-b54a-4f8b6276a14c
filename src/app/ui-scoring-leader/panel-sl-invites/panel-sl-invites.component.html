<panel-sl-rafi-invites *ngIf="showRangeFinderModal" [markingWindowId]="markingWindowId" [markingWindowGroupId]="markingWindowGroupId" [mode]="'invite'" (close)="showRangeFinderModal = false;" (reload)="reload()"></panel-sl-rafi-invites>

<div class="modal-container" *ngIf="showNewWaveForm">
  <div class="modal-contents">
        <h1>{{formMode}} Invitation Wave</h1> 
        <span>
            <span class="lang" [class.is-bold]="!newWaveForm.french">
                EN
            </span>
            <mat-slide-toggle 
                [(ngModel)]="newWaveForm.french" 
                color="primary"
                (change)="forceLangChange()"
            ></mat-slide-toggle>
            <span class="lang" [class.is-bold]="newWaveForm.french">
                FR
            </span>
        </span>
        <br/><br/>
        <mat-form-field appearance="fill">
            <mat-label>Name of Wave</mat-label>
            <input matInput placeholder="Name" [(ngModel)]="newWaveForm.name">
        </mat-form-field>
        <div class="upload-field">
            <p *ngIf="formMode == 'New'">Upload a scorer spreadsheet using <a [href]="lang.tra('link_inv_templ_mrkg_mrkr')">this template</a>.</p>
            <p *ngIf="formMode == 'Edit'">If you want to update the invitees, upload a new scorer spreadsheet using <a [href]="lang.tra('link_inv_templ_mrkg_mrkr')">this template</a>.</p>
            <ng-container>
                <button [disabled]="isLocked" mat-raised-button (click)="openInput()">{{isValidImportRecord() ? "Reupload file" : "Upload file"}}</button>
                <input id="fileInput" hidden type="file" (change)="fileChange($event.target.files)" name="file" accept=".csv,.xlsx" (click)="$event.target.value=null">        
            </ng-container>
            <ng-container *ngIf="importRecord">
                <ng-container *ngIf="!isValidImportRecord()">
                    <p class="err">{{importRecord}}</p>
                </ng-container>
                <ng-container *ngIf="isValidImportRecord()">
                    <p class="f-name">{{fileName}}</p>
                    <p>The following scorers will be invited to this test window:</p>
                    <ag-grid-angular
                    style="width: auto; height: 200px; max-width:100%;"
                    class="ag-theme-alpine"
                    [rowData]="importRecord"
                    [columnDefs]="importRecordColumnDefs"
                    [defaultColDef]="defaultColDef"
                    [rowSelection]="'multiple'"
                    (gridReady)="onGridReady($event)">
                    </ag-grid-angular>
                </ng-container>
                
            </ng-container>
        </div>
        <br/>
        <mat-form-field appearance="fill">
            <mat-label>Subject</mat-label>
            <input matInput placeholder="Name" [(ngModel)]="invitationSubject">
        </mat-form-field>
        <div class="content-field">
            <mat-slide-toggle [(ngModel)]="showDefaultMessageContent" color="primary">
                Show default email content ({{selectedLang()}})
            </mat-slide-toggle>
            <div 
                *ngIf="showDefaultMessageContent && !isForcingLangChange" 
                class="email-content-container"
            >
                <div class="left">
                    <h5>
                        New Users
                    </h5>

                    <div class="markdown-container">
                        {{getInviteMessage('new_user_content')}}
                    </div>
                    
                </div>
                <div class="right">
                    <h5>
                        Existing Users
                    </h5>
                    <div class="markdown-container">
                        {{getInviteMessage('existing_user_content')}}
                    </div>
                   
                </div>
               
            </div>
            
        </div>
        <br/>
        <mat-form-field appearance="fill">
            <mat-label>Additional Message</mat-label>
            <textarea matInput [(ngModel)]="newWaveForm.invitation_message"></textarea>
        </mat-form-field>
        <br/>
        <div class="date-time">
            <div class="date-container">
                <mat-form-field appearance="fill">
                    <mat-label>Send Date</mat-label>
                    <input [min]="now" matInput [matDatepicker]="sendDatePicker" [(ngModel)]="newWaveForm.send_date">
                    <mat-datepicker-toggle matSuffix [for]="sendDatePicker"></mat-datepicker-toggle>
                    <mat-datepicker #sendDatePicker></mat-datepicker>
                </mat-form-field>
            </div>
            <div class="time-container"> 
                <mat-form-field appearance="fill">
                    <mat-label>Time</mat-label>
                    <mat-select [(ngModel)]='newWaveForm.hours'>
                      <mat-option *ngFor="let hour of inviteHourOptions" [value]="hour.value">
                        {{ hour.label }}
                      </mat-option>
                    </mat-select>
                </mat-form-field>
            </div>
        </div>
        
        <mat-slide-toggle [(ngModel)]="configureDeadlines" color="primary">
            Configure Deadlines
        </mat-slide-toggle>
        <ng-container *ngIf="configureDeadlines" [@inOutAnimation]>
            <mat-form-field appearance="fill">
                <mat-label>Create Account By</mat-label>
                <input [min]="now" matInput [matDatepicker]="createDeadlinePicker" [(ngModel)]="newWaveForm.create_deadline">
                <mat-datepicker-toggle matSuffix [for]="createDeadlinePicker"></mat-datepicker-toggle>
                <mat-datepicker #createDeadlinePicker></mat-datepicker>
            </mat-form-field>
            <mat-form-field appearance="fill">
                <mat-label>Complete Training By</mat-label>
                <input [min]="now" matInput [matDatepicker]="completeTrainingDeadlinePicker" [(ngModel)]="newWaveForm.complete_training_deadline">
                <mat-datepicker-toggle matSuffix [for]="completeTrainingDeadlinePicker"></mat-datepicker-toggle>
                <mat-datepicker #completeTrainingDeadlinePicker></mat-datepicker>
            </mat-form-field>
            <mat-form-field appearance="fill">
                <mat-label>Start Scoring By</mat-label>
                <input [min]="now" matInput [matDatepicker]="startScoringDeadlinePicker" [(ngModel)]="newWaveForm.start_scoring_deadline">
                <mat-datepicker-toggle matSuffix [for]="startScoringDeadlinePicker"></mat-datepicker-toggle>
                <mat-datepicker #startScoringDeadlinePicker></mat-datepicker>
            </mat-form-field>
        </ng-container>
        <div class="flex" style="display:flex; justify-content:flex-end;">
            <button [disabled]="isLocked" mat-flat-button (click)="toggleNewWaveForm()"><tra slug="btn_cancel"></tra></button>
            <button [disabled]="isLocked || !validateNewInvitationWave() || isNewInviteSending" mat-flat-button (click)="createInvitationWave()" color="primary" matTooltip="The invitations will not be sent out until the specified send date."><tra slug="lbl_send"></tra></button>
        </div>
    </div>
</div>


<button [disabled]="isLocked" (click)="toggleNewWaveForm()" class="button">Invite Scorers</button>

<button [disabled]="isLocked" (click)="showRangeFinderModal = true" class="button">Invite Range Finders</button>

<menu-bar 
[menuTabs]="views"
[tabIdInit]="selectedView"
(change)="selectView($event)"
></menu-bar>

<ng-container *ngIf="isLoaded && selectedView == 'waves'">
    <div style="margin-bottom:1em; display:flex; flex-direction:row; justify-content: space-between;">
        <div>
            <!-- <button class="button" [disabled]="!gridApi || !selectedWave()" (click)="toggleEditWaveForm()">Edit Wave</button> -->
        </div>
        <div>
            <export-table-contents [tableContents]="invitationWaves" [filename]=getExportFilename()></export-table-contents>
        </div>
    </div>
    <ag-grid-angular
        class="ag-theme-alpine ag-grid-fullpage"
        [rowData]="invitationWaves"
        [columnDefs]="wavesColumnDefs"
        [defaultColDef]="defaultColDef"
        [rowSelection]="'multiple'"
        (gridReady)="onGridReady($event)">
    </ag-grid-angular>
</ng-container>
<ng-container *ngIf="isLoaded && selectedView == 'users'">
    <div style="margin-bottom:1em; display:flex; flex-direction:row; justify-content: space-between;">
        <div>
           You may export the filtered data below using the button on the right.
        </div>
        <div>
            <export-table-contents [tableContents]="sanitizedInvitations" [filename]=getExportFilename()></export-table-contents>
        </div>
    </div>
    <div>
        <ag-grid-angular
            style="width: auto; height: 600px; max-width:100%;"
            class="ag-theme-alpine"
            [rowData]="invitations"
            [columnDefs]="invitesColumnDefs"
            [defaultColDef]="defaultColDef"
            rowSelection="multiple"
            [enableCellTextSelection]="true"
            (gridReady)="onGridReady($event)">
        </ag-grid-angular>
    </div>
</ng-container>

<div style="margin-top: 2em">
  <button class="button" (click)="checkDuplicateMimt()">Check for false duplicate scorer tasks in marking window {{markingWindowId}}</button>
</div>


<div class="modal-container" *ngIf="cModal()">
  <div [ngSwitch]="cModal().type">
      <div *ngSwitchCase="IInvalitePageModal.DUPLICATE_TASKS" class="modal-contents" style="width: 40em;">
        <p class="has-text-centered">This window has duplicate tasks.</p>
        <div style="display: flex; justify-content: center; margin-top: 1em">
          <button class="button" (click)="downloadDuplicateLog()">Download details</button>
          <button class="button is-danger" (click)="removeDuplicateMimt()">Remove duplicates</button>
        </div>
        <modal-footer [pageModal]="pageModal" [confirmButton]="false"></modal-footer>

      <div>
    </div>
</div>

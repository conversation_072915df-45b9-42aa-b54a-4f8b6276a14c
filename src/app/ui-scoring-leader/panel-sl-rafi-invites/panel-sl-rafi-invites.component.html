<div class="modal-container">
    <div class="modal-contents">
        <div *ngIf="mode == 'invite'">

          <h1>New Range Finder Invitation Wave</h1> 

          <mat-form-field appearance="fill">
            <mat-label>Name of Wave</mat-label>
            <input matInput placeholder="Name" [(ngModel)]="waveName">
          </mat-form-field>

          <span>
            <span>EN</span>
            <mat-slide-toggle [(ngModel)]="isInviteFr" color="primary" (change)="changeInviteLang()"></mat-slide-toggle>
            <span>FR</span>
          </span>

          <br/><br/>

          <div class="upload-field">
            <p>Upload a range finder spreadsheet using <a [href]="lang.tra('link_inv_wave_templ_mrkg_rafi')">this template</a>.</p>

            <ng-container>
              <button [disabled]="isLocked" mat-raised-button (click)="openInput()">{{isValidImportRecord() ? "Reupload file" : "Upload file"}}</button>
              <input id="fileInput" hidden type="file" (change)="fileChange($event.target.files)" name="file" accept=".csv,.xlsx" (click)="$event.target.value=null">        
            </ng-container>
            <ng-container *ngIf="importRecord">
                <ng-container *ngIf="!isValidImportRecord()">
                    <p class="err">{{importRecord}}</p>
                </ng-container>
                <ng-container *ngIf="isValidImportRecord()">
                    <p class="f-name">{{fileName}}</p>
                    <p>The following range finders will be invited to this test window:</p>
                    <ag-grid-angular
                    style="width: auto; height: 200px; max-width:100%;"
                    class="ag-theme-alpine"
                    [rowData]="importRecord"
                    [defaultColDef]="defaultColDef"
                    [columnDefs]="importRecordColumnDefs"
                    [rowSelection]="'multiple'"
                    (gridReady)="onGridReady($event)">
                    </ag-grid-angular>
                </ng-container>
                
            </ng-container>
          </div>

          <br/>
          <mat-form-field appearance="fill">
              <mat-label>Subject</mat-label>
              <input matInput placeholder="Name" [(ngModel)]="invitationSubject">
          </mat-form-field>

          <div class="content-field">
            <mat-slide-toggle [(ngModel)]="showDefaultMessageContent" color="primary">
                Show default email content ({{selectedLang()}})
            </mat-slide-toggle>

            <div *ngIf="showDefaultMessageContent" class="email-content-container">
              <div class="left">
                  <h5>New Users</h5>
                  <div class="markdown-container">{{inviteMessageNew}}</div>
              </div>
              <div class="right">
                  <h5>Existing Users</h5>
                  <div class="markdown-container">{{inviteMessageExisting}}</div>
              </div>
            </div>
            
          </div>

          <br/>
          <mat-form-field appearance="fill">
              <mat-label>Additional Message</mat-label>
              <textarea matInput [(ngModel)]="additionalMessage"></textarea>
          </mat-form-field>
          <br/>

          <br/>
          <div class="date-time">
              <div class="date-container">
                  <mat-form-field appearance="fill">
                      <mat-label>Send Date</mat-label>
                      <input [min]="now" matInput [matDatepicker]="sendDatePicker" [(ngModel)]="sendDate">
                      <mat-datepicker-toggle matSuffix [for]="sendDatePicker"></mat-datepicker-toggle>
                      <mat-datepicker #sendDatePicker></mat-datepicker>
                  </mat-form-field>
              </div>
              <div class="time-container"> 
                  <mat-form-field appearance="fill">
                      <mat-label>Time</mat-label>
                      <mat-select [(ngModel)]='sendHour'>
                        <mat-option *ngFor="let hour of inviteHourOptions" [value]="hour.value">
                          {{ hour.label }}
                        </mat-option>
                      </mat-select>
                  </mat-form-field>
              </div>
          </div>
          
          
            <br>
            <div class="flex" style="display:flex; justify-content:flex-end;">
              <button mat-flat-button (click)="cancel()"><tra slug="btn_cancel"></tra></button>
              <button mat-flat-button color="primary" [disabled]="isLocked || isNewInviteSending || !isValidImportRecord() || !sendDate || !waveName" (click)="invite()" matTooltip="The invitations will not be sent out until the specified send date."><tra slug="lbl_send"></tra></button>
            </div>
        </div>
        <div *ngIf="mode == 'revoke'">
            <div *ngFor="let account of accounts">
                {{account.first_name}} {{account.last_name}} - {{account.contact_email}}
            </div>
            <br>
            <div class="example-button-row">
                <button mat-flat-button color="primary" (click)="revoke()"><tra slug="mrkg_revoke"></tra></button>
                &nbsp;&nbsp;&nbsp;<button mat-flat-button (click)="cancel()"><tra slug="btn_cancel"></tra></button>
            </div>
        </div>
    </div>
</div>

import { Component, EventEmitter, Input, Output } from '@angular/core';
import { ReceiveVideoStreamIndexTask } from 'amazon-chime-sdk-js';
import { AuthService } from 'src/app/api/auth.service';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { RoutesService } from 'src/app/api/routes.service';
import { LangService } from 'src/app/core/lang.service';
import moment from 'moment';

@Component({
  selector: 'panel-sl-rafi-invites',
  templateUrl: './panel-sl-rafi-invites.component.html',
  styleUrls: ['./panel-sl-rafi-invites.component.scss']
})
export class PanelSlRafiInvitesComponent {

  @Input() accounts: any[];
  @Input() mode: string;
  @Input() markingWindowId: number;
  @Input() markingWindowGroupId: number;
  @Input() isLocked : boolean;

  @Output() close = new EventEmitter<boolean>();
  @Output() reload = new EventEmitter<boolean>();

  showImportRecord;
  importRecord;
  fileName;

  gridApi;
  gridColumnApi;

  isInviteFr:boolean;

  waveName: string;
  invitationSubject:string = this.lang.tra("subj_email_acct_invite_scor_rafi", "en");
  inviteMessageNew:string = this.lang.tra("email_acct_invite_scor_rafi", "en");
  inviteMessageExisting:string = this.lang.tra("email_acct_invite_scor_rafi_ret", "en");
  additionalMessage:string;

  showDefaultMessageContent:boolean = false;

  sendDate;
  sendHour;
  now = new Date();
  inviteHourOptions: { value: number; label: string }[] = [];


  constructor(
    private auth:AuthService,
    private routes: RoutesService,
    public lang: LangService,
    private loginGuard: LoginGuardService
  ) {}

  ngOnInit(): void {
    this.generateHourOptions();
  }

  isNewInviteSending: boolean = false;
  /** Send the invites for range finding */
  async invite() {
    this.isNewInviteSending = true;
    const query = { 
      marking_window_group_id: this.markingWindowGroupId,
      marking_window_id: this.markingWindowId
    }

    const importRecord = this.importRecord.map(r => {
      return {
        contact_email: r.contact_email,
        first_name: r.first_name,
        last_name: r.last_name,
        uid: r.uid
      }
    })

    const data = {
      name: this.waveName,
      invitation_subject: this.invitationSubject,
      invitation_message: this.additionalMessage,
      send_date: this.getSendDate(),

      language: this.selectedLang(),
      importRecord
    }
    await this.auth.apiCreate(this.routes.SCOR_LEAD_INVITE_RAFI, data, {query})
    this.isNewInviteSending = false;
    this.reload.emit(true);
    this.close.emit(true);
  }

  async revoke() {
    await this.auth.apiRemove(this.routes.SCOR_LEAD_INVITE_RAFI, null, {
      query: {
        marking_window_group_id: this.markingWindowGroupId,
        uids: this.accounts.map(acc => acc.uid),
      }
    })
    this.reload.emit(true);
    this.close.emit(true);
  }
  
  cancel() {
    this.close.emit(true);
  }

  openInput() {
    document
    .getElementById("fileInput")
    .click();
  }

  /** Check the data in the uploaded excel file, store the user records */
  async fileChange(fileList : FileList) {
    for (let i = 0; i < fileList.length; i++) {
        const file = fileList.item(i);
        const fileExt = file.name.split('.').pop();

        if (fileExt !== 'xlsx') {
            return this.loginGuard.quickPopup("Unsupported file type. Please upload an excel (.xlsx) file.")
        }
        this.showImportRecord = true;
        this.importRecord = null;
        this.importRecord = await this.auth.xlsxToInvitation(file, this.markingWindowId, this.markingWindowGroupId, true)
        .catch(e => {
            this.loginGuard.quickPopup(e.message)
            this.importRecord = e;
        });
        this.fileName = file.name
    }
  }

  isValidImportRecord() {
    return this.importRecord && Array.isArray(this.importRecord) && this.importRecord.length > 0
  }

  async onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
  }

  defaultColDef = {
    sortable: true,
    resizable: true,
    filter: true
  }

  importRecordColumnDefs = [
    { headerName: 'Name', valueGetter: (params) => params.data.first_name + " " + params.data.last_name }, 
    { headerName: 'Email', field: 'contact_email'}, 
    { headerName: 'New user?', valueGetter: (params) => params.data.uid ? 'No' : 'Yes'},
  ];

  /** When language is toggled, change the language of the template strings */
  changeInviteLang(){
    const currLang = this.selectedLang();
    this.invitationSubject = this.lang.tra("subj_email_acct_invite_scor_rafi", currLang);
    this.inviteMessageNew = this.lang.tra("email_acct_invite_scor_rafi", currLang);
    this.inviteMessageExisting = this.lang.tra("email_acct_invite_scor_rafi_ret", currLang);
  }

  selectedLang(){
    return this.isInviteFr ? 'fr' : 'en';
  }

  getSendDate(){
    const date = new Date(this.sendDate);
    const sendDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())
    sendDate.setHours(this.sendHour ?? "0")
    return sendDate.toISOString().slice(0, 19).replace('T', ' ');
  }

  generateHourOptions() {
    this.inviteHourOptions = Array.from({ length: 24 }, (_, i) => ({
      value: i,
      label: moment({ hour: i }).format('h:mm a')
    }));
  }

}

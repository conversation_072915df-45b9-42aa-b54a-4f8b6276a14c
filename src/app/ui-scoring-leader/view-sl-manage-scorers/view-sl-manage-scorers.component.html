<div class="page-body">
  <div>

    <header 
      [breadcrumbPath]="breadcrumb" 
      [hasSidebar]="true"
      [hideMessageCentre]="true"
      techSupportDataKey="SCORING_SUPPORT"
    ></header>
      
<div class="page-content is-fullpage" >
    <div class="page-container">
      <div class="content-container">

          <!-- <menu-bar 
            [menuTabs]="views"
            [tabIdInit]="selectedView"
            (change)="selectView($event)"
          ></menu-bar> -->
          <h1>{{selectedView ? selectedView.caption : 'Accounts'}}</h1>
          <ul *ngIf="!selectedView">
            <li><a (click)="navSubPage('invitations')">Invitations</a></li>
            <li><a (click)="navSubPage('scorers')">Scorers</a></li>
            <!-- <li><a (click)="navSubPage('supervisors')">Supervisors</a></li> -->
            <li><a (click)="navSubPage('range-finders')">Range Finders</a></li>
            <li><a (click)="navSubPage('leaders')">Leaders</a></li>
            <li><a (click)="navSubPage('cas-access')">CAS Access</a></li>
          </ul>
          <div  *ngIf="selectedView">
            <ng-container *ngIf="selectedView.id != 'invitations'">
              <div *ngIf="">

              </div>
              <panel-sl-accounts 
                [markingWindowId]="markingWindowId"
                [markingWindowGroupId]="markingWindowGroupId" 
                [selectedView]="selectedView"
                [isLocked]="isLocked"
                [isAllowClearScorerData]="isAllowClearScorerData"
              ></panel-sl-accounts>
            </ng-container>
            <ng-container *ngIf="selectedView.id == 'invitations'">
              <panel-sl-invites 
                [markingWindowId]="markingWindowId"
                [markingWindowGroupId]="markingWindowGroupId"
                [markingWindowEmailInviteMsg]="markingWindowEmailInviteMsg"
                [isLocked]="isLocked"
              ></panel-sl-invites>
            </ng-container>
            
          </div>
          <!-- <div [ngSwitch]="selectedView" *ngIf="markingWindowId">
            <ng-container *ngSwitchCase="ScorerMenuView.INVITES">
              <panel-sl-invites
                [markingWindowId]="markingWindowId"
              ></panel-sl-invites>
            </ng-container> -->
            <!-- <panel-sl-scorer-manage [markingWindowId]="markingWindowId" ></panel-sl-scorer-manage> -->
          <!-- </div> -->

          <!-- <div *ngIf="false">
              <img *ngIf="!isUploading" src="https://eqao.vretta.com/authoring/user_uploads/21/authoring/scorer%20stats/*************/scorer%20stats.png">
              <img *ngIf="isUploading" src="https://eqao.vretta.com/authoring/user_uploads/21/authoring/manage%20scorers/*************/manage%20scorers.png">
          </div> -->

          </div>
        </div>
      </div>
    </div>
    <footer [hasLinks]="false" techSupportDataKey="SCORING_SUPPORT"></footer>
</div>

<!-- <chat-box [accountType]="'leader'"></chat-box> -->

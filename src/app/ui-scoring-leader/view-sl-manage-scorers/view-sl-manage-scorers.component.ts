import { Component, On<PERSON>nit, <PERSON><PERSON><PERSON>roy, ViewChild, ElementRef } from '@angular/core';
import { SidepanelService } from '../../core/sidepanel.service';
import { UserSiteContextService } from '../../core/usersitecontext.service';
import { Router, ActivatedRoute } from '@angular/router';
import { WhitelabelService } from '../../domain/whitelabel.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { LangService } from '../../core/lang.service';
import { RoutesService } from '../../api/routes.service';
import { AccountType } from '../../constants/account-types';
import { DomSanitizer } from '@angular/platform-browser';
import { AuthService } from '../../api/auth.service';
import { IMenuTabConfig } from '../../ui-partial/menu-bar/menu-bar.component';


enum ScorerMenuView {
  INVITES = 'invitations',
  SCORERS = 'scorers',
  SUPERVISORS = 'supervisors',
  RANGE_FINDERS = 'range-finders',
  LEADERS = 'leaders',
  CAS = 'cas-access',
}
@Component({
  selector: 'view-sl-manage-scorers',
  templateUrl: './view-sl-manage-scorers.component.html',
  styleUrls: ['./view-sl-manage-scorers.component.scss']
})
export class ViewSlManageScorersComponent implements OnInit {

  constructor(
    public whitelabelService: WhitelabelService,
    private route: ActivatedRoute,
    private router: Router,
    private sidePanel: SidepanelService,
    private lang: LangService,
    private auth: AuthService,
    private userSiteContext: UserSiteContextService,
    public  loginGuard: LoginGuardService,
    public  sanitizer: DomSanitizer,
    private breadcrumbsService: BreadcrumbsService,
  ) { }

  markingWindowId:number;
  markingWindowGroupId: number;
  markingWindowEmailInviteMsg;
  breadcrumb:any[];
  ScorerMenuView = ScorerMenuView;
  isLocked:boolean = true;
  isAllowClearScorerData: boolean = false;

  selectedView = null;
  views: IMenuTabConfig<ScorerMenuView>[] = [
    {id: ScorerMenuView.INVITES, caption: 'Invitations' },
    {id: ScorerMenuView.SCORERS, caption: 'Scorers' },
    {id: ScorerMenuView.RANGE_FINDERS, caption: 'Range Finders' },
    {id: ScorerMenuView.LEADERS, caption: 'Leaders' },
    // {id: ScorerMenuView.SUPERVISORS, caption: 'Supervisors' },
    {id: ScorerMenuView.CAS, caption: 'CAS Access' },
  ];

  ngOnInit(): void {
    this.sidePanel.activate();
    this.sidePanel.unexpand();
    this.loginGuard.activate();
    this.route.params.subscribe(params => {
      this.markingWindowId = +params['markingWindow'];
      this.updateMarkingWindowInfo();
      this.selectedView = this.views.find(v => v.id == params['subView']);
      this.updateBreadcrumb()
    });
  }
  async updateMarkingWindowInfo(){
    this.isLocked = true;
    if (this.markingWindowId){
      const query = { isManageScorers: 1}
      const scoringWindowStatus = await this.auth.apiGet('public/scor-lead/windows', this.markingWindowId, { query });
      this.markingWindowGroupId = scoringWindowStatus.group_id
      this.markingWindowEmailInviteMsg = JSON.parse(scoringWindowStatus.invite_email_message || '{}')
      this.isLocked = scoringWindowStatus.is_locked == 1;
      this.isAllowClearScorerData = scoringWindowStatus.is_allow_clear_scorer_data == 1;
    }
  }
  selectView(viewId:ScorerMenuView){
    this.selectedView = viewId;
  }
  getBaseRoute(){
    return `/${this.lang.c()}/${AccountType.SCOR_LEAD}`;
  }
  updateBreadcrumb(){
    // getTrainingTypeCaption(this.trainingType);
    let currentTabName = 'Accounts Test Window '+this.markingWindowId;
    const baseRoute = this.getBaseRoute();
    this.breadcrumb = [
      this.breadcrumbsService._CURRENT( this.lang.tra('lbl_osslt_scorlead_dashboard'), `${baseRoute}/dashboard`),
      this.breadcrumbsService._CURRENT( currentTabName, `${baseRoute}/accounts/${this.markingWindowId}`),
    ];

    if(this.selectedView) {
      this.breadcrumb.push(this.breadcrumbsService._CURRENT( this.selectedView.caption, this.router.url))
    }
  }

  navSubPage(subpage) {
    this.router.navigate([subpage], {relativeTo: this.route} )
  }

}

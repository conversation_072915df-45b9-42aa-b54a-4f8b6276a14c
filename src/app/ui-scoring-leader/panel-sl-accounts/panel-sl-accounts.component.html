
<panel-sl-new-message *ngIf="showMessageCentreModal" [markingWindowId]="markingWindowId" [markingWindowGroupId]="markingWindowGroupId" [prefillRecipients]="rowsSelected()" (close)="showMessageCentreModal = false;"></panel-sl-new-message>
<panel-sl-rafi-invites *ngIf="showRangeFinderModal" [markingWindowId]="markingWindowId" [markingWindowGroupId]="markingWindowGroupId" [accounts]="rowsSelected()" [mode]="rangeFinderModalMode" (close)="showRangeFinderModal = false;" (reload)="loadAccounts()"></panel-sl-rafi-invites>

<div style="margin-bottom: 1em;">
    <button *ngIf="selectedView.id == 'invitations'" [disabled]="isLocked">
        New Scorer Invitation Wave
    </button>
    <div style="margin-bottom:1em; display:flex; flex-direction:row; justify-content: space-between;">
        <div>
            <button *ngIf="selectedView.id == 'scorers'" (click)="messageSelected()" [disabled]="isLocked || (rowsSelected().length < 1)" class="button is-small">
                Message Scorer<ng-container *ngIf="rowsSelected().length > 1">s</ng-container>
            </button>
            <button *ngIf="selectedView.id == 'scorers'" (click)="initUnlinkScorerDataModal()" [disabled]="isLocked || !isAllowClearScorerData || (rowsSelected().length < 1)" class="button is-small is-danger is-light">
              Clear Scorer Data
            </button>
            <button *ngIf="selectedView.id == 'range-finders'" (click)="revokeRangeFinder()" [disabled]="isLocked || (rowsSelected().length < 1)" class="button is-danger is-small">
                Revoke Access<ng-container *ngIf="rowsSelected().length > 1">s</ng-container>
            </button>
        </div>
        <div>
            <export-table-contents [tableContents]="sanitizedAccounts" [filename]="getExportFilename()"></export-table-contents>
        </div>
    </div>
</div>
<ag-grid-angular
class="ag-theme-alpine ag-grid-fullpage"
[rowData]="accounts"
[columnDefs]="columnDefs"
rowSelection="multiple"
[enableCellTextSelection]="true"
(firstDataRendered)="onFirstDataRendered($event)"
(gridReady)="onGridReady($event)">
</ag-grid-angular>


<div class="custom-modal" *ngIf="cModal()">
  <div [ngSwitch]="cModal().type" class="modal-contents" style="width:42em;">
    <div>
      <div *ngSwitchCase="ScorLeadAccountsModal.CLEAR_SCORER_DATA">
        <h2>Clear Scorer Data (mw #{{markingWindowId}})</h2>
        <p><tra-md slug="scor_lead_scorer_unlink_instr"></tra-md></p>
        <div class="notification">
          <p *ngFor="let scorer of cmc().selectedScorers">{{scorer.contact_email}}</p>
        </div>
        <button class="button is-danger is-rounded" (click)="confirmUnlinkScorerData()">Proceed</button>
        <modal-footer [pageModal]="pageModal" [confirmButton]="false" [closeMessage]="'btn_close'"></modal-footer>
      </div>
    </div>
  </div>
</div>

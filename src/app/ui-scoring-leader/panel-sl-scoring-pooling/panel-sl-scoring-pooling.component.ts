import { Component, Input, OnInit } from '@angular/core';
import { formatDate } from '@angular/common';
import { faRecordVinyl } from '@fortawesome/free-solid-svg-icons';
import { agGridNumberSort } from 'src/app/core/util/sort';
import { generateSanitizedRecords } from 'src/app/scoring-leader-utils/sanitizeTableRows';
import moment from 'moment';
import { AuthService } from '../../api/auth.service';
import { LangService } from '../../core/lang.service';
import { ColDef, GridOptions } from 'ag-grid-community';
import { RoutesService } from 'src/app/api/routes.service';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { saveAs } from 'file-saver';
import { DownloadLogButtonRendererComponent } from './download-log-button-renderer/download-log-button-renderer.component'
import * as _ from 'lodash';

@Component({
  selector: 'panel-sl-scoring-pooling',
  templateUrl: './panel-sl-scoring-pooling.component.html',
  styleUrls: ['./panel-sl-scoring-pooling.component.scss']
})
export class PanelSlScoringPoolingComponent implements OnInit {

  markingWindows: any[] = [];
  actionableWindows: any[] = [];
  selectedScoringWindowID: number | string;

  markingWindowIdsToPool: number[] = [];
  markingWindowIdsToScorInvalidate: number[] = [];
  includeSampleSchools: boolean = false;
  includeRecentReuploads: boolean = false;
  isAggressivePool: boolean = false;
  daysSinceReupload: number = 0;
  requireReviewForReuploads: boolean = true;

  poolingClickDisabled: boolean = false
  scanReviewPoolingClickDisabled: boolean = false
  refreshClickDisabled: boolean = false;
  ongoingPoolingMsg: string;

  isLeaderScoreOutdatedLoading:boolean = false;
  isInvalidatePending: boolean = false;
  selectedInvalidLog;
  invalidScorLogRecords: any[]= [];
  refreshInvalidScorClickDisabled: boolean = false;

  poolingRecords:any[] = [];

  constructor(
    private auth:AuthService,
    private lang:LangService,
    private routes: RoutesService,
    private loginGuard: LoginGuardService
  ) { }

  ngOnInit(): void {
    this.loadWindows();
    this.loadScoringPooling();
    this.loadInvalidScorLogs();
  }

  async loadWindows(){
    this.selectedScoringWindowID = 'all';
    const windowRecords = await this.auth.apiFind(this.routes.SCOR_LEAD_WINDOWS, {
      query: {
        mode: 'scoring-pooling',
      }
    })

    this.markingWindows = windowRecords.map((record, i) => {
      return {
        id: record.window_id,
        caption: record.window_name,
        dateFrom: moment(record.start_on).format('MMM. D, YYYY'),
        dateTo: moment(record.end_on).format('MMM. D, YYYY'),
        stats: {},
        meta:{
          markingWindowId: record.window_id,
          isActive: record.is_active == 1,
          isArchived: record.is_archived == 1,
          isPaused: record.is_paused == 1,
          group_id: record.group_id
        }
      }
    })

    this.actionableWindows = this.markingWindows.filter(mw => mw.meta.isActive && !mw.meta.isArchived && !mw.meta.isPaused)

    this.markingWindows.unshift({
      id: 'all',
      caption: 'All',
      stats: {},
      meta:{
        markingWindowId: 'all'
      },
    })
  }

  async loadInvalidScorLogs(){
    this.refreshInvalidScorClickDisabled = true
    this.invalidScorLogRecords = await this.auth.apiGet(this.routes.SCOR_LEAD_INVALIDATE_SCORES, -1)
    this.refreshInvalidScorClickDisabled = false;
  }

  async loadScoringPooling(){
    this.refreshClickDisabled = true;
    this.poolingRecords = await this.auth.apiFind(this.routes.SCOR_LEAD_SCORING_POOLING);
    for (let record of this.poolingRecords) {
      if (record.is_completed && record.updated_on == null) {
        record.startedOn = null;
        record.completedOn = record.created_on;
      } else {
        record.startedOn = record.created_on;
        record.completedOn = record.is_completed ? record.updated_on : null;
      }
    }
    // Determine if any poolings are ongoing now - to block starting new ones.
    const ongoingPoolingRecord = this.poolingRecords.find(record => record.is_ongoing)
    if (ongoingPoolingRecord){
      this.ongoingPoolingMsg = `Can not start new pooling - pooling started on ${this.formatDateEst(ongoingPoolingRecord.startedOn)} is in progress.`
    } else {
      this.ongoingPoolingMsg = null;
    }
    this.refreshClickDisabled = false;
  }

  selectedScoringWindow() {
    if(!this.markingWindows) return null;
    return this.markingWindows.find(mw => mw.meta.markingWindowId == this.selectedScoringWindowID);
  }

  async doPool() {
    this.poolingClickDisabled = true;
    this.refreshClickDisabled = true;
    this.auth.apiUpdate(this.routes.SCOR_LEAD_SCORING_POOLING, 0, { 
      isAggressivePool: this.isAggressivePool, 
      include_recent_reuploads: this.includeRecentReuploads, 
      include_sample_schools: this.includeSampleSchools, 
      days: this.daysSinceReupload, 
      require_review_for_reuploads: this.requireReviewForReuploads,
      markingWindowIdsToPool: this.markingWindowIdsToPool
    })
    .catch((e) => {
      // If pooling was started by someone else after the last records refresh, so new pooling was blocked, display an error
      if (e.code == 503 && e.message == "ONGOING POOLING") {
        const errorMsg = `Error starting new pooling - pooling started on ${this.formatDateEst(e.data.startedOn)} is in progress.`
        this.loginGuard.quickPopup(errorMsg)
      }
    })
    .finally(() => {
      // Give it 5sec to create log records for newly started poolings
      setTimeout(() => {
        // Refresh the records and the ongoing pooling message
        this.loadScoringPooling()
        .then(() => {
          this.poolingClickDisabled = false;
          this.refreshClickDisabled = false;
        });
      }, 5 * 1000);
    })
  }

  async doScanReviewPool() {
    this.scanReviewPoolingClickDisabled = true;
    this.auth.apiCreate(this.routes.SCOR_LEAD_SCAN_REVIEW_POOLING, { 
      isAggressivePool: this.isAggressivePool, 
      include_recent_reuploads: this.includeRecentReuploads, 
      include_sample_schools: this.includeSampleSchools, 
      days: this.daysSinceReupload, 
      require_review_for_reuploads: this.requireReviewForReuploads,
    })
    .finally(() => {
      this.scanReviewPoolingClickDisabled = false;
    })
  }

  formatDateEst = (inputDate) => {
    if (!inputDate) return null
    return formatDate(new Date(inputDate), 'medium', 'en-US') + " EST"
  }

  exportCSV(){
    const fileName = `Recent_pooling_history_${this.selectedScoringWindowID == "all" ? "ALL" : `MW_${this.selectedScoringWindowID}`}.csv`
    this.poolingGridOptions.api.exportDataAsCsv({
      fileName,
      shouldRowBeSkipped: (params) => {
        return params.node.rowIndex >= 20
      }
    });
  }

  dateFilterParams = {
    comparator: (filterLocalDateAtMidnight: Date, cellDate: Date) => {
      if (
        (filterLocalDateAtMidnight.getDate() == cellDate.getDate()) && 
        (filterLocalDateAtMidnight.getFullYear() == cellDate.getFullYear()) && 
        (filterLocalDateAtMidnight.getMonth() == cellDate.getMonth())
      ){
        return 0
      }
      else if (cellDate < filterLocalDateAtMidnight) return -1
      else if  (cellDate > filterLocalDateAtMidnight) return 1
    },
  };

  poolingColDefs: ColDef[] = [
    { 
      headerName:'ID', 
      field:'id', 
      width: 70, 
      comparator: agGridNumberSort, 
      filter: 'agNumberColumnFilter' 
    },
    { 
      headerName:'MW ID', 
      field:'marking_window_id', width:120, 
      comparator: agGridNumberSort, 
      filter: 'agNumberColumnFilter' 
    },
    { 
      headerName:'MW Name', 
      field:'marking_window_name', width:150
    },
    { 
      headerName:'Records Pooled', 
      field:'num_records', width:170, 
      comparator: agGridNumberSort, 
      filter: 'agNumberColumnFilter' 
    },
    { 
      headerName:'Started On', 
      field:'startedOn', 
      valueGetter: params => new Date(params.data.startedOn),
      cellRenderer: params => this.formatDateEst(params.data.startedOn),
      width:150,
      filter: 'agDateColumnFilter',
      filterParams: this.dateFilterParams
    },
    {
      headerName:'Last Updated On', 
      field:'updated_on', 
      valueGetter: params => new Date(params.data.updated_on),
      cellRenderer: params => this.formatDateEst(params.data.updated_on),
      width:150, 
      filter: 'agDateColumnFilter',
      filterParams: this.dateFilterParams
    },
    { 
      headerName:'Completed On', 
      field:'completedOn', 
      valueGetter: params => new Date(params.data.completedOn),
      cellRenderer: params => this.formatDateEst(params.data.completedOn),
      width:150, 
      filter: 'agDateColumnFilter',
      filterParams: this.dateFilterParams
    },
    { 
      headerName:'Status', 
      field:'is_completed', 
      valueGetter: params => {
        let status = ''
        if (params.data.is_completed){
          status = 'Completed'
        } else if (params.data.is_error){
          status = 'Error'
        } else if (params.data.is_uncompleted){
          status = 'Uncompleted'
        }
        else {
          status = 'Started'
        }
        if (params.data.alert_sent){
          status += ' (Alert Sent)'
        }
        return status;
      },
      width:110 
    }
  ]

  invalidScoreLogColDef: ColDef[] = [
    { 
      headerName:'ID', 
      field:'id', 
      width: 70, 
      comparator: agGridNumberSort, 
      filter: 'agNumberColumnFilter' 
    },
    { 
      headerName:'MW ID', 
      field:'marking_window_id', width:120, 
      comparator: agGridNumberSort, 
      filter: 'agNumberColumnFilter' 
    },
    { 
      headerName:'MW Name', 
      field:'marking_window_name', width:150
    },
    { 
      headerName:'Created By', 
      field:'created_by', width:150
    },
    { 
      headerName:'# Scores Invalidated', 
      field:'num_mcbr_invalidated', width:170, 
      comparator: agGridNumberSort, 
      filter: 'agNumberColumnFilter' 
    },
    { 
      headerName:'Started On', 
      field:'created_on', 
      valueGetter: params => new Date(params.data.created_on),
      cellRenderer: params => this.formatDateEst(params.data.created_on),
      width:150,
      filter: 'agDateColumnFilter',
      filterParams: this.dateFilterParams
    },
    { 
      headerName:'Completed On', 
      field:'completed_on', 
      valueGetter: params => new Date(params.data.completed_on),
      cellRenderer: params => this.formatDateEst(params.data.completed_on),
      width:150, 
      filter: 'agDateColumnFilter',
      filterParams: this.dateFilterParams
    },
    { 
      headerName:'Status', 
      field:'is_completed', 
      valueGetter: params => params.data.is_completed? 'Completed' : 'Started',
      cellRenderer: params => params.data.is_completed? 'Completed' : 'Started',
      width:110 
    },
    { 
      headerName: 'Log Data', 
      field: 'log_data', 
      width: 150,
      cellRenderer: 'downloadLogButtonRendererComponent',
      cellRendererParams: {
        clicked: this.onLogDownload.bind(this)
      },
      cellClass: 'ui-grid-vcenter',
    }
  ]

  onLogDownload(params) {
    const logId = params.data.id
    const logData = JSON.parse(params.data.log_data)
    this.downloadCsv(logData, Object.keys(logData[0]), `marking_invalidate_score_log_data_${logId}`)
  }

  defaultColDef: ColDef = {
    filter: true,
    sortable: true,
    resizable: true,
  }
  poolingGridOptions:GridOptions = {
    pagination: true,
    paginationPageSize: 20,
    suppressPaginationPanel: true,
    columnDefs: this.poolingColDefs,
    defaultColDef: this.defaultColDef,
    onRowDataChanged: () => this.applyWindowFilter(),
  };

  scorInvalidLogGridOptions:GridOptions = {
    pagination: true,
    paginationPageSize: 20,
    suppressPaginationPanel: true,
    columnDefs: this.invalidScoreLogColDef,
    defaultColDef: this.defaultColDef,
    frameworkComponents: {
      downloadLogButtonRendererComponent: DownloadLogButtonRendererComponent
    },
  };

  applyWindowFilter() {
    const targetWindow = this.selectedScoringWindowID
    const filterWindow = this.poolingGridOptions.api.getFilterInstance("marking_window_id");
    if (targetWindow == 'all') {
      filterWindow.setModel(null);
    } else {
      filterWindow.setModel({
        filter: targetWindow,
        filterType: "number",
        type: "equals"
      });
    }
    this.poolingGridOptions.api.onFilterChanged();
  }

  /**
   * Add or remove a marking window from the list of IDs to be pooled / have responses invalidated
   * @param $event - event to check or uncheck the box
   * @param targetWindowId - ID of the relevant marking window
   * @param targetWindowList - either markingWindowIdsToPool or markingWindowIdsToScorInvalidate
   */
  toggleWindowToPool($event, targetWindowId:number, targetWindowList:number[]){
    const isChecked = $event.target.checked
    if (isChecked) {
      targetWindowList.push(targetWindowId)
    }
    else {
      _.pull(targetWindowList, targetWindowId)
    }
  }

  /** Return if the button to start pooling is disabled */
  isPoolingBtnDisabled(){
    return (this.poolingClickDisabled || this.ongoingPoolingMsg || !this.markingWindowIdsToPool.length)
  }

  invalidateOutdatedScores(){
    this.isInvalidatePending = true;
    const data = {
      markingWindowIds: this.markingWindowIdsToScorInvalidate
    }
    this.auth.apiCreate(this.routes.SCOR_LEAD_INVALIDATE_SCORES, data)
    .then(() => {
      this.loadInvalidScorLogs();
    })
    .finally(() => {
      this.isInvalidatePending = false;
    })
  }

  downloadLeaderOutdatedScores(){
    this.isLeaderScoreOutdatedLoading = true;
    const query = {
      markingWindowIds: this.markingWindowIdsToScorInvalidate.join(",")
    }
    this.auth.apiFind(this.routes.SCOR_LEAD_INVALIDATE_SCORES, {query})
    .then(outdatedScorRecords => {
      if (!outdatedScorRecords.length) {
        this.loginGuard.quickPopup("No outdated records.")
      } else {
        this.loginGuard.confirmationReqActivate({
          caption: `${outdatedScorRecords.length} outdated leader score records found. Download as CSV?`,
          btnProceedConfig: {
            caption: "btn_download"
          },
          confirm: () => {
            this.downloadCsv(outdatedScorRecords, Object.keys(outdatedScorRecords[0]), "outdated_leader_scores")
          }
        })
      }
    })
    .catch(err => {
      this.loginGuard.quickPopup("Error loading records.")
    })
    .finally(() => {
      this.isLeaderScoreOutdatedLoading = false;
    })
  }

      
    /**
   * Download data as CSV
   * @param data - list of objects with headers as keys
   * @param header - list of header keys / strings
   * @param fileName - string to name the downloaded file
   */
    downloadCsv(data: any, header:string[], fileName:string) {
      const replacer = (key, value) => value === null ? '' : value;
      let csv = data.map(row => header.map(fieldName => JSON.stringify(row[fieldName], replacer)).join(','));
      csv.unshift(header.join(','));
      let csvArray = csv.join('\r\n');
      var blob = new Blob([csvArray], {type: 'text/csv' })
      saveAs(blob, fileName);
    }


}

<div class="page-body">
  <div>
    <header [breadcrumbPath]="breadcrumb" ></header>
    <div class="page-content is-fullpage">
      
      <div *ngIf="!isLoaded"> Loading... </div>
      
      <div  *ngIf="isLoaded && testWindowId" class="editing-container">
        <div class="window-info">
          <div>
            <div> 
              <strong>Test Window ID: </strong> 
              <code>{{testWindowId}}</code> 
            </div>
            <div 
              style="margin: 1rem 0 1rem 0; line-height:1.8rem" 
              class="test-title">
              {{getTestWindowTitle()}}
            </div>
            <div>
              <div style="margin: 0.3rem 0 0.3rem 0;">
                <strong>From:</strong> {{getTestWindowDateStart()}} 
                <span *ngIf="false" class="tag is-danger">Closed</span> 
              </div>
              <div>
                <strong>To</strong> {{getTestWindowDateEnd()}} 
                <span *ngIf="false" class="tag is-danger">Closed</span> 
              </div>
              <!-- <div>Anonymyzed Results: <a [routerLink]="getDataDashboard()">Data Dashboard</a></div> -->
            </div>
          </div>
        </div>

        <menu-bar
          [menuTabs]="mainMenuTabs"
          [tabIdInit]="currentMainMenuTab"
          (change)="selectMainMenuTab($event)"
        ></menu-bar>

        <div [ngSwitch]="currentMainMenuTab">
          <div *ngSwitchCase="TWMainMenuTab.CONFIGURE">

            <table style="width:auto;">
              <tr>
                <th>AC</th>
                <td><check-toggle [isChecked]="isPropNumericChecked(TWAttr.is_active)" (toggle)="togglePropCheckedNumeric(TWAttr.is_active)"></check-toggle></td>
                <td>Used for showing Test Windows to the School Admin/Teacher</td>
              </tr>
              <tr>
                <th>QA</th>
                <td><check-toggle [isChecked]="isPropNumericChecked(TWAttr.is_qa)" (toggle)="togglePropCheckedNumeric(TWAttr.is_qa)"></check-toggle></td>
                <td>Used for Quality Assurance or User Acceptance Testing</td>
              </tr>
              
              <tr>
                <th>BG</th>
                <td><check-toggle [isChecked]="isPropNumericChecked(TWAttr.is_bg)" (toggle)="togglePropCheckedNumeric(TWAttr.is_bg)"></check-toggle></td>
                <td>Background-use (can keep this on during configuration to avoid it showing up for users, while distinguishing this from a QA window intended for validation only)</td>
              </tr>
            </table>

            <h3>Testing Dates</h3>
            <div>
              <div>
                <p>The dates and times defined in this section determine the boundaries within which test administrators must create their test sessions.</p>
                <table style="width:auto;">
                  <tr>
                    <th style="width:4px;"></th>
                    <th>Start/End</th>
                    <th>Date</th>
                    <th>Time</th>
                  </tr>
                  <tr class="v-center">
                    <td style="background-color:#7BB658"></td>
                    <td>Starts On</td>
                    <td> <input-date [fc]="getStartDateFc()" [isDisabled]="!editDates.isEditing()"></input-date> </td>
                    <td> <input-time [fc]="getStartTimeFc()" [isAutoSubmission]="false" [isDisabled]="!editDates.isEditing()"></input-time> </td>
                  </tr>
                  <tr  class="v-center">
                    <td style="background-color:#5C5C5C"></td>
                    <td>Ends On</td>
                    <td> <input-date [fc]="getEndDateFc()" [isDisabled]="!editDates.isEditing()"></input-date> </td>
                    <td> <input-time [fc]="getEndTimeFc()" [isAutoSubmission]="false" [isDisabled]="!editDates.isEditing()"></input-time> </td>
                  </tr>
                </table>
              </div>
              <div>
                <br>
                <p><tra-md slug="lbl_reg_lock_on"></tra-md></p>
                <table style="width:auto;">
                  <tr>
                    <th style="width:4px;"></th>
                    <th>Reg Lock On</th>
                    <th>Date</th>
                    <th>Time</th>
                  </tr>
                  <tr class="v-center">
                    <td style="background-color:#7BB658"></td>
                    <td>Reg Lock On</td>
                    <td> <input-date [fc]="getRegLockDateFc()" [isDisabled]="!editDates.isEditing() || !myCtrlOrg.hasRole(UserRoles.TEST_CTRL_REG_CTRL)"></input-date> </td>
                    <td> <input-time [fc]="getRegLockTimeFc()" [isAutoSubmission]="false" [isDisabled]="!editDates.isEditing() || !myCtrlOrg.hasRole(UserRoles.TEST_CTRL_REG_CTRL)"></input-time> </td>
                  </tr>
                </table>
              </div>
              <edit-save [formlet]="editDates" ></edit-save>
            </div>

            <h3>Test Window Title & Description</h3>
            <p>You can name and describe the test window for test administrators and test takers in this section.</p>

            <div class="space-between">
              <table style="width:auto">
                <tr>
                  <th></th>
                  <th 
                    *ngFor="let langCode of nameFormTableDef.cols" 
                    [ngSwitch]="langCode">
                      {{getLangName(langCode)}}
                  </th>
                </tr>
                <tr *ngFor="let tableProp of nameFormTableDef.rows">

                  <td>
                    <strong [ngSwitch]="tableProp">
                      <tra 
                        *ngSwitchCase="TWAttr.title"  
                        slug="Title">
                      </tra>
                      <tra 
                        *ngSwitchCase="TWAttr.notes" 
                        slug="Description">
                      </tra>
                    </strong>
                  </td>
                  <td 
                    *ngFor="let langCode of nameFormTableDef.cols" 
                    [ngSwitch]="!!editName.isEditing()">

                      <div *ngSwitchCase="false">
                        {{fcv(getNameFormFC(tableProp,langCode))}}
                      </div>

                      <textarea 
                        *ngSwitchCase="true" 
                        class="textarea is-small" 
                        [formControl]="getNameFormFC(tableProp,langCode)">
                    </textarea>
                  </td>

                </tr>
              </table>
              <edit-save [formlet]="editName" ></edit-save>
            </div>

            <ng-template #twDescriptionCell let-attr="attr">
              <td class="quick-text-replace">
                <span>{{getPropString(attr)}}</span>
                <button (click)="patchPropValPrompt(attr)">Replace</button>
                <span *ngIf="isPropSaving(attr)" class="label is-small is-warning">Saving...</span>
              </td>
            </ng-template>
            
            <table style="width:auto; margin-top:2em">
              <tr>
                <td>Assessment Code (<code>type_slug</code>)</td>
                <ng-container *ngTemplateOutlet="twDescriptionCell; context: { attr: TWAttr.type_slug }"></ng-container>
              </tr>
              <tr>
                <td>Test Window month code (<code>window_code</code>)</td>
                <ng-container *ngTemplateOutlet="twDescriptionCell; context: { attr: TWAttr.window_code }"></ng-container>
              </tr>
              <tr>
                <td>Academic Year (<code>academic_year</code>) <br> Format YYYY-YYYY, ie: 2022-2023</td>
                <ng-container *ngTemplateOutlet="twDescriptionCell; context: { attr: TWAttr.academic_year }"></ng-container>
              <tr>
            </table>            

            <h3>Assessment Components & Test Window TD Components & Component Reporting Categories</h3>
            <p>Currently only Primary and Junior need to set up for assessment_component, test_window_td_components and tw_component_reporting_categories</p>
            <div *ngIf = "isCopyAvailable()" class="space-between">
              <button class="button is-small is-info" 
                      style="margin-top:0.75em;" 
                      [disabled]="!isCopyAvailable()"
                      (click)="copyAssessmentTwtdComponentModalStart(Modal.COPY_ASSESSMENT_TWTD_COMPONENTS)"
              >
                  Copy assessment_component, test_window_td_components and component_reporting_categories from previous test window
              </button>
            </div>
            <p *ngIf = "assessmentComponentRecord.length">Current Assessment Components:</p>
            <table *ngIf = "assessmentComponentRecord.length" style="width:auto">
              <tr>
                <th style="width:4px;"></th>
                <th>ID</th>
                <th>Assessment Code</th>
                <th>Map TD ID</th>
                <th>IS NO TD ORDER</th>
              </tr>
              <tr *ngFor = "let assessmentComponent of assessmentComponentRecord" class="v-center">
                <td style="background-color:#7BB658"></td>
                <td>{{assessmentComponent.id}}</td>
                <td>{{assessmentComponent.assessment_code}}</td>
                <td>{{assessmentComponent.map_td_id}}</td>
                <td>{{assessmentComponent.is_no_td_order}}</td>
              </tr>
            </table>
            <p *ngIf = "testWindowTdComponentRecord.length">Current Test Window Td Components:</p>
            <table *ngIf = "testWindowTdComponentRecord.length" style="width:auto">
              <tr>
                <th style="width:4px;"></th>
                <th>ID</th>
                <th>TYPE SLUG</th>
                <th>TWTDAR ORDER</th>
                <th>IS_SCANNABLE</th>
              </tr>
              <tr *ngFor = "let testWindowTdComponent of testWindowTdComponentRecord" class="v-center">
                <td style="background-color:#7BB658"></td>
                <td>{{testWindowTdComponent.id}}</td>
                <td>{{testWindowTdComponent.type_slug}}</td>
                <td>{{testWindowTdComponent.twtdar_order}}</td>
                <td>{{testWindowTdComponent.is_scannable}}</td>
              </tr>
            </table>
            <p *ngIf = "componentReportingCategoryRecord.length">Current Test Window Component Reporting Categories:</p>
            <table *ngIf = "componentReportingCategoryRecord.length" style="width:auto">
              <tr>
                <th style="width:4px;"></th>
                <th>ID</th>
                <th>COMPONENT SLUG</th>
                <th>REPORTING CATEGORY ID</th>
                <th>PSYCH PIPELINE SLUG</th>
              </tr>
              <tr *ngFor = "let componentReportingCategory of componentReportingCategoryRecord" class="v-center">
                <td style="background-color:#7BB658"></td>
                <td>{{componentReportingCategory.id}}</td>
                <td>{{componentReportingCategory.component_slug}}</td>
                <td>{{componentReportingCategory.reporting_category_id}}</td>
                <td>{{componentReportingCategory.psych_pipeline_slug}}</td>
              </tr>
            </table>

            <h3>Assessment Price</h3>
            <p>Please set up single assessment price for this test window.</p>
            <div class="space-between">
              <table style="width:auto">
                <tr *ngFor="let tableProp of priceFormTableDef.rows">
                  <td>
                    <strong [ngSwitch]="tableProp">
                      <tra 
                        *ngSwitchCase="APAttr.ap_price_per_student" 
                        slug="Assessment Price">
                      </tra>
                      <tra 
                        *ngSwitchCase="APAttr.ap_description" 
                        slug="Description">
                      </tra>
                    </strong>
                  </td>
                  <td [ngSwitch]="!!editPrice.isEditing()">
                    <div *ngSwitchCase="false">
                        {{fcv(getPriceFormFC(tableProp, ''))}}
                    </div>
                    <textarea 
                      *ngSwitchCase="true" 
                      placeholder="123.45" 
                      class="textarea is-small" 
                      [formControl]="getPriceFormFC(tableProp, '')">
                    </textarea>
                  </td>
                </tr>
              </table>
              <edit-save [formlet]="editPrice" ></edit-save>
            </div>

            <h3>School Semesters</h3>
            <p>Please set up semesters for this test window.</p>
            <div class="space-between">
              <button class="button is-small is-info" 
                      style="margin-top:0.75em;" 
                      (click)="copySchoolSemesterModalStart(Modal.COPY_SCHOOL_SEMESTERS)"
              >
                Copy school_semesters from previous test window
              </button>
            </div>
            <p *ngIf = "schoolSemesterRecord.length">Current Test Window Semesters:</p>
            <table *ngIf = "schoolSemesterRecord.length" style="width:auto">
              <tr>
                <th style="width:4px;"></th>
                <th>ID</th>
                <th>namespace</th>
                <th>foreign_scope_id</th>
                <th>foreign_id</th>
                <th>year</th>
                <th>name</th>
              </tr>
              <tr *ngFor = "let schoolSemester of schoolSemesterRecord" class="v-center">
                <td style="background-color:#7BB658"></td>
                <td>{{schoolSemester.id}}</td>
                <td>{{schoolSemester.namespace}}</td>
                <td>{{schoolSemester.foreign_scope_id}}</td>
                <td>{{schoolSemester.foreign_id}}</td>
                <td>{{schoolSemester.year}}</td>
                <td>{{schoolSemester.name}}</td>
              </tr>
            </table>

            <h3>Test Window Auto Submission</h3>
            <div class="space-between">
              <table style="width:auto;">
                <tr>
                  <th style="width:4px;"></th>
                  <th>Start On</th>
                  <th>Close On</th>
                  <th>Grace Period (mins)</th>
                  <th>Extension Period (mins)</th>
                </tr>
                <tr class="v-center">
                  <td style="background-color:#7BB658"></td>
                  <td>
                    <input-time [fc]="getAutoSubmissionStartTimeFc()" [isAutoSubmission]="true" [isDisabled]="!editAutoSubmission.isEditing()"></input-time>
                  </td>
                  <td>
                    <input-time [fc]="getAutoSubmissionEndTimeFc()" [isAutoSubmission]="true" [isDisabled]="!editAutoSubmission.isEditing()"></input-time>
                  </td>
                  <td>
                    <div [ngSwitch]="!!editAutoSubmission.isEditing()">
                      <div *ngSwitchCase="false"> {{fcv(getAutoSubmissionFormFC('auto_close_grace_period_m', ''))}}</div>
                      <input type="number" *ngSwitchCase="true" class="textarea is-small" [formControl]="getAutoSubmissionFormFC('auto_close_grace_period_m', '')"/>
                    </div>
                  </td>
                  <td>
                    <div [ngSwitch]="!!editAutoSubmission.isEditing()">
                      <div *ngSwitchCase="false">{{fcv(getAutoSubmissionFormFC('auto_close_default_extension_m', ''))}}</div>
                      <input type="number" *ngSwitchCase="true" class="textarea is-small" [formControl]="getAutoSubmissionFormFC('auto_close_default_extension_m', '')"/>
                    </div>
                  </td>
                </tr>
              </table>
              <edit-save [formlet]="editAutoSubmission" ></edit-save>
            </div>

            <h3>Auto Approve Unsubmit Time Limit</h3>
            <p><tra-md slug="tc_auto_approve_unsubmit_time_limit_desc"></tra-md></p>
            <div class="space-between">
              <div>
                <mat-slide-toggle [(ngModel)]="setAutoApproveUnsubmitTimeLimit" (change)="toggleAutoApproveUnsubmitTimeLimit()" [disabled]="!editAutoApproveUnsubmit.isEditing()">
                  Set/Unset auto approve unsubmit time limit
                </mat-slide-toggle>
                <table style="width:auto;" *ngIf ='setAutoApproveUnsubmitTimeLimit'>
                  <tr>
                    <th style="width:4px;"></th>
                    <th>Time Limit (hours)</th>
                  </tr>
                  <tr class="v-center">
                    <td style="background-color:#7BB658"></td>
                    <td>
                      <div [ngSwitch]="!!editAutoApproveUnsubmit.isEditing()">
                        <div *ngSwitchCase="false"> {{fcv(getAutoApproveUnsubmitTimeLimitFromFc('auto_approve_unsubmit_time_limit_h', ''))}}</div>
                        <input type="number" *ngSwitchCase="true" class="textarea is-small" [formControl]="getAutoApproveUnsubmitTimeLimitFromFc('auto_approve_unsubmit_time_limit_h', '')"/>
                      </div>
                    </td>
                </table>
              </div>
              <edit-save [formlet]="editAutoApproveUnsubmit" ></edit-save>
            </div>  

            <div *ngIf="isAllowedPreCreateTestSessionWindowType()">
              <h3>Create Test Sessions</h3>
              <p><strong>This section currently is for TQPQ Only</strong></p>
              <br>
              <button *ngIf="!havePreCreateTestSession()" class="button is-small is-info" style="margin-top:0.75em;" (click)="createPreCreateTestSessionForWindow()">
                    Create Test Sessions For Test Window
              </button>
              <div *ngIf="havePreCreateTestSession()">
                <table style="width:auto">
                  <tr>
                    <th>Test Session ID</th>
                    <th>Questionnaire Test Session ID</th>
                    <th>Test Window ID</th>
                    <th>Slug</th>
                    <th>Account Type</th>
                    <th>Role Type</th>
                  </tr>
                  <tr *ngFor="let ts of getTestWindowPreCreatedTestSessions()">
                    <td>{{ts.test_session_id}}</td>
                    <td>{{ts.qts_id}}</td>
                    <td>{{ts.test_window_id}}</td>
                    <td>{{ts.slug}}</td>
                    <td>{{ts.account_type}}</td>
                    <td>{{ts.role_type}}</td>
                  </tr>
                </table>
                <div class="space-between">
                  <table style="width:auto;">
                    <tr>
                      <th style="width:4px;"></th>
                      <th>Due On</th>
                    </tr>
                    <tr class="v-center">
                      <td style="background-color:#7BB658"></td>
                      <td>
                        <div *ngIf="!editPreCreateTestSessionDueOn.isEditing()">{{formatDueOnDate(testWindowPreCreatedTestSessions[0].due_on)}}</div>
                        <input *ngIf="!!editPreCreateTestSessionDueOn.isEditing()" type="datetime-local" [formControl]="getPrCreateTestSessionFormFC('DUE_ON')">
                      </td>
                    </tr> 
                  </table>  
                  <edit-save [formlet]="editPreCreateTestSessionDueOn" ></edit-save>
                </div>  
              </div>
            </div>  

            <h3>Assessment Design Allocation</h3>
            <panel-twtar [testWindowId]="testWindowId" [typeSlug]="getPropString(TWAttr.type_slug)" [reloadTwtdar]="reloadTwtdar" (copyTwtdarSetting)="copyTwtdarSettingModalStart(Modal.COPY_TWTDAR_SETTING)"></panel-twtar>

            <!-- 
            <h3>Test Design</h3>
            <p>The test design contains the content of the questions (items), scoring keys, the mapping of items to testlets, and any other information that is needed to deliver the test to the applicant and capture the results.</p>
            <div [ngSwitch]="isTestDesignDefined()" style="display:flex; flex-direction:row;">
              <div *ngSwitchCase="false" class="notification is-warning" style="margin-top:0.5em; width:auto;">
                No test design selected.
              </div>
              <div *ngSwitchCase="true">
                <table style="width:auto;" >
                  <tr>
                    <td><strong>ID</strong></td>
                    <td><code>{{getCurrentTestDesignId()}}</code></td>
                  </tr>
                  <tr>
                    <td><strong>Name</strong></td>
                    <td>{{getTestDesignInfoProp(getCurrentTestDesignId(), TDInfoProps.name)}}</td>
                  </tr>
                  <tr>
                    <td><strong>Created On</strong></td>
                    <td>
                      {{getTestDesignInfoProp(getCurrentTestDesignId(), TDInfoProps.created_on)}}
                      <div class="little-attribution">by {{getTestDesignInfoProp(getCurrentTestDesignId(), TDInfoProps.created_by)}}</div>
                    </td>
                  </tr>
                  <tr>
                    <td><strong>Last Updated On</strong></td>
                    <td>
                      {{getTestDesignInfoProp(getCurrentTestDesignId(), TDInfoProps.last_updated_on)}}
                      <div class="little-attribution">by {{getTestDesignInfoProp(getCurrentTestDesignId(), TDInfoProps.last_updated_by)}}</div>
                    </td>
                  </tr>
                </table>
              </div>
            </div>
            <div *ngIf="editTestDesign.isEditing()">
              <div class="select" style="margin-top:0.5em;">
                <select [formControl]="editTestDesign.getFC(TWAttr.test_design_id)">
                  <option *ngFor="let testDesign of testDesignsAvail" [value]="testDesign.id">
                    {{testDesign.id}}: 
                    {{getTestDesignInfoProp(testDesign.id, TDInfoProps.name)}}
                    ({{getTestDesignInfoProp(testDesign.id, TDInfoProps.last_updated_on)}})
                  </option>
                </select>
              </div>
            </div>
            <edit-save [formlet]="editTestDesign" ></edit-save> 
            -->

            <h3>Reporting Configuration</h3>
            <div>
              <table style="width:auto;">
                <tr>
                  <td style="width: 16em;">ISR version</td>
                  <td>
                    <div [ngSwitch]="!!editISRVersion.isEditing()">
                      <div *ngSwitchCase="false">
                        {{fcv(editISRVersion.getFC(TWAttr.isr_version))}}
                      </div>
                      <div *ngSwitchCase="true">
                        <input type="number" class="input is-small is-stub" [formControl]="editISRVersion.getFC(TWAttr.isr_version)"> 
                      </div>
                    </div>
                    <edit-save [formlet]="editISRVersion" [isInline]="false"></edit-save>
                  </td>
                </tr>
              </table>  
            </div>

            <h3>School Participation Initialization</h3>
            <div>
              Coming soon... (school_participation, school_semesters, school_classes, school_special_material_requests )
            </div>

            <h3>Rules</h3>
            <!-- <p>You can allow multiple attempts</p> -->
            <div>
              <table style="width:auto;">
                <tr>
                  <td style="width: 16em;">Standard Duration (in minutes)</td>
                  <td>
                    <div [ngSwitch]="!!editDuration.isEditing()">
                      <div *ngSwitchCase="false">
                        {{fcv(editDuration.getFC(TWAttr.duration_m)) || '___' }} minutes
                      </div>
                      <div *ngSwitchCase="true">
                        <input type="number" class="input is-small is-stub" [formControl]="editDuration.getFC(TWAttr.duration_m)"> 
                      </div>
                    </div>
                    <edit-save [formlet]="editDuration" [isInline]="false"></edit-save>
                  </td>
                </tr>
                <tr>
                  <td style="width: 16em;">Allow Multiple Attempts</td>
                  <td><check-toggle [isChecked]="isPropNumericChecked(TWAttr.is_multi_attempt)" (toggle)="togglePropCheckedNumeric(TWAttr.is_multi_attempt)"></check-toggle></td>
                </tr>
                <tr *ngIf="isPropNumericChecked(TWAttr.is_multi_attempt)">
                  <td>
                    Allowed attempts
                  </td>
                  <td>
                    <div [ngSwitch]="!!editAllowedAttempts.isEditing()">
                      <div *ngSwitchCase="false">
                        {{fcv(editAllowedAttempts.getFC(TWAttr.num_attempts)) || '___' }}
                        attempts
                        per
                        {{getIntervalCaption(fcv(editAllowedAttempts.getFC(TWAttr.attempts_intvl))) || '___' }}
                      </div>
                      <div *ngSwitchCase="true">
                        <div>
                          <input type="number" class="input is-small is-stub" [formControl]="editAllowedAttempts.getFC(TWAttr.num_attempts)"> 
                          attempts
                        <!-- </div>
                        <div> -->
                          per
                          <div class="select is-small" style="display:inline-block">
                            <select [formControl]="editAllowedAttempts.getFC(TWAttr.attempts_intvl)">
                              <option *ngFor="let interval of allowedIntervals" [value]="interval.id">{{interval.caption}}</option>
                            </select>
                          </div>
                        </div>
                      </div>
                    </div>
                    <edit-save [formlet]="editAllowedAttempts" [isInline]="false"></edit-save>
                  </td>
                </tr>
                
                <tr>
                  <td>Allow invigilators to un-submit assessments/sections</td>
                  <td><check-toggle [isChecked]="isPropNumericChecked(TWAttr.is_invig_unsubmit)" (toggle)="togglePropCheckedNumeric(TWAttr.is_invig_unsubmit)"></check-toggle></td>
                </tr>
                <tr>
                  <td>Allow invigilators to enter responses on behalf of test-takers</td>
                  <td><check-toggle [isChecked]="isPropNumericChecked(TWAttr.is_invig_taketest)" (toggle)="togglePropCheckedNumeric(TWAttr.is_invig_taketest)"></check-toggle></td>
                </tr>
                <tr>
                  <td>Allowed Delivery Methods</td>
                  <td>
                    <p><check-toggle [isChecked]="isPropNumericChecked(TWAttr.is_allow_test_centre)" (toggle)="togglePropCheckedNumeric(TWAttr.is_allow_test_centre)"></check-toggle> Test Centre (Computer Lab)</p>
                    <p><check-toggle [isChecked]="isPropNumericChecked(TWAttr.is_allow_classroom)" (toggle)="togglePropCheckedNumeric(TWAttr.is_allow_classroom)"></check-toggle> Classroom (BYOD)</p>
                    <p><check-toggle [isChecked]="isPropNumericChecked(TWAttr.is_allow_remote)" (toggle)="togglePropCheckedNumeric(TWAttr.is_allow_remote)"></check-toggle> Remote Proctoring</p>
                  </td>
                </tr>

                <tr *ngIf="isPropNumericChecked(TWAttr.is_allow_remote)">
                  <td>
                    Remote Delivery Methods
                  </td>
                  <td>
                    <p><check-toggle [isChecked]="isPropNumericChecked(TWAttr.is_allow_mobile_tether)" (toggle)="selectRemoteDelivery(TWAttr.is_allow_mobile_tether)"></check-toggle> Mobile Tethering</p>
                    <p><check-toggle [isChecked]="isPropNumericChecked(TWAttr.is_allow_video_conference)" (toggle)="selectRemoteDelivery(TWAttr.is_allow_video_conference)"></check-toggle> Video Conference</p>
                  </td>
                </tr>
                <tr>
                  <td style="width: 16em;">Show Reports When Inactive</td>
                  <td><check-toggle [isChecked]="isPropNumericChecked(TWAttr.show_inactive_tw_report)" (toggle)="togglePropCheckedNumeric(TWAttr.show_inactive_tw_report)"></check-toggle></td>
                </tr>
                <tr>
                  <td style="width: 16em;">Enable name validation for test-takers</td>
                  <td>
                    <check-toggle [isChecked]="isPropNumericChecked(TWAttr.is_require_stu_validate)" (toggle)="togglePropCheckedNumeric(TWAttr.is_require_stu_validate)"></check-toggle>
                  </td>
                </tr>
                <tr *ngIf="isPropNumericChecked(TWAttr.is_require_stu_validate)">
                  <td>
                    Allowed name validation attempts
                  </td>
                  <td>
                    <div class="edit-button" [ngSwitch]="!!editMaxStuValideAttempts.isEditing()">
                      <div *ngSwitchCase="false">
                        {{fcv(editMaxStuValideAttempts.getFC(TWAttr.max_stu_validate_attempts)) || '___' }} failed attempts allowed
                      </div>
                      <div *ngSwitchCase="true">
                        <input type="number" class="input is-small is-stub" [formControl]="editMaxStuValideAttempts.getFC(TWAttr.max_stu_validate_attempts)"> 
                      </div>
                    </div>
                    <edit-save [formlet]="editMaxStuValideAttempts" [isInline]="false"></edit-save>
                  </td>
                </tr>
              </table>
            </div>

          </div>
          <div *ngSwitchCase="TWMainMenuTab.MONITOR">
            <h3>Availability</h3>
            <div>
              <table style="width:auto;">
                <tr>
                  <td style="max-width:12em;"> Allow new <strong>test sessions</strong> to be created by test administrators </td>
                  <td><check-toggle [isChecked]="isPropNumericChecked(TWAttr.is_allow_new_ts)" (toggle)="togglePropCheckedNumeric(TWAttr.is_allow_new_ts)"></check-toggle></td>
                </tr>
                <tr>
                  <td> Allow <strong>new bookings</strong> to be made by test takers </td>
                  <td><check-toggle [isChecked]="isPropNumericChecked(TWAttr.is_allow_new_bookings)" (toggle)="togglePropCheckedNumeric(TWAttr.is_allow_new_bookings)"></check-toggle></td>
                </tr>
                <tr>
                  <td> Allow <strong>results</strong> to be seen by test takers </td>
                  <td><check-toggle [isChecked]="isPropNumericChecked(TWAttr.is_allow_results_tt)" (toggle)="togglePropCheckedNumeric(TWAttr.is_allow_results_tt)"></check-toggle></td>
                </tr>
                <tr>
                  <td> Show <strong>ISR</strong> question attempted to test takers </td>
                  <td><check-toggle [isChecked]="isPropNumericChecked(TWAttr.show_isr_num_q)" (toggle)="togglePropCheckedNumeric(TWAttr.show_isr_num_q)"></check-toggle></td>
                </tr>
                <tr>
                  <td> Allow <strong>appeal</strong> for test taker result</td>
                  <td><check-toggle [isChecked]="isPropNumericChecked(TWAttr.is_allow_appeals)" (toggle)="togglePropCheckedNumeric(TWAttr.is_allow_appeals)"></check-toggle></td>
                </tr>
                <tr>
                  <td> Show <strong>appeal complete</strong> for appeal</td>
                  <td><check-toggle [isChecked]="isPropNumericChecked(TWAttr.show_appeal_result)" (toggle)="popUpWarningMessage(TWAttr.show_appeal_result)"></check-toggle></td>
                </tr>
                <tr>
                  <td> Require individual ISR approval </td>
                  <td><check-toggle [isChecked]="isPropNumericChecked(TWAttr.is_req_isr_appro)" (toggle)="togglePropCheckedNumeric(TWAttr.is_req_isr_appro)"></check-toggle></td>
                </tr>
                <tr>
                  <td> Is IRT ready? </td>
                  <td><check-toggle [isChecked]="isPropNumericChecked(TWAttr.irt_ready)" (toggle)="togglePropCheckedNumeric(TWAttr.irt_ready)"></check-toggle></td>
                </tr>
                <tr>
                  <td> Prevent Students from writing </td>
                  <td><check-toggle [isChecked]="isPropNumericChecked(TWAttr.is_closed)" (toggle)="popUpWarningMessage(TWAttr.is_closed)"></check-toggle></td>
                </tr>
                <tr>
                  <td> Alternative materials accessible </td>
                  <td><check-toggle [isChecked]="isPropNumericChecked(TWAttr.is_alt_file_access)" (toggle)="popUpWarningMessage(TWAttr.is_alt_file_access)"></check-toggle></td>
                </tr>
                <tr>
                  <td> Show Results CSV to Boards and Schools </td>
                  <td><check-toggle [isChecked]="isPropNumericChecked(TWAttr.show_report_to_Board)" (toggle)="togglePropCheckedNumeric(TWAttr.show_report_to_Board)"></check-toggle></td>
                </tr>
                <tr>
                  <td> Unsubmit Request Opt2 Link To Accommodation </td>
                  <td><check-toggle [isChecked]="isPropNumericChecked(TWAttr.unsubmit_request_opt2_link_to_acc)" (toggle)="togglePropCheckedNumeric(TWAttr.unsubmit_request_opt2_link_to_acc)"></check-toggle></td>
                </tr>
                <tr>  
                  <td> Enable <strong>Scheduled Sessions</strong> Extract Generation</td>
                  <td><check-toggle [isChecked]="isPropNumericChecked(TWAttr.is_allow_session_schedule)" (toggle)="togglePropCheckedNumeric(TWAttr.is_allow_session_schedule)"></check-toggle></td>
                </tr>
                <tr>  
                  <td> Activate <b>End Of Window</b> ISR Generation (G9 Only) </td>
                  <td><check-toggle [isChecked]="isPropNumericChecked(TWAttr.is_end_window_isr)" (toggle)="togglePropCheckedNumeric(TWAttr.is_end_window_isr)"></check-toggle></td>
                </tr>
              </table>
            </div>

            <h3>Test Sessions</h3>
            <div>
              <p>You can use this section to view test sessions which have been created and to gather basic statistics on test sessions that have been administered.</p>
              <table  style="width:auto;">
                <tr>
                  <td >Number of test sessions created</td>
                  <td><a>{{testWindowSummary.num_sessions_created}}</a></td>
                </tr>
                <tr>
                  <td>Number of test sessions administered</td>
                  <td><a>{{testWindowSummary.num_sessions_administered}}</a></td>
                </tr>
              </table>
            </div>

            <h3>Schedule RT Audit</h3>
            <div>
              <p>Set dates RT audits will run</p>
              <table style="width:auto;">
                <tr>
                  <th style="width:4px;"></th>
                  <th>Start/End</th>
                  <th>Date</th>
                </tr>
                <tr class="v-center">
                  <td style="background-color:#7BB658"></td>
                  <td>Starts On</td>
                  <td> <input-date [fc]="getRtAuditStartDateFc()" [isDisabled]="!editRtAuditCronJob.isEditing()"></input-date> </td>
                </tr>
                <tr  class="v-center">
                  <td style="background-color:#5C5C5C"></td>
                  <td>Ends On</td>
                  <td> <input-date [fc]="getRtAuditEndDateFc()" [isDisabled]="!editRtAuditCronJob.isEditing()"></input-date> </td>
                </tr>
              </table>
              <hr>
              <p>Set time frame to run audits on selected days</p>
              <span style="display: flex; align-items: center; flex-direction: column; width: fit-content;">
                <table style="width:auto;">
                  <tr>
                    <th>From</th>
                    <th width="4px"></th>
                    <th>To</th>
                  </tr>
                 
                  <tr class="v-center">
                    <td> <input-time [fc]="getRtAuditStartTimeFc()" [isDisabled]="!editRtAuditCronJob.isEditing()"></input-time> </td>
                    <td width="4px"> - </td>
                    <td> <input-time [fc]="getRtAuditEndTimeFc()"  [isDisabled]="!editRtAuditCronJob.isEditing()"></input-time> </td>
                  </tr>
                </table>
                <p *ngIf="isOvernightAudit()" style="color: red">
                  Audit scheduled to run overnight
                </p>
              </span>
              <edit-save [formlet]="editRtAuditCronJob"></edit-save>
            </div>

            <h3>School Admin Sign Off Student Data </h3>
            <div>
              <p>This will auto approve all the school admin sign off for the student data in this test window.</p>
              <p>It will also take a snapshot for all the student current data.</p>
              <div style="margin-top:1em;">
                <button (click)="autoApproveAdminSignOff()" class="button is-success">Auto Approve Admin Sign Off Student Data</button>
              </div>
              <div>
                <p>Previous Auto Approve Dates:</p>
                {{testWindowSummary.AutoApproveDates}}
              </div>
            </div>
            <hr>
            <h3>ISR Requests for All Registered Students</h3>
            <div>
              <ul>
                <li>This will auto request the ISRs for all registered students.</li>
                <li>Please make sure <b>End Of Window ISR Generation</b> flag is activated.</li>
                <li>This process <b>does not</b> check if school admin sign off student data</li>
              </ul>  
              <div style="margin-top:1em;">
                <button (click)="autoRequestISR()" class="button is-success">Auto Request Registered Students ISRs</button>
              </div>
            </div>
            <h3>Generate CSV Report for board and schools</h3>
            <div>
              <ul>
                <li>This will trigger bulk CSV report generation for all boards and schools.</li>
                <li>The action can only be performed after TTRI lockdown.</li>
                <li>Please make sure all students have a snapshot.</li>
              </ul>
              <mat-slide-toggle [(ngModel)]="setMissingCsvBoardOnly">
                Missing CSV Report Only
              </mat-slide-toggle>
              <div style="margin-top:1em;">
                <button class="button" (click)="bulkGenerateCsvReport()" class="button is-success">Bulk Generate CSV report</button>
                <button class="button" *ngIf="bulkCsvReportGenerationRecords.length && !bulkCsvReportGenerationRecords[0].process_completed_on" (click)="stopCsvBulkReportGenerationProcess()" class="button is-danger">Stop Bulk Generation Process</button>
                <button class="button" (click)="regenerateCSVbrdMidentsModalStart()">Regenerate CSV report by Board Midents</button>
              </div>
              <mat-slide-toggle style="margin: 0.5em;" [(ngModel)]="showCsvGenerationRecords">
                Show Bulk CSV Generation Records
              </mat-slide-toggle>
              <button class="button is-small" (click)="CsvGeneratedBoardModalStart()">Show Boards with CSV Reports</button>
              <ng-container *ngIf="showCsvGenerationRecords">
                <button class="button is-small" (click)="getCsvBulkReportGenerationProgress()">Refresh</button>
                <div *ngIf="bulkCsvReportGenerationRecords.length" style="overflow-y:scroll; height:fit-content;">
                  <table>
                    <tr>
                      <th>ID</th>
                      <th>Missing Report Only</th>
                      <th>Created On</th>
                      <th>Progress / Total Board Count</th>
                      <th>Failed Generation Count</th>
                      <th>Process Completed On</th>
                      <th>Is Revoked?</th>
                    </tr>
                    <ng-container *ngFor="let bulkRecord of bulkCsvReportGenerationRecords">
                      <tr>
                        <td>{{bulkRecord.id}}</td>
                        <td>{{bulkRecord.missing_report_sd_only}}</td>
                        <td>{{bulkRecord.created_on}}</td>
                        <td>{{bulkRecord.started_generation_schl_dist_count}} / {{bulkRecord.total_sd_count}}</td>
                        <td>{{bulkRecord.fail_generation_sd_count}}</td>
                        <td>
                          <ng-container *ngIf="!bulkRecord.process_completed_on; else completed">
                            <i class="fa fa-ellipsis fa-spin"></i>
                          </ng-container>
                          <ng-template #completed>
                            {{bulkRecord.process_completed_on}}
                          </ng-template>
                        </td>
                        <td>{{bulkRecord.is_revoked}}</td>
                      </tr>
                    </ng-container>
                  </table>
                </div>  
              </ng-container>
            </div>
            <h3>Reset School Admin ISR Records</h3>
            <div>
              <ul>
                <li>This will reset the school admin access records in the "Generated Reports" tab of the scheduled sessions extract</li>
              </ul>  
              <div style="margin-top:1em;">
                <button (click)="resetSchoolAdminISRAccessRecords()" class="button is-success">Reset School Admin ISR Access Records</button>
              </div>
            </div>

            <h3>Archive Test Window</h3>
            <div>
              <p>If the test window no longer needs to be managed, it can be archived so that it will no longer appear on the test controller dashboard.</p>
              <div style="margin-top:1em;">
                <button (click)="archiveTestWindow()" class="button is-danger">Archive this Test Window</button>
              </div>
            </div>
            <!-- <h3>Download Anonymized Results</h3>
            <div>
              <paginator></paginator>
              <table></table>
            </div> -->
            
          </div>
          <div *ngSwitchCase="TWMainMenuTab.SCORING_WINDOWS">
            <scoring-window-setup [testWindowId]="testWindowId" [calendarOptions]="calendarOptions" [isPj]="isTwPj()"></scoring-window-setup>
          </div>
        </div>

        <div *ngIf="currentMainMenuTab !== TWMainMenuTab.SCORING_WINDOWS" class="calendar-container" style="max-width:60em">
          <full-calendar 
          #calendar
          [options]="calendarOptions"
          deepChangeDetection="true">
          </full-calendar>
        </div>

      </div>

    </div>
  </div>
</div>


<div class="custom-modal" *ngIf="cModal()">
    <div class="modal-contents">
      <div [ngSwitch]="cModal().type">
        <div *ngSwitchCase="Modal.COPY_TWTDAR_SETTING">
          <tc-modal-copy-twtdar-setting 
            [view] = "Modal.COPY_TWTDAR_SETTING"
            (twtdarSettingTW)="copySourceTWConfigToDestinationTW($event)"
            (closeTwtdarSettingModal)="copyTWConfigModalFinish()">
          </tc-modal-copy-twtdar-setting>
        </div>
        <div *ngSwitchCase="Modal.COPY_ASSESSMENT_TWTD_COMPONENTS">
          <tc-modal-copy-twtdar-setting 
            (twtdarSettingTW)="copySourceAssessmentTwtdComponentToDestinationTW($event)"
            (closeTwtdarSettingModal)="copyTWConfigModalFinish()">
          </tc-modal-copy-twtdar-setting>
        </div>
        <div *ngSwitchCase="Modal.COPY_SCHOOL_SEMESTERS">
          <tc-modal-copy-twtdar-setting 
            (twtdarSettingTW)="copySourceSemesterTWConfigToDestinationTW($event)"
            (closeTwtdarSettingModal)="copyTWConfigModalFinish()">
          </tc-modal-copy-twtdar-setting>
        </div>
        <div *ngSwitchCase="Modal.REGENERATE_CSV_REPORT_BY_BOARD">
          <h3>Regenerate CSV Report</h3>
          <div class="input-button">
            <label for="csv-board-mident">Board midents (Please add single mident):</label>
            <div class="input-controls">
              <input type="number" id="csv-board-mident" [(ngModel)]="csvSingleBoardMident" style="margin: 0.3em;">
              <button class="button is-small is-info" (click)="addToRegenerateCSVbrdMidents(csvSingleBoardMident)" [disabled]="!csvSingleBoardMident">Add</button>
            </div>
          </div>
          <div class="input-button">
            <span>Added Board Mident(s):</span>
            <ul>
              <li *ngFor="let mident of regenerateCSVbrdMidents; let i = index">
                <div class="input-controls">
                  <span>{{ mident }}</span>
                  <button class="button is-small is-danger" (click)="regenerateCSVbrdMidents.splice(i, 1)">Remove</button>
                </div>
              </li>
            </ul>
          </div>
          <modal-footer [pageModal]="pageModal" [isEditDisable]="!regenerateCSVbrdMidents.length"></modal-footer>
        </div>
        <div *ngSwitchCase="Modal.CSV_GENERATED_BOARDS">
          <table>
            <thead>
              <tr>
                <th>Board Mident</th>
                <th>Generated On</th>
              </tr>
            </thead>
            <tbody>
              <ng-container *ngFor="let board of csvReportGeneratedBoards">
                <tr>
                  <td>{{board.board_mident}}</td>
                  <td>{{board.report_generate_on}}</td>
                </tr>
              </ng-container>
            </tbody>
          </table>
          <modal-footer [pageModal]="pageModal"></modal-footer>
        </div>
      </div>
    </div>
</div>

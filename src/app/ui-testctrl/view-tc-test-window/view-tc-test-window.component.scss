@import '../../../styles/page-types/standard.scss';
@import '../../../styles/page-types/registration-or-login-form.scss';
@import '../../../styles/page-types/landing.scss';
@import '../../../styles/partials/_media.scss';
@import '../../../styles/partials/_colors.scss';
@import '../../../styles/partials/_modal.scss';

.custom-modal { @extend %custom-modal; }

.page-body {
    @extend %page-body;
    .page-content {
        // @extend %page-form-content;
    }
}

.page-panels-horz{
    display: flex;
    flex-direction:row;
    justify-content: space-between;
    .editing-container {
        flex-grow:1;
    }
    .calendar-container {
        flex-grow:0;
        margin-left: 3em;
        width: 50em;
        // font-size: 0.8em;
    }
}

.window-info {
    padding-bottom:2em;
    border-bottom: 3px solid #333;
}

h3 {
    font-size: 1.3em;
    padding-bottom: 0.5em;
    border-bottom: 1px solid #f1f1f1;
}

.test-title {
    font-size: 2.2em;
    font-weight: 600;
}



input.input.is-stub {
    width:6em;
}

tr.v-center td {
    vertical-align: middle;
}

.little-attribution {
    font-size:0.75em;
    padding-left: 1em;
}

.quick-text-replace {
    span,button {
        margin-right:0.5em;
    }
}

.input-button {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.input-controls {
    display: flex;
    gap: 0.5em;
    align-items: center;
}

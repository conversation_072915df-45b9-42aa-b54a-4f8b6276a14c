import { Component, OnInit } from '@angular/core';
import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { LangService } from '../../core/lang.service';
import { AccountType } from "../../constants/account-types";
import { BoardLead, BOARD_LEAD_VIEWS } from './data/view';
import { IMenuTabConfig } from '../../ui-partial/menu-bar/menu-bar.component';
import { SidepanelService } from '../../core/sidepanel.service';
import {AuthService, getFrontendDomain} from '../../api/auth.service';
import { RoutesService } from "../../api/routes.service";
import { ActivatedRoute } from '@angular/router';
import { Router } from '@angular/router';
import { ClassFilterId, MySchoolService } from '../../ui-schooladmin/my-school.service';
import { SbBoardComponent } from 'src/app/ui-dist-admin/sb-board/sb-board.component';
import { SbSessionsComponent } from 'src/app/ui-dist-admin/sb-sessions/sb-sessions.component';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { formatDate } from '@angular/common';
import { downloadFromExportData, downloadCSVFromExportData, IExportColumn } from 'src/app/ui-testctrl/tc-table-common/tc-table-common.component';
import { MemDataPaginated } from '../../ui-partial/paginator/helpers/mem-data-paginated';
import * as _ from 'lodash';
import { MyBoardService } from 'src/app/ui-dist-admin/my-board.service';
import { combineLatest } from 'rxjs';
import { tap } from 'rxjs/operators';

const IS_SCHOOL_DRILLDOWN_ACTIVE = false;
interface IView<T> extends IMenuTabConfig<T> {
  imgUrl: string,
  description: string,
  hasIndicator?: boolean,
}

export const OSSLTResultColumns =  [ 
  "SchMident", "SchName", "Grouping", "StudentOEN", "SASN"
  , "FirstName", "LastName", "EligibilityStatus", "HasStarted", "HasSubmitted", "StartedOn"
  , "SubmittedOn", "HasReport", "Result", "OSSLTScaleScore", 'ScaleScoreDescription', "Note"
];

@Component({
  selector: 'view-board-lead-dashboard',
  templateUrl: './view-board-lead-dashboard.component.html',
  styleUrls: ['./view-board-lead-dashboard.component.scss']
})
export class BoardLeadComponent implements OnInit {

  constructor(
    public lang: LangService,
    public mySchool: MySchoolService,
    public myBoard: MyBoardService,
    private breadcrumbsService: BreadcrumbsService,
    private sidePanel: SidepanelService,
    private auth: AuthService,
    private routes: RoutesService,
    private route: ActivatedRoute,
    private router: Router,
    ) { }
  public breadcrumb = [];
  schools =[];  
  boardLeadInfo;
  BoardLead = BoardLead;
  selectedView: BoardLead;
  views: IView<BoardLead>[] = [];
  sbBoard: SbBoardComponent;
  schlDistGroupId: string;
  isSecreteUser:string
  isLoadingSchools = true;
  schoolsTable: MemDataPaginated<any>;
  techReadyDropDown = [{val: 'any', display: 'sa_any'}, {val: '1', display: 'lbl_yes'}, {val: '0', display: 'lbl_no'}];
  selectedtechReadyVal = 'any';
  account_type = 'None';
  hasAccess: boolean;

  csvTestWindows:any[];
  currentAdminWindow: any;
  isShowingReports: boolean = false;
  currentClassFilter
  classFilter

  ngOnInit(){
    this.sidePanel.activate();
    this.sidePanel.unexpand();
    this.updateBreadcrumb();
    this.route.queryParams.subscribe((queryParams) => {
      if (queryParams.isSecreteUser) {
        this.isSecreteUser = queryParams.isSecreteUser 
      }
      const distGroupId  = queryParams['district'];
      if (distGroupId){
        this.schlDistGroupId = distGroupId
      }
    });

    //call api
    this.auth.apiFind(this.routes.BOARD_CURR_LEAD_SCHOOLS,this.configureQueryParams()).then(result =>{
      this.account_type= result[0].account_type
      this.schools = result;
      this.schlDistGroupId = result[0]?.schl_dist_group_id;    

      // put sandbox school on top of the list
      this.schools.sort((schl1,schl2) => {
        if(schl1.is_sandbox == 1){
          return -1
        }
        if(schl2.is_sandbox == 1){
          return 1
        }
        return 0
      })

      this.schoolsTable = new MemDataPaginated({
        data: this.schools,
        configurablePageSize: true,
        filterSettings: {
          schl_mident: (schl: any, val: string) => _.includes((schl.school_mident.toString()), val),
          schl_name: (schl: any, val: string) => _.includes(schl.school_name.toLowerCase(), val.toLowerCase()),
          schl_type: (schl: any, val: string) => _.includes(this.getSchoolTypeSlug(schl.type_slug).toLowerCase(), val.toLowerCase()),
          schl_tech_readi_primary: (schl: any, val: string) => schl.primaryTechReady.toString() == val,
          schl_tech_readi_junior: (schl: any, val: string) => schl.juniorTechReady.toString() == val,
          schl_tech_readi_g9: (schl: any, val: string) => schl.g9TechReady.toString() == val,
          schl_tech_readi_osslt: (schl: any, val: string) => schl.ossltTechReady.toString() == val,
        },
        sortSettings: {
          schl_mident: (schl: any) => _.sortBy(schl.school_mident.toString()),
          schl_name: (schl: any) => _.sortBy(schl.school_name),
          schl_type: (schl: any) =>  _.sortBy(schl.type_slug),
          schl_tech_readi_primary: (schl: any) => schl.primaryTechReady === 1,
          schl_tech_readi_junior: (schl: any) => schl.juniorTechReady === 1,
          schl_tech_readi_g9: (schl: any) => schl.g9TechReady === 1,
          schl_tech_readi_osslt: (schl: any) => schl.ossltTechReady === 1,
        }
      });
      this.isLoadingSchools = false
      this.createClassFilterToggles();
    });
    // The API called should be trigger when both user and district group id are fetched
    const combinedObservable = combineLatest([this.auth.user().asObservable(), this.route.queryParams]);
    combinedObservable.pipe(
      tap(() => {
        const user = this.auth.user().value;
        if (!user) {
          return;
        }
        this.auth
          .apiGet(this.routes.DIST_ADMIN_SUMMARY, user.uid, { query: {clientDomain: getFrontendDomain(), schlDistGroupId: this.schlDistGroupId}})
          .then(res => {
            this.boardLeadInfo = res;
            this.hasAccess = true;
            this.csvTestWindows = res.csvTestWindows
          })
      })
    ).subscribe();
    this.views = [];
    this.selectedView = this.BoardLead.SCHOOLS;
    BOARD_LEAD_VIEWS.forEach(view => {
      if(view.id != BoardLead.REPORTS || this.isAllowBypassDomain()){
        this.views.push( Object({
          ...view,
          caption: this.lang.tra(view.caption),
          description: this.lang.tra(view.description),
        }))
      }
    })
  }

  getSchoolTypeSlug(schl_type_slug){
    switch(schl_type_slug){
      case 'ELEMENTARY':
        return this.lang.tra("bl_schl_type_elementary")
      case 'SECONDARY':
        return this.lang.tra("bl_schl_type_secondary")
      case 'K12':
      default:
        return this.lang.tra("bl_schl_type_K12")
    }
  }

  getBaseRoute() {
    return `/${this.lang.c()}/${AccountType.SCHOOL_DISTRICT_CURRI}`;
  }

  getViewRoute(viewSlug: BoardLead) {
    return this.getBaseRoute() + '/' + viewSlug
  }

  selectView(id: BoardLead) {
    this.selectedView = id;
    if(this.selectedView == BoardLead.REPORTS && this.boardLeadInfo){
      const boardLang = this.boardLeadInfo["brd_lang"];
      this.lang.setCurrentLanguage(boardLang);
    }
  }
  
  updateBreadcrumb() {
    let boardLead = this.lang.tra('bl_dashboard_board_lead');
    const urlTree = this.router.parseUrl(this.router.url);
    const schlDistGroupId = urlTree.queryParams;
    this.breadcrumb = [
      this.breadcrumbsService._CURRENT(boardLead, this.getBaseRoute(), schlDistGroupId)
    ];
  }

  onRouteChange(routeParams: any) {
    window.scrollTo(0, 0);
    this.updateBreadcrumb();
  }

  // configureQueryParams(){
  //   return null
  // }

  getSchoolBaseURL(){
    return  `/${this.lang.c()}/${AccountType.SCHOOL_ADMIN}` + '/dashboard'
  }

  goToSchool(schl){
    const schl_group_id = schl.schl_group_id
    this.auth.apiPatch(this.routes.BOARD_CURR_LEAD_SCHOOLS,0,{schl_group_id},this.configureQueryParams())
      .then(result =>{
        this.mySchool.resetSchoolGroupIdAndRole(schl_group_id)
        const queryParams = { school: schl.schl_group_id, ...this.route.snapshot.queryParams};
        const route = this.getSchoolBaseURL()
        this.router.navigate([route], {
          relativeTo: this.route,
          queryParams
        });
      });
  }

  createClassFilterToggles(){
    this.classFilter = this.myBoard.classFilterToggles
    if(this.account_type){
      switch(this.account_type){
        case 'school_district_curr':
          break
        case 'schl_disct_curr_ele':
          this.classFilter = this.classFilter.filter( cft => cft.id == ClassFilterId.Primary || cft.id == ClassFilterId.Junior)
          break
        case 'schl_disct_curr_sec':
          this.classFilter = this.classFilter.filter( cft => cft.id == ClassFilterId.G9 || cft.id == ClassFilterId.OSSLT)
          break    
        case 'None':
        default:
          this.classFilter = [];
          break; 
      }
    }
  }

  onCurrentAdminWindowChange(newAdminWindow: any) {
    this.currentAdminWindow = newAdminWindow;
  }

  configureQueryParams(schlDistGroupId?: string){
    if(schlDistGroupId) {
      if(this.currentAdminWindow){
        return { query: {schl_dist_group_id: schlDistGroupId, clientDomain: getFrontendDomain(), testWindowId: this.currentAdminWindow.tw_id, isSecreteUser: this.isSecreteUser }}
      } else {
        return { query: {schl_dist_group_id: schlDistGroupId, clientDomain: getFrontendDomain() }}
      }
    }
    if(this.schlDistGroupId) {
      if(this.currentAdminWindow){
        return { query: {schl_dist_group_id: this.schlDistGroupId, testWindowId: this.currentAdminWindow.tw_id }}
      } else {
        return { query: {schl_dist_group_id: this.schlDistGroupId }}
      }
    }
    return null;
  }

  isSchoolActive(schl:any){
    if (!IS_SCHOOL_DRILLDOWN_ACTIVE){
      if (schl.is_sandbox == 1){
        return true;
      }
      return false;
    }
    return true;
  }

  isAllowBypassDomain(){
    return true;
  }

  pageChanged(){
  }
}

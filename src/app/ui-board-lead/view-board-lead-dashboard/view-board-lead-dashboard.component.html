<div class="page-body view-sa-dash">
  <div>
    <header [breadcrumbPath]="breadcrumb" [hasSidebar]="true"></header>
    <div class="board-title">
      <div>
        <div style="margin-bottom: 1em;">
          <ntf-new-messages></ntf-new-messages>
        </div>
        <menu-bar [menuTabs]="views" [tabIdInit]="selectedView" (change)="selectView($event)"></menu-bar>
        <div [ngSwitch]="selectedView">
          <div *ngSwitchCase="BoardLead.SCHOOLS">
            <div style="margin-top: 2em;">
              <p><tra-md slug="pj_board_school_welcome"></tra-md></p>
              <!-- <p><tra-md slug="pj_board_curriculum_dashboard"></tra-md></p> -->
            </div>
            <div *ngIf = "!isLoadingSchools">
              <paginator 
                [model]="schoolsTable.getPaginatorCtrl()" 
                [page]="schoolsTable.getPage()" 
                [numEntries]="schoolsTable.numEntries()" 
                (pageChange)="pageChanged()">
              </paginator>
              <div class="school-table-container">
                <table class="table is-hoverable ">
                  <tr>
                    <th class="flush"><table-header id = "schl_mident" caption = "bl_dashboard_schl_mident" [ctrl] = "schoolsTable" [isSortEnabled]="true"></table-header></th>
                    <th class="flush"><table-header id = "schl_name" caption = "bl_dashboard_name" [ctrl] = "schoolsTable" [isSortEnabled]="true"></table-header></th>
                    <th class="flush"><table-header id = "schl_type" caption = "bl_dashboard_type" [ctrl] = "schoolsTable" [isSortEnabled]="true"></table-header></th>
                    <th class="flush" *ngIf="account_type=='school_district_curr'||account_type=='schl_disct_curr_ele'"><table-header id = "schl_tech_readi_primary" caption = "bl_dashboard_tech_readi_primary" [filterMode]="'LIST'" [list]="techReadyDropDown" [selectedVal]='selectedtechReadyVal' [ctrl] = "schoolsTable" [isSortEnabled]="true"></table-header></th>
                    <th class="flush" *ngIf="account_type=='school_district_curr'||account_type=='schl_disct_curr_ele'"><table-header id = "schl_tech_readi_junior" caption = "bl_dashboard_tech_readi_junior" [filterMode]="'LIST'" [list]="techReadyDropDown" [selectedVal]='selectedtechReadyVal' [ctrl] = "schoolsTable" [isSortEnabled]="true"></table-header></th>
                    <th class="flush" *ngIf="account_type=='school_district_curr'||account_type=='schl_disct_curr_sec'"><table-header id = "schl_tech_readi_g9" caption = "bl_dashboard_tech_readi_g9" [filterMode]="'LIST'" [list]="techReadyDropDown" [selectedVal]='selectedtechReadyVal' [ctrl] = "schoolsTable" [isSortEnabled]="true"></table-header></th>
                    <th class="flush" *ngIf="account_type=='school_district_curr'||account_type=='schl_disct_curr_sec'"><table-header id = "schl_tech_readi_osslt" caption = "bl_dashboard_tech_readi_osslt" [filterMode]="'LIST'" [list]="techReadyDropDown" [selectedVal]='selectedtechReadyVal' [ctrl] = "schoolsTable" [isSortEnabled]="true"></table-header></th>
                  </tr>
                  <tr *ngFor="let schl of schoolsTable.getCurrentPageData(); let index = index;">
                    <td>{{schl.school_mident}}</td>
                    <td>
                      <span *ngIf="schl.is_sandbox == 1" class="tag sandbox"><tra slug="bl_lbl_sandbox_school"></tra></span>
                      <span [ngSwitch]="isSchoolActive(schl)">
                        <a *ngSwitchCase="true" (click)="goToSchool(schl)" >{{schl.school_name}}</a>
                        <span *ngSwitchCase="false">{{schl.school_name}}</span>
                      </span>
                    </td>
                    <td>
                      <tra-md *ngIf="schl.type_slug == 'ELEMENTARY'" slug ="bl_schl_type_elementary"></tra-md>
                      <tra-md *ngIf="schl.type_slug == 'SECONDARY'" slug ="bl_schl_type_secondary"></tra-md>
                      <tra-md *ngIf="schl.type_slug == 'K12'" slug ="bl_schl_type_K12"></tra-md>
                    </td>
                    <td *ngIf="account_type=='school_district_curr'||account_type=='schl_disct_curr_ele'"><i *ngIf="schl.primaryTechReady === 1" class="fa fa-check"></i></td>
                    <td *ngIf="account_type=='school_district_curr'||account_type=='schl_disct_curr_ele'"><i *ngIf="schl.juniorTechReady === 1" class="fa fa-check"></i></td>
                    <td *ngIf="account_type=='school_district_curr'||account_type=='schl_disct_curr_sec'"><i *ngIf="schl.g9TechReady === 1" class="fa fa-check"></i></td>
                    <td *ngIf="account_type=='school_district_curr'||account_type=='schl_disct_curr_sec'"><i *ngIf="schl.ossltTechReady === 1" class="fa fa-check"></i></td>
                  </tr>  
                </table>
              </div>
            </div>  
          </div>
          <div *ngSwitchCase="BoardLead.SESSIONS">
            <sb-sessions [boardInfo]="boardLeadInfo" [account_type]="account_type"></sb-sessions>
          </div>
          <div *ngSwitchCase="BoardLead.REPORTS">
            <sb-report
              (currentAdminWindowChange)="onCurrentAdminWindowChange($event)"
              [configureQueryParams]="configureQueryParams.bind(this)"
              [csvTestWindows]="csvTestWindows"
              [schlDistGroupId]="schlDistGroupId"
              [currentAdminWindow]="currentAdminWindow"
              [classFilterToggles]="classFilter"
            ></sb-report>
          </div>
        </div>
      </div> 
    </div> 
  </div>
  <footer [hasLinks]="true"></footer>
</div>


@import '../styles/drag-drop.scss';

.template-header {
    width: 22em;
    border-bottom: 1px solid #ccc;
    padding-bottom: 0.5em;
    margin-bottom: 1em;
}

.configuration-list {
    display: flex;
    flex-direction: column;
    gap:1em;
}

.configuration-header {
    color: #363636;
    font-weight: 700;
}

.text-list {
    display: flex;
    flex-direction: column;
    gap: 0.5em;
}

.textarea-text-list {
    min-width: auto;
}

.selection-table {
    height: 6em;
    font-size: 10px;
}

.invisible {
    opacity: 0%;
}

.center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.table {
    tr {
      td {
        min-width: 7em;
      }
    }
}

.select-table {
    tr {
      td {
        min-width: 2em;
        max-width: 4em;
      }
    }
}

.wrap-content {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.advanced-inline-container {
    display:flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    &.inline-block {
        display:inline-block;
        vertical-align: middle;
    }
    &.flex-wrap {
        display:flex;
        flex-wrap: wrap;
        justify-content: flex-start;
    }
    .inline-text-block {
        display:inline;
    }
    .inline-block {
        display:inline-block;
        vertical-align: middle;
    }
}

.invisible-borders {
    border: none;
    padding: 0em;
    min-width: 0em !important;
}

.text-number::-webkit-outer-spin-button,
.text-number::-webkit-inner-spin-button {
    /* display: none; <- Crashes Chrome on hover */
    -webkit-appearance: none;
    margin: 0; /* <-- Apparently some margin are still there even though it's hidden */
}

.text-number {
    -moz-appearance:textfield; /* Firefox */
}

.separator {
    border-bottom: 1px solid #d9d9d9;
    padding-bottom: 1em;
}

.option-container-buttons {
    display: flex;
    flex-direction: column;
    gap: 1em;
}

.no-margin-bottom {
    margin-bottom: 0px !important;
}

.gap {
    gap: 1em;
}

.sub-property-div {
    padding-left: 1em;
    border-left: 0.2em solid #e0e0e0;
    margin-left: 0.5em;
    margin-top: 0.25em;
}

.button {
    margin-right: 0px;
}

.small-icon {
    color: black;
    &:hover {
        color: #555766;
    }
}

.is-disabled {
    pointer-events: none;
    opacity: 0.5;
}

.capture-math-container {
  min-width: 8em;
  flex-grow: 1;
  font-size: 1.5em;
  text-align: center;
  background-color: white;
}

.draggable {
  cursor: move;
}

.option-divider {
    margin: 1em 0;
}
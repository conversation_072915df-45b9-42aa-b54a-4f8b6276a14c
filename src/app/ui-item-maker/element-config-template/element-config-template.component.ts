import { Component, ElementRef, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, Renderer2, SimpleChanges, ViewChild } from '@angular/core';
import { AuthScopeSetting, AuthScopeSettingsService } from '../auth-scope-settings.service';
import { EditingDisabledService } from '../editing-disabled.service';
import { IContentElementTemplate, TemplateConfigDefRow } from '../../ui-testrunner/element-render-template/model';
import { ITemplateDef, mcqStyleSub, StyleprofileService, TemplateVersionConfig } from 'src/app/core/styleprofile.service';
import { Subject, Subscription } from 'rxjs';
import { createDefaultElement, elementIconById, generateDefaultElementText, generateDefaultElementTextLink } from '../item-set-editor/models';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { ElementType, IContentElement, IElementTypeDef } from 'src/app/ui-testrunner/models';
import { updateChangeCounter } from '../services/data-bind';
import { indexOf } from '../services/util';
import { ElementTypeDefs } from "src/app/ui-testrunner/models/ElementTypeDefs";
import { IContentElementMcq, IContentElementMcqOption, IContentElementMcqOptionInTable, IContentElementMcqTableColEachCell, IMcqSharedObject, McqDisplay } from '../../ui-testrunner/element-render-mcq/model';
import { ICanvasSharedObject, IContentElementCanvas } from '../../ui-testrunner/element-render-canvas/model';
import { getCellColSpan, getCellRowSpan } from '../element-config-table/merge-cell-functions';
import { IContentElementTable, IContentElementTableCell } from 'src/app/ui-testrunner/element-render-table/model';
import { LangService } from 'src/app/core/lang.service';
import { IContentElementInput, InputFormat } from 'src/app/ui-testrunner/element-render-input/model';
import { IContentElementValidator, IValidatorAdvCombinationEntry, IValidatorCombinationProp, ValidatorMode } from 'src/app/ui-testrunner/element-render-validator/model';
import { ItemBankCtrl } from '../item-set-editor/controllers/item-bank';
import { ensureResponseEntryIds, ensureResponseEntryIdsLocal } from '../item-set-editor/models/expected-answer';
import { IContentElementText, TextParagraphStyle } from 'src/app/ui-testrunner/element-render-text/model';
import { IContentElementSelectionTable } from 'src/app/ui-testrunner/element-render-selection-table/model';
import { TwiddleState } from '../../ui-partial/twiddle/twiddle.component';
import { SharedObjectMapService } from 'src/app/ui-testrunner/element-render-interactive-diagram/shared-object-map.service';
import * as _ from 'lodash';
import { IContentElementImage } from 'src/app/ui-testrunner/element-render-image/model';
import { getValueByPath, upgradeTemplateContent } from './util';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { IAuthRestrictions } from '../item-set-editor/item-set-editor.component';

const THROTTLE_TIMEOUT = 150;
const validatorCompatibleTypes = ['input', 'mcq', 'scientific_notation', 'interactive_diagram'];
const HIDDEN_CONFIGS = ['translation']
const RESTRICTED = 'Restricted';

@Component({
  selector: 'element-config-template',
  templateUrl: './element-config-template.component.html',
  styleUrls: ['./element-config-template.component.scss']
})
export class ElementConfigTemplateComponent implements OnInit, OnDestroy {

  @Input() element: IContentElementTemplate;
  @Input() authRestrictions: IAuthRestrictions = {};

  templateSelectionSub:Subscription
  templateSelectionCancelationSub:Subscription
  isAwaitingTemplateSelection:boolean;
  
  removeMouseGuidebox: Function;
  currentMovingElementPath?: string = undefined;
  pageClickListener: Function;

  // Do not want to have inputs within a mcq option
  staticInlineElements: {id: string, label: string}[] = [
    {id: 'text', label: 'Text'},
    {id: 'math', label: 'Math'},
    {id: 'image', label: 'Image'},
  ]

  // alignment options
  public alignments = [
    {id:'left', icon:'fa fa-align-left'},
    {id:'center', icon:'fa fa-align-center'},
    {id:'right', icon:'fa fa-align-right'},
  ]

  public vertical_alignments = [
    {id:'top', icon:'fa fa-arrow-up'},
    {id:'middle', icon:'fas fa-arrows-alt-v'},
    {id:'bottom', icon:'fa fa-arrow-down'},
  ]

    // alignment options
    public alignmentsJustify = [
      {id:'flex-start', icon:'fa-align-left'},
      {id:'center', icon:'fa-align-center'},
      {id:'flex-end', icon:'fa-align-right'},
    ]
  
  private callbackSubscriptions: {[id: string]: Subject<void>}[] = [];
  constructor(
    private authScopeSettings: AuthScopeSettingsService,
    private editingDisabled: EditingDisabledService,
    private styleProfileService: StyleprofileService,
    private lang: LangService,
    private itemBankCtrl: ItemBankCtrl,
    private renderer: Renderer2,
    private sharedObjectMap: SharedObjectMapService,
    private loginGuard: LoginGuardService
  ) { }

  ngOnInit(): void {
    setTimeout(() => { // temporary delay, because of risk of questions not saving
      if(!this.element.templateConfigs) {
        return;
      }
      if(this.hasTranslation()) {
        this.syncTranslations();
      }
      if(this.hasValidator()) {
        this.autoAlignValidators();
      }
    }, 1000);
    this.initCallbackSubscription();
  }
  twiddleTargets = new TwiddleState(false);

  elementTypes = ElementType;
  mcqElementTypeDefs:IElementTypeDef[] = [
    ElementTypeDefs.TEXT,
    ElementTypeDefs.MATH
  ];
  
  twiddleToggleMap: Map<string, Map<string, Map<number, boolean>>> = new Map();
  getTwiddleToggleInfo(elementRef: string, key: string, id: number) {
    if(!this.twiddleToggleMap.has(elementRef)) {
      const keyMap: Map<string, Map<number, boolean>> = new Map();
      const numberMap: Map<number, boolean> = new Map();
      numberMap.set(id, false);
      keyMap.set(key, numberMap);
      this.twiddleToggleMap.set(elementRef, keyMap);
    }
    if(!this.twiddleToggleMap.get(elementRef).has(key)) {
      const numberMap: Map<number, boolean> = new Map();
      numberMap.set(id, false);
      this.twiddleToggleMap.get(elementRef).set(key, numberMap);
    }
    if(!this.twiddleToggleMap.get(elementRef).get(key).has(id)) {
      this.twiddleToggleMap.get(elementRef).get(key).set(id, false);
    }

    return this.twiddleToggleMap.get(elementRef).get(key).get(id);
  }

  setTwiddleToggleInfo(elementRef: string, key: string, id: number, toggle: boolean) {
    console.log('setting', toggle);
    this.twiddleToggleMap.get(elementRef).get(key).set(id, toggle);
  }

  getColNumber(col_i: number) {
    return `Column ${col_i + 1}`;
  }

  getRowNumber(row_i: number) {
    return `Row ${row_i + 1}`
  }

  ngOnDestroy() :void {
    if (this.templateSelectionSub){
      this.templateSelectionSub.unsubscribe()
    }
    if (this.templateSelectionCancelationSub){
      this.templateSelectionCancelationSub.unsubscribe()
    }
  }

  hasTranslation() {
    return this.element.templateConfigs.find((conf) => conf.configType == 'translation') != undefined;
  }

  /**
   * Will check for any translation configs in the template, and automatically update their content according to the current language.
   */
  syncTranslations() {
    const validators = this.element.templateConfigs.filter((conf) => conf.configType == 'translation');
    validators.forEach((config) => {
      // Do not do anything if there isnt a translation for both french and english.
      if(!config.translationEN || !config.translationFR) {
        return;
      }
      
      const element: string = this.getValueByPath(config.elementRef);
      // Only do this for certain element types, incase of misconfiguration.
      if(typeof element != 'string' && typeof element != 'number') {
        return;
      }

      const translation = this.lang.getCurrentLanguage() == 'fr' ? config.translationFR : config.translationEN
      this.setValueByPath(config.elementRef, translation)
    })
  }

  openTemplateOptions(){
    this.templateSelectionSub = this.styleProfileService.templateSelection.subscribe(this.loadTemplate)
    this.styleProfileService.templateSelectionReq.next(true);
    this.templateSelectionCancelationSub = this.styleProfileService.templateSelectionReq.subscribe((isActivate) => {
      if (!isActivate){
        this.endTemplateListener()
      }
    })
  }

  endTemplateListener(){
    if(this.templateSelectionSub) {
      this.templateSelectionSub.unsubscribe()
    }
    if(this.templateSelectionCancelationSub) {
      this.templateSelectionCancelationSub.unsubscribe()
    }
    this.templateSelectionSub = null;
    this.templateSelectionCancelationSub = null;
  }

  /**
   * Helper function for syncing values from the previous content to the new version's content.
   * @param templateDef the newly loaded template definition.
   * @param templateConfig the parsed template configuration.
   */
  migrateCurrentContent(templateDef:ITemplateDef, templateConfig: TemplateVersionConfig) {
    upgradeTemplateContent(templateDef, templateConfig, this.element);
  }

  getUpgradeColor() {
    if(this.isReadOnly()) {
      return 'grey';
    }
    if(!this.newVersionHasMigrationOptions()) {
      return '#f14668'
    }
    return '';
  }

  hasNewVersion() {
    if(!this.element.templateId) {
      return false;
    }
    
    const templateConfig = this.styleProfileService.templateConfigRef.get(this.element.templateId);

    if(!templateConfig) {
      return false;
    }

    return templateConfig.current_version_id != this.element.templateVersion;
  }

  newVersionHasMigrationOptions() {
    try {
      const templateConfig = this.styleProfileService.templateConfigRef.get(this.element.templateId);
  
      if(!templateConfig) {
        return false;
      }
  
      if(!templateConfig.migrationOptions) {
        return false;
      }
  
      const migrationOptions = JSON.parse(templateConfig.migrationOptions);
  
      return migrationOptions.length > 0;
    } catch (err) {
      return false;
    }
  }

  upgradeVersion() {
    let captionSlug = 'lbl_upgrade_template_confirm';

    if(!this.newVersionHasMigrationOptions()) {
      captionSlug = 'lbl_upgrade_template_confirm_warning';
    }

    const caption = this.lang.tra(captionSlug);

    this.loginGuard.confirmationReqActivate({
      caption,
      confirm: () => {
        const newTemplateDef = this.styleProfileService.templates.find((template) => template.id == this.element.templateId);
        this.loadTemplate(newTemplateDef);
      }
    });
  }

  loadTemplate = (templateDef:ITemplateDef) => {
    this.endTemplateListener()
    const templateConfig = this.styleProfileService.templateConfigRef.get(templateDef.id);

    if(templateDef.id == this.element.templateId) {
      this.migrateCurrentContent(templateDef, templateConfig);
    } else {
      this.element.templateName = templateDef.template_name;
      this.element.templateId = templateConfig.tqt_id;
      this.element.templateVersion = templateConfig.current_version_id;
      this.element.templateDescription = templateDef.description ;

      try{
        this.element.content = JSON.parse(templateConfig.content_config);
      } catch(err) {
        this.element.content = [];
        alert('An error has occurred loading the template configurations');
      }
      try{
        this.element.migrationOptions = JSON.parse(templateConfig.migrationOptions);
      } catch(err) {
        this.element.migrationOptions = [];
        alert('An error has occurred loading the template configurations');
      }
      try{
        this.element.templateConfigs = JSON.parse(templateConfig.template_config);
      } catch(err) {
        this.element.templateConfigs = [];
        alert('An error has occurred loading the template configurations');
      }
    }

    if(this.element.templateConfigs && this.hasTranslation()) {
      this.syncTranslations();
    }

    if(this.hasValidator()) {
      this.autoAlignValidators();
    }
    this.initCallbackSubscription();
  }

  hasTemplateConfigs() {
    return this.element.templateConfigs?.length > 0;
  }

  isConfigTypeMatch(configType: string, config: TemplateConfigDefRow) {
    return config.configType == configType;
  }

  testValueByPath(elementRef: string) {
    console.log(this.getValueByPath(elementRef));
  }

  isLabelFR(config: TemplateConfigDefRow) {
    return this.lang.getCurrentLanguage() === 'fr' && config.label_fr && config.label_fr.length > 0;
  }
  /**
   * Helper function to get the value of a property based on elementRef
   * @param elementRef The element reference path for the required property.
   * @param newValue The new value for the property
   */
  getValueByPath(elementRef: string) {
    return getValueByPath(elementRef, this.element.content);
  }

  /**
   * Helper function to set the value of objects based on elementRef
   * @param elementRef The element reference path for the required property.
   * @param newValue The new value for the property
   */
  setValueByPath(elementRef: string, newValue: any): void {
    const content = this.element.content;
    const keys = elementRef.split('.');
    const lastKey = keys.pop();
    const lastObject = keys.reduce((currentObject, key) => {
        const index = isNaN(+key) ? key : parseInt(key, 10);
        if (!currentObject[index]) {
            throw new Error('Invalid path or object does not exist at specified path');
        }
        return currentObject[index];
    }, content);
    lastObject[lastKey] = newValue;

    this.traverseAndUpdate();
  }

  /**
   * Helper function to traverse and update all elements in the template for blocks that require change counter updates.
   * @param content Template content 
   */
  traverseAndUpdate(content: any = this.element.content) {
    if (typeof content !== 'object' || content === null) {
        return;
    }

    if (content.hasOwnProperty('elementType') && content.elementType != ElementType.VIRTUAL_TOOLS) {
        updateChangeCounter(content);
    }

    for (const key in content) {
        if (content.hasOwnProperty(key)) {
            this.traverseAndUpdate(content[key]);
        }
    }
  }
  
  getParentPath(elementRef: string, level = 1): string|undefined {
    const keys = elementRef.split('.');
    if (keys.length < level) return '';
    return keys.slice(0, keys.length - level).join('.');
  }

  getIconByElementTypeId(elementTypeId:string){
    return elementIconById.get(elementTypeId);
  }

  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }

  drop(arr:any, event: CdkDragDrop<string[]>) {
    moveItemInArray(arr, event.previousIndex, event.currentIndex);
  }

  changeToFrame() {
    this.element.elementType = ElementType.FRAME;
  }

  toggleValue(obj: any, key: string) {
    obj[key] = !obj[key];
  }

  onImageUploaded() {
    const content = this.element.content;
    this.traverseAndUpdate();
  }

  removeElement(content:any[], element:any){
    if (this.isReadOnly()) return;
    if (window.confirm('Remove this part?')){
      let i = indexOf(content, element);
      if (i !== -1){
        content.splice(i, 1)
      }
    }

    if(this.hasValidator()) {
      this.autoAlignValidators();
    }
  }
  
  objectClearAndAssign(target: any, source: any) {
    for (let key in target) {
      if (target.hasOwnProperty(key)) delete target[key];
    }
    return Object.assign(target, source);
  }
  

  ensureResponseEntryIds() {
    ensureResponseEntryIdsLocal(this.element.content);
  }

  alignValidators() {
    if(this.hasValidator()) {
      this.autoAlignValidators();
    }
  }
  
  getMcqTypeFromOption(mcq: IContentElementMcq) {
    const firstOption = mcq.options?.[0];
    if (firstOption == undefined) return 'text';
    
    if (firstOption.elementType == 'text') {
      if (firstOption.paragraphStyle == 'advanced-inline' || firstOption.paragraphStyle == 'bullet') {
        return 'advanced-inline';
      } else {
        return 'text';
      }
    } else {
      return firstOption.elementType;
    }
  }

  insertMCQEntry(
    options:any[],
    elementType: ElementType | string,
    paragraphStyle: TextParagraphStyle | string = TextParagraphStyle.REGULAR) {
    let content:any = '';
    const optionElement:IContentElementMcqOption = {
      ... createDefaultElement(elementType),
      elementType,
      optionType: ElementType.MCQ_OPTION,
      content,
      isCorrect: false,
      optionId: -1,
      link: generateDefaultElementTextLink()
    };

    if (paragraphStyle === TextParagraphStyle.ADVANCED_INLINE) {
      optionElement.paragraphStyle = TextParagraphStyle.ADVANCED_INLINE;
    } else if (paragraphStyle === TextParagraphStyle.BULLET) {
      optionElement.paragraphStyle = TextParagraphStyle.BULLET;
    } else {
      delete optionElement.paragraphStyle;
    }

    options.push(optionElement)
  }

  insertMCQTableEntry(content: IContentElementMcq, elementType: ElementType | string) {
    if (elementType === TextParagraphStyle.ADVANCED_INLINE) {
      this.insertMCQEntry(content.options, ElementType.TEXT);
      this.alignMCQTableColCounts(content, ElementType.TEXT, true);
      return;
    }
    this.insertMCQEntry(content.options, elementType);
    this.alignMCQTableColCounts(content, elementType);
  }

  setCellElementType(col, elementType: ElementType | string, cellType: ElementType | string, isAdvInline: boolean = false){
    let content:any = '';
    const optionElement:IContentElementMcqOption = {
      ...createDefaultElement(elementType),
      elementType,
      optionType: ElementType.MCQ_OPTION,
      content,
      isCorrect: false,
      optionId: -1,
      link: generateDefaultElementTextLink()
    };
    if (isAdvInline) {
      optionElement.paragraphStyle = TextParagraphStyle.ADVANCED_INLINE;
    }
    col.cellInfo = optionElement;
    col.cellType = cellType;
    return col;
  }

  alignMCQTableColCounts(content: IContentElementMcq, elementType: string = 'text', isAdvInline: boolean = false){
    for (let option of content.options){
      const rowOption = <IContentElementMcqOptionInTable> option;
      if (!rowOption.cols){
        rowOption.cols = []
      }
      const targetLength = content.tableCols.length;
      for (let i=rowOption.cols.length; i>targetLength; i--){
        rowOption.cols.pop()
      }
      for (let i=rowOption.cols.length; i<targetLength; i++){
        let newCell = createDefaultElement(ElementType.MCQ_TABLE_TEXT) as IContentElementMcqOption;
        if (elementType == ElementType.MATH) {
          newCell = this.setCellElementType(newCell, elementType, elementType);
        } else if (isAdvInline) {
          newCell = this.setCellElementType(newCell, ElementType.TEXT, 'advanced-inline', true);
        }
        rowOption.cols.push(newCell)
      }
    }
  }

  addMCQTableCol(content: IContentElementMcq, elementType: ElementType | string = ElementType.TEXT){
    if (content.tableCols.length < 10){
      const newColCell = {} as IContentElementMcqTableColEachCell
      content.tableCols.push(newColCell)  
    }
    if (elementType === TextParagraphStyle.ADVANCED_INLINE) {
      this.alignMCQTableColCounts(content, ElementType.TEXT, true);
      return;
    }
    this.alignMCQTableColCounts(content, elementType);  
  }

  removeMCQTableCol(content: IContentElementMcq, index: number){
    if (content.tableCols.length < 2){
      return;
    }
    if (!window.confirm('Remove this element?')){
      return;
    }

    content.tableCols.splice(index, 1);
    content.options.forEach((option) => {
      if(option.cols) {
        option.cols.splice(index, 1);
      }
    });
    
    this.alignMCQTableColCounts(content);  
  }

  getRowPlaceholder(index: number) {
    return `column ${index + 1}`
  }

  testLog(obj: any) {
    console.log(obj, 'testing');
  }

  generateDefaultSelectTableCell() {
    const obj = generateDefaultElementText(ElementType.TEXT) 
    return { 
      elementType: ElementType.SELECT_TABLE_CELL,
      content: obj 
    };
  }

  selectTableInsertAnswer(element: any) {
    const elGrid = element.checkBoxRows;
    element.topRow.push(this.generateDefaultSelectTableCell());
    elGrid.forEach((row)=>{
      row.push({value: false});
    })
    
    this.traverseAndUpdate();
  }

  // options for justify-content mapped alignIds
  alignMapping = {
    left: 'flex-start',
    center: 'center',
    right: 'flex-end'
  }

  /*
    Set the column alignment for the select table.
    Case 1: Leftmost column
    The topleft text does not get aligned. It stays center aligned by default since
    it's fixed.
    Case 2: Other columns
    Only the column header gets aligned since the checkbox is supposed to only be
    center aligned.
  */
  selectTableSetColumnAlignment(element: any, alignId: string, col_i: number = -1) {
    if (col_i !== -1) {
      // Align col header for column col_i
      element.topRow[col_i].alignText = alignId;
      element.topRow[col_i].alignItems = this.alignMapping[alignId];
      element.topRow[col_i].content.alignment = alignId;
    } else {
      // Align leftmost column (except header)
      element.leftCol.forEach(col => {
        col.alignText = alignId
        col.alignItems = this.alignMapping[alignId];
        col.content.alignment = alignId;
      });
    }

  }

  /*
    Extract the value of a field from cells in a specific column col.
    For ex: get alignText value of cells in column col
  */
  selectTableGetColumnField(col, field) {
    if (col.length === 0) {
      return;
    }

    let value = col[0][field];
    if (!value) {
      for (let i = 1; i < col.length; i++) {
        value = col[i][field];
        if (value) {
          return value;
        }
      }
    }

    return value;
  }

  selectTableInsertQuestion(element: any) {
    const elGrid = element.checkBoxRows;
    const newCell: any = this.generateDefaultSelectTableCell();
    const align = this.selectTableGetColumnField(element.leftCol, 'alignText');
    newCell.alignText = align;
    newCell.alignItems = this.selectTableGetColumnField(element.leftCol, 'alignItems');
    newCell.content.alignment = align;
    element.leftCol.push(newCell);
    elGrid.push([]);
    for (let i = 0;i<element.topRow.length;i++) {
      elGrid[elGrid.length-1].push({value: false});
    }

    this.traverseAndUpdate();
  }

  selectTableDeleteColumn(element: any, col:number) {
    if (this.isReadOnly()) return;
    if (element.topRow.length==1) {
      alert("Table must have at least one column");
      return;
    }
    if (confirm("Are you sure you want to delete this column?")) {
      const topRow = element.topRow;
      if (topRow.length==1) return;
      element.topRow.splice(col, 1);
      element.checkBoxRows.forEach((row, index)=>{
        row.splice(col,1);
      })
    }
    this.traverseAndUpdate();
  }

  selectTableDeleteRow(element: IContentElementSelectionTable, row:number) {
    if (this.isReadOnly()) return;
    if (element.leftCol.length==1) {
      alert("Table must have at least one row");
      return;
    }
    if (confirm("Are you sure you want to delete this row?")) {
      const leftCol = element.leftCol;
      if (leftCol.length==1) return;
      leftCol.splice(row, 1);
      element.checkBoxRows.splice(row, 1);
    }
    this.traverseAndUpdate();
  }

  tableGetCell(element: any, row_i:number, col_i:number){
    return element.grid[row_i][col_i];
  }

  tableGetCellRowSpan(element: any, row_i: number, col_i: number){
    const cell = this.tableGetCell(element, row_i, col_i);
    if (cell.mergedCellId){
        return cell.mergedCellRowSpan + 1;
    }
    return 1;
  }

  getImageText(element) {
    if (element.altText.length > 0) {
      return element.altText;
    }
    return 'Image';
  }

  // Get all advanced list elements except image
  getSelectionTableAdvancedInlineElementsMinusImage(element) {
    return element.filter(child => child.elementType !== ElementType.IMAGE);
  }

  tableGetCellColSpan(element: any, row_i: number, col_i: number){
    const cell = this.tableGetCell(element, row_i, col_i);
    if (cell.mergedCellId){
        return cell.mergedCellColSpan + 1;
    }
    return 1;
  }

  tableFindMergedCellGroup(element: any, targetMergedCellId: number) {
    const cells = [];
    element.grid.forEach((row, row_i) => {
      row.forEach((column, col_i) => {
        const cell = this.tableGetCell(element, row_i, col_i)
        if (cell.mergedCellId === targetMergedCellId) cells.push(cell);
      })
    })
    return cells;
  }

  tableClearMergedCellDataInCellElement(elementCell: IContentElementTableCell){
    elementCell.mergedCellId = undefined;
    elementCell.mergedCellTopLeft = undefined;
    elementCell.mergedCellBottomRight = undefined;
    elementCell.mergedCellRowSpan = undefined;
    elementCell.mergedCellColSpan = undefined;
  }

  tableCleanUpMergedCellGroup(element: any){
    const mergeCellGroupIds = new Map<number, IContentElementTableCell[]>();
    element.grid.forEach((row, row_i) => {
      row.forEach((column, col_i) => {
        const cell = this.tableGetCell(element, row_i, col_i)
        if (cell.mergedCellId !== undefined){
          const mapEntryValue = mergeCellGroupIds.get(cell.mergedCellId);
          if (mapEntryValue) mapEntryValue.push(cell);
          else mergeCellGroupIds.set(cell.mergedCellId, [cell]);
        }
      })
    })

    const mergedGroupsWithOnlyOneCell = Array.from(mergeCellGroupIds.values()).filter((cells) => cells.length === 1);
    mergedGroupsWithOnlyOneCell.forEach(cell => {
      this.tableClearMergedCellDataInCellElement(cell[0]);
    })
  }


  hasValidator() {
    return this.element.templateConfigs && this.element.templateConfigs.find((conf) => conf.configType == 'validator') != undefined;
  }

  autoAlignValidators() {
    const validators = this.element.templateConfigs.filter((conf) => conf.configType == 'validator');
    validators.forEach((config) => {
      if(!config.secondaryRef) {
        alert('VALIDATOR_CONFIG_INVALID');
        return;
      }
      this.syncValidatorEntries(config.elementRef, config.secondaryRef);
    })
  }

  syncValidatorEntries(elementRef: string, secondaryRef: string) {
    const element: IContentElementValidator = this.getValueByPath(elementRef);
    const entryContainer: IContentElement[] = this.getValueByPath(secondaryRef);
    if(element.mode == ValidatorMode.ALL_OR_NOTHING) {
      const entryIds: number[] = [];
      this.traverseAndEnsureValidatorAON(entryContainer, entryIds);
      element.targetEntryIdList = entryIds.join(',');
      return;
    } else if(element.mode == ValidatorMode.ADVANCED_COMBINATION) {
      const entries: IValidatorAdvCombinationEntry[] = [];
      this.traverseAndEnsureValidatorAdvCombination(entryContainer, entries)
      element.advCombinationEntries = entries;
      this.alignAdvCombinations(element);
    }
  }

  getValidatorIDs(validator: IContentElementValidator) {
    if(validator.mode == ValidatorMode.ALL_OR_NOTHING) {
      return validator.targetEntryIdList
    } else if(validator.mode == ValidatorMode.ADVANCED_COMBINATION) {
      return validator.advCombinationEntries.map((entry) => entry.validateId).join(',')
    }
  }

  /**
   * Function that goes through each MCQ or input element, and adds each entry ID to the specified array. 
   * Also explicitly sets scoreWeight to 0 automatically for the element.
   * @param content the object or array to traverse.
   * @param entries array that the entry IDs will be pushed to.
   */
  traverseAndEnsureValidatorAdvCombination(content: any, entries: IValidatorAdvCombinationEntry[]) {
    this.ensureResponseEntryIds();
    if (typeof content !== 'object' || content === null) {
        return;
    }

    if (content.hasOwnProperty('elementType') && validatorCompatibleTypes.includes(content.elementType) && content.entryId != undefined && content.entryId != null) {
      // Set all validator inputs and options to scoreWeight 0
      content.scoreWeight = 0;
      // include element only if it's not optional
      if (!content.isOptional) {
        const combinationEntry: IValidatorAdvCombinationEntry = {
          validateId: content.entryId as number,
          elementType: content.elementType
        }
        entries.push(combinationEntry);
      }
    }

    for (const key in content) {
        if (content.hasOwnProperty(key)) {
            this.traverseAndEnsureValidatorAdvCombination(content[key], entries);
        }
    }
  }

    /**
   * Function that goes through each MCQ or input element, and adds each entry ID to the specified array. 
   * Also explicitly sets scoreWeight to 0 automatically for the element.
   * @param content the object or array to traverse.
   * @param entryIds array that the entry IDs will be pushed to.
   */
    traverseAndEnsureValidatorAON(content: any, entryIds: number[]) {
      this.ensureResponseEntryIds();
      if (typeof content !== 'object' || content === null) {
          return;
      }
  
      if (content.hasOwnProperty('elementType') && validatorCompatibleTypes.includes(content.elementType) && content.entryId != undefined && content.entryId != null) {
        // Set all validator inputs and options to scoreWeight 0
        content.scoreWeight = 0;
        // include element only if it's not optional
        if (!content.isOptional) entryIds.push(content.entryId);
      }
  
      for (const key in content) {
          if (content.hasOwnProperty(key)) {
              this.traverseAndEnsureValidatorAON(content[key], entryIds);
          }
      }
    }

  createDefaultInlineCell = (paragraphStyle: TextParagraphStyle = TextParagraphStyle.ADVANCED_INLINE) => {
    const cell:IContentElementText = <IContentElementText>createDefaultElement(ElementType.TEXT);
    cell.paragraphStyle = paragraphStyle;
    cell.advancedList = [];
    cell.advancedList.push(createDefaultElement(ElementType.TEXT));

    return cell;
  }

  /*
    Get the alignment of column col_i in the table. If there are no elements
    in the column, align defaults to center. Alignment is found by iterating
    over column elements. When the alignment is found, it is immediately returned.
    If it is not found, align defaults to center.
  */
  getCellAlignmentforTableElement(element, col_i) {
    const num_rows = element.grid.length;
    if (num_rows === 0) {
      return;
    }

    let align = element.grid[0][col_i].align;
    if (!align) {
      for (let i = 1; i < num_rows; i++) {
        align = element.grid[i][col_i].align;
        if (align) return align;
      }
    }
    return align;
  }

  tableAddRow(element: any){
    const numCols = element.grid.length ? element.grid[0].length : 0;
    const modelRow = [];
    for (let i=0; i<numCols; i++){
      const cell = <IContentElementTableCell>createDefaultElement(ElementType.TABLE_TEXT);
      cell.align = this.getCellAlignmentforTableElement(element, i);
      modelRow.push(cell);
    }
    element.grid.push(modelRow);

    if(this.hasValidator()) {
      this.autoAlignValidators();
    }
  }

  selectTableElements: { id: string, label: string }[] = [
    {id: 'select_table_text', label: 'Text'},
    {id: 'math', label: 'Math'},
    {id: 'image', label: 'Image'},
    {id: 'advanced_inline', label: 'Advanced Inline'},
  ]

  /*
    Build an mcq element with options of type paragraphStyle.
    Customize display style, number of options, whether radio
    and label is disabled or not.
  */
  buildAdvancedInlineMCQ(
    displayStyle: McqDisplay,
    numOptions: number = 4,
    paragraphStyle: TextParagraphStyle = TextParagraphStyle.ADVANCED_INLINE,
    isRadioDisabled: boolean = false,
    isOptionLabelsDisabled: boolean = false
  ): IContentElementMcq {
    const advancedInlineMcqElement: IContentElementMcq = <IContentElementMcq> createDefaultElement(ElementType.MCQ);
    advancedInlineMcqElement.displayStyle = displayStyle;
    advancedInlineMcqElement.isRadioDisabled = isRadioDisabled;
    advancedInlineMcqElement.isOptionLabelsDisabled = isOptionLabelsDisabled;
    advancedInlineMcqElement.options = [];
    for (let i = 0; i < numOptions; i++) {
      advancedInlineMcqElement.options.push(
        {...this.createDefaultInlineCell(paragraphStyle), optionType: ElementType.MCQ_OPTION, content: '', isCorrect:true, optionId: -1, link: generateDefaultElementTextLink()}
      );
    }
    advancedInlineMcqElement.options.push();
    return advancedInlineMcqElement;
  }

  isElementAdvancedInline(paragraphStyle) {
    return [TextParagraphStyle.ADVANCED_INLINE, TextParagraphStyle.BULLET].includes(paragraphStyle);
  }

  onSelectTableChange(newElement: string, cell: any) {
    const alignment = cell.alignment;

    if (newElement === 'select_table_text') {
      const textElement = createDefaultElement(ElementType.TEXT);
      this.objectClearAndAssign(cell, textElement);
    } else if (newElement === 'math') {
      this.objectClearAndAssign(cell, createDefaultElement(ElementType.MATH));
    } else if (newElement === 'image') {
      this.objectClearAndAssign(cell, createDefaultElement(ElementType.IMAGE));
    } else if (newElement === 'advanced_inline') {
      const advCell = this.createDefaultInlineCell();
      this.objectClearAndAssign(cell, advCell);
    }

    // The alignment should remain the same when element type is changed
    cell.alignment = alignment;
    if (newElement === 'advanced_inline') {
      cell.advancedList.forEach(sub_text => {
        sub_text.alignment = alignment;
      })
    }

    if(this.hasValidator()) {
      this.autoAlignValidators();
    }
  }

  getTableElement(element: IContentElementMcq, isSelectTable: boolean = false) {

    if (element.elementType === 'text' && isSelectTable) {
      return 'select_table_text';
    } else if(element.elementType == 'math') {
      return 'math';
    } else if (element.elementType == 'mcq' && element.displayStyle == McqDisplay.CUSTOM_DROPDOWN && element.options[0].elementType == this.elementTypes.TEXT && element.options[0].paragraphStyle == 'advanced-inline') {
      return 'dropdown_advanced_inline';
    } else if (element.elementType == 'mcq' && element.displayStyle == McqDisplay.VERTICAL && element.options[0].elementType == this.elementTypes.TEXT && element.options[0].paragraphStyle == 'advanced-inline') {
      return 'vertical_advanced_inline';
    } else if (element.elementType == 'mcq' && element.displayStyle == McqDisplay.VERTICAL && element.options[0].elementType == this.elementTypes.TEXT && element.options[0].paragraphStyle == 'bullet') {
      return 'vertical_advanced_text_bullet';
    } else if(element.elementType == 'mcq' && element.displayStyle == McqDisplay.DROPDOWN && element.options[0].elementType == this.elementTypes.TEXT) {
      return 'dropdown_text';
    } else if(element.elementType == 'mcq' && element.displayStyle == McqDisplay.CUSTOM_DROPDOWN && element.options[0].elementType == this.elementTypes.MATH) {
      return 'dropdown_math';
    } else if(element.elementType == 'text' && element.paragraphStyle == TextParagraphStyle.ADVANCED_INLINE) {
      return 'advanced_inline';
    } else if (element.elementType == 'mcq' && element.displayStyle == McqDisplay.VERTICAL && element.options[0].elementType == this.elementTypes.TEXT) { 
      return 'vertical_text';
    } else if (element.elementType == 'mcq' && element.displayStyle == McqDisplay.VERTICAL && element.options[0].elementType == this.elementTypes.MATH) { 
      return 'vertical_math';
    } else if (element.elementType == 'text') {
      return 'text';
    } else if (element.elementType == 'input') {
      return 'keyboard_input';
    } else if(element.elementType === 'image') {
      return 'image';
    }

    return 'text';
  }
  
  setMcqStyle(mcqElement: IContentElementMcq) {
    const defaultStyle = this.styleProfileService.getDefaultMcqStyles();
    const style = defaultStyle?.[0]
    if (!style) return;
    const displayStyle: mcqStyleSub = style[mcqElement.displayStyle];
    if (displayStyle) this.setObjectStyle(mcqElement, displayStyle);
  }

  /*
    Set the horizontal and vertical alignment for mcq table columns
  */
  setColAlignmentForMcqTable(element: IContentElementMcq, col_i: number, alignId: string, isVertical: boolean) {
    element.options.forEach(option => {
      if (isVertical) {
        option.cols[col_i].alignVertical = alignId;
      } else {
        option.cols[col_i].align = alignId;
      }
    })
  }

  setObjectStyle(obj: any, style : any){
    for (let key in style) obj[key] = style[key];
  }

  addRatioTerm(ratioTerms: string[]){
    const rt = null;
    ratioTerms.push(rt);
  }

  removeLastElement(content: any[]) {
    if(content.length == 0) {
      return;
    }
    return content.pop();
  }

  setValueByIndex(content: any[], index: number, newValue: any): void {
    content[index] = newValue.target.value;
    this.traverseAndUpdate();
  }
  
  /**
  Create a guide box that follows the mouse
  This is used to show the user the size of the element they are about to move
  */
  createMouseGuideBox(width: number, height: number) {
    if (this.removeMouseGuidebox) this.removeMouseGuidebox();
    
    let guideBox: HTMLDivElement = this.renderer.createElement('div');
    guideBox.style.width = width + 'px';
    guideBox.style.height = height + 'px';
    guideBox.style.backgroundColor = 'rgba(0, 0, 0, 0)';
    guideBox.style.left = '0';
    guideBox.style.top = '0';
    guideBox.style.transform = 'translate(-50%, -50%)';
    guideBox.style.visibility = 'hidden';
    guideBox.style.position = 'absolute';
    guideBox.style.zIndex = '2000';
    guideBox.style.pointerEvents = 'none';
    guideBox.style.border = '1px dashed black';
    document.body.appendChild(guideBox);
    
    // guideBox will follow the mouse
    let listener = this.renderer.listen('document', 'mousemove', (event) => {
      requestAnimationFrame(() => {
        guideBox.style.visibility = 'visible';
        guideBox.style.left = event.clientX + 'px';
        guideBox.style.top = event.clientY + 'px';
      });
    });
    let removeMouseGuidebox = () => {
      guideBox.remove();
      listener();
    };
    this.removeMouseGuidebox = removeMouseGuidebox;
  }
  
  requestMouseClickPosition(
    ev: MouseEvent, guideSize: {x: number,y: number},
    callback: (mousePos: { x: number, y: number }) => any,
  ){
    const buttonElement = ev.target;
    if (this.pageClickListener) this.pageClickListener();
    
    this.createMouseGuideBox(guideSize.x, guideSize.y);
    
    let cancelListener: CallableFunction;
    let cleanup = () => {
      cancelListener();
      this.removeMouseGuidebox?.();
      this.pageClickListener?.();
      this.currentMovingElementPath = undefined;
    }
    
    cancelListener  = this.renderer.listen('document', 'contextmenu', (event) => {
      event.preventDefault();
      cleanup();
    });
    this.pageClickListener = this.renderer.listen('document', 'click', (event) => {
      if (event.target == buttonElement) return;
      event.preventDefault();
      cleanup();
      const mousePos = { x: event.clientX, y: event.clientY };
      callback(mousePos);
    });
  }
  
  /**
    NOTE: This method is prone to error, as it relies on the structure of the html
    We might want to consider a more robust way of getting the size of the option
  */
  getMcqFreformOptionSize(mcqContainerDiv: HTMLDivElement, optionIndex: number): {x:number, y:number} {
    // get all childrens (might be nested) of mcqContainerDiv that have class "option-button-container"
    const optionButtonContainers = mcqContainerDiv.getElementsByClassName('option-button-container');
    const optionButtonHTMLElement = optionButtonContainers[optionIndex];
    if (optionButtonHTMLElement == undefined) return {x: 10, y: 10};
    const optionButtonRect = optionButtonHTMLElement.getBoundingClientRect();
    return {x: optionButtonRect.width, y: optionButtonRect.height};
  }
  
  isMcqFreeformBeingMoved(mcqOptionsRef: string, optionIndex: number): boolean {
    const optionRef = `${mcqOptionsRef}.${optionIndex}`;
    return this.currentMovingElementPath == optionRef;
  }
  
  requestMoveMcqFreeformOption(ev: MouseEvent, mcqOptionsRef: string, optionIndex: number) {
    const mcqElementRef = this.getParentPath(mcqOptionsRef);
    const mcqElement = this.getValueByPath(mcqElementRef);
    const mcqSharedObject: IMcqSharedObject = this.sharedObjectMap.get(mcqElement);
    const mcqContainerDiv = mcqSharedObject?.mcqContainerDiv;
    if (mcqContainerDiv == undefined) return;
    
    // console.log(mcqContainerDiv);
    const fontSize = parseFloat(window.getComputedStyle(mcqContainerDiv).fontSize);
    const containerPos = mcqContainerDiv.getBoundingClientRect();
    const optionSize = this.getMcqFreformOptionSize(mcqContainerDiv, optionIndex);
    
    const optionRef = `${mcqOptionsRef}.${optionIndex}`;
    this.currentMovingElementPath = optionRef;
    this.requestMouseClickPosition(ev, optionSize, (mousePos) => {
      const optionXPx = mousePos.x - containerPos.x - optionSize.x / 2;
      const optionYPx = mousePos.y - containerPos.y - optionSize.y / 2;
      
      let optionXEm = optionXPx / fontSize;
      let optionYEm = optionYPx / fontSize;
      // round to 1 decimal place
      optionXEm = Math.round(optionXEm * 10) / 10;
      optionYEm = Math.round(optionYEm * 10) / 10;
      
      this.setValueByPath(optionRef + '.x', optionXEm);
      this.setValueByPath(optionRef + '.y', optionYEm);
    })
  }
  
  /**
    NOTE: This method is prone to error, as it relies on the structure of the html
    We might want to consider a more robust way of getting the size of the option
  */
  getKeyboardInputFreformOptionSize(mcqContainerDiv: HTMLDivElement, inputIndex: number): {x:number, y:number} {
    // get all childrens (might be nested) of mcqContainerDiv that have class "option-button-container"
    const optionButtonContainers = mcqContainerDiv.getElementsByClassName('numeric-inputs');
    const optionButtonHTMLElement = optionButtonContainers[inputIndex];
    if (optionButtonHTMLElement == undefined) return {x: 10, y: 10};
    const optionButtonRect = optionButtonHTMLElement.getBoundingClientRect();
    return {x: optionButtonRect.width, y: optionButtonRect.height};
  }
  
  isKeyboardInputFreeformBeingMoved(canvasDisplayListRef: string, inputIndex: number): boolean {
    const optionRef = `${canvasDisplayListRef}.${inputIndex+1}`;
    return this.currentMovingElementPath == optionRef;
  }
  
  requestMoveKeyboardInputFreeformOption(ev: MouseEvent, canvasDisplayListRef: string, inputIndex: number) {
    const canvasElementRef = this.getParentPath(canvasDisplayListRef, 3);
    const canvasElement = this.getValueByPath(canvasElementRef);
    const canvasSharedObject: ICanvasSharedObject = this.sharedObjectMap.get(canvasElement);
    const canvasContainerDiv = canvasSharedObject?.canvasContainerDiv;
    if (canvasContainerDiv == undefined) return;
    
    const fontSize = parseFloat(window.getComputedStyle(canvasContainerDiv).fontSize);
    const containerPos = canvasContainerDiv.getBoundingClientRect();
    const optionSize = this.getKeyboardInputFreformOptionSize(canvasContainerDiv, inputIndex);
    
    const optionRef = `${canvasDisplayListRef}.${inputIndex+1}`;
    this.currentMovingElementPath = optionRef;
    this.requestMouseClickPosition(ev, optionSize, (mousePos) => {
      const optionXPx = mousePos.x - containerPos.x - optionSize.x / 2;
      const optionYPx = mousePos.y - containerPos.y - optionSize.y / 2;
      
      let optionXEm = optionXPx / fontSize;
      let optionYEm = optionYPx / fontSize;
      // round to 1 decimal place
      optionXEm = Math.round(optionXEm * 10) / 10;
      optionYEm = Math.round(optionYEm * 10) / 10;
      
      this.setValueByPath(optionRef + '.x', optionXEm);
      this.setValueByPath(optionRef + '.y', optionYEm);
    })
  }
  
  insertEntry(content:any[], elementType: ElementType, defaultWeight?: number){
    const newElement: any = createDefaultElement(elementType);
    if (elementType === ElementType.INPUT) {
      newElement.inputNumFormat = RESTRICTED;
    }

    if(defaultWeight != undefined) {
      content.push(
        {
          ...newElement
          , scoreWeight: defaultWeight
        }
      );
    } else {
      content.push(newElement);
    }
  }
  
  updateChangeCounter(element: any){
    if (!element._changeCounter){
      element._changeCounter = 0;
    }
    element._changeCounter ++;
  }
  
  updateChangeCounterByRef(elementRef: string){
    const element = this.getValueByPath(elementRef);
    if (element == undefined) {
      console.warn(`Element ${elementRef} not found`);
      return;
    };
    this.throttledUpdate(element);
  }
  
  update(element: any) {
    this.throttledUpdate(element);
  }

  throttledUpdate = _.throttle((element: any) => {
    this.updateChangeCounter(element);
  }, THROTTLE_TIMEOUT);

  insertSelectParagraphOption(elementRef: string){
    const selectTextParagraphs = this.getValueByPath(elementRef).paragraphs;
    const newParagraph = [];
    selectTextParagraphs.push(newParagraph);
    this.insertMCQEntry(newParagraph, ElementType.TEXT)
    this.updateChangeCounterByRef(elementRef)
  }

  private getElementProps() {
    let elementProps: IValidatorCombinationProp = {
      validateId: -1,
      elementType: "",
      correctValue: "",
      dndElements: []
    };
    return elementProps;
  }

  ValidatorMode = ValidatorMode;
  /**
   * Creates a new combination row based on the entry elements
   */
  addAdvCombination(element: IContentElementValidator) {
    if (!element.advCombinations) element.advCombinations = [];
    if(element.advCombinations && element.advCombinations.length) { // If the combination has already been instantiated, copy the format of the 1st row.
      const newCombination: IValidatorCombinationProp[] = element.advCombinations[0].map(obj => {
        const deepCopy: IValidatorCombinationProp = JSON.parse(JSON.stringify(obj));
        deepCopy.correctValue = undefined;
        return deepCopy;
      });
      element.advCombinations.push(newCombination);
      return;
    } else if(element.advCombinationEntries && element.advCombinationEntries.length) { // If the combination has not been instantiated, create a new row based on advCombinationEntries
      const combination: IValidatorCombinationProp[] = [];
      element.advCombinationEntries.forEach((entry) => {
        combination.push({...this.getElementProps(), validateId: entry.validateId, elementType:  entry.elementType});
      });
      element.advCombinations.push(combination);
      return;
    }
    element.advCombinations.push([this.getElementProps()]);
  }

  /**
   * Deletes a specified combination row
   * @param combinationIdx the index of the row to delete
   */
  deleteAdvCombination(element: IContentElementValidator, combinationIdx: number) {
    if (!element.advCombinations) return;
    element.advCombinations.splice(combinationIdx, 1);
  }

  /**
   * 
   * @returns Determines whether or not the configuration has any entry elements
   */
  hasEntries(element: IContentElementValidator) {
    return element.advCombinationEntries && element.advCombinationEntries.length;
  }

  getLabelFromEntryId(entryId: number) {
    return this.findObjectByEntryId(this.element.content, entryId)?.validator_label || entryId;
  }

  findObjectByEntryId(obj: IContentElement | IContentElement[], entryId: number) {
    // Base case: Check if the current object has the desired validator_label
    if (obj && !Array.isArray(obj) && obj.entryId === entryId) {
        return obj;
    }

    // Iterate over all properties of the object
    for (let key in obj) {
        if (obj.hasOwnProperty(key)) {
            // If the property is an object itself, recurse into it
            if (typeof obj[key] === 'object' && obj[key] !== null) {
                let result = this.findObjectByEntryId(obj[key], entryId);
                if (result) {
                    return result;
                }
            }
        }
    }

    // Return null if no matching object is found
    return null;
  }

  alignAdvCombinations(content: IContentElementValidator){
    for (let combination of content.advCombinations){
      const targetLength = content.advCombinationEntries.length;
      // If combination is more than combination entry length, pop until lengths are the same.
      for (let i=combination.length; i>targetLength; i--){
        combination.pop()
      }
      // If combination is less than combination entry length, push until lengths are the same.
      for (let i=combination.length; i<targetLength; i++){
        const newCombinationEl = {...this.getElementProps(), validateId: content.advCombinationEntries[i].validateId, elementType:  content.advCombinationEntries[i].elementType}
        combination.push(newCombinationEl);
      }
      // Ensure entryIDs are correct
      for (let i=0; i<targetLength; i++) {
        if(combination[i].validateId != content.advCombinationEntries[i].validateId) {
          combination[i].validateId = content.advCombinationEntries[i].validateId;
        }
      }
    }
  }

  getMCQOptionLabel(index: number) {
    const alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    if(index >= alphabet.length) {
      return 'Index out of bounds.';
    }

    return alphabet[index];
  }
  
  disableMCQOption(option: IContentElementMcqOption) {
    option.isDisabled = !option.isDisabled;
    if (option.isDisabled) {
      option.isCorrect = false;
    }
  }
  
  subscribeCallbackChange(path: string, callback: ()=>void){
    if (!this.callbackSubscriptions[path]){
      this.callbackSubscriptions[path] = new Subject();
    }
    this.callbackSubscriptions[path].subscribe(callback);
  }
  triggerCallbackChange(path: string) {
    if (this.callbackSubscriptions[path]){
      this.callbackSubscriptions[path].next();
    }
  }
  initCallbackSubscription() {
    if(!this.element.templateConfigs) {
      return;
    }

    for (let conf of this.element.templateConfigs) {
      // mcq-freeform inside canvas
      if (conf.configType == 'mcq-freeform'){
        const mcqElementRef = this.getParentPath(conf.elementRef);
        const mcqElement = this.getValueByPath(mcqElementRef);
        if (mcqElement == undefined) return;
        const possibleCanvasElementRef = this.getParentPath(mcqElementRef, 4);
        const possibleCanvasElement = this.getValueByPath(possibleCanvasElementRef);
        if (possibleCanvasElement == undefined) return;
        if (possibleCanvasElement.elementType == ElementType.CANVAS) {
          this.subscribeCallbackChange(possibleCanvasElementRef, () => {
            mcqElement.width = possibleCanvasElement.width;
            mcqElement.height = possibleCanvasElement.height;
          });
        }
      }
    }
  }
  
  async loadImage(url: string): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = (err) => reject(err);
      img.src = url;
    });
  }
  async getImageSize(imageElement: IContentElementImage): Promise<{ width: number, height: number }> {
    const url = imageElement.url;
    if (!url) return { width: 0, height: 0 };
    try {
      const img = await this.loadImage(url);
      const width = imageElement.scale * 20 / 100;
      const height = width * img.height / img.width;
      return { width, height };
    }
    catch (err) {
      console.error(err);
      return {width: 0, height: 0};
    }
  }
  handleImageChange(imagePath: string){
    // canvas bgImg
    if (imagePath.endsWith('.bgImg')) {
      const possibleCanvasElementRef = this.getParentPath(imagePath);
      const possibleCanvasElement = this.getValueByPath(possibleCanvasElementRef);
      if (possibleCanvasElement == undefined) return;
      if (possibleCanvasElement.elementType == ElementType.CANVAS) {
        const imageElement = this.getValueByPath(imagePath);
        this.getImageSize(imageElement).then((size) => {
          possibleCanvasElement.width = size.width;
          possibleCanvasElement.height = size.height;
          this.triggerCallbackChange(possibleCanvasElementRef);
        });
      }
    }
  }

  setAlignmentForMcqFreeform(option: IContentElementMcqOption, alignId: string) {
    option.align = alignId;
  }
}

<div class="text-list gap" cdkDropList (cdkDropListDropped)="drop(mcqElementOptions, $event)" [class.no-pointer-events]="isReadOnly()">
    <div *ngFor="let option of mcqElementOptions" class="separator draggable" style="display: flex;flex-direction: column; gap: 1em;" cdkDrag>
        <div class="space-between gap">
            <div class="option-input-field" (mousedown)="$event.stopPropagation()">
              <div *ngIf="elementType == 'math'" class="capture-math-container">
                  <capture-math [obj]="option" prop="content" [isManualKeyboard]="true" [class.is-disabled]="isReadOnly()"></capture-math>
              </div>
              <div *ngIf="elementType == 'image'">
                <capture-image [element]="option" fileType="image" [isNoScale]="false" [displayImageControls]="false" (onUploaded)="onImageUploadEmit()"></capture-image>
                <asset-library-link [element]="option"></asset-library-link>
              </div>
              <div *ngIf="elementType == 'advanced-inline'">
                <template-advanced-inline
                    [advancedList]="option.advancedList"
                    [isTextList]="false"
                    [twiddleToggleMap]="twiddleToggleMap"
                    [hasValidator]="hasValidator"
                    [isStaticElementOnly]="true"
                    [allowTableElement]="true"
                    (onRemoveElement)="removeElementEmit($event.content, $event.element)"
                    (onValidatorAlign)="onValidatorAlignEmit()"
                    (onAssignEntryId)="onAssignEntryIdEmit()"
                    (onImageUploaded)="onImageUploadEmit()"
                    (onTwiddleToggle)="onTwiddleToggleEmit($event.elementRef, $event.key, $event.id, $event.toggle)"
                >
                </template-advanced-inline>
              </div>
              <div *ngIf="elementType == 'text'">
                <input
                    textInputTransform
                    type="text" 
                    class="input" 
                    style="text-align:center;" 
                    [(ngModel)]="option.content"
                >
              </div>
            </div>
            <div class="gap" style="display: flex" 
              [style.flex-direction]="elementType == 'advanced-inline' || elementType == 'image' ? 'column' : 'row'">
              <div class="option-container-buttons" (mousedown)="$event.stopPropagation()">
                  <a 
                    class="button is-correct-toggle"
                    [class.is-disabled]="isReadOnly()"
                    [class.is-success] = "option.isCorrect"
                    [class.is-danger]  = "!option.isCorrect"
                    (click)="toggleCorrectOption(option)"
                  >
                    <i 
                      class="fa fa-check" 
                      [class.fa-check] = "option.isCorrect"
                      [class.fa-times] = "!option.isCorrect"
                      style="width: 16px;"
                      aria-hidden="true"
                    ></i>
                  </a>
              </div>
              <!-- Set to true if we bring in MCQ option adding -->
              <a class="button" (click)="removeElementEmit(mcqElementOptions, option);" (mousedown)="$event.stopPropagation()">
                  <i class="fas fa-trash"  aria-hidden="true"></i>
              </a>   
            </div>
        </div>
        <div *ngIf="secondaryElement && secondaryElement.displayStyle == 'freeform'" style="display: flex; flex-direction: column; gap: 1em;">
            <div style="display: flex; flex-direction: row; align-items: center; gap: 0.5em; margin-left: auto">
              <div>X: </div>
              <input 
                  type="number" 
                  class="input is-small" 
                  style="width:5em; text-align:center" 
                  [(ngModel)]="option.x"
                  step="0.1"
              >
              <div>Y: </div>
              <input 
                  type="number" 
                  class="input is-small" 
                  style="width:5em; text-align:center" 
                  [(ngModel)]="option.y"
                  step="0.1"
              >
            </div>
        </div>
    </div>
    <!-- Set to true if we bring in MCQ option adding -->
    <button *ngIf="elementType !== 'advanced-inline'" style="width: 100%" (click)="insertMCQEntry(mcqElementOptions, elementType)" class="button is-primary">Add Option</button>
    <!-- add advanced inline mcq options -->
    <button *ngIf="elementType === 'advanced-inline'" style="width: 100%" (click)="insertMCQEntry(mcqElementOptions, 'text', 'advanced-inline')" class="button is-primary">Add Option</button>
    <twiddle *ngIf="hasValidator" style="width: 100%;" caption="Other Options" (change)="onTwiddleToggleEmit(elementRef, 'mcq_options', mcqEntryId, $event)"></twiddle>
    <div class="text-list gap" *ngIf="getTwiddleToggleInfo(elementRef, 'mcq_options', mcqEntryId)">
        <strong>Entry Labels</strong>  
        <div style="display: flex; gap: 1em; align-items: center;">
            Block Label:
            <input 
                type="text" 
                class="input"
                [(ngModel)]="mcqElementOptions['validator_label']"
            >
        </div>
        <div *ngFor="let option of mcqElementOptions; let i = index;" style="display: flex; gap: 1em; align-items: center;">
            {{getMCQOptionLabel(i)}}
            <input 
                type="text" 
                class="input"
                [(ngModel)]="option.validator_label"
            >
        </div>
    </div>
</div>

<fieldset [disabled]="isReadOnly()">
  <div *ngIf="data.type == 'button'">
    <div class="button-container">
      <button 
        *ngFor="let option of data.options" 
        [disabled]="isReadOnly()" 
        [class.is-info]="value == option.id" 
        class="button is-small"  
        (click)="setValue(option.id)"
      >
        <i *ngIf="option.icon" class="fa {{option.icon}}" style="margin-right: 0.5em;"></i>
        <span *ngIf="option.caption">{{option.caption}}</span>
        <span *ngIf="!option.icon && !option.caption && option.id">{{option.id}}</span>
      </button>
    </div>
  </div>
  <div *ngIf="data.type == 'dropdown'">
    <div class="select is-fullwidth">
      <select [disabled]="isReadOnly()" [ngModel]="value" (ngModelChange)="setValue($event)">
        <option *ngFor="let option of data.options" [value]="option.id">{{option.caption ?? option.id}}</option>
      </select>
    </div>
  </div>
</fieldset>
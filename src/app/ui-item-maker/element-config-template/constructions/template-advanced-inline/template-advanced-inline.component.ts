import { Component, ElementRef, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { AuthScopeSetting, AuthScopeSettingsService } from '../../../auth-scope-settings.service';
import { EditingDisabledService } from '../../../editing-disabled.service';
import { IAdvancedInlineHiddenConfigs, IContentElementTemplate, TemplateConfigDefRow } from '../../../../ui-testrunner/element-render-template/model';
import { ITemplateDef, mcqStyleSub, StyleprofileService, TemplateVersionConfig } from 'src/app/core/styleprofile.service';
import { Subscription } from 'rxjs';
import { createDefaultElement, elementIconById, generateDefaultElementText, generateDefaultElementTextLink } from '../../../item-set-editor/models';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { ElementType, IContentElement, IElementTypeDef } from 'src/app/ui-testrunner/models';
import { updateChangeCounter } from '../../../services/data-bind';
import { indexOf } from '../../../services/util';
import { ElementTypeDefs } from "src/app/ui-testrunner/models/ElementTypeDefs";
import { IContentElementMcq, IContentElementMcqOption, IContentElementMcqOptionInTable, IContentElementMcqTableColEachCell, McqDisplay } from '../../../../ui-testrunner/element-render-mcq/model';
import { getCellColSpan, getCellRowSpan } from '../../../element-config-table/merge-cell-functions';
import { IContentElementTable, IContentElementTableCell } from 'src/app/ui-testrunner/element-render-table/model';
import { LangService } from 'src/app/core/lang.service';
import { IContentElementInput, InputFormat } from 'src/app/ui-testrunner/element-render-input/model';
import { IContentElementValidator } from 'src/app/ui-testrunner/element-render-validator/model';
import { ItemBankCtrl } from '../../../item-set-editor/controllers/item-bank';
import { ensureResponseEntryIds, ensureResponseEntryIdsLocal } from '../../../item-set-editor/models/expected-answer';
import { IContentElementText, TextParagraphStyle } from 'src/app/ui-testrunner/element-render-text/model';
import { IContentElementSelectionTable } from 'src/app/ui-testrunner/element-render-selection-table/model';
import { TwiddleState } from '../../../../ui-partial/twiddle/twiddle.component';
import * as _ from 'lodash';

const RESTRICTED = 'Restricted';
const ADVANCED_INLINE_ID = 'text:advanced-inline';

@Component({
  selector: 'template-advanced-inline',
  templateUrl: './template-advanced-inline.component.html',
  styleUrls: ['./template-advanced-inline.component.scss']
})
export class TemplateAdvancedInlineComponent implements OnInit, OnDestroy {

  inlineElementsDefault: {id: string, label: string}[] = [
    {id: 'text', label: 'Text'},
    {id: 'math', label: 'Math'},
    {id: 'image', label: 'Image'},
    {id: 'dropdown_text', label: 'Dropdown MCQ - Text'},
    {id: 'dropdown_math', label: 'Dropdown MCQ - Math'},
    {id: 'dropdown_advanced_inline', label: 'Dropdown - Advanced Inline'},
    {id: 'keyboard_input', label: 'Keyboard Input'},
    {id: 'scientific_notation', label: 'Scientific Notation'},
    {id: 'bookmark_link', label: 'Bookmark Link'},
    {id: 'template', label: 'Template'},
    {id: 'score_entry', label: 'Score Entry'}
  ]

  @Input() config: TemplateConfigDefRow;
  @Input() advancedList: IContentElement[];
  @Input() isTextList: boolean = false;
  @Input() isCanvas: boolean = false;
  @Input() twiddleToggleMap: Map<string, Map<string, Map<number, boolean>>> = new Map();
  @Input() hasValidator: boolean = false;
  @Input() isStaticElementOnly: boolean = false;
  @Input() inlineElements: { id: string, label: string }[];
  @Input() allowBookmarkLink: boolean = true;
  @Input() allowAdvancedInlineElement: boolean = false;
  @Input() allowTableElement: boolean = false;
  @Output() onRemoveElement = new EventEmitter<any>();
  @Output() onValidatorAlign = new EventEmitter<any>();
  @Output() onAssignEntryId = new EventEmitter<any>();
  @Output() onImageUploaded = new EventEmitter<any>();
  @Output() onTwiddleToggle = new EventEmitter<any>();

  elementTypes = ElementType;

  advancedInlineInputFormats = [
    {id: InputFormat.NUMBER,   icon:'fa-calculator', caption:'Number'},
    // {id: InputFormat.FRACTION, icon:'fa-pencil-alt',     caption:'Fraction'},
    {id: InputFormat.RATIO,    icon:'fa-chart-pie',  caption:'Ratio'},
    // {id: InputFormat.ALGEBRA,  icon:'fa-pencil-alt',     caption:'Algebra'},
    // {id: InputFormat.TEXT,     icon:'fa-font',       caption:'Essay / Writing'},
    {id: InputFormat.WORDS,    icon:'fa-font',       caption:'Word(s)'},
    //{id: InputFormat.FORM,     icon:'fa-font', caption: "Form"}
    // {id: InputFormat.NUMBER_LIST,  icon:'fa-calculator',       caption:'Number (List)'},
  ]

  // Do not want to have a mcq within a mcq option
  inlineElementsWithoutMcq: {id: string, label: string}[] = [
    {id: 'text', label: 'Text'},
    {id: 'math', label: 'Math'},
    {id: 'image', label: 'Image'},
    {id: 'keyboard_input', label: 'Keyboard Input'},
    {id: 'scientific_notation', label: 'Scientific Notation'},
    {id: 'bookmark_link', label: 'Bookmark Link'},
    {id: 'template', label: 'Template'},
    {id: 'score_entry', label: 'Score Entry'}
  ]
  staticInlineElements: {id: string, label: string}[] = [
    {id: 'text', label: 'Text'},
    {id: 'math', label: 'Math'},
    {id: 'image', label: 'Image'},
    {id: 'bookmark_link', label: 'Bookmark Link'},
  ]
  staticInlineElementsWithoutBookmarkLink: {id: string, label: string}[] = [
    {id: 'text', label: 'Text'},
    {id: 'math', label: 'Math'},
    {id: 'image', label: 'Image'},
  ]
  inlineElementTable: {id: string, label: string}[] = [
    {id: 'table', label: 'Table'},
  ]
  advancedInlineElement = {id: ADVANCED_INLINE_ID, label: 'Advanced Inline'};

  constructor(
    private authScopeSettings: AuthScopeSettingsService,
    private editingDisabled: EditingDisabledService,
    private styleProfileService: StyleprofileService,
    public lang: LangService,
    private itemBankCtrl: ItemBankCtrl
  ) { }

  ngOnInit(): void {
    if (this.inlineElements == undefined) {
      if (this.isStaticElementOnly && this.allowBookmarkLink) {
        this.inlineElements = this.staticInlineElements;
      } else if (this.isStaticElementOnly && !this.allowBookmarkLink) {
        this.inlineElements = this.staticInlineElementsWithoutBookmarkLink;
      } else {
        this.inlineElements = this.inlineElementsDefault;
      }
    }
    if (this.allowAdvancedInlineElement) {
      this.inlineElements.push(this.advancedInlineElement);
    }
    if (this.allowTableElement) {
      this.inlineElements.push(...this.inlineElementTable);
    }
  }

  ngOnDestroy() :void {
  }
  
  get hiddenConfigs(): IAdvancedInlineHiddenConfigs {
    return this.config?.hiddenConfigs as IAdvancedInlineHiddenConfigs;
  }
  
  removeElementEmit(content: any[], element: any) {
    this.onRemoveElement.emit({content, element});
  }

  insertMCQEntry(options:any[], elementType: ElementType, isAdvancedInline: boolean = false){
    let content:any = '';
    const optionElement:IContentElementMcqOption = {
      ... createDefaultElement(elementType),
      elementType,
      optionType: ElementType.MCQ_OPTION,
      content,
      isCorrect: false,
      optionId: -1,
      link: generateDefaultElementTextLink()
    };

    if (isAdvancedInline) {
      optionElement.paragraphStyle = TextParagraphStyle.ADVANCED_INLINE;
    } else {
      delete optionElement.paragraphStyle;
    }

    options.push(optionElement)
  }

  toggleValue(obj: any, key: string) {
    obj[key] = !obj[key];
  }
  
  objectClearAndAssign(target: any, source: any) {
    for (let key in target) {
      if (target.hasOwnProperty(key)) delete target[key];
    }
    return Object.assign(target, source);
  }

  createDefaultInlineCell = () => {
    const cell:IContentElementText = <IContentElementText>createDefaultElement(ElementType.TEXT);
    cell.paragraphStyle = TextParagraphStyle.ADVANCED_INLINE;
    cell.advancedList = [];
    cell.advancedList.push(createDefaultElement(ElementType.TEXT));

    return cell;
  }

  /*
    Build an mcq element with options of type advanced inline.
    Customize display style and number of options.
  */
  buildAdvancedInlineMCQ(displayStyle: McqDisplay, numOptions: number = 4): IContentElementMcq {
    const advancedInlineMcqElement: IContentElementMcq = <IContentElementMcq> createDefaultElement(ElementType.MCQ);
    advancedInlineMcqElement.displayStyle = displayStyle;
    advancedInlineMcqElement.options = [];
    for (let i = 0; i < numOptions; i++) {
      advancedInlineMcqElement.options.push(
        {...this.createDefaultInlineCell(), optionType: ElementType.MCQ_OPTION, content: '', isCorrect:true, optionId: -1, link: generateDefaultElementTextLink()}
      );
    }
    advancedInlineMcqElement.options.push();
    return advancedInlineMcqElement;
  }
  
  getMcqTypeFromOption(mcq: IContentElementMcq) {
    const firstOption = mcq.options?.[0];
    if (firstOption == undefined) return 'text';
    
    if (firstOption.elementType == 'text') {
      if (firstOption.paragraphStyle == 'advanced-inline' || firstOption.paragraphStyle == 'bullet') {
        return 'advanced-inline';
      } else {
        return 'text';
      }
    } else {
      return firstOption.elementType;
    }
  }

  changeBlockElement(newElement: string, cell: IContentElement, defaultWeight?: number) {
    console.log(newElement, cell);
    if(newElement == 'dropdown_text') {
      const mcqDropdownText: IContentElementMcq = <IContentElementMcq>createDefaultElement(ElementType.MCQ);
      mcqDropdownText.displayStyle = McqDisplay.DROPDOWN
      this.setMcqStyle(mcqDropdownText);
      this.objectClearAndAssign(cell, mcqDropdownText)
      cell = mcqDropdownText;
    } else if(newElement == 'dropdown_math') {
      const mcqDropdownMath: IContentElementMcq = <IContentElementMcq>createDefaultElement(ElementType.MCQ);
      mcqDropdownMath.displayStyle = McqDisplay.CUSTOM_DROPDOWN
      mcqDropdownMath.options = [
        {...createDefaultElement(ElementType.MATH), optionType: ElementType.MCQ_OPTION, content: '', isCorrect:true, optionId: -1, link: generateDefaultElementTextLink()},
        {...createDefaultElement(ElementType.MATH), optionType: ElementType.MCQ_OPTION, content: '', isCorrect:false, optionId: -1, link: generateDefaultElementTextLink()},
      ];
      this.setMcqStyle(mcqDropdownMath);
      this.objectClearAndAssign(cell, mcqDropdownMath)
    } else if (newElement === 'dropdown_advanced_inline') {
      // Dropdown needs to be custom since default cannot handle advanced inline
      const mcqDropdownAdvancedInline: IContentElementMcq = this.buildAdvancedInlineMCQ(McqDisplay.CUSTOM_DROPDOWN);
      this.objectClearAndAssign(cell, mcqDropdownAdvancedInline)
    } else if (newElement == 'text') {
      this.objectClearAndAssign(cell, createDefaultElement(ElementType.TEXT));
    } else if(newElement == 'math') {
      this.objectClearAndAssign(cell, createDefaultElement(ElementType.MATH));
    } else if(newElement == 'keyboard_input') {
      const inputElement: IContentElementInput= createDefaultElement(ElementType.INPUT) as IContentElementInput;
      inputElement.inputNumFormat = RESTRICTED;
      this.objectClearAndAssign(cell, inputElement);
    } else if(newElement == 'vertical_text') {
      const mcqVertText: IContentElementMcq = <IContentElementMcq>createDefaultElement(ElementType.MCQ);
      this.setMcqStyle(mcqVertText);
      this.objectClearAndAssign(cell, mcqVertText)
    } else if(newElement == 'vertical_math') {
      const mcqVertMath: IContentElementMcq = <IContentElementMcq>createDefaultElement(ElementType.MCQ);
      mcqVertMath.options = [
        {...createDefaultElement(ElementType.MATH), optionType: ElementType.MCQ_OPTION, content: '', isCorrect:true, optionId: -1, link: generateDefaultElementTextLink()},
        {...createDefaultElement(ElementType.MATH), optionType: ElementType.MCQ_OPTION, content: '', isCorrect:false, optionId: -1, link: generateDefaultElementTextLink()},
      ];
      this.setMcqStyle(mcqVertMath);
      this.objectClearAndAssign(cell, mcqVertMath)
    } else if (newElement === 'vertical_advanced_inline') {
      const mcqVerticalAdvancedInline: IContentElementMcq = this.buildAdvancedInlineMCQ(McqDisplay.VERTICAL);
      this.setMcqStyle(mcqVerticalAdvancedInline);
      this.objectClearAndAssign(cell, mcqVerticalAdvancedInline);
    } else if(newElement == 'image') {
      this.objectClearAndAssign(cell, createDefaultElement(ElementType.IMAGE));
    } else if(newElement == 'scientific_notation') {
      this.objectClearAndAssign(cell, createDefaultElement(ElementType.SCIENTIFIC_NOTATION));
      cell.isShowAdvancedOptions = false;
    } else if (newElement === 'bookmark_link') {
      this.objectClearAndAssign(cell, createDefaultElement(ElementType.BOOKMARK_LINK));
    } else if (newElement === ADVANCED_INLINE_ID) {
      let advancedInlineElement = <IContentElementText>createDefaultElement(ElementType.TEXT);
      advancedInlineElement.paragraphStyle = TextParagraphStyle.ADVANCED_INLINE;
      advancedInlineElement.alignment = 'left';
      this.objectClearAndAssign(cell, advancedInlineElement);
    } else if (newElement == 'table') {
      this.objectClearAndAssign(cell, createDefaultElement(ElementType.TABLE));
    }

    if(defaultWeight != undefined) {
      (<IContentElementInput>cell).scoreWeight = defaultWeight;
    }

    this.onAssignEntryIdEmit();
    this.onValidatorAlignEmit();
  }

  getElement(element: IContentElementMcq) {
    if(element.elementType == 'math') {
      return 'math';
    } else if (element.elementType == 'mcq' && element.displayStyle == McqDisplay.CUSTOM_DROPDOWN && element.options[0].elementType == this.elementTypes.TEXT && element.options[0].paragraphStyle == 'advanced-inline') {
      return 'dropdown_advanced_inline';
    } else if (element.elementType == 'mcq' && element.displayStyle == McqDisplay.VERTICAL && element.options[0].elementType == this.elementTypes.TEXT && element.options[0].paragraphStyle == 'advanced-inline') {
      return 'vertical_advanced_inline';
    } else if(element.elementType == 'mcq' && element.displayStyle == McqDisplay.DROPDOWN && element.options[0].elementType == this.elementTypes.TEXT) {
      return 'dropdown_text';
    } else if(element.elementType == 'mcq' && element.displayStyle == McqDisplay.DROPDOWN && element.options[0].elementType == this.elementTypes.MATH) {
      return 'dropdown_math';
    } else if(element.elementType == 'text') {
      return 'text';
    } else if(element.elementType == 'input') {
      return 'keyboard_input';
    } else if(element.elementType == 'mcq' && element.displayStyle == McqDisplay.VERTICAL && element.options[0].elementType == this.elementTypes.TEXT) { 
      return 'vertical_text';
    } else if(element.elementType == 'mcq' && element.displayStyle == McqDisplay.VERTICAL && element.options[0].elementType == this.elementTypes.MATH) { 
      return 'vertical_math';
    } else if(element.elementType == 'image') {
      return 'image';
    } else if (element.elementType == 'scientific_notation') {
      return 'scientific_notation';
    } else if (element.elementType === ElementType.BOOKMARK_LINK) {
      return 'bookmark_link';
    } else if (element.elementType === ElementType.TABLE) {
      return 'table';
    }

    return 'text';
  }

  insertAdvancedListDefaultEntry(content:any[], elementType: ElementType, defaultWeight?: number){
    let newElement;
    if(defaultWeight != undefined) {
      newElement = {
        ...createDefaultElement(elementType)
        , scoreWeight: defaultWeight
      };
    } else {
      newElement = createDefaultElement(elementType);
    }

    content.push(newElement);

    this.onAssignEntryIdEmit();
    this.onValidatorAlignEmit();

    return newElement;
  }

  onImageUploadEmit() {
    this.onImageUploaded.next();
  }

  onValidatorAlignEmit() {
    this.onValidatorAlign.next();
  }

  onAssignEntryIdEmit() {
    this.onAssignEntryId.next();
  }

  drop(arr:any, event: CdkDragDrop<string[]>) {
    console.log(arr, event);
    // console.log('drop', arr)
    moveItemInArray(arr, event.previousIndex, event.currentIndex);
  }

  elementTypeForInsertion: string;
  insertAdvancedListEntry(content:IContentElement[], defaultWeight?: number){
    if(!this.elementTypeForInsertion) {
      return;
    }

    console.log(this.elementTypeForInsertion, ' new element');
    const newElement = this.insertAdvancedListDefaultEntry(content, this.elementTypes.TEXT, defaultWeight);
    this.changeBlockElement(this.elementTypeForInsertion, newElement, defaultWeight);
  }

  onTwiddleToggleEmit(elementRef: string, key: string, id: number, toggle: boolean) {
    this.onTwiddleToggle.next({elementRef, key, id, toggle});
  }  

  getTwiddleToggleInfo(elementRef: string, key: string, id: number) {
    if(!this.twiddleToggleMap.has(elementRef)) {
      const keyMap: Map<string, Map<number, boolean>> = new Map();
      const numberMap: Map<number, boolean> = new Map();
      numberMap.set(id, false);
      keyMap.set(key, numberMap);
      this.twiddleToggleMap.set(elementRef, keyMap);
    }
    if(!this.twiddleToggleMap.get(elementRef)?.has(key)) {
      const numberMap: Map<number, boolean> = new Map();
      numberMap.set(id, false);
      this.twiddleToggleMap.get(elementRef)?.set(key, numberMap);
    }
    if(!this.twiddleToggleMap.get(elementRef)?.get(key)?.has(id)) {
      this.twiddleToggleMap.get(elementRef)?.get(key)?.set(id, false);
    }

    return this.twiddleToggleMap.get(elementRef)?.get(key)?.get(id);
  }

  getMCQOptionLabel(index: number) {
    const alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    if(index >= alphabet.length) {
      return 'Index out of bounds.';
    }

    return alphabet[index];
  }
  
  setMcqStyle(mcqElement: IContentElementMcq) {
    const defaultStyle = this.styleProfileService.getDefaultMcqStyles();
    const style = defaultStyle?.[0]
    if (!style) return;
    const displayStyle: mcqStyleSub = style[mcqElement.displayStyle];
    if (displayStyle) this.setObjectStyle(mcqElement, displayStyle);
  }
  
  setObjectStyle(obj: any, style : any){
    for (let key in style) obj[key] = style[key];
  }
  
  isAdvancedInlineBlock(block: IContentElement): boolean {
    return block.elementType === ElementType.TEXT && block.paragraphStyle === TextParagraphStyle.ADVANCED_INLINE;
  }
  
  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }
}

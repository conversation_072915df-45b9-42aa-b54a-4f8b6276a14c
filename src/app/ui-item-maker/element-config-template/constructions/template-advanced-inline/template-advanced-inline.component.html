<div class="text-list" style="gap: 1em;" cdkDropList (cdkDropListDropped)="drop(advancedList, $event);" >
    <div cdkDrag *ngFor="let block of advancedList" class="sub-property-div">
        <div class="separator " style="display: flex; flex-direction: column; gap: 1em;">
            <div cdkDragHandle style="width: 100%; display: flex; justify-content: flex-end; cursor: move;">
                <a class="small-icon" (click)="removeElementEmit(advancedList, block);" [class.no-pointer-events]="isReadOnly()">
                    <i (click)="removeElementEmit(advancedList, block);" class="fas fa-trash"  aria-hidden="true"></i>
                </a> 
            </div>
            <div style="display: flex; justify-content: space-between; gap: 1.25em;">
                <div *ngIf="block.elementType == 'text'" style="width: 100%;">
                    <div *ngIf="!isAdvancedInlineBlock(block)">
                        <textarea
                            textInputTransform
                            cdkTextareaAutosize 
                            [cdkTextareaAutosize]="true" 
                            [cdkAutosizeMinRows]="2"
                            [(ngModel)]="block.caption"
                            class="textarea textarea-text-list"
                        ></textarea>
                    </div>
                    <div *ngIf="isAdvancedInlineBlock(block)">
                      <template-advanced-inline
                        [config]="config"
                        [advancedList]="block.advancedList"
                        [twiddleToggleMap]="twiddleToggleMap"
                        [hasValidator]="hasValidator"
                        [isStaticElementOnly]="isStaticElementOnly"
                        [allowBookmarkLink]="allowBookmarkLink"
                        [allowAdvancedInlineElement]="false"
                        (onRemoveElement)="removeElementEmit($event.content, $event.element)"
                        (onValidatorAlign)="onValidatorAlignEmit()"
                        (onAssignEntryId)="onAssignEntryIdEmit()"
                        (onImageUploaded)="onImageUploadEmit()"
                        (onTwiddleToggle)="onTwiddleToggleEmit($event.elementRef, $event.key, $event.id, $event.toggle)">
                      </template-advanced-inline>
                    </div>
                </div>
                <div *ngIf="block.elementType == 'math'" class="capture-math-container">
                    <capture-math [obj]="block" prop="latex" [isManualKeyboard]="true" [class.is-disabled]="isReadOnly()"></capture-math>
                </div>
                <div *ngIf="block.elementType == 'table'" style="flex-grow: 1;">
                  <template-table
                      [config]="config"
                      [tableElement]="block"
                      [hasValidator]="hasValidator"
                      [twiddleToggleMap]="twiddleToggleMap"
                      [isNonInteractive]="true"
                      (onRemoveElement)="removeElementEmit($event.content, $event.element)"
                      (onValidatorAlign)="onValidatorAlignEmit()"
                      (onAssignEntryId)="onAssignEntryIdEmit()"
                      (onImageUploaded)="onImageUploadEmit()"
                      (onTwiddleToggle)="onTwiddleToggleEmit($event.elementRef, $event.key, $event.id, $event.toggle)"
                  ></template-table>
                </div>
                <!-- [mcqOptionNumber]="op_i" -->
                <capture-image *ngIf="block.elementType == 'image'" [element]="block" fileType="image" [isNoScale]="false" [displayImageControls]="false" (onUploaded)="onImageUploadEmit()"></capture-image>
                <asset-library-link *ngIf="block.elementType == 'image'" [element]="block"></asset-library-link>
                <div *ngIf="block.elementType == 'mcq'" class="text-list" style="width: 100%;">
                  <template-mcq
                    [elementType]="getMcqTypeFromOption(block)"
                    [mcqElementOptions]="block.options"
                    [twiddleToggleMap]="twiddleToggleMap"
                    [hasValidator]="hasValidator"
                    (onRemoveElement)="removeElementEmit($event.content, $event.element)"
                    (onValidatorAlign)="onValidatorAlignEmit()"
                    (onAssignEntryId)="onAssignEntryIdEmit()"
                    (onImageUploaded)="onImageUploadEmit()"
                    (onTwiddleToggle)="onTwiddleToggleEmit($event.elementRef, $event.key, $event.id, $event.toggle)"
                  >
                  </template-mcq>
                </div>
                <template-input
                    *ngIf="block.elementType == 'input'" 
                    style="width: 100%;"
                    [config]="config"
                    [block]="block"
                    (onRemoveElement)="removeElementEmit($event.content, $event.element)"
                >
                </template-input>   
                <div *ngIf="block.elementType == 'scientific_notation'">
                  <small>Entry {{block.entryId}}</small>
                  <element-config-scientific-notation 
                    [element]="block" [isSimplified]="true" 
                    [hiddenConfigs]="hiddenConfigs?.contentHiddenConfigs?.['scientific-notation'] ?? {}"
                  >
                  </element-config-scientific-notation>
                </div>
                <div *ngIf="block.elementType === 'bookmark_link'">
                    <element-config-bookmark-link [element] = "block"></element-config-bookmark-link>
                </div>
            </div>
            <div *ngIf="isCanvas" style="display: flex; flex-direction: column; gap: 1em;">
                <div style="display: flex; gap: 1em;">
                    Global X: 
                    <input 
                        type="number" 
                        class="input" 
                        style="width:120px; text-align:center" 
                        [(ngModel)]="block.x"
                        min="1"
                        step="1"
                    >
                </div>
                <div style="display: flex; gap: 1em;">
                    Global Y: 
                    <input 
                        type="number" 
                        class="input" 
                        style="width:120px; text-align:center" 
                        [(ngModel)]="block.y"
                        min="1"
                        step="1"
                    >
                </div>
            </div>
        </div>
    </div>
    <div class="field has-addons" style="margin-top:1em;">
        <div class="control is-expanded">
            <div class="select is-fullwidth">
                <select [(ngModel)]="elementTypeForInsertion">
                    <option *ngFor="let elementType of inlineElements; let index = index" [value]="elementType.id">
                        {{lang.tra(elementType.label)}}
                    </option>
                </select>
            </div>
        </div>
        <div class="control">
            <button class="button is-primary" (click)="insertAdvancedListEntry(advancedList, config?.defaultWeight)">Add Element</button>
        </div>
    </div>
</div>
<div class="text-list gap">  
    <div class="text-list">
        <div class="configuration-header">
            Columns
        </div>
        <twiddle style="width: 100%;" caption="Column Info" (change)="onTwiddleToggleEmit(elementRef, 'columns' + getMcqOptionNumber(), 0, $event)"></twiddle>
        <div class="text-list gap" *ngIf="getTwiddleToggleInfo(elementRef, 'columns' + getMcqOptionNumber(), 0)">
            <div style="display: flex; flex-flow: column wrap">
                <div *ngFor="let cell of tableElement.grid[0]; let col_i = index;" class="text-list">
                    <div class="space-between">
                        <twiddle style="width: 100%;" [caption]="getColNumber(col_i)" (change)="onTwiddleToggleEmit(elementRef, 'columns_list' + getMcqOptionNumber(), col_i, $event)"></twiddle>
                        <a class="small-icon" (click)="tableDeleteCol(tableElement, col_i)">
                            <i class="fas fa-trash"  aria-hidden="true"></i>
                        </a> 
                    </div>
                    <div *ngIf="getTwiddleToggleInfo(elementRef, 'columns_list' + getMcqOptionNumber(), col_i)" class="text-list">
                        <div  class="buttons is-no-wrap" style="margin-bottom: 0.5em;">
                            <button *ngFor="let align of alignments" [disabled]="isReadOnly()" class="button is-small"  (click)="setColAlignment(tableElement, col_i, align.id)">
                              <i class="fa {{align.icon}}"></i>
                            </button>
                        </div>
                        <div  class="buttons is-no-wrap" style="margin-bottom: 0.5em;">
                            <button *ngFor="let align of vertical_alignments" [disabled]="isReadOnly()" class="button is-small"  (click)="setColVerticalAlignment(tableElement, col_i, align.id)">
                              <i class="{{align.icon}}"></i>
                            </button>
                        </div>
                        <div *ngIf="tableElement.isColWidthConst && tableElement.isColWidthConstIndividual" style="margin-left: 0.5em">
                            Column {{col_i + 1}} Width: 
                            <input type="number" [(ngModel)]="getTableIndividualColWidthConstArray(tableElement)[col_i]"  style="width:5em; margin-left: 1em;">
                        </div>
                    </div>
                </div>
                <div class="column-configuration">
                    <label class="configuration-header">
                        Fix column width
                    </label>
                    <input 
                        type="checkbox"
                        [(ngModel)]="tableElement.isColWidthConst" 
                    >
                </div>
                <div *ngIf="tableElement.isColWidthConst && !tableElement.isColWidthConstIndividual" class="column-configuration">
                    <tra class="configuration-header" slug="auth_width"></tra>
                    <input type="number" [(ngModel)]="tableElement.colWidthConst"  style="width:5em;">
                </div>
                <div *ngIf="tableElement.isColWidthConst" class="column-configuration">
                    <label class="configuration-header">
                        Fix individual column width
                    </label>
                    <input 
                        type="checkbox" 
                        [(ngModel)]="tableElement.isColWidthConstIndividual"
                    >
                </div>
            </div>
            <button class="button is-primary" (click)="tableAddCol(tableElement)">
                Add column
            </button>
        </div>
    </div>
    <div class="text-list">
        <div class="configuration-header">
            Rows
        </div>
        <div class="text-list" style="gap: 1.5em;">
            <div *ngFor="let row of tableElement.grid; let row_i = index;" class="text-list">
                <div class="space-between gap">
                    <twiddle style="width: 100%;" [caption]="getRowNumber(row_i)" (change)="onTwiddleToggleEmit(elementRef, 'rows' + getMcqOptionNumber(), row_i, $event)"></twiddle>
                    <a class="button" style="margin-right: 0px;" (click)="tableDeleteRow(tableElement, row_i)">
                        <i class="fas fa-trash"  aria-hidden="true"></i>
                    </a>
                </div>
                <div *ngIf="getTwiddleToggleInfo(elementRef, 'rows' + getMcqOptionNumber(), row_i)" class="text-list gap">
                    <div *ngFor="let cell of row; let col_i = index;">
                        <div style="margin-left: 0.5em;">
                            Column {{col_i + 1}}
                        </div>
                        <div class="sub-property-div">
                            <div *ngIf="cell.elementType != 'text'" class="select is-fullwidth" style="margin-bottom: 1em;">
                                <select (change)="onTableElementChange($event.target.value, cell)">
                                    <option  *ngFor="let elementType of tableElements; let index = index" [selected]="elementType.id == getTableElement(cell)" [value]="elementType.id">
                                        {{elementType.label}}
                                    </option>
                                </select>
                            </div>
                            <div *ngIf="cell.elementType == 'table_text'">
                                <input
                                    textInputTransform
                                    [(ngModel)]="cell.val"
                                    type="text" 
                                    class="input" 
                                    style="text-align:center;" 
                                >
                            </div>
                            <div *ngIf="cell.elementType == 'math'" class="capture-math-container">
                              <capture-math [obj]="cell" prop="latex" [isManualKeyboard]="true" [class.is-disabled]="isReadOnly()"></capture-math>
                            </div>
                            <div *ngIf="cell.elementType == 'input'">
                              <template-input
                                  style="width: 100%;"
                                  [config]="config"
                                  [block]="cell"
                                  (onRemoveElement)="removeElementEmit($event.content, $event.element)"
                              >
                              </template-input>
                            </div>
                            <div *ngIf="cell.elementType == 'text' &&cell.paragraphStyle == 'advanced-inline' && cell.advancedList && cell.advancedList.length">
                                <div *ngFor="let element of cell.advancedList" class="text-list separator gap">
                                    <div class="select is-fullwidth">
                                        <select (change)="onTableElementChange($event.target.value, cell)">
                                            <option  *ngFor="let elementType of tableElements; let index = index" [selected]="elementType.id == getTableElement(element)" [value]="elementType.id">
                                                {{elementType.label}}
                                            </option>
                                        </select>
                                    </div>
                                    <div *ngIf="element.elementType == 'math'" class="capture-math-container">
                                        <capture-math [obj]="element" prop="latex" [isManualKeyboard]="true" [class.is-disabled]="isReadOnly()"></capture-math>
                                    </div>
                                    <input
                                        textInputTransform
                                        *ngIf="element.elementType == 'text' && element.paragraphStyle != 'advanced-inline'"
                                        [(ngModel)]="element.caption"
                                        type="text" 
                                        class="input" 
                                        style="text-align:center;" 
                                    >
                                    <template-input
                                        *ngIf="element.elementType == 'input'" 
                                        style="width: 100%;"
                                        [config]="config"
                                        [block]="element"
                                        (onRemoveElement)="removeElementEmit($event.content, $event.element)"
                                    >
                                    </template-input>
                                    <template-advanced-inline
                                        *ngIf="element.elementType == 'text' && element.paragraphStyle == 'advanced-inline'"
                                        [advancedList]="element.advancedList"
                                        [config]="config"
                                        [twiddleToggleMap]="twiddleToggleMap"
                                        [hasValidator]="hasValidator"
                                        [isStaticElementOnly]="isNonInteractive"
                                        [allowBookmarkLink]="allowBookmarkLink"
                                        (onRemoveElement)="removeElementEmit($event.content, $event.element)"
                                        (onValidatorAlign)="onValidatorAlignEmit()"
                                        (onAssignEntryId)="onAssignEntryIdEmit()"
                                        (onImageUploaded)="onImageUploadEmit()"
                                        (onTwiddleToggle)="onTwiddleToggleEmit($event.elementRef, $event.key, $event.id, $event.toggle)"
                                    >
                                    </template-advanced-inline>
                                    <div *ngIf="element.elementType == 'mcq'" class="text-list">
                                        <div *ngFor="let option of element.options" style="display: flex; flex-direction: column; gap: 1em;">
                                            <div  class="space-between gap">
                                                <div *ngIf="option.elementType == 'math'" class="capture-math-container">
                                                    <capture-math [obj]="option" prop="content" [isManualKeyboard]="true" [class.is-disabled]="isReadOnly()"></capture-math>
                                                </div>
                                                <input
                                                    textInputTransform
                                                    *ngIf="option.elementType == 'text' && !isElementAdvancedInline(option.paragraphStyle)" 
                                                    type="text" 
                                                    class="input" 
                                                    style="text-align:center;" 
                                                    [(ngModel)]="option.content"
                                                >
                                                <capture-image *ngIf="option.elementType == 'image'" [element]="option" fileType="image" [isNoScale]="false" [displayImageControls]="false" (onUploaded)="onImageUploadEmit()"></capture-image>
                                                <asset-library-link *ngIf="option.elementType == 'image'" [element]="option"></asset-library-link>
                                                <div *ngIf="option.elementType === 'text' && isElementAdvancedInline(option.paragraphStyle)">
                                                    <template-advanced-inline
                                                            [advancedList]="option.advancedList"
                                                            [config]="config"
                                                            [isTextList]="false"
                                                            [twiddleToggleMap]="twiddleToggleMap"
                                                            [hasValidator]="hasValidator"
                                                            [isStaticElementOnly]="true"
                                                            [allowBookmarkLink]="allowBookmarkLink"
                                                            (onRemoveElement)="removeElementEmit($event.content, $event.element)"
                                                            (onValidatorAlign)="onValidatorAlignEmit()"
                                                            (onAssignEntryId)="onAssignEntryIdEmit()"
                                                            (onImageUploaded)="onImageUploadEmit()"
                                                            (onTwiddleToggle)="onTwiddleToggleEmit($event.elementRef, $event.key, $event.id, $event.toggle)"
                                                        >
                                                        </template-advanced-inline>
                                                </div>
                                                <div class="option-container-buttons" style="flex-direction: row; gap: 0.25em">
                                                    <a 
                                                        class="button is-correct-toggle"
                                                        [class.is-disabled]="isReadOnly()"
                                                        [class.is-success] = "option.isCorrect"
                                                        [class.is-danger]  = "!option.isCorrect"
                                                        (click)="toggleValue(option,'isCorrect')"
                                                    >
                                                        <i 
                                                        class="fa fa-check" 
                                                        [class.fa-check] = "option.isCorrect"
                                                        [class.fa-times] = "!option.isCorrect"
                                                        style="width: 16px;"
                                                        aria-hidden="true"
                                                        ></i>
                                                    </a>
                                                    <a class="button" style="margin-right: 0px;" (click)="removeElementEmit(element.options, option);">
                                                        <i class="fas fa-trash"  aria-hidden="true"></i>
                                                    </a>  
                                                </div>
                                            </div>
                                        </div>
                                        <button 
                                            *ngIf="element.options && element.options.length && element.options[0].elementType === 'text'"
                                            style="width: 100%" 
                                            (click)="insertMCQEntry(element.options, elementTypes.TEXT, element.options[0].paragraphStyle)"
                                            class="button is-primary"
                                        >
                                            Add Option
                                        </button>
                                        <button *ngIf="element.options && element.options.length && element.options[0].elementType == 'math'" style="width: 100%" (click)="insertMCQEntry(element.options, elementTypes.MATH)" class="button is-primary">Add Option</button>
                                        <twiddle *ngIf="hasValidator" style="width: 100%;" caption="Other Options" (change)="onTwiddleToggleEmit(elementRef, 'mcq_options' + getMcqOptionNumber(), element.entryId, $event)"></twiddle>
                                        <div class="text-list gap" *ngIf="getTwiddleToggleInfo(elementRef, 'mcq_options' + getMcqOptionNumber(), element.entryId)">
                                            <strong>Entry Labels</strong>  
                                            <div style="display: flex; gap: 1em; align-items: center;">
                                                Block Label:
                                                <input 
                                                    type="text" 
                                                    class="input"
                                                    [(ngModel)]="element.validator_label"
                                                >
                                            </div>
                                            <div *ngFor="let option of element.options; let i = index;" style="display: flex; gap: 1em; align-items: center;">
                                                {{getMCQOptionLabel(i)}}
                                                <input 
                                                    type="text" 
                                                    class="input"
                                                    [(ngModel)]="option.validator_label"
                                                >
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div *ngIf="cell.elementType == 'mcq'">
                              <template-mcq
                                [elementRef]="elementRef"
                                [elementType]="getMcqTypeFromOption(cell)"
                                [mcqElementOptions]="cell.options"
                                [twiddleToggleMap]="twiddleToggleMap"
                                [hasValidator]="hasValidator"
                                (onRemoveElement)="removeElementEmit($event.content, $event.element)"
                                (onValidatorAlign)="onValidatorAlignEmit()"
                                (onAssignEntryId)="onAssignEntryIdEmit()"
                                (onImageUploaded)="onImageUploadEmit()"
                                (onTwiddleToggle)="onTwiddleToggleEmit($event.elementRef, $event.key, $event.id, $event.toggle)"
                              >
                              </template-mcq>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <button class="button is-primary" (click)="tableAddRow(tableElement)">
            Add row
        </button>
    </div>
    <div class="text-list">
        <div class="configuration-header">
            Enable header row?
        </div>
        <div>
            <input 
                type="checkbox"
                [(ngModel)]="tableElement.someRowsAreHeader" 
                (change)="onToggleHeaderRow()"
            >
        </div>
        <div *ngIf="tableElement.someRowsAreHeader">
          <b><label>Header Alignment</label></b>
          <div  class="buttons is-no-wrap" style="margin-bottom: 0.5em;">
              <button *ngFor="let align of alignments" [disabled]="isReadOnly()" class="button is-small"  (click)="setHeaderAlignment(tableElement, align.id)">
                <i class="fa {{align.icon}}"></i>
              </button>
          </div>
        </div>
    </div>
</div>
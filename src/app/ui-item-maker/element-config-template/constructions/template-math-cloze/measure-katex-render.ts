import katex from 'katex';
import 'katex/contrib/mhchem/mhchem.js';

const hiddenContainer = document.createElement('div');
hiddenContainer.style.position = 'absolute';
hiddenContainer.style.left = '-9999px';
hiddenContainer.style.top = '-9999px';
hiddenContainer.style.visibility = 'hidden';
document.body.appendChild(hiddenContainer);

export function measureKaTeX(texStr: string): {x:number, y:number} {
    hiddenContainer.innerHTML = '';

    const element = document.createElement('div');
    hiddenContainer.appendChild(element);

    katex.render(texStr, element);
    const rect = element.getBoundingClientRect();
    const widthPx = rect.width;
    const heightPx = rect.height;
    const fontSize = parseFloat(getComputedStyle(element).fontSize);
    const widthEm = widthPx / fontSize;
    const heightEm = heightPx / fontSize;

    hiddenContainer.innerHTML = '';
  return { x: widthEm, y: heightEm };
}

<fieldset *ngIf="isUseAdvancedInlineMath" style="margin-bottom: 1em;">
  
  <hr>
  
  <div *ngIf="advancedInlineMathContent" cdkDropList (cdkDropListDropped)="drop($event, advancedInlineMathContent)">
    <div *ngFor="let content of advancedInlineMathContent" class="drag-list" cdkDrag [cdkDragDisabled]="isReadOnly()">
      
      <div class="space-between gap" *ngIf="!isSingleInlineElement">
        
        <div style="width: 100%;">
          <div *ngIf="content.type == 'cloze'" style="width: 100%;">
            <katex-display [expression]="clozeMathExpression" [displayMode]="true"></katex-display>
          </div>
          <div *ngIf="content.type == 'text'" style="text-align:center; font-size:150%; width: 100%;">
            <textarea
              textInputTransform
              [(ngModel)]="content.content"
              (mousedown)="$event.stopPropagation()"
              (input)="validateConfig()"
              style="margin-bottom: 0.5em"
              class="textarea"
              cdkTextareaAutosize
              [cdkTextareaAutosize]="true"
              [cdkAutosizeMinRows]="2"
            ></textarea>
          </div>
          <div *ngIf="content.type == 'math'" style="text-align:center; width: 100%;">
            <div class="capture-math-container drag-list">
              <capture-math 
                style="cursor: text"
                [obj]="content" 
                prop="content" 
                [class.is-disabled]="isReadOnly()"
                (onChange)="validateConfig()"
                [isManualKeyboard]="true"
              ></capture-math>
            </div>
          </div>
        </div>
        
        <div class="option-container-buttons" *ngIf="content.type != 'cloze'">
          <a class="button" (click)="removeAdvancedInlineContent(content);" [class.no-pointer-events]="isReadOnly()">
              <i class="fas fa-trash"  aria-hidden="true"></i>
          </a>   
        </div>
        
      </div>
      
    </div>
  </div>
  <select-element 
    [elementTypes]="advancedInlineTypes"
    (insert)="insertAdvancedInlineContent($event)"
    style="margin-top:1em;" 
  ></select-element>
  
  <hr>
</fieldset>

<div>
  <div class="configuration-header">
    <span>Cloze Expression</span>
  </div>
  <div class="capture-math-container"
    (focusin)="onFocus()"
    (focusout)="onBlur()"
  >
    <capture-math #captureMath 
      [obj]="getMathObject()" 
      (onChange)="validateConfig()"
      [class.is-disabled]="isReadOnly()"
      prop="unprocessedTex" [isManualKeyboard]="true">
    </capture-math>
  </div>
  <!-- <button (click)="validateConfig()">generate</button> -->
  <br>
  <div style="margin-bottom: 1em;">
    <button 
      (click)="insertNewTarget()" 
      (mouseenter)="setNewTargetButtonHoverState(true)"
      (mouseleave)="setNewTargetButtonHoverState(false)"
      class="button is-small has-icon"
      [disabled]="!isFocus"
    >
      <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
      <span>Add New Target</span>
    </button>
  </div>
  
  <div style="margin-bottom: 1em;" *ngIf="!isHideFontSizeOption">
    <label>
      <tra slug="auth_katex_font_size"></tra>
      <num-scrub [el]="getMathObject()" prop="fontSize" [min]="0" [max]="5" (change)="onFontSizeChange()"></num-scrub>
    </label>
  </div>
</div>

<div>
  <div class="configuration-header">
    <span>Elements</span>
  </div>
  <div 
    *ngFor="let element of getCanvasDisplayListObject() | slice:1; let i = index" 
    class="element-container"
    [class.is-danger]="i >= labelList.length"
  >
    
    <ng-container *ngIf="i < labelList.length">
      <div class="space-between" style="margin-bottom: 2em;">
        <div>
          <span>Target:</span>
          <input type="text" disabled value="{{labelList[i]}}" style="width: 5em"/>
        </div>
        <div>
          <button (click)="resetInputType(element)" class="button is-small has-icon">
            <span class="icon"><i class="fas fa-undo-alt" aria-hidden="true"></i></span>
            <span>Change type</span>
          </button>
        </div>
      </div>
    </ng-container>
    
    <ng-container *ngIf="i >= labelList.length">
      <div class="space-between" style="margin-bottom: 2em;">
        <div>
          <span style="color: #ce0000; font-weight: bold;">Unused Input (Missing Target)</span>
        </div>
        <div>
          <button (click)="removeElement(getCanvasDisplayListObject(), element)" class="button is-small has-icon">
            <span class="icon"><i class="fas fa-trash" aria-hidden="true"></i></span>
          </button>
        </div>
      </div>
    </ng-container>
    
    <ngSwitch [ngSwitch]="element.elementType">
      <div *ngSwitchCase="'text'" [class.no-pointer-events]="isReadOnly()">
        <!-- Placeholder For Unselected Input Type -->
        <div style="display: flex; flex-flow: row wrap; gap: 0.5em;">
            <a 
              *ngFor="let inputType of inputTypeOptions" 
              class="button is-small input-format-option-button" 
              style="padding: 2em; margin-right: 0px;" 
              (click)="selectInputType(element, inputType.id)" 
            >
                <div class="input-format-option">
                <div class="icon">
                    <i class="fa" [ngClass]="inputType.icon"></i>
                </div>
                <div>
                    {{inputType.caption}}
                </div>
                </div>
            </a>
        </div>
      </div>
      <div *ngSwitchCase="'mcq'">
        <div *ngFor="let option of element.options" class="separator" style="display: flex;flex-direction: column; gap: 1em;">
            <div class="space-between gap">
                <div *ngIf="option.elementType && option.elementType == 'math'" style="text-align:center; font-size:150%; width: 100%;">
                    <capture-math 
                      [obj]="option" 
                      prop="content" 
                      [class.is-disabled]="isReadOnly()"
                      (onChange)="validateConfig()"
                      [isManualKeyboard]="true"
                    ></capture-math>
                </div>
                <input
                    *ngIf="!option.elementType || option.elementType == 'text'" 
                    type="text" 
                    class="input" 
                    style="text-align:center;" 
                    (input)="validateConfig()"
                    [(ngModel)]="option.content"
                >
                <div class="option-container-buttons">
                    <a 
                      class="button is-correct-toggle"
                      [class.is-disabled]="isReadOnly()"
                      [class.is-success] = "option.isCorrect"
                      [class.is-danger]  = "!option.isCorrect"
                      (click)="toggleValue(option,'isCorrect')"
                    >
                      <i 
                        class="fa fa-check" 
                        [class.fa-check] = "option.isCorrect"
                        [class.fa-times] = "!option.isCorrect"
                        style="width: 16px;"
                        aria-hidden="true"
                      ></i>
                    </a>
                </div>
                <a class="button" (click)="removeElement(element.options, option);" [class.no-pointer-events]="isReadOnly()">
                    <i class="fas fa-trash"  aria-hidden="true"></i>
                </a>   
            </div>
        </div>
        <!-- <button style="width: 100%" (click)="insertMCQEntry(getValueByPath(config.elementRef), config.optionElementType)" class="button is-primary">Add</button> -->
        <div style="padding-top: 1em;">
            <button 
              (click)="insertMCQEntry(element.options, element.displayStyle)" 
              class="button is-fullwidth"
            >
              Add Option
            </button>
        </div>
        
      </div>
      <div *ngSwitchCase="'input'">
        <template-input
            style="width: 100%;"
            [block]="element"
            [isHideEntryId]="true"
            (onSelectInputFormat)="validateConfig()"
            (onInput)="validateConfig()"
            (onRemoveElement)="removeElement($event.content, $event.element)"
        ></template-input>
      </div>
      <div *ngSwitchDefault>
        Unknown element type: {{element.type}}
      </div>
    </ngSwitch>
  </div>
</div>

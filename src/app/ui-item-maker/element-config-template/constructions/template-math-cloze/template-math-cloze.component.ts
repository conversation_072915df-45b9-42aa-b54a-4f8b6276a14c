import { Component, ElementRef, EventEmitter, Input, AfterViewInit, OnDestroy, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { AuthScopeSetting, AuthScopeSettingsService } from '../../../auth-scope-settings.service';
import { EditingDisabledService } from '../../../editing-disabled.service';
import { ITemplateDef, processText, StyleprofileService, StylingProcess, TemplateVersionConfig } from 'src/app/core/styleprofile.service';
import { LangService } from 'src/app/core/lang.service';
import { ItemBankCtrl } from '../../../item-set-editor/controllers/item-bank';
import { IClozeMathHiddenConfigs, TemplateConfigDefRow } from 'src/app/ui-testrunner/element-render-template/model';
import { ICanvasSharedObject, IContentElementCanvas, IContentElementCanvasDisplayElement } from 'src/app/ui-testrunner/element-render-canvas/model';
import { ElementType, IScoredResponse } from 'src/app/ui-testrunner/models';
import { IContentElementMath } from 'src/app/ui-testrunner/element-render-math/model';
import { SharedObjectMapService } from 'src/app/ui-testrunner/element-render-interactive-diagram/shared-object-map.service';
import { IContentElementText, TextParagraphStyle } from 'src/app/ui-testrunner/element-render-text/model';
import { createDefaultElement } from 'src/app/ui-item-maker/item-set-editor/models';
import { IContentElementMcq, IContentElementMcqOption, McqDisplay } from 'src/app/ui-testrunner/element-render-mcq/model';
import { measureKaTeX } from './measure-katex-render'
import { CaptureMathComponent } from 'src/app/widget-math/capture-math/capture-math.component';
import { numberToAlphabet, alphabetToNumber } from 'src/app/ui-testrunner/element-render-interactive-diagram/constructions/common';
import { ElementTypeDefs } from "src/app/ui-testrunner/models/ElementTypeDefs";
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { IContentElementInput } from 'src/app/ui-testrunner/element-render-input/model';

interface IContentElementMathWithPreprocessor extends IContentElementMath {
  advancedInlineMathContent?: {
    content: string;
    type: ElementType | "cloze";
  }[];
  unprocessedTex?: string;
}
const PLACEHOLDER_ID_PREFIX = 'placeholder-math-cloze-'
const MIN_CUSTOM_DROPDOWN_WIDTH = 3;
type positionData = {x: number, y: number, isScript: boolean}

// from the css
// .is-adjusted-body { font-size: 84%; }
// [class.is-adjusted-body]="isBody() && whitelabel.isABED()"
const FONTSIZE_ADJUSTMENT_FACTOR = 0.84;
// .katex .fontsize-ensurer.reset-size6.size3, .katex .sizing.reset-size6.size3 {
//     font-size: .738em !important; //it was .7em before in the source code of katex
// }
const KATEX_SCRIPT_FONTSIZE = 0.738;
const HORIZONTAL_PADDING = 0.1

// regex for atomic notation
const NUCLEAR_REGEX  = /\^(\{.*?\}|\d)_(\{.*?\}|\d)(\\ce\{.*?\}|\{\\ce\{.*?\}\})/g;
const NUCLEAR_REGEX2 = /_(\{.*?\}|\d)\^(\{.*?\}|\d)(\\ce\{.*?\}|\{\\ce\{.*?\}\})/g;

enum InputType {
  NONE = 'none', // before user selects
  KEYBOARD = 'keyboard',
  DROPDOWN_TEXT = 'dropdown-text',
  DROPDOWN_MATH = 'dropdown-math',
}
const FOCUS_TIMEOUT = 100;

enum InputAlignment {
  LEFT = 'left',
  RIGHT = 'right',
  CENTER = 'center',
}

@Component({
  selector: 'template-math-cloze',
  templateUrl: './template-math-cloze.component.html',
  styleUrls: ['./template-math-cloze.component.scss']
})
export class TemplateMathClozeComponent implements OnInit, OnDestroy, AfterViewInit {
  
  @Input() config: TemplateConfigDefRow;
  @Input() canvasElement: IContentElementCanvas;
  @Input() isUseAdvancedInlineMath: boolean = true;
  @Output() onRemoveElement = new EventEmitter<any>();
  @Output() onValidatorAlign = new EventEmitter<any>();
  @Output() onAssignEntryId = new EventEmitter<any>();
  @Output() onImageUploaded = new EventEmitter<any>();
  
  @ViewChild('captureMath') captureMath!: CaptureMathComponent;

  labelList: string[] = [];
  elementTypes = ElementType;
  inputTypes = InputType;
  isPrescriptSet: Set<string> = new Set();
  
  advancedInlineTypes = [
    ElementTypeDefs.TEXT,
    ElementTypeDefs.MATH,
  ]
  
  inputTypeOptions = [
    {id: InputType.KEYBOARD,       icon:'fa-keyboard', caption:'Keyboard Input'},
    {id: InputType.DROPDOWN_TEXT,  icon:'fa-sort-alpha-down',  caption:'Dropdown Text'},
    {id: InputType.DROPDOWN_MATH,  icon:'fa-sort-numeric-down',  caption:'Dropdown Math'},
  ]
  textStylingProcess: StylingProcess[] = [];

  constructor(
    private authScopeSettings: AuthScopeSettingsService,
    private editingDisabled: EditingDisabledService,
    private styleProfileService: StyleprofileService,
    private lang: LangService,
    private itemBankCtrl: ItemBankCtrl,
    private sharedObjectMap: SharedObjectMapService
  ) { }

  ngOnInit(): void {
    this.styleProfileService.getStyleProfileChanges().subscribe((hasStyleProfile) => {
      if (hasStyleProfile) {
        this.textStylingProcess = 
          this.styleProfileService.getStyleProfile()[this.lang.c()]?.renderStyling?.plainText?.transforms ?? [];
        this.validateConfig(false);
      }
    })
  }
  
  ngAfterViewInit(): void {
    this.validateConfig(false);
  }
  
  ngOnDestroy() :void {
  }
  
  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }
  
  getCanvasDisplayListObject(): IContentElementCanvasDisplayElement[] {
    return this.canvasElement?.pages[0]?.displayList;
  }
  getMathObject(): IContentElementMathWithPreprocessor {
    return this.getCanvasDisplayListObject()[0] as IContentElementMathWithPreprocessor;
  }
  get clozeMathExpression(): string {
    const mathObj = this.getMathObject();
    if (mathObj.advancedInlineMathContent == undefined) return '';
    return mathObj.unprocessedTex;
  }
  get advancedInlineMathContent(): IContentElementMathWithPreprocessor['advancedInlineMathContent'] {
    return this.getMathObject().advancedInlineMathContent;
  }
  get isSingleInlineElement(): boolean {
    const advancedInlineMathContent = this.advancedInlineMathContent;
    return advancedInlineMathContent?.length === 1 && advancedInlineMathContent[0].type === 'cloze';
  }
  
  toggleValue(obj: any, key: string) {
    obj[key] = !obj[key];
  }
  
  removeElement(content: any[], element: any) {
    this.onRemoveElement.emit({content, element});
  }
  
  addNewTextElement(text: string) {
    const canvasDisplayList = this.getCanvasDisplayListObject();
    const textElement = createDefaultElement(ElementType.TEXT) as IContentElementText;
    textElement.caption = "TARGET";
    canvasDisplayList.push(textElement);
  }
  
  selectInputType(element: IContentElementCanvasDisplayElement, type: InputType){
    const canvasDisplayList = this.getCanvasDisplayListObject();
    const index = canvasDisplayList.indexOf(element);
    if (index === -1) return;
    
    const newElement = this.getInitInputTypeElement(type);
    canvasDisplayList[index] = newElement;
    this.validateConfig();
    
    this.onAssignEntryId.next();
    this.onValidatorAlign.next()
  }
  resetInputType(element: IContentElementCanvasDisplayElement){
    const canvasDisplayList = this.getCanvasDisplayListObject();
    const index = canvasDisplayList.indexOf(element);
    if (index === -1) return;
    
    const confirm = window.confirm('Changing the input type will remove the current input data. Are you sure you want to proceed?');
    if (!confirm) return;
    
    const newElement = this.getInitInputTypeElement(InputType.NONE);
    canvasDisplayList[index] = newElement;
    this.validateConfig();
  }
  deleteInputElement(element: IContentElementCanvasDisplayElement){
    const canvasDisplayList = this.getCanvasDisplayListObject();
    const index = canvasDisplayList.indexOf(element);
    if (index === -1) return;
    const confirm = window.confirm('Are you sure you want to delete this input element?');
    if (!confirm) return;
    canvasDisplayList.splice(index, 1);
    this.validateConfig();
  }
  drop(event: CdkDragDrop<any[]>, contents: any[]) {
    moveItemInArray(contents, event.previousIndex, event.currentIndex);
    this.validateConfig()
  }
  
  getInitInputTypeElement(type: InputType): IContentElementCanvasDisplayElement{
    switch(type){
      case InputType.NONE: {
        const element = createDefaultElement(ElementType.TEXT) as IContentElementText;
        element.caption = 'TARGET';
        return element;
      }
      case InputType.KEYBOARD: {
        const element = createDefaultElement(ElementType.INPUT);
        element['inputNumFormat'] = 'Restricted';
        return element;
      }
      case InputType.DROPDOWN_TEXT: {
        const element = createDefaultElement(ElementType.MCQ) as IContentElementMcq;
        element.displayStyle = McqDisplay.DROPDOWN;
        return element;
      }
      case InputType.DROPDOWN_MATH: {
        const element = createDefaultElement(ElementType.MCQ) as IContentElementMcq;
        element.displayStyle = McqDisplay.CUSTOM_DROPDOWN;
        for (let option of element.options){
          option.elementType = ElementType.MATH;
          option.paragraphStyle = TextParagraphStyle.REGULAR;
        }
        return element;
      }
    }
  }
  
  insertMCQEntry(options:any[], mcqDisplayStyle: McqDisplay){
    let content:any = '';
    switch (mcqDisplayStyle) {
      case McqDisplay.DROPDOWN: {
        const optionElement: IContentElementMcqOption = {
          elementType: ElementType.TEXT,
          optionType: ElementType.MCQ_OPTION,
          content,
          isCorrect: false,
          optionId: -1,
          link: { elementType: "text_link", caption: "" }
        };
        delete optionElement.paragraphStyle;
        options.push(optionElement)
      } break;
      case McqDisplay.CUSTOM_DROPDOWN: {
        // for custom dropdown, we need to insert a math element
        const optionElement: IContentElementMcqOption = {
          elementType: ElementType.MATH,
          optionType: ElementType.MCQ_OPTION,
          content,
          isCorrect: false,
          optionId: -1,
          link: { elementType: "text_link", caption: "" }
        };
        optionElement.paragraphStyle = TextParagraphStyle.REGULAR;
        options.push(optionElement)
      } break;
    }
  }
  
  validateConfig(recalculateSize: boolean = true): void {
    this.ensureClozeExist();
    this.ensureScoreWeight();
    this.updatePlaceholderLabels();
    this.resolveNewLabels();
    this.positionElements(); // make sure the style is correct
    this.resolveCustomDropdownWidth();
    this.updateValidtorLabel();
    if (recalculateSize) {
      window.requestAnimationFrame(() => {
        // request animation frame to ensure the math element is rendered
        this.processMathExpression();
        window.requestAnimationFrame(() => {
          this.positionElements();
        });
      });
    }
  }
  
  ensureClozeExist() {
    let clozeExist = false;
    const mathObj = this.getMathObject();
    if (mathObj.advancedInlineMathContent == undefined) mathObj.advancedInlineMathContent = [];
    for (let el of mathObj.advancedInlineMathContent){
      if (el.type === "cloze") {
        clozeExist = true; break;
      }
    }
    if (!clozeExist) mathObj.advancedInlineMathContent.push({
      content: '',
      type: "cloze",
    });
  }
  
  ensureScoreWeight() {
    const displayList = this.getCanvasDisplayListObject();
    for (let element of displayList){
      (element as IScoredResponse).scoreWeight = 0;
    }
  }
  
  updateValidtorLabel() {
    const displayList = this.getCanvasDisplayListObject();
    for (let i = 1; i < displayList.length; i++) {
      displayList[i].validator_label = this.labelList[i-1];
    }
  }
  
  updatePlaceholderLabels() {
    const mathObject = this.getMathObject();
    const labels = mathObject.unprocessedTex.match(/\\boxed\{(.+?)\}/g);
    if (!labels) {
      this.labelList = [];
    } else {
      this.labelList = labels.map(label => label.slice(7, -1));
    }
  }
  
  resolveNewLabels(){
    const inputElementCount = this.getCanvasDisplayListObject().length - 1;
    const labelCount = this.labelList.length;
    for (let i = inputElementCount; i < labelCount; i++){
      this.addNewTextElement(this.labelList[i])
    }
  }
  
  resolveCustomDropdownWidth(){
    const displayList = this.getCanvasDisplayListObject();
    for (let element of displayList){
      if (element.elementType === ElementType.MCQ && element.displayStyle === McqDisplay.CUSTOM_DROPDOWN){
        const mcqElement = element as IContentElementMcq;
        const options = mcqElement.options;
        const texStrings = options.map(option => option.content);
        const sizes = texStrings.map(s => measureKaTeX(s));
        const maxWidth = Math.max(...sizes.map(s => s.x), MIN_CUSTOM_DROPDOWN_WIDTH);
        mcqElement.maxCustomDDWidth = maxWidth;
      }
    }
  }
  
  isValidNuclearScriptContent(content: string): boolean {
    // only allow content to be number, letters, some special characters, or \\boxed{[A-Za-z0-9]}
    if (content.match(/^\{[a-zA-Z0-9\-,.]+\}$/)) return true;
    if (content.match(/^\{\\boxed\{[[A-Za-z0-9_\-]+\}\}$/)) return true;
    return false;
  }
  isValidTargetContent(content: string): boolean {
    if (content.match(/^[a-zA-Z0-9\-.]+$/)) return true;
    return false;
  }
  handleTextTex(texstr: string): string {
    let str = texstr;
    
    // replace special characters
    // str = str.replace(/\\/g, '\\textbackslash');
    str = str.replace(/&ndash;/g, '--');
    str = str.replace(/&mdash;/g, '---');
    str = str.replace(/&/g, '\\&');
    str = str.replace(/%/g, '\\%');
    str = str.replace(/\{/g, '\\{');
    str = str.replace(/\}/g, '\\}');
    str = str.replace(/\$/g, '\\$');
    str = str.replace(/#/g, '\\#');
    str = str.replace(/\_/g, '\\_');
    str = str.replace(/\^/g, '\\^{}');
    str = str.replace(/~/g, '\\~{}');
    
    return str;
  }
  handleTex(texstr: string): string {
    let str = texstr;
    
    // replace \text{...} with \htmlClass{custom-tnr-katex}{\text{...}}
    str = str.replace(/\\text\{(.*?)\}/g, '\\htmlClass{custom-tnr-katex}{\\text{$1}}');
    str = str.replace(/\\textit\{(.*?)\}/g, '\\htmlClass{custom-tnr-katex}{\\textit{$1}}');
    
    // replace ^[a-ZA-Z0-9] with ^{[a-zA-Z0-9]}
    // replace _[a-ZA-Z0-9] with _{[a-zA-Z0-9]}
    str = str.replace(/(\^|_)([a-zA-Z0-9])/g, '$1{$2}');
    // handle ^{...}_{...}\\ce{...} convert to \\begin{matrix*}[r] ... \\\\ ... \\end{matrix*}\\ce{...}
    str = str.replace(NUCLEAR_REGEX, (match, p1: string, p2: string, ce) => {
      if (!this.isValidNuclearScriptContent(p1) || !this.isValidNuclearScriptContent(p2)) return match;
      const s1 = p1.startsWith('\\boxed') ? p1 : `\\scriptstyle{${p1}}`;
      const s2 = p2.startsWith('\\boxed') ? p2 : `\\scriptstyle{${p2}}`;
      return `\\begin{matrix*}[r] ${s1} \\\\ ${s2} \\end{matrix*}${ce}`;
    });
    // handle _{...}^{...}\\ce{...} convert to \\begin{matrix*}[r] ... \\\\ ... \\end{matrix*}\\ce{...}
    str = str.replace(NUCLEAR_REGEX2, (match, p1: string, p2: string, ce) => {
      if (!this.isValidNuclearScriptContent(p1) || !this.isValidNuclearScriptContent(p2)) return match;
      const s1 = p1.startsWith('\\boxed') ? p1 : `\\scriptstyle{${p1}}`;
      const s2 = p2.startsWith('\\boxed') ? p2 : `\\scriptstyle{${p2}}`;
      return `\\begin{matrix*}[r] ${s2} \\\\ ${s1} \\end{matrix*}${ce}`;
    });
    return str;
  }
  getCommandsFromTag(tags: string[]): string[] {
    return tags.map(tag => {
      switch (tag) {
        case 'strong': return '\\textbf';
        case 'b': return '\\textbf';
        case 'em': return '\\textit';
        case 'i': return '\\textit';
        case 'del': return '\\sout';
        case 'strike': return '\\sout';
        case 'u': return '\\underline';
        default: return '';
      }
    }).filter((x) => x !== '');
  }
  getCombinedTex(): string{
    const mathObject = this.getMathObject();
    if (mathObject.advancedInlineMathContent == undefined) return mathObject.unprocessedTex;
    
    let combined = mathObject.advancedInlineMathContent.map(element => {
      if (element.type === "cloze") {
        return mathObject.unprocessedTex;
      } else if (element.type === ElementType.TEXT){
        let content = element.content;
        content = processText(content, this.textStylingProcess);
        content = this.handleTextTex(content);
        const words = content.split(/(\s+|<[^>]+>)/).filter(x => x != ''); // split to words and tags
        
        const isEndInSpace = words.length >= 1 && words[words.length - 1].trim() == '' && words[words.length - 1].length > 0;
        const activeTags: string[] = []; 
        let taggedWords: {commands: string[], content: string}[] = [];
        for (let i in words){
          const w = words[i];
          if (w.trim() == ''){
            continue;
          } else if (w.match(/^<\/[^>]+>$/)){
            // close tag
            const tag = w.slice(2, -1);
            const id = activeTags.findIndex((x) => x == tag);
            if (id !== -1) activeTags.splice(id, 1);
          } else if (w.match(/^<[^>]+>$/)){
            // open tag
            const tag = w.slice(1, -1);
            activeTags.push(tag)
          } else {
            const commands = this.getCommandsFromTag(activeTags);
            taggedWords.push({commands, content: w});
          }
        }
        const wrappedWords = taggedWords.map(({ commands, content }, i) => {
          const isItalic = commands.includes('\\textit');
          const space = (isEndInSpace || (i !== taggedWords.length - 1)) ? ' ' : '';
          let word = isItalic ? `{${content}${space}}` : `\\text{${content}${space}}`
          for (let com of commands) word = `${com}{${word}}`;
          return word;
        });
        
        let result = wrappedWords.join('\\allowbreak');
        result = result.replace(/\\&nbsp;/g, ' ').replace(/\\&NoBreak;/g, '');
        return result;
      } else {
        return element.content;
      }
    })
    // combined.push(mathObject.unprocessedTex);
    return combined.join('\\mkern2mu\\allowbreak ');
  }
  
  evaluateInputAlignment(texstr: string): void {
    this.isPrescriptSet.clear();
    let str = texstr;
    const isTarget = (content: string) => Boolean(content.match(/^\{\\boxed\{[[A-Za-z0-9_\-]+\}\}$/));
    // use str.replace to iterate because in "es2015" we don't have matchAll
    str.replace(NUCLEAR_REGEX, (match, p1: string, p2: string, ce) => {
      if (isTarget(p1)) this.isPrescriptSet.add(p1.slice(8,-2));
      if (isTarget(p2)) this.isPrescriptSet.add(p2.slice(8,-2));
      return match;
    });
    str.replace(NUCLEAR_REGEX2, (match, p1: string, p2: string, ce) => {
      if (isTarget(p1)) this.isPrescriptSet.add(p1.slice(8,-2));
      if (isTarget(p2)) this.isPrescriptSet.add(p2.slice(8,-2));
      return match;
    });
    // const prescriptRegex = /\{\}\^(\{.*?\})_(\{.*?\})(\\ce\{.*?\}|\{\\ce\{.*?\}\})/g;
    const prescriptSubSupRegex = /\{\}_(\{.*?\}|\d)\^(\{.*?\}|\d)(\{.*?\})/g;
    const prescriptSubRegex = /\{\}_\{\\boxed\{(.*?)\}\}/g;
    const prescriptSupRegex = /\{\}\^\{\\boxed\{(.*?)\}\}/g;
    str.replace(prescriptSubSupRegex, (match, p1: string, p2: string) => {
      if (isTarget(p1)) this.isPrescriptSet.add(p1.slice(8,-2));
      if (isTarget(p2)) this.isPrescriptSet.add(p2.slice(8,-2));
      return match;
    });
    str.replace(prescriptSubRegex, (match, p1: string) => {
      this.isPrescriptSet.add(p1);
      return match;
    });
    str.replace(prescriptSupRegex, (match, p1: string) => {
      this.isPrescriptSet.add(p1);
      return match;
    });
  }
  
  processMathExpression() {
    const mathObject = this.getMathObject();
    const sizeMap = this.getHTMLElementSizeMap(this.labelList);
    const combinedTex = this.getCombinedTex();
    const tex = this.handleTex(combinedTex);
    const processed = this.replaceTexBoxedWithPlaceholder(tex, sizeMap);
    mathObject.latex = processed;
    
    this.evaluateInputAlignment(combinedTex);
  }
  positionElements() {
    const positions = this.getKatexElementPositions(this.labelList);
    const displayList = this.getCanvasDisplayListObject();
    
    const mathExprFontSize = this.getMathObject().fontSize ?? 1/FONTSIZE_ADJUSTMENT_FACTOR;
    const mathExprFontScale = mathExprFontSize / (1/FONTSIZE_ADJUSTMENT_FACTOR);
    
    // theoreatically this should be same, but just in case something goes wrong, use the smaller one
    const count = Math.min(positions.length, displayList.length - 1);
    for (let i = 0; i < count; i++){
      // set the (i+1)th element because displayList[0] is the math element
      displayList[i + 1].x = positions[i].x + HORIZONTAL_PADDING;
      displayList[i + 1].y = positions[i].y;
      
      if (this.isPrescriptSet.has(this.labelList[i])){
        // make it flush with the right side
        displayList[i + 1].x = positions[i].x + 3*HORIZONTAL_PADDING;
      }
      
      if (positions[i].isScript) {
        displayList[i + 1].elementScale = KATEX_SCRIPT_FONTSIZE * 100 * mathExprFontScale;
      } else{
        displayList[i + 1].elementScale = 100 * mathExprFontScale;
      }
    }
    
    // element that doesn't have corresponding cloze target
    for (let i = positions.length; i < displayList.length - 1; i++){
      displayList[i + 1].x = 0;
      displayList[i + 1].y = 0;
      delete displayList[i + 1].elementScale;
    }
    
    // setup alignment
    for (let i = 0; i < displayList.length - 1; i++){
      if (displayList[i+1].elementType === ElementType.INPUT) {
        const el = displayList[i+1] as IContentElementInput;
        if (positions[i].isScript) {
          el.numberInputAlign = InputAlignment.LEFT;
        }
        if (this.isPrescriptSet.has(this.labelList[i])){
          el.numberInputAlign = InputAlignment.RIGHT;
          el.isNoRightPadding = true;
        } else {
          delete el.isNoRightPadding;
        }
      }
    }
    
    // setup styling
    for (let i = 0; i < displayList.length - 1; i++){
      if (displayList[i+1].elementType === ElementType.INPUT) {
        const el = displayList[i+1] as IContentElementInput;
        el.isCompact = true;
      }
    }
  }
  
  onFontSizeChange() {
    this.positionElements(); // resize the input element DOMs
    window.requestAnimationFrame(() => {
      this.validateConfig();
    })
  }
  
  isLabelContainDuplicates(labels: string[]): boolean {
    return labels.length !== new Set(labels).size;
  }
  
  replaceTexBoxedWithPlaceholder(texstr: string, sizeMap: Map<string, {x: number, y: number}>): string {
    let str = texstr;
    // str = str.replace(/\\boxed\{(.+?)\}/g, '\\htmlId{$1}{\\phantom{\\rule{10em}{3em}}}');
    str = str.replace(/\\boxed\{(.*?)\}/g, (match, p1) => {
      if (!this.isValidTargetContent(p1)) return match;
      const size = sizeMap.get(p1) ?? {x:10,y:3}; 
      const id = PLACEHOLDER_ID_PREFIX + p1;
      const baselineOffset = -0.2 - (size.y - 1) / 2;
      const width = size.x + 2*HORIZONTAL_PADDING;
      const height = size.y;
      return `\\htmlId{${id}}{\\phantom{\\rule[${baselineOffset}em]{${width}em}{${height}em}}}`
      // return `\\htmlId{${id}}{{\\rule[${baselineOffset}em]{${width}em}{${height}em}}}`
    });
    str = "\\displaystyle " + str;
    return str;
  }
  
  /** return the size in em */
  getHTMLElementSizeMap(labels: string[]): Map<string, {x: number, y: number}> { 
    const map = new Map<string, {x: number, y: number}>();
    const canvasSharedObject: ICanvasSharedObject = this.sharedObjectMap.get(this.canvasElement);
    const canvasContainerDiv = canvasSharedObject?.canvasContainerDiv;
    if (canvasContainerDiv == undefined) return map;
    const elements = Array.from(canvasContainerDiv.querySelectorAll(
      '.inline-text-block.is-body, .numeric-inputs, .select-dropdown, .custom-dropdown'
    ));
    const canvasFontSize = parseFloat(window.getComputedStyle(canvasContainerDiv).fontSize);
    const mathObjectFontSize = this.getMathObject().fontSize ?? 1/FONTSIZE_ADJUSTMENT_FACTOR;
    elements.forEach((el, i) => {
      const label = labels[i];
      const rect = el.getBoundingClientRect();
      const factor = canvasFontSize * mathObjectFontSize * FONTSIZE_ADJUSTMENT_FACTOR;
      map.set(label, {
        x: rect.width / factor,
        y: rect.height / factor
      })
    })
    return map;
  }
  
  getKatexElementPositions(labels: string[]): positionData[] {
    const canvasSharedObject: ICanvasSharedObject = this.sharedObjectMap.get(this.canvasElement);
    const canvasContainerDiv = canvasSharedObject?.canvasContainerDiv;
    if (canvasContainerDiv == undefined) return [];
    
    const canvasRect = canvasContainerDiv.getBoundingClientRect();
    const fontSize = parseFloat(window.getComputedStyle(canvasContainerDiv).fontSize);
    
    const katexElements = labels.map(label => {
      const id = `${PLACEHOLDER_ID_PREFIX}${label}`;
      return canvasContainerDiv.querySelector(`#${id}`);
    });
    
    const katexPositions = katexElements.map(el => {
      el = el?.children?.[0] ?? el;
      if (el == undefined) return {x: 0, y: 0, isScript: false}
      const rect = el.getBoundingClientRect();
      const xPx = rect.left - canvasRect.left;
      const yPx = rect.top - canvasRect.top;
      const xEm = xPx / fontSize;
      const yEm = yPx / fontSize;
      const isScript = el.classList.contains('mtight');
      return {x: xEm, y: yEm, isScript};
    });
    
    return katexPositions;
  }

  
  insertNewTarget() {
    const alphabetLabels = this.labelList.filter(label => /^[A-Z]+$/.test(label));
    const numericLabels = alphabetLabels.map(s => alphabetToNumber(s));
    const newLabel = numberToAlphabet(Math.max(...numericLabels, -1) + 1);
    this.captureMath?.mathField?.insert(`\\boxed{${newLabel}}`)
  }
  
  isNewTargetButtonHovered = false;
  isFocus: boolean = false;
  cursorPosition: number = 0;
  focusTimer = null;
  setNewTargetButtonHoverState(state: boolean) {
    this.isNewTargetButtonHovered = state;
  }
  onFocus(){
    clearTimeout(this.focusTimer);
    this.isFocus = true;
  }
  onBlur(){
    this.focusTimer = setTimeout(() => {
      if (!this.isNewTargetButtonHovered){
        this.isFocus = false;
      } else {
        this.onBlur();
      }
    }, FOCUS_TIMEOUT);
  }
  
  insertAdvancedInlineContent(elementType: ElementType){
    const mathObj = this.getMathObject();
    if (mathObj.advancedInlineMathContent == undefined) mathObj.advancedInlineMathContent = [];
    mathObj.advancedInlineMathContent.push({
      content: '',
      type: elementType,
    });
    this.validateConfig();
  }
  removeAdvancedInlineContent(element: any){
    const confirm = window.confirm('Are you sure you want to delete this element?');
    if (!confirm) return;
    
    const mathObj = this.getMathObject();
    if (mathObj.advancedInlineMathContent == undefined) return;
    const index = mathObj.advancedInlineMathContent.indexOf(element);
    if (index === -1) return;
    
    mathObj.advancedInlineMathContent.splice(index, 1);
    this.validateConfig();
  }
  
  get isHideFontSizeOption(): boolean {
    return (this.config.hiddenConfigs as IClozeMathHiddenConfigs)?.isHideFontSize;
  }
}

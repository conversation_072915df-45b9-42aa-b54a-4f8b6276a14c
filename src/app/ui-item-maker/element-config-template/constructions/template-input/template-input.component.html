<div style="width: 100%;">
    <small *ngIf="!isHideEntryId">
        Entry {{block.entryId}}
    </small>
    <div *ngIf="!block.format" style="display: flex; flex-flow: row wrap; gap: 0.5em;">
        <a *ngFor="let format of advancedInlineInputFormats" class="button is-small input-format-option-button" style="padding: 2em; margin-right: 0px;" (click)="selectInputFormat(block, format.id)" [class.is-info]="isFormatActive(block, format.id)">
            <div class="input-format-option">
            <div class="icon">
                <i class="fa" [ngClass]="format.icon"></i>
            </div>
            <div>
                {{format.caption}}
            </div>
            </div>
        </a>
    </div>
    <div *ngIf="block.format == 'ratio'" class="text-list">
        <div class="space-between">
            Value
            <div style="display: flex; gap: 0.25em;">
                <div *ngFor="let ratioTerm of block.ratioTerms; let i = index;" style="display: flex; gap: 0.25em; align-items: center;">
                    <input 
                        type="text" 
                        class="input" 
                        style="width:40px; text-align:center"
                        [value]="ratioTerm"
                        (change)="setValueByIndex(block.ratioTerms, i, $event); emitInput()"
                    >
                    <div *ngIf="i != block.ratioTerms.length - 1">
                        :
                    </div>
                </div>
            </div>
        </div>
        <ng-container *ngTemplateOutlet="optionalInfo"></ng-container>
        <div style="display: flex; gap: 1em; justify-content: flex-end;">
            <button class="button is-small" (click)="addRatioTerm(block.ratioTerms)">Add Term</button>
            <button class="button is-small" (click)="removeLastElement(block.ratioTerms)">Remove Term</button>
        </div>
        <div class="space-between">
            Max character limit
            <input 
                type="text" 
                class="input" 
                style="width:120px; text-align:center" 
                (input)="emitInput()"
                [(ngModel)]="block.maxRatioChar"
            >
        </div>
    </div>
    <div *ngIf="block.format == 'number'" class="text-list">
        <div class="space-between">
            Answer
            <input 
                type="text" 
                class="input" 
                style="width:120px; text-align:center" 
                (input)="emitInput()"
                [(ngModel)]="block.value"
            >
        </div>
        <ng-container *ngTemplateOutlet="optionalInfo"></ng-container>
        <div class="space-between">
            Max character limit
            <input 
                type="text" 
                class="input" 
                style="width:120px; text-align:center" 
                (input)="emitInput()"
                [(ngModel)]="block.mathCharLimit"
            >
        </div>
        <div class="form-row" style="margin-top:1em">
            <div class="form-row-label">
              Disable Decimals
            </div>
            <div class="form-row-input">
              <input type="checkbox" [(ngModel)]="block.isDecimalsNotAllowed">
            </div>
        </div>
        <div class="form-row">
          <div class="form-row-label">
            <tra slug="auth_input_block_negative"></tra>
          </div>
          <div class="form-row-input">
            <input type="checkbox" [(ngModel)]="block.isNegativeSignBlocked">
          </div>
        </div>
    </div>
    <div *ngIf="block.format == 'words'" class="text-list">
        <div class="space-between gap">
            Minimum characters
            <input 
                type="number" 
                class="input text-number" 
                style="width:120px; text-align:center" 
                (input)="emitInput()"
                [(ngModel)]="block.minChars"
                min="1"
                step="1"
            >
        </div>
        <div class="space-between gap">
            Maximum characters
            <input 
                type="number" 
                class="input text-number" 
                style="width:120px; text-align:center" 
                (input)="emitInput()"
                [(ngModel)]="block.maxChars"
                min="1"
                step="1"
            >
        </div>
        <ng-container *ngTemplateOutlet="optionalInfo"></ng-container>
        <div class="text-list">
            <div class="space-between gap">
                Answer
                <div class="text-list">
                    <div *ngFor="let answer of block.allowedWordAnswers" class="space-between gap">
                        <input [(ngModel)]="answer.val" type="text" class="input" placeholder="Answer">
                        <a class="button" style="margin-right: 0px;" (click)="removeElementEmit(block.allowedWordAnswers, answer)">
                            <i class="fas fa-trash"  aria-hidden="true"></i>
                        </a>
                    </div>
                </div>
            </div>
            <button (click)="addWordAnswer(block)" class="button">Add answer</button>
        </div>
    </div>
    <div *ngIf="block.format == 'fraction'" class="text-list">
        <div class="form-row space-between">
            <div class="form-row-label">
              Mixed Number?
            </div>
            <div class="form-row-input">
              <input type="checkbox" [(ngModel)]="block.isMixedNumber">
            </div>
        </div>
        <div class="space-between form-row">
            Answer
            <div style="display: flex; flex-direction: row; align-items: center; gap: 0.5em">
              <div>
                <input 
                    *ngIf="block.isMixedNumber"
                    type="text" 
                    class="input fraction-input" 
                    style="width:120px; text-align:center" 
                    (input)="emitInput()"
                    [(ngModel)]="block.fracWholeNumber"
                >
              </div>
              <div>
                <input 
                    type="text" 
                    class="input fraction-input" 
                    style="width:120px; text-align:center" 
                    (input)="emitInput()"
                    [(ngModel)]="block.fracNumerator"
                >
                <div style="background-color: black; height: 2px; margin: 0.5em 0;">
                </div>
                <input 
                    type="text" 
                    class="input fraction-input"
                    style="width:120px; text-align:center" 
                    (input)="emitInput()"
                    [(ngModel)]="block.fracDenominator"
                >
              </div>
            </div>
        </div>
        <ng-container *ngTemplateOutlet="optionalInfo"></ng-container>
        <div class="space-between">
            Max character limit
            <input 
                type="text" 
                class="input" 
                style="width:120px; text-align:center" 
                (input)="emitInput()"
                [(ngModel)]="block.maxCharFraction"
            >
        </div>
        <div class="form-row space-between">
            <div class="form-row-label">
              <tra slug="checkbox_dont_accept_equivalent_fractions"></tra>
            </div>
            <div class="form-row-input">
              <input type="checkbox" [(ngModel)]="block.isStrictSimplified">
            </div>
        </div>
    </div>
    <div *ngIf="block.format == 'algebra'" class="text-list">
        <div class="space-between" style="gap: 0.5em">
            <div>Expected<br>Expression</div>
            <div style="flex-grow: 1; text-align: center; font-size: 150%;">
              <capture-math [class.no-pointer-events]="isReadOnly()" [obj]="block" prop="latex" [startBlank]="true" [isManualKeyboard]="true" [isAlgebra]="true"></capture-math>
            </div>
        </div>
        <div class="space-between" style="gap: 0.5em">
            <div>Extra<br>Cases</div>
            <div>
              <textarea [(ngModel)]="block.latexExtraCasesMultiline"></textarea>
            </div>
        </div>
    </div>
</div>

<ng-template #optionalInfo>
  <div *ngIf="block.isOptional" class="space-between">
    <span></span>
    <div class="optional-info">optional</div>
  </div>
</ng-template>
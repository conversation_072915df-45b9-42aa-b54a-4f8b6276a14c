import { Component, ElementRef, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { AuthScopeSetting, AuthScopeSettingsService } from '../../../auth-scope-settings.service';
import { EditingDisabledService } from '../../../editing-disabled.service';
import { IContentElementTemplate, TemplateConfigDefRow } from '../../../../ui-testrunner/element-render-template/model';
import { ITemplateDef, StyleprofileService, TemplateVersionConfig } from 'src/app/core/styleprofile.service';
import { Subscription } from 'rxjs';
import { createDefaultElement, elementIconById, generateDefaultElementText, generateDefaultElementTextLink } from '../../../item-set-editor/models';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { ElementType, IContentElement, IElementTypeDef } from 'src/app/ui-testrunner/models';
import { updateChangeCounter } from '../../../services/data-bind';
import { indexOf } from '../../../services/util';
import { ElementTypeDefs } from "src/app/ui-testrunner/models/ElementTypeDefs";
import { IContentElementMcq, IContentElementMcqOption, IContentElementMcqOptionInTable, IContentElementMcqTableColEachCell, McqDisplay } from '../../../../ui-testrunner/element-render-mcq/model';
import { getCellColSpan, getCellRowSpan } from '../../../element-config-table/merge-cell-functions';
import { IContentElementTable, IContentElementTableCell } from 'src/app/ui-testrunner/element-render-table/model';
import { LangService } from 'src/app/core/lang.service';
import { IContentElementInput, InputFormat } from 'src/app/ui-testrunner/element-render-input/model';
import { IContentElementValidator } from 'src/app/ui-testrunner/element-render-validator/model';
import { ItemBankCtrl } from '../../../item-set-editor/controllers/item-bank';
import { ensureResponseEntryIds, ensureResponseEntryIdsLocal } from '../../../item-set-editor/models/expected-answer';
import { IContentElementText, TextParagraphStyle } from 'src/app/ui-testrunner/element-render-text/model';
import { IContentElementSelectionTable } from 'src/app/ui-testrunner/element-render-selection-table/model';
import { TwiddleState } from '../../../../ui-partial/twiddle/twiddle.component';

@Component({
  selector: 'template-input',
  templateUrl: './template-input.component.html',
  styleUrls: ['./template-input.component.scss']
})
export class TemplateInputComponent implements OnInit, OnDestroy {

  @Input() config: TemplateConfigDefRow;
  @Input() block: IContentElementInput;
  @Input() isHideEntryId: boolean = false;
  @Input() allowOptionalOnEmpty: boolean = false;
  @Output() onSelectInputFormat = new EventEmitter<any>();
  @Output() onInput = new EventEmitter<any>();
  @Output() onRemoveElement = new EventEmitter<any>();
  @Output() onValidatorAlign = new EventEmitter<any>();

  elementTypes = ElementType;

  advancedInlineInputFormats = [
    { id: InputFormat.NUMBER, icon: 'fa-calculator', caption: 'Number' },
    { id: InputFormat.FRACTION, icon:'fa-pencil-alt', caption:'Fraction'},
    { id: InputFormat.RATIO, icon: 'fa-chart-pie', caption: 'Ratio' },
    {id: InputFormat.ALGEBRA,  icon:'fa-pencil-alt',     caption:'Algebra'},
    // {id: InputFormat.TEXT,     icon:'fa-font',       caption:'Essay / Writing'},
    { id: InputFormat.WORDS, icon: 'fa-font', caption: 'Word(s)' },
    //{id: InputFormat.FORM,     icon:'fa-font', caption: "Form"}
    // {id: InputFormat.NUMBER_LIST,  icon:'fa-calculator',       caption:'Number (List)'},
  ]
  constructor(
    private authScopeSettings: AuthScopeSettingsService,
    private editingDisabled: EditingDisabledService,
    private styleProfileService: StyleprofileService,
    private lang: LangService,
    private itemBankCtrl: ItemBankCtrl
  ) { }

  ngOnInit(): void {
  }

  ngOnDestroy(): void {
  }

  selectInputFormat(content: IContentElementInput, inputFormatId: InputFormat) {
    content.format = inputFormatId;
    if (inputFormatId === InputFormat.NUMBER) {
      content.inputNumFormat = 'Restricted';
    }
    this.onSelectInputFormat.emit({ content, inputFormatId });
    this.checkOptional();
  }

  isFormatActive(content: IContentElementInput, inputFormatId: InputFormat) {
    return inputFormatId === content.format;
  }

  addRatioTerm(ratioTerms: string[]) {
    const rt: any = null;
    ratioTerms.push(rt);
  }

  removeLastElement(content: any[]) {
    if (content.length == 0) {
      return;
    }
    return content.pop();
  }

  setValueByIndex(content: any[], index: number, newValue: any): void {
    content[index] = newValue.target.value;
  }

  addWordAnswer(element: IContentElementInput) {
    if (!element.allowedWordAnswers || !element.allowedWordAnswers.length) {
      element.allowedWordAnswers = []
    }

    element.allowedWordAnswers.push({ val: '' })
    this.checkOptional();
  }
  
  isInputEmpty() {
    switch (this.block.format) {
      case InputFormat.NUMBER: {
        return !this.block.value || this.block.value.trim() == '';
      }
      case InputFormat.FRACTION: {
        const numEmpty = !this.block.fracNumerator || this.block.fracNumerator.trim() == '';
        const denEmpty = !this.block.fracDenominator || this.block.fracDenominator.trim() == '';
        const wholeEmpty = !this.block.isMixedNumber ||  !this.block.fracWholeNumber || this.block.fracWholeNumber.trim() == '';
        return numEmpty && denEmpty && wholeEmpty;
      }
      case InputFormat.RATIO: {
        return this.block.ratioTerms.every(term => !term || term.trim() == '');
      }
      case InputFormat.WORDS: {
        return this.block.allowedWordAnswers.length == 0;
      }
    }
    return false;
  }
  
  checkOptional() {
    const prev = !!this.block.isOptional;
    if (!this.allowOptionalOnEmpty) {
      delete this.block.isOptional;
      if (prev) {
        this.emitValidatorAlign();
      }
    } else {
      this.block.isOptional = this.isInputEmpty();
      if (prev !== this.block.isOptional) {
        this.emitValidatorAlign();
      }
    }
  }


  removeElementEmit(content: any[], element: any) {
    this.onRemoveElement.emit({ content, element });
    this.checkOptional();
  }
  emitInput() {
    this.checkOptional();
    this.onInput.emit();
  }
  isReadOnly() {
    return this.editingDisabled.isReadOnly();
  }
  emitValidatorAlign() {
    this.onValidatorAlign.emit();
  }
}

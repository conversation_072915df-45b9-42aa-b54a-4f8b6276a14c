<fieldset [disabled]="isReadOnly()">
  <div *ngIf="isOuterFrame">
    <label *ngIf="isOuterFrame">
      <input type="checkbox" [(ngModel)]="element._isSimplifiedConfigView"/>
      <span> Simplified View</span>
    </label><br>
      
    <label *ngIf="!getIsSimplifiedConfigView()">
      <input type="checkbox" [(ngModel)]="element.isShowAdvancedOptions"/>
      <span> Show Advanced Options</span>
    </label>
  </div>
  
  <div *ngIf="getIsShowAdvancedOptions()" class="style-toggle-container">
    <ng-container
      *ngIf="isAllowShowBorder(this.element)"
      [ngTemplateOutlet]="styleToggleCheckbox"
      [ngTemplateOutletContext]="{label: 'Show Border', key: 'border', activeValue: '1px solid #ccc'}"
    ></ng-container>
    
    <ng-container
      *ngIf="!isOuterFrame" [ngTemplateOutlet]="styleToggleCheckbox"
      [ngTemplateOutletContext]="{label: 'Padding', key: 'padding', activeValue: '0.5em'}"
    ></ng-container>
    
    <!-- <ng-container
      *ngIf="getStimType(this.element) == 'horizontal_container'" [ngTemplateOutlet]="styleToggleCheckbox"
      [ngTemplateOutletContext]="{label: 'Center Content', key: 'align-items', activeValue: 'center'}"
    ></ng-container> -->
    
    <label *ngIf="getStimType(this.element) == 'table'">
      <input type="checkbox" [(ngModel)]="getInnerContentElement(this.element).isHideBorder"/>
      <span> Remove table borders</span>
    </label>
    
    <ng-container
      *ngIf="getStimType(this.element) == 'vertical_container' && !isOuterFrame && !isVerticalContainerAlwaysFullWidth" [ngTemplateOutlet]="styleToggleCheckbox"
      [ngTemplateOutletContext]="{label: 'Extend To Full Width', key: 'width', activeValue: '100%', defaultValue: 'fit-content'}"
    ></ng-container>
    
    <ng-container
      *ngIf="getStimType(this.element) == 'vertical_container' && !isOuterFrame" [ngTemplateOutlet]="styleToggleCheckbox"
      [ngTemplateOutletContext]="{label: 'Extra bottom margin', key: 'margin-bottom', activeValue: '1em'}"
    ></ng-container>
    
  </div>
  
  <div *ngIf="(parentElementType == 'horizontal_container' || parentElementType == 'vertical_container') && getIsShowAdvancedOptions()">
    <hr style="margin: 0.3em 0; height: 1px">
    <label><b>Alignment</b></label>
    <div *ngIf="parentElementType == 'vertical_container'" class="style-toggle-alignment-container">
      <button 
        *ngFor="let align of horizontalAlignments"
        [disabled]="isReadOnly()" 
        class="button is-small"  
        (click)="toggleHorizontalAlignment(align.id)" 
      >
        <i class="fa {{align.icon}}"></i>
        <span style="margin-left: 1em">{{align.id}}</span>
      </button>
    </div>
    <div *ngIf="parentElementType == 'horizontal_container'" class="style-toggle-alignment-container">
      <button 
        *ngFor="let align of verticalAlignments"
        [disabled]="isReadOnly()" 
        class="button is-small"  
        (click)="toggleVerticalAlignment(align.id)" 
      >
        <i class="fa {{align.icon}}"></i>
        <span style="margin-left: 1em">{{align.id}}</span>
      </button>
    </div>
  </div>
  
  <ng-template #styleToggleCheckbox let-label="label" let-key="key" let-activeValue="activeValue" let-defaultValue="defaultValue">
    <label>
      <input type="checkbox" [checked]="element.styleRaw[key] == activeValue" (change)="toggleStyle(key, activeValue, defaultValue ?? '')" />
      <span> {{label}}</span>
    </label>
  </ng-template>
  
  <div *ngIf="!isContainer">
    <div [ngSwitch]="getStimType(this.element)">
      <div *ngSwitchCase="'text'">
        <textarea 
            textInputTransform
            [(ngModel)]="element.content[0]['caption']"
            cdkTextareaAutosize 
            [cdkTextareaAutosize]="true" 
            [cdkAutosizeMinRows]="2"
            class="textarea"
        ></textarea>
      </div>
      <div *ngSwitchCase="'image'">
        <capture-image [element]="element.content[0]" fileType="image" [isNoScale]="false" [displayImageControls]="false" (change)="syncImageCaptionSize()"></capture-image>
        <asset-library-link [element]="element.content[0]"></asset-library-link>
        <br>
        <label><b>Caption</b></label>
        <div *ngIf="!isImageHasCaption()">
          <button class="button" [disabled]="isReadOnly()" (click)="imageAddCaption()">
            Add Caption
          </button>
        </div>
        <div *ngIf="isImageHasCaption()">
          <div *ngIf="getImageCaptionFrameElement() as captionElement">
            <div *ngIf="getIsShowAdvancedOptions()" class="style-toggle-container">
              <label>
                <input type="checkbox" 
                  [checked]="captionElement.styleRaw['border'] == '1px solid #ccc'" 
                  (change)="toggleStyleElement(captionElement, 'border', '1px solid #ccc')" />
                <span> Show Border</span>
              </label>
            </div>
            <textarea 
                textInputTransform
                [(ngModel)]="captionElement['content'][0]['caption']"
                (input)="syncImageCaptionSize()"
                cdkTextareaAutosize 
                [cdkTextareaAutosize]="true" 
                [cdkAutosizeMinRows]="2"
                class="textarea"
            ></textarea>
            <button class="button" [disabled]="isReadOnly()" (click)="imageRemoveCaption()">
              Remove Caption
            </button>
          </div>
        </div>
      </div>
      <div *ngSwitchCase="'math'">
        <div class="capture-math-container">
            <capture-math [obj]="element.content[0]" prop="latex" [isManualKeyboard]="true" [class.is-disabled]="isReadOnly()"></capture-math>
        </div>
      </div>
      <div *ngSwitchCase="'table'">
        <template-table
            [elementRef]="elementRef + '.content.0'"
            [tableElement]="element.content[0]"
            [twiddleToggleMap]="twiddleToggleMap"
            [isNonInteractive]="true"
            (onRemoveElement)="removeElementEmit($event.content, $event.element)"
            (onImageUploaded)="onImageUploadEmit()"
            (onTwiddleToggle)="onTwiddleToggleEmit($event.elementRef, $event.key, $event.id, $event.toggle)"
        ></template-table>
      </div>
      <div *ngSwitchCase="'virtual_tools'">
        <element-config [contentElement]="element.content[0]"></element-config>
      </div>
      <div *ngSwitchCase="'text:advanced_inline'">
        <template-advanced-inline
            [isStaticElementOnly]="true"
            [advancedList]="element.content[0]['advancedList']"
            (onRemoveElement)="removeElementEmit($event.content, $event.element)"
            (onImageUploaded)="onImageUploadEmit()"
        ></template-advanced-inline>
      </div>
      <div *ngSwitchCase="'text:bullet'">
        <template-advanced-inline
            [isStaticElementOnly]="true"
            [advancedList]="element.content[0]['advancedList']"
            [allowAdvancedInlineElement]="true"
            (onRemoveElement)="removeElementEmit($event.content, $event.element)"
            (onImageUploaded)="onImageUploadEmit()"
        ></template-advanced-inline>
      </div>
      <div *ngSwitchCase="'text:numbered'">
        <template-advanced-inline
            [isStaticElementOnly]="true"
            [advancedList]="element.content[0]['advancedList']"
            [allowAdvancedInlineElement]="true"
            (onRemoveElement)="removeElementEmit($event.content, $event.element)"
            (onImageUploaded)="onImageUploadEmit()"
        ></template-advanced-inline>
      </div>
      <div *ngSwitchDefault>
        NOT IMPLEMENTED
      </div>
    </div>
  </div>
  
  <div *ngIf="isContainer">
    
    <ng-template #innerStim let-contentElement="contentElement" let-index="index">
      <template-stim
        [elementRef]="elementRef + '.content.' + index"
        [element]="contentElement"
        [isOuterFrame]="false"
        [isContainer]="isContainerType(contentElement)"
        [isShowAdvancedOptions]="getIsShowAdvancedOptions()"
        [twiddleToggleMap]="twiddleToggleMap"
        [isSimplifiedConfigView]="getIsSimplifiedConfigView()"
        [isVerticalContainerAlwaysFullWidth]="isVerticalContainerAlwaysFullWidth"
        [isAllowBorderOnlyOnOuterVerticalContainer]="isAllowBorderOnlyOnOuterVerticalContainer"
        [isChildOfOuterFrame]="isOuterFrame"
        [parentElementType]="getStimType(element)"
        (onImageUploaded)="onImageUploadEmit()"
        (onRemoveElement)="removeElementEmit($event.content, $event.element)"
        (onTwiddleToggle)="onTwiddleToggleEmit($event.elementRef, $event.key, $event.id, $event.toggle)"
      ></template-stim>
    </ng-template>
    
    <div *ngIf="getIsSimplifiedConfigView()">
      <div *ngFor="let contentElement of element.content; let i = index">
        
        <div *ngIf="isCollapsableInSimplifiedViewType(getStimType(contentElement))">
          <twiddle style="width: 100%;" [caption]="stimElementLabels[getStimType(contentElement)]" (change)="onTwiddleToggleEmit(elementRef, 'content', i, $event)" [defaultValue]="true"></twiddle>
          <div *ngIf="getTwiddleToggleInfo(elementRef, 'content', i, true)" class="sub-property-div">
            <div class="simplified-nested-element collapsable" [class.not-container]="!isContainerType(contentElement)">
              <ng-container *ngTemplateOutlet="innerStim; context: {contentElement: contentElement, index: i}"></ng-container>
            </div>
          </div>
        </div>
        
        <div *ngIf="!isCollapsableInSimplifiedViewType(getStimType(contentElement))">
          <div class="simplified-nested-element" [class.not-container]="!isContainerType(contentElement)">
            <ng-container *ngTemplateOutlet="innerStim; context: {contentElement: contentElement, index: i}"></ng-container>
          </div>
        </div>
        
      </div>
    </div>
    
    <div *ngIf="!getIsSimplifiedConfigView()">
      <div cdkDropList (cdkDropListDropped)="dropContent($event)">
        <div *ngFor="let contentElement of element.content; let i = index" cdkDrag>
          <div class="nested-element">
            <div class="nested-element-header" style="font-size: 0.8em;" cdkDragHandle>
              <button (click)="contentElement._isCollapsed = !contentElement._isCollapsed" class="button is-small">
                  <i class="fa" [ngClass]="stimElementIcons[getStimType(contentElement)]" aria-hidden="true"></i>
              </button>
              <a [class.is-disabled]="isReadOnly()" class="button is-small" (click)="removeElementEmit(element.content, contentElement);" >
                <i class="fas fa-trash"></i>
              </a>                  
            </div>
            <div class="nested-element-content" *ngIf="!contentElement._isCollapsed">
              <ng-container *ngTemplateOutlet="innerStim; context: {contentElement: contentElement, index: i}"></ng-container>
            </div>
          </div>
        </div>
      </div>
      
      <div class="field has-addons" style="margin-top:1em;">
          <div class="control is-expanded">
              <div class="select is-fullwidth">
                  <select [(ngModel)]="elementTypeForInsertion">
                      <option *ngFor="let elementType of stimElementOptions" [value]="elementType">
                          {{lang.tra(stimElementLabels[elementType])}}
                      </option>
                  </select>
              </div>
          </div>
          <div class="control">
              <button class="button is-primary" (click)="addStimElement(elementTypeForInsertion)">Add Element</button>
          </div>
      </div>
    </div>
    
  </div>
</fieldset>
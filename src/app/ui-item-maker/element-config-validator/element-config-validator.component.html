<fieldset [disabled]="isReadOnly()">
    <div class="simple-form-container" style="display: flex; flex-direction: column" >
        <div *ngIf="isAllOrNothingMode()">
            <div>Target IDs (separate by comma)</div>
            <input type="text" [(ngModel)]="element.targetEntryIdList" /> 
        </div>
        <div *ngIf="isSingleMode()">
            <label style="width: 50%">Validate ID <input type="number" [(ngModel)]="element.validateId" /> </label>
            <label style="width: 50%">Validate Property <input type="text" [(ngModel)]="element.validateProp"> </label>
            <label style="width: 50%">Correct Value <input type="text" [(ngModel)]="element.correctValue"> </label>
        </div>
        <div *ngIf="isComboMode()">
            <button (click)="addCombination()" class="button is-primary" type="submit">Add Combination </button>
            <div class="combo-info">
                <p><strong>Info!</strong> Please set elements weight to 0 explicitly.</p>
            </div>              
            <ng-container *ngFor="let combinations of element.combinations; let combinationIdx = index">
                <hr/>
                <button (click)="addCombinationElement(combinationIdx)" class="button is-primary is-small" style="margin: 0.3em;">Add element</button>
                <ng-container *ngFor="let combination of combinations; let elementIdx = index">
                    <label style="width: 50%">Validate ID <input type="number" [(ngModel)]="combination.validateId" /> </label>
                    <label style="width: 50%">Element Type 
                        <!-- <input type="text" [(ngModel)]="combination.elementType"> -->
                        <select [(ngModel)]="combination.elementType" style="margin-left: 1em;">
                            <option *ngFor="let elementType of elementTypes" [ngValue]="elementType.id">
                              {{lang.tra(elementType.caption)}}
                            </option>
                        </select>
                    </label>
                <ng-container [ngSwitch]="hasTargetIds(combination.elementType)">
                    <label 
                        *ngSwitchCase="false"
                        style="width: 50%">
                        Correct {{getCorrectType(combination.elementType)}} 
                        <input type="text" [(ngModel)]="combination.correctValue"> 
                    </label>
                    <ng-container *ngSwitchCase="true">
                        <button (click)="addTarget(combinationIdx,elementIdx)" class="button is-primary is-small" style="margin: 0.3em;">Add Target</button>
                        <ng-container *ngFor="let dndElement of combination.dndElements let targetIdx = index">
                            <div>
                                <label style="width: 50%">order matters?: <input type="checkbox" [(ngModel)]="dndElement.isOrderImp"></label>
                                <label style="width: 50%">Target Id: <input type="text" [(ngModel)]="dndElement.targetId"></label>
                                <label style="width: 75%">Correct {{getCorrectType(combination.elementType)}} <input type="text" [(ngModel)]="dndElement.correctValue"> </label>
                                <button (click)="removeTarget(combinationIdx, elementIdx, targetIdx)" style="margin: 0.3em;" class="button is-danger is-small" >Delete Target</button>
                            </div>
                        </ng-container>
                    </ng-container>
                </ng-container>
                <button (click)="deleteCombinationElement(combinationIdx,elementIdx)" style="margin: 0.3em;" class="button is-danger is-small" >Delete Element</button>
                </ng-container><br>
                <div *ngIf="combinations.length>0">
                    <label style="width: 50%">Score <input type="number" [(ngModel)]="combinations[0].score" /> </label>
                </div>
                <button (click)="deleteCombination(combinationIdx)" class="button is-danger" style="margin-top: 0.3em;">Delete Combination</button>
            </ng-container>
        </div>
        <div *ngIf="isAdvComboMode()" class="column-flex gap">
            <div class="combo-info">
                <p><strong>Info!</strong> Please set elements weight to 0 explicitly.</p>
            </div>
            <div>
                <strong>Entries</strong>
                <div class="column-flex gap">
                   <div *ngFor="let entry of element.advCombinationEntries; let entryIdx = index;" style="display: flex; justify-content: space-between; max-width: 23em;">
                        <div class="column-flex" style="gap: 0.25em;">
                            <label style="width: 50%">
                                Validate ID 
                                <input class="input" type="number" [(ngModel)]="entry.validateId" (ngModelChange)="onValidateIdChange($event, entryIdx)"/> 
                            </label>
                            <label style="width: 50%">
                                Element Type 
                                <div class="select" style="max-width: auto;">
                                    <select [(ngModel)]="entry.elementType" (ngModelChange)="onElementTypeChange($event, entryIdx)" style="margin-left: 1em;">
                                        <option *ngFor="let elementType of elementTypes" [ngValue]="elementType.id">
                                        {{lang.tra(elementType.caption)}}
                                        </option>
                                    </select>
                                </div>
                            </label>
                        </div>
                        <a class="button" style="margin-right: 0px;" (click)="deleteEntryElement(entryIdx);">
                            <i class="fas fa-trash"  aria-hidden="true"></i>
                        </a>
                   </div> 
                   <div>
                    <button class="button is-primary" (click)="addEntryElement()">Add Entry</button>
                   </div>
                </div>  
            </div>
            <div>
                <strong>Combinations</strong>    
                <table class="table">
                    <tr *ngIf="element.advCombinations && element.advCombinations.length">
                        <th style="width: 1em;"></th>
                        <th *ngFor="let column of element.advCombinations[0]" style="max-width: 5em;">
                            Entry: {{getLabelFromEntryId(column.validateId)}}
                        </th>
                    </tr>
                    <tr *ngFor="let combination of element.advCombinations; let combinationIdx = index;">
                        <th style="text-align: center; vertical-align: middle;">
                            <a class="small-icon" style="color: black;" (click)="deleteAdvCombination(combinationIdx);">
                                <i class="fas fa-trash"  aria-hidden="true"></i>
                            </a> 
                        </th>
                        <th *ngFor="let answer of combination" style="max-width: 5em;">
                            <input class="input" style="text-align: center;" type="text" [(ngModel)]="answer.correctValue">
                        </th>
                    </tr>
                </table>
                <button [disabled]="!hasEntries()" (click)="addAdvCombination()" class="button is-primary" style="margin-top: 1em;" type="submit">Add Combination </button>
            </div>
        </div>
        <hr/>
        <div class="form-row" *ngIf="isScoreWeightEnabled()">
            <div class="form-row-label">
                Score Weight
            </div>
            <div class="form-row-input">
                <input type="text" class="input" [(ngModel)]="element.scoreWeight">
            </div>
        </div>
    </div>
    <div class="control is-expanded">
        <div [class.is-disabled]="isReadOnly()" class="select is-fullwidth">
          <select [(ngModel)]="element.mode">
            <option *ngFor="let mode of modes; let index = index" [value]="mode.id">
              {{mode.caption}}
            </option>
          </select>
        </div>
      </div>
</fieldset>

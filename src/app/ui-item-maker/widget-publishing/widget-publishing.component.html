<div *ngIf="publishingCtrl">
  <div>
    <button *ngIf="!editingDisabled.isSingularReadOnly()" class="button is-main" [disabled]="publishingCtrl.isPublishingTestDesign || isReadOnly()" (click)="publishingCtrl.publishTestDesign()"><tra slug="ie_publish_test_design"></tra></button>
    <span *ngIf="publishingCtrl.isPublishingBlockedByItemRegisterLoad()" class="tag">Loading Item Register variables...</span>  
  </div>
  <div class="notification" *ngIf="publishingCtrl.isPublishingTestDesign">Publishing test design....</div>
  <div style="margin-top:1em;" *ngIf="publishingCtrl.testDesignReleaseHistory">
    <strong><tra slug="ie_publishing_history"></tra>: </strong> 
    <button (click)="publishingCtrl.isShowingTestDesignReleaseHistory = !publishingCtrl.isShowingTestDesignReleaseHistory" class="button is-small">
      <span>
        {{publishingCtrl.testDesignReleaseHistory.length}} 
        <tra slug="ie_release_history_releases"></tra>
      </span>
    </button>
    <tra *ngIf="publishingCtrl.isShowingTestDesignReleaseHistory" slug="auth_releases_shown"></tra> <input *ngIf="publishingCtrl.isShowingTestDesignReleaseHistory" type="number" min="1" class="input is-small" [formControl]="filterNum" style="width:4em;">
    <div style=" margin-bottom: 1em">
      <div style="display: flex; flex-direction: column;">
        <div style="display: flex; align-items: center;">
          <span style="margin-right: 0.5em">
            Public Version is Access Controlled?
          </span> 
          <ng-container *ngIf="isReadOnly()">
            {{ itemBankCtrl.publicPwdProtected ? 'Yes' : 'No' }}
          </ng-container>
          <ng-container *ngIf="!isReadOnly()">
            <check-toggle [isChecked]="itemBankCtrl.publicPwdProtected" (toggle)="publishingCtrl.togglePwdProtected($event)"></check-toggle>
          </ng-container>
        </div>
      </div>
      <a href="/#/{{lang.c()}}/test-auth/public-test-runner/{{itemBankCtrl.customTaskSetId}}/1/{{itemBankCtrl.groupId}}/{{ itemBankCtrl.single_groupId ? itemBankCtrl.single_groupId : -1 }}" target="_blank">
        Link to latest version of the assessment
      </a>
    </div>
    <div *ngIf="publishingCtrl.isShowingTestDesignReleaseHistory && resetter" class="space-between" style="align-items:flex-start;">
      <div style="display: flex; flex-direction: column; gap: 1em;">
        <div>
          <button class="button is-small" (click)="publishingCtrl.loadTestDesignReleaseHistory()"><tra slug="mrkg_refresh"></tra></button>
        </div>
        <table class="table is-condensed is-small is-bordered" style="width:auto;">
          <tr>
            <th><tra slug="ie_id"></tra></th>
            <th><tra slug="ie_name"></tra></th>
            <th><tra slug="lbl_stat"></tra></th>
            <th><tra slug="lbl_last_completed_stage"></tra></th>
            <th><tra slug="ie_date_released"></tra></th>
            <th><tra slug="Sign Off"></tra></th>
            <th *ngIf="publishingCtrl.isMultiForm()">Addl. Forms</th>
            <th>Public</th>
            <th *ngIf="saveLoadCtrl.isEarlyYears" >Sample ISR</th>
          </tr>
          
          <tr *ngFor="let record of getReleases();" [class.is-publish-focus]="activePublishedTestDesign==record.id">
              <td>{{record.id}}</td>
              <td>
                <div style="font-size: 1.2em;">
                  {{record.name}}
                </div>
                <div *ngIf="record.twtdars" style="margin-top:0.5em; margin-left:1em">
                  <strong><u>Administration Windows</u></strong>
                  <ul  style="margin-top:0em; "> 
                    <li *ngFor="let twtdar of record.twtdars">
                      <div>
                        <strong>{{renderTwTitle(twtdar.title)}}</strong>
                      </div>
                      <div>
                        {{renderDate(twtdar.date_start)}} to
                        {{renderDate(twtdar.date_end)}}
                        <a (click)="twtdar.__isExpanded=!twtdar.__isExpanded">(more)</a>
                      </div>
                      <div *ngIf="twtdar.__isExpanded">
                        <code>
                          [{{twtdar.tw_id}}] {{twtdar.type_slug}}
                          <div style="color: #888">
                            {{twtdar.user_metas_filter}}
                          </div>
                        </code>
                      </div>
                    </li>
                  </ul>
                </div>
              </td>
              <td>
                <span *ngIf="record.is_publishing_in_progress == 1 && record.is_error == 0">
                  <tra slug="lbl_status_inprog"></tra>
                </span>
                <span *ngIf="record.is_publishing_in_progress == 0 && record.is_error == 0">
                  <tra slug="lbl_status_published"></tra>
                </span>
                <span *ngIf="record.is_error == 1">
                  <tra slug="lbl_error"></tra>
                </span>
              </td>
              <td>
                {{record.last_stage_completed}}
              </td>
              <td>{{renderDate(record.published_on || record.created_on, 'datefmt_timestamp')}}</td>
              <td>
                <button *ngIf="!isSignOffStarted(record)" [disabled]="isSignOffLoading" (click)="startTestDesignSignOff(record)" class="button is-small">Sign-Off</button>
                <div *ngIf="isSignedOff(record)">
                  <span *ngIf="hasComparedTestDesign(record)" class="compared-to">
                    Compared to {{record.compared_to_td_id}}
                  </span>
                  <span *ngIf="!hasComparedTestDesign(record)" class="compared-to">
                    Initial
                  </span>
                  &nbsp;
                  Signed off by {{record.approved_by}} on {{record.approved_on}}
                </div>
                <div *ngIf="isSignOffStarted(record) && !isSignedOff(record)">
                  Sign off <span class="compared-to">in progress</span> started by {{record.started_by}} on {{record.started_on}}
                  <br>
                  <a (click)="restoreTestDesignSignOff(record.tdso_id)">Click here to continue</a>
                </div>
              </td>
              <td *ngIf="publishingCtrl.isMultiForm()">
                <a (click)="selectPublishedTestDesign(record.id)">
                  View ({{record.num_forms}} forms)
                </a>
                <button 
                  [disabled]="publishingCtrl.isPublishingTestDesign || isReadOnly()" 
                  (click)="publishingCtrl.publishTestDesign(record.id)"
                >
                  Publish More
                </button>
              </td>
              <td>
                <check-toggle [isChecked]="record.is_public==1" (toggle)="publishingCtrl.toggleTestDesignIsPublic(record)"></check-toggle>
                <a *ngIf="record.is_public==1" href="/#/{{lang.c()}}/test-auth/shared-test-version/{{itemBankCtrl.customTaskSetId}}/{{record.id}}" target="_blank" style="margin-left: 0.5em">
                  <tra slug="auth_link"></tra> 
                </a>
              </td>
              <!-- <td *ngIf="saveLoadCtrl.isEarlyYears">
                <a (click)="openIsrOptions(record.id)">Print</a>
              </td> -->
          </tr>
        </table>
      </div>
      <div *ngIf="activeTestDesignForms" style="margin-left:2em;">
        <h4>Associated Test Forms/Panels</h4>
        <p>Student counts indicated below include sample school students.</p>
        <table class="table is-condensed is-small is-bordered" style="width:auto;">
          <tr>
            <td>id</td>
            <td>form/panel name </td>
            <td>lang</td>
            <td>Students Assigned</td>
            <td>Students Submitted</td>
            <td>Created On</td>
            <td>Last Assigned On</td>
            <td>revoked? </td>
          </tr>
          <tr *ngFor="let testForm of activeTestDesignForms">
            <td>{{testForm.id}}</td>
            <td>{{testForm.source_tf_id}} </td>
            <td>{{testForm.lang}}</td>
            <td>{{testForm.num_students_assigned}}</td>
            <td>{{testForm.num_students_submitted}}</td>
            <td>{{testForm.created_on}} </td>
            <td>{{testForm.last_assigned_on}}</td>
            <td>
              <span *ngIf="testForm.is_revoked == 1">
                Revoked <button (click)="unrevokeForm(testForm)">Undo</button>
              </span>
              <span *ngIf="testForm.is_revoked == 0">
                <button style="color:red;" (click)="revokeForm(testForm)">Revoke</button>
              </span>
            </td>
          </tr>
        </table>
      </div>
    </div>
    <!-- <div class="custom-modal" *ngIf="dateSelectorModalIsOpen">
      <div class="modal-contents" style="max-width:80vw;">
        <h1>Select Dates</h1>
        <mat-form-field>
          <input matInput 
            [matDatepicker]="picker"
            [formControl]="dateControl"
            (dateChange)="addDate($event)">
          <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
          <mat-datepicker #picker></mat-datepicker>
        </mat-form-field>
        <div>Dates Selected: {{ sampleDates | json }}</div>
        <div class="buttons">
          <button mat-button (click)="clearDates()">Clear Dates</button>
          <button mat-button (click)="closeDateModal()">Cancel</button>
          <button [disabled]="sampleDates.length == 0" mat-button (click)="printSampleIsr()">Confirm</button>
        </div>
      </div>
    </div> -->
    <!-- to do -->
  </div>
</div>
<fieldset [disabled]="isReadOnly()">
  <div style="margin-bottom: 1em">
    <button (click)="setUseImage(true)" class="button is-small has-icon" [class.is-info]="isUseImage()">
      <span class="icon"><i class="fa fa-image" aria-hidden="true"></i></span>
      <span>Allow Images</span>
      <!-- Allow Images -->
    </button>
    <button (click)="setUseImage(false)" class="button is-small has-icon" [class.is-info]="!isUseImage()">
      <span class="icon"><i class="fa fa-font" aria-hidden="true"></i></span>
      <span>Text Only</span>
    </button>
    
    <div>
      <label class="checkbox">
        <input type="checkbox" [(ngModel)]="element.isShowAdvancedOptions"/>
        Show Advanced Options
      </label>
    </div>
  </div>
</fieldset>

<div class="table-container" [class.is-reordering]="isReordering">
  <div *ngIf="isReordering"></div>
  <div *ngIf="isReordering" class="table-reorder-column-container" cdkDropList (cdkDropListDropped)="dropColumn($event)" [cdkDropListOrientation]="'horizontal'">
    <div *ngFor="let _ of element.table[0]; let i = index" cdkDrag>
      <div class="table-reorder-handle" [style.width.px]="tableDOMColumnSizes[i]">
        <div>{{i+1}}</div>
      </div>
    </div>
  </div>
  <div *ngIf="isReordering" class="table-reorder-row-container" cdkDropList (cdkDropListDropped)="dropRow($event)">
    <div *ngFor="let _ of tableForm; let i = index" cdkDrag>
      <div class="table-reorder-handle" [style.height.px]="tableDOMRowSizes[i]">
        <div>{{i+1}}</div>
      </div>
    </div>
  </div>
  <table #tableDOM>
    <tr *ngIf="element.table[0] && !isReordering">
      <td></td>
      
      <ng-container *ngIf="!element.isShowAdvancedOptions">
        <td 
          *ngFor="let cell of element.table[0]; let j = index"
          [class.no-pointer-events]="isReadOnly()" class="selectable-cell" (click)="elementEditDeleteColumn(j)"
        >
          <i class="fas fa-trash" aria-hidden="true"></i>
        </td>
      </ng-container>
      <ng-container *ngIf="element.isShowAdvancedOptions">
        <td *ngFor="let cell of element.table[0]; let j = index" style="background-color: #ccc;">
          <div style="display: flex; justify-content: center;">
            <button [disabled]="isReadOnly()" class="button is-small" (click)="elementEditDeleteColumn(j)">
              <i class="fa fa-trash"></i>
            </button>
          </div>
          <div style="display: flex; justify-content: center; margin-top: 0.2em;">
            <button *ngFor="let align of alignments" [disabled]="isReadOnly()" class="button is-small" (click)="elementEditSetColumnAlignment(j, align.id)">
              <i class="fa {{align.icon}}"></i>
            </button>
          </div>
        </td>
      </ng-container>
      
    </tr>
    <tr *ngFor="let row of tableForm; let i = index; trackBy: trackByFn">
      <td [class.no-pointer-events]="isReadOnly()" class="selectable-cell" (click)="elementEditDeleteRow(i)" *ngIf="!isReordering">
        <i class="fas fa-trash" aria-hidden="true"></i>
      </td>
      <td *ngFor="let cell of row; let j = index; trackBy: trackByFn">
        
        <div *ngIf="isUseImage(); then cellAllowImage; else cellTextOnly"></div>
        
        <!-- TODO: simplify this -->
        <ng-template #cellTextOnly>
          
          <ng-container *ngIf="isMathCell(i,j)">
            <div 
              tabindex="-1"
              class="capture-math-container" 
              (focusin)="setFocusedCell(i,j)"
              (focusout)="resetFocusedCell()"
            >
              <capture-math #cellText [obj]="getCellObject(i,j)" prop="text" [isManualKeyboard]="true" [class.is-disabled]="isReadOnly()" (onChange)="rerender()"></capture-math>
            </div>
          </ng-container>
          
          <ng-container *ngIf="!isMathCell(i,j)">
            <textarea
              #cellText
              textInputTransform
              [formControl]="cell"
              style="min-width: 10em;"
              class="textarea is-small"
              [class.target-cell]="isTargetCell(i,j)"
              cdkTextareaAutosize
              [cdkTextareaAutosize]="true"
              [cdkAutosizeMinRows]="2"
              (focus)="setFocusedCell(i, j, cell, 'cell_' + i + '_' + j)"
              (blur)="resetFocusedCell()"
            ></textarea>
          </ng-container>
          <div *ngIf="isTargetCell(i,j)" class="cell-target-info">
            (target)
          </div>
        </ng-template>
        
        <ng-template #cellAllowImage>
          <ng-container *ngIf="isMathCell(i,j)">
            <div 
              tabindex="-1"
              class="capture-math-container" 
              (focusin)="setFocusedCell(i,j)"
              (focusout)="resetFocusedCell()"
            >
              <capture-math #cellTextWithImg [obj]="getCellObject(i,j)" prop="text" [isManualKeyboard]="true" [class.is-disabled]="isReadOnly()" (onChange)="rerender()"></capture-math>
            </div>
          </ng-container>
          <ng-container *ngIf="!isMathCell(i,j)">
            <textarea
              #cellTextWithImg
              textInputTransform
              [formControl]="cell"
              style="min-width: 10em;"
              class="textarea is-small"
              [class.target-cell]="isTargetCell(i,j)"
              cdkTextareaAutosize
              [cdkTextareaAutosize]="true"
              [cdkAutosizeMinRows]="2"
              (focus)="setFocusedCell(i, j, cell, 'imgCell_' + i + '_' + j)"
              (blur)="resetFocusedCell()"
            ></textarea>
          </ng-container>
          <div *ngIf="isTargetCell(i,j)" class="cell-target-info">
            (target)
          </div>
          
          <div *ngIf="!isImageCell(i,j) && !isTargetCell(i,j)">
            <button (click)="elementEditInitImage(i,j)">Add Image</button>
          </div>
          <div *ngIf="isImageCell(i,j)">
            <button (click)="elementEditRemoveImage(i,j)">Remove Image</button>
            <div *ngIf="!element.isShowAdvancedOptions && getCellImageElement(i,j).url">
              <div class="simple-image-container">
                <img [src]="getCellImageElement(i,j).url" style="max-width: 10em; max-height: 10em;">
              </div>
              <div>
                scale:
                <input type="number" [(ngModel)]="getCellImageElement(i,j).scale" class="small-input" (change)="rerender()">
              </div>
            </div>
            <div *ngIf="element.isShowAdvancedOptions || !getCellImageElement(i,j).url">
              <capture-image [element]="getCellImageElement(i,j)" (change)="rerender()"></capture-image>
              <asset-library-link [element]="getCellImageElement(i,j)"></asset-library-link>     
            </div>
            
          </div>
          
        </ng-template>
        
      </td>
    </tr>
  </table>
</div>

<fieldset [disabled]="isReadOnly()">
  <div *ngIf="!isReordering">
    <button (click)="buttonAddRow()" class="button is-small has-icon">
      <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
      <span>New Row</span>
    </button>
    <button (click)="buttonAddColumn()" class="button is-small has-icon">
      <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
      <span>New Column</span>
    </button>
  </div>
  <div *ngIf="!isReordering">
    <button 
      (click)="elementEditToggleTargetOnFocusedCell()" 
      (mouseenter)="setToggleButtonHoverState(true)"
      (mouseleave)="setToggleButtonHoverState(false)"
      class="button is-small has-icon"
      [disabled]="!isFocusedCellCanToggleTarget()"
    >
      <span class="icon"><i class="fa fa-toggle-on" aria-hidden="true"></i></span>
      <span>Toggle Text/Target</span>
    </button>
    <button 
      (click)="elementEditToggleMathOnFocusedCell()" 
      (mouseenter)="setToggleButtonHoverState(true)"
      (mouseleave)="setToggleButtonHoverState(false)"
      class="button is-small has-icon"
      [disabled]="!isFocusedCellCanToggleMath()"
    >
      <span class="icon"><i class="fas fa-square-root-alt" aria-hidden="true"></i></span>
      <span>Toggle Math</span>
    </button>
    
  </div>
  
  <div style="margin-top: 0.5em">
    <button  *ngIf="!isReordering"
      (click)="startReordering()"
      class="button is-small has-icon"
    >
      <span class="icon"><i class="fas fa-sort" aria-hidden="true"></i></span>
      <span>Reorder Row/Column</span>
    </button>
    
    <button  *ngIf="isReordering"
      (click)="endReordering()"
      class="button is-small has-icon"
    >
      <span class="icon"><i class="fas fa-sort" aria-hidden="true"></i></span>
      <span>Done Reordering</span>
    </button>
  </div>
</fieldset>


<hr/>

<fieldset (change)="rerender()">
  <label class="label">Label</label>
  <div style="margin-top: 0.5em">
    <label>Label: </label> 
    <textarea
      *ngIf="!element.label.isMath"
      textInputTransform
      #label
      [formControl]="labelForm"
      (input)="rerender()"
      style="min-width: 10em; max-width: 20em; margin-bottom: 0.5em; display: inline-block;"
      class="textarea is-small"
      cdkTextareaAutosize
      [cdkTextareaAutosize]="true"
      [cdkAutosizeMinRows]="2"
      (focus)="setTextFocus(labelForm, 'label')"
    ></textarea>
    <div *ngIf="element.label.isMath" class="capture-math-container" 
      style="width: 12em; display: inline-block; vertical-align: top;"
    >
      <capture-math 
        [obj]="element.label" 
        (onChange)="rerender()"
        [class.is-disabled]="isReadOnly()"
        [class.is-disabled]="isReadOnly()"
        prop="text" [isManualKeyboard]="true">
      </capture-math>
    </div>
    <div *ngIf="!element.label.isMath"
      (click)="element.label.isMath = true; rerender()" 
      class="target-option-button"
      [class.no-pointer-events]="isReadOnly()">
      <i class="fas fa-square-root-alt" aria-hidden="true"></i>
    </div>
    <div *ngIf="element.label.isMath"
      (click)="element.label.isMath = false; rerender()"
      class="target-option-button"
      [class.no-pointer-events]="isReadOnly()">
      <i class="fas fa-font" aria-hidden="true"></i>
    </div>
    <ng-container *ngIf="element.label.text">
      <br>Label Position:
      <select [(ngModel)]="element.label.position">
        <option *ngFor="let option of diagramLabelPositionOptions" [value]="option.id"><tra [slug]="option.caption"></tra></option>
      </select>
    </ng-container>
  </div>
</fieldset>

<hr />

<fieldset [disabled]="isReadOnly()" (change)="rerender()">
  <label class="checkbox">
    <input type="checkbox" [formControl]="isShowAnswerForm"/>
    Show Answer
  </label>
</fieldset>

<div class="twiddle-container" [class.error-bg-color]="isOptionsContainsError()">
  <twiddle caption="Target Options" [state]="twiddleTargets" ></twiddle>
</div >
<div *ngIf="twiddleTargets.value">
  <div style="margin: 1em 0">
    <ng-container *ngIf="!element.config.isUsingReusableDraggable && !element._isAlwaysSeparateAnswerSetup">
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="element.config.isAllowMultipleAnswer" (change)="rerender()"/>
      Accept Multiple Answers
    </label><br>
    </ng-container>
    <label class="checkbox">
      <input type="checkbox" [formControl]="isUsingReusableDraggableForm"/>
      Reusable Draggables
    </label><br>
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="element.config.isUsingAnswerGroup"/>
      Answer Group
    </label>
    <div *ngIf="element.config.isUsingAnswerGroup" class="sub-property-div" style="margin-top: 0; color: #888">
      <span class="icon"><i class="fas fa-info-circle" aria-hidden="true"></i></span>
      Answer Group is based on Home Layout
    </div>
  </div>
  <dg-int-dnd-target-options
    [dndTargets]="element.dndTargets"
    [dndTargetOptionsForm]="dndTargetOptionsForm"
    [nonUniqueOptionTexts]="nonUniqueOptionTexts"
    [isUsingReusableDraggable]="element.config.isUsingReusableDraggable"
    [isAlwaysSeparateAnswerSetup]="element._isAlwaysSeparateAnswerSetup"
    [isAllowGrouping]="element.config.isAllowGrouping"
    [isUseImage]="element.config.isUseImage"
    [isShowAdvancedOptions]="element.isShowAdvancedOptions"
    [isAllowNonScoringTarget]="element.config.isAllowNonScoringTarget"
    [isUsingAnswerGroup]="element.config.isUsingAnswerGroup"
    [answerGroupCount]="element.homeConfig?.element?.length"
    (onSetIsAllowEmptyTarget)="element.config.isAllowEmptyTarget = $event"
    (onChange)="validateConfig()"
  ></dg-int-dnd-target-options>
  
  <div style="margin-top: 1em; background-color: #eee; padding: 1em; border-radius: 0.5em;">
    <span>The item is considered to be answered if:</span>
    <div class="select">
      <select [(ngModel)]="element.dndIsFilledMode" (change)="validateConfig()">
        <option *ngFor="let option of dndIsFilledModeOptions" [value]="option.id">
          <span *ngIf="option.id == 'auto'">Auto ({{dndIsFilledCaptionMap[element._autoDnDIsFilledMode]}})</span>
          <span *ngIf="option.id != 'auto'">{{option.caption}}</span>
        </option>
      </select>
    </div>
  </div>
  
</div>

<div class="twiddle-container" [class.error-bg-color]="isAnswersContainsError()">
  <twiddle 
    *ngIf="element.config.isAllowMultipleAnswer || this.element.config.isUsingReusableDraggable || this.element._isAlwaysSeparateAnswerSetup"
    caption="Answers" [state]="twiddleAnswers"></twiddle >
</div>
<div *ngIf="twiddleAnswers.value 
  && (element.config.isAllowMultipleAnswer || this.element.config.isUsingReusableDraggable || this.element._isAlwaysSeparateAnswerSetup)
  && element.altAnswers">
    
  <div style="margin: 1em 0">
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="element.config.isAllowMultipleAnswer" (change)="rerender()"/>
      Accept Multiple Answers
    </label>
    <br><label class="checkbox">
      <input type="checkbox" [(ngModel)]="element.config.isAllowEmptyTarget" (change)="rerender()"/>
      Allow empty target
    </label>
  </div>
  <dg-int-dnd-answers
    [defaultAnswerSet]="defaultAnswerSet"
    [altAnswers]="element.altAnswers"
    [isShowDefaultAnswerSet]="!this.element.config.isUsingReusableDraggable && !this.element._isAlwaysSeparateAnswerSetup"
    [isAllowMultipleAnswer]="element.config.isAllowMultipleAnswer"
    [isAllowEmptyTarget]="element.config.isAllowEmptyTarget"
    [idToLabelMap]="idToLabelMap"
    [idToUrlMap]="idToUrlMap"
    [idIsMathSet]="idIsMathSet"
    [targetIdList]="targetIdList"
    [optionIdList]="sortedOptionIdList"
    [isAllowNonScoringTarget]="element.config.isAllowNonScoringTarget"
    [targets]="element.dndTargets"
    (onChange)="validateConfig()"
  ></dg-int-dnd-answers>
</div>

<twiddle caption="Home Layout" [state]="twiddleHome"></twiddle>
<div *ngIf="twiddleHome.value">
  <div *ngIf="isPossibleToAlignHomeLabelToFirstRow" style="margin-bottom: 1em;">
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="element.config.alignHomeLabelToFirstRow" (change)="validateConfig()"/>
      Align Home Label to First Row
    </label>
  </div>
  <dg-int-dnd-home-layout
    [element]="element.homeConfig"
    [parentElement]="element"
    [idToLabelMap]="idToLabelMap"
    [idToUrlMap]="idToUrlMap"
    [idIsMathSet]="idIsMathSet"
    (onChange)="validateConfig()"
    (onResetState)="requestResetState()"
  ></dg-int-dnd-home-layout>
</div>

<twiddle *ngIf="!hiddenConfigs || !hiddenConfigs.hideStyle" caption="Style" [state]="twiddleStyle"></twiddle>
<div *ngIf="twiddleStyle.value">
  <fieldset style="margin-bottom: 0.5em" (change)="rerender()">
    <span>Option Width:</span>
    <input type="number" [(ngModel)]="element.config.optionFixedWidth" class="small-input">
    <br>
    <span>Content Justify: </span>
    <select [(ngModel)]="element.config.contentJustify" (change)="validateConfig()">
      <option *ngFor="let option of contentJustifyOptions" [value]="option.id">{{option.caption}}</option>
    </select>
  </fieldset>
  <dg-int-dnd-color-style
    [styleConfig]="element.styleConfig"
    [constructionElement]="element"
    [dgIntElement]="parentElement"
    [idToLabelMap]="idToLabelMap"
    [targetIdList]="targetIdList"
    [homeIdList]="homeIdList"
    [sortedOptionIdList]="sortedOptionIdList"
    [homeConfig]="element.homeConfig"
    [isSimplified]="hiddenConfigs.simpleStyleMode"
    (onChange)="validateConfig()"
  ></dg-int-dnd-color-style>
</div>

<twiddle *ngIf="!hiddenConfigs || !hiddenConfigs.hideConfig" caption="Config" [state]="twiddleConfig"></twiddle>
<div *ngIf="twiddleConfig.value">
  <fieldset [disabled]="isReadOnly()" (change)="validateConfig()">
    <div class="sub-property-div control">
      <br> Scoring Weight:
      <input type="number" [(ngModel)]="parentElement.scoreWeight" class="small-input">
      <br *ngIf="!element.config.isAllowMultipleAnswer">
      <label class="checkbox" *ngIf="!element.config.isAllowMultipleAnswer">
        <input type="checkbox" [(ngModel)]="parentElement.enableProportionalScoring"/>
        Enable Proportional Scoring
      </label>
      <br>
      <br> Maximum Home Width:
      <input type="number" [(ngModel)]="element.config.homeMaxWidth" class="small-input">
      <br> Maximum Option Width:
      <input type="number" [(ngModel)]="element.config.optionMaxWidth" class="small-input">
      <br> Padding-x:
      <input type="number" [(ngModel)]="element.config.padding[1]" class="small-input">
      <br> Padding-y:
      <input type="number" [(ngModel)]="element.config.padding[0]" class="small-input">
      <br>
      <br>
      <label>Diagram Padding</label>
      <br> top:
      <input type="number" [(ngModel)]="element.diagramPadding.top" class="small-input">
      <br> bottom:
      <input type="number" [(ngModel)]="element.diagramPadding.bottom" class="small-input">
      <br> left:
      <input type="number" [(ngModel)]="element.diagramPadding.left" class="small-input">
      <br> right:
      <input type="number" [(ngModel)]="element.diagramPadding.right" class="small-input">
      <br>
      <br>
      <label class="checkbox">
        <input type="checkbox" [(ngModel)]="element.config.isAllowNonScoringTarget" (change)="validateConfig()"/>
        Allow Non Scoring Target
      </label>
      <br>
      <label class="checkbox">
        <input type="checkbox" [(ngModel)]="element._isAlwaysSeparateAnswerSetup" (change)="validateConfig()"/>
        Always Separate Answer Setup
      </label>
    </div>
  </fieldset>
</div>

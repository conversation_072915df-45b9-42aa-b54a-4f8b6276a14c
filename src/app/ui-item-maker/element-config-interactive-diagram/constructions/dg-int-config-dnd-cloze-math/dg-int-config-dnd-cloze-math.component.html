<fieldset [disabled]="isReadOnly()">
  <div class="capture-math-container"
    (focusin)="onFocus()"
    (focusout)="onBlur()"
  >
    <capture-math #captureMath 
      [obj]="element" 
      (onChange)="validateConfig()"
      [class.is-disabled]="isReadOnly()"
      prop="content" [isManualKeyboard]="true">
    </capture-math>
  </div>
  
  <br>
  <div>
    <button 
      (click)="insertNewTarget()" 
      (mouseenter)="setNewTargetButtonHoverState(true)"
      (mouseleave)="setNewTargetButtonHoverState(false)"
      class="button is-small has-icon"
      [disabled]="!isFocus"
    >
      <span class="icon"><i class="fa fa-plus" aria-hidden="true"></i></span>
      <span>Add New Target</span>
    </button>
  </div>
</fieldset>

<!-- <div class="note" style="margin-top: 1em;">
  *use <span class="code">\boxed&#123;target_name&#125;</span> or <span class="code">\boxed&#123;\text&#123;target_name&#125;&#125;</span> to create a target
</div> -->

<hr/>

<fieldset (change)="rerender()">
  <label class="label">Label</label>
  <div style="margin-top: 0.5em">
    <label>Label: </label> 
    <textarea
      *ngIf="!element.label.isMath"
      textInputTransform
      #label
      [formControl]="labelForm"
      (input)="rerender()"
      style="min-width: 10em; max-width: 20em; margin-bottom: 0.5em; display: inline-block;"
      class="textarea is-small"
      cdkTextareaAutosize
      [cdkTextareaAutosize]="true"
      [cdkAutosizeMinRows]="2"
      (focus)="setTextFocus(labelForm, 'label')"
    ></textarea>
    <div *ngIf="element.label.isMath" class="capture-math-container" 
      style="width: 11em; display: inline-block; vertical-align: top;"
    >
      <capture-math 
        [obj]="element.label" 
        (onChange)="rerender()"
        [class.is-disabled]="isReadOnly()"
        [class.is-disabled]="isReadOnly()"
        prop="text" [isManualKeyboard]="true">
      </capture-math>
    </div>
    <div *ngIf="!element.label.isMath"
      (click)="element.label.isMath = true; rerender()" 
      class="target-option-button"
      [class.no-pointer-events]="isReadOnly()">
      <i class="fas fa-square-root-alt" aria-hidden="true"></i>
    </div>
    <div *ngIf="element.label.isMath"
      (click)="element.label.isMath = false; rerender()"
      class="target-option-button"
      [class.no-pointer-events]="isReadOnly()">
      <i class="fas fa-font" aria-hidden="true"></i>
    </div>
    <ng-container *ngIf="element.label.text">
      <br>Label Position:
      <select [(ngModel)]="element.label.position">
        <option *ngFor="let option of diagramLabelPositionOptions" [value]="option.id"><tra [slug]="option.caption"></tra></option>
      </select>
    </ng-container>
  </div>
</fieldset>

<hr />

<fieldset [disabled]="isReadOnly()">
  <label class="checkbox">
    <input type="checkbox" [formControl]="isShowAnswerForm" (change)="rerender()"/>
    Show Answer
  </label>
</fieldset>


<div class="twiddle-container" [class.error-bg-color]="isOptionsContainsError()">
  <twiddle caption="Target Options" [state]="twiddleTargets" ></twiddle>
</div >
<div *ngIf="twiddleTargets.value">
  <div style="margin: 1em 0">
    <ng-container *ngIf="!element.config.isUsingReusableDraggable && !element._isAlwaysSeparateAnswerSetup">
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="element.config.isAllowMultipleAnswer" (change)="validateConfig()"/>
      Accept Multiple Answers
    </label><br>
    </ng-container>
    <label class="checkbox">
      <input type="checkbox" [formControl]="isUsingReusableDraggableForm" (change)="validateConfig()"/>
      Reusable Draggables
    </label><br>
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="element.config.isUsingAnswerGroup"/>
      Answer Group
    </label>
    <div *ngIf="element.config.isUsingAnswerGroup" class="sub-property-div" style="margin-top: 0; color: #888">
      <span class="icon"><i class="fas fa-info-circle" aria-hidden="true"></i></span>
      Answer Group is based on Home Layout
    </div>
  </div>
  <dg-int-dnd-target-options
    [dndTargets]="element.dndTargets"
    [dndTargetOptionsForm]="dndTargetOptionsForm"
    [nonUniqueOptionTexts]="nonUniqueOptionTexts"
    [isUsingReusableDraggable]="element.config.isUsingReusableDraggable"
    [isAlwaysSeparateAnswerSetup]="element._isAlwaysSeparateAnswerSetup"
    [isAllowGrouping]="element.config.isAllowGrouping"
    [isUseImage]="element.config.isUseImage"
    [isShowAdvancedOptions]="element.isShowAdvancedOptions"
    [isAllowNonScoringTarget]="element.config.isAllowNonScoringTarget"
    [isUsingAnswerGroup]="element.config.isUsingAnswerGroup"
    [answerGroupCount]="element.homeConfig?.element?.length"
    [isInputAlwaysMath]="true"
    (onSetIsAllowEmptyTarget)="element.config.isAllowEmptyTarget = $event"
    (onChange)="validateConfig()"
  ></dg-int-dnd-target-options>
  
  <div style="margin-top: 1em; background-color: #eee; padding: 1em; border-radius: 0.5em;">
    <span>The item is considered to be answered if:</span>
    <div class="select">
      <select [(ngModel)]="element.dndIsFilledMode" (change)="validateConfig()">
        <option *ngFor="let option of dndIsFilledModeOptions" [value]="option.id">
          <span *ngIf="option.id == 'auto'">Auto ({{dndIsFilledCaptionMap[element._autoDnDIsFilledMode]}})</span>
          <span *ngIf="option.id != 'auto'">{{option.caption}}</span>
        </option>
      </select>
    </div>
  </div>
  
</div>

<div class="twiddle-container" [class.error-bg-color]="isAnswersContainsError()">
  <twiddle 
    *ngIf="element.config.isAllowMultipleAnswer || this.element.config.isUsingReusableDraggable || this.element._isAlwaysSeparateAnswerSetup"
    caption="Answers" [state]="twiddleAnswers"></twiddle >
</div>
<div *ngIf="twiddleAnswers.value 
  && (element.config.isAllowMultipleAnswer || this.element.config.isUsingReusableDraggable || this.element._isAlwaysSeparateAnswerSetup)
  && element.altAnswers">
    
  <div style="margin: 1em 0">
    <label class="checkbox">
      <input type="checkbox" [(ngModel)]="element.config.isAllowMultipleAnswer" (change)="validateConfig()"/>
      Accept Multiple Answers
    </label>
    <br><label class="checkbox">
      <input type="checkbox" [(ngModel)]="element.config.isAllowEmptyTarget" (change)="rerender()"/>
      Allow empty target
    </label>
  </div>
  <dg-int-dnd-answers
    [defaultAnswerSet]="defaultAnswerSet"
    [altAnswers]="element.altAnswers"
    [isShowDefaultAnswerSet]="!this.element.config.isUsingReusableDraggable && !this.element._isAlwaysSeparateAnswerSetup"
    [isAllowMultipleAnswer]="element.config.isAllowMultipleAnswer"
    [isAllowEmptyTarget]="element.config.isAllowEmptyTarget"
    [idToLabelMap]="idToLabelMap"
    [idToUrlMap]="idToUrlMap"
    [idIsMathSet]="idIsMathSet"
    [targetIdList]="targetIdList"
    [optionIdList]="sortedOptionIdList"
    [isAllowNonScoringTarget]="element.config.isAllowNonScoringTarget"
    [targets]="element.dndTargets"
    (onChange)="validateConfig()"
  ></dg-int-dnd-answers>
</div>

<twiddle caption="Home Layout" [state]="twiddleHome"></twiddle>
<div *ngIf="twiddleHome.value">
  <dg-int-dnd-home-layout
    [element]="element.homeConfig"
    [parentElement]="element"
    [idToLabelMap]="idToLabelMap"
    [idToUrlMap]="idToUrlMap"
    [idIsMathSet]="idIsMathSet"
    (onChange)="validateConfig()"
    (onResetState)="requestResetState()"
  ></dg-int-dnd-home-layout>
</div>

<twiddle *ngIf="!hiddenConfigs || !hiddenConfigs.hideStyle" caption="Style" [state]="twiddleStyle"></twiddle>
<div *ngIf="twiddleStyle.value">
  <div style="margin-bottom: 0.5em;">
    <span>Content Justify: </span>
    <select [(ngModel)]="element.config.contentJustify" (chage)="rerender()">
      <option *ngFor="let option of contentJustifyOptions" [value]="option.id">{{option.caption}}</option>
    </select>
  </div>
  <dg-int-dnd-color-style
    [styleConfig]="element.styleConfig"
    [constructionElement]="element"
    [dgIntElement]="parentElement"
    [idToLabelMap]="idToLabelMap"
    [targetIdList]="targetIdList"
    [homeIdList]="homeIdList"
    [sortedOptionIdList]="sortedOptionIdList"
    [homeConfig]="element.homeConfig"
    [isSimplified]="hiddenConfigs.simpleStyleMode"
    (onChange)="validateConfig()"
  ></dg-int-dnd-color-style>
</div>

<twiddle *ngIf="!hiddenConfigs || !hiddenConfigs.hideConfig" caption="Config" [state]="twiddleConfig"></twiddle>
<div *ngIf="twiddleConfig.value">
  <fieldset [disabled]="isReadOnly()" (change)="validateConfig()">
    <div class="sub-property-div control">
      <br> Scoring Weight:
      <input type="number" [(ngModel)]="parentElement.scoreWeight" class="small-input">
      <br *ngIf="!element.config.isAllowMultipleAnswer">
      <label class="checkbox" *ngIf="!element.config.isAllowMultipleAnswer">
        <input type="checkbox" [(ngModel)]="parentElement.enableProportionalScoring"/>
        Enable Proportional Scoring
      </label>
      <br>
      <br> Maximum Home Width:
      <input type="number" [(ngModel)]="element.config.homeMaxWidth" class="small-input">
      <br> Padding-x:
      <input type="number" [(ngModel)]="element.config.padding[1]" class="small-input">
      <br> Padding-y:
      <input type="number" [(ngModel)]="element.config.padding[0]" class="small-input">
      <br>
      <br>
      <label>Diagram Padding</label>
      <br> top:
      <input type="number" [(ngModel)]="element.diagramPadding.top" class="small-input">
      <br> bottom:
      <input type="number" [(ngModel)]="element.diagramPadding.bottom" class="small-input">
      <br> left:
      <input type="number" [(ngModel)]="element.diagramPadding.left" class="small-input">
      <br> right:
      <input type="number" [(ngModel)]="element.diagramPadding.right" class="small-input">
      <br>
      <br>
      <label class="checkbox">
        <input type="checkbox" [(ngModel)]="element.config.isAllowNonScoringTarget" (change)="validateConfig()"/>
        Allow Non Scoring Target
      </label>
      <br>
      <label class="checkbox">
        <input type="checkbox" [(ngModel)]="element._isAlwaysSeparateAnswerSetup" (change)="validateConfig()"/>
        Always Separate Answer Setup
      </label>
    </div>
  </fieldset>
</div>

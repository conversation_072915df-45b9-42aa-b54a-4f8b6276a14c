import * as _ from 'lodash';
import * as moment from 'moment-timezone';

import { Component, OnInit, OnDestroy, ViewEncapsulation, ViewChild, ElementRef } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { FormControl } from '@angular/forms';
import { ScrollService } from '../../core/scroll.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { LangService } from '../../core/lang.service';
import { Observable, Subscription } from 'rxjs';
import { RoutesService } from '../../api/routes.service';
import {AuthService, getFrontendDomain} from '../../api/auth.service';
import { ItemMakerService, IItemSetDef } from '../item-maker.service';
import { IAuthoringGroup } from '../item-maker.service';
import { UserRoles } from '../../api/models/roles';
import { IMenuTabConfig } from '../../ui-partial/menu-bar/menu-bar.component';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { PageModalController, PageModalService } from 'src/app/ui-partial/page-modal.service';
import { AuthRolesService } from '../auth-roles.service';
import { IItemTag } from '../item-tag/item-tag.component';
import { map, startWith } from 'rxjs/operators';
import { SidepanelService } from 'src/app/core/sidepanel.service';
import { testAuthPanels } from 'src/app/core/main-nav/panels/test-auth';

enum ViewTypes {
  ACTIVE = 'ACTIVE',
  ARCHIVED = 'ARCHIVED'
}

const dateSort = (d1, d2) => {
  const m_d1 = moment(d1);
  const m_d2 = moment(d2);
  if (m_d1.isBefore(m_d2)) {
    return 1;
  } 
  else {
    return -1;
  }
}

const ASSIGNMENT_SORT_UID = (a,b, uid) => {
  if (+a.assigned_uid === +uid) {
    if (+a.assigned_uid === +b.assigned_uid) {
      return dateSort(a.created_on, b.created_on);
    }
    return -1;
  } 
  else {
    if (+a.assigned_uid === +b.assigned_uid) {
      return dateSort(a.created_on, b.created_on);
    }
    return 1;
  }
}

export enum AuthoringModal {
  MANAGE_GROUP      = 'manage-group',
  MANAGE_ITEM_BANK     = 'manage-item-bank',
  MANAGE_GROUP_TAGS = 'manage-group-tags'
}
export enum AuthView {
  DESIGNS = 'designs',
  SETS    = 'sets',
  GROUPS  = 'groups',
}


@Component({
  selector: 'view-im-dashboard',
  templateUrl: './view-im-dashboard.component.html',
  styleUrls: ['./view-im-dashboard.component.scss'],
  encapsulation: ViewEncapsulation.Emulated
})
export class ViewImDashboardComponent implements OnInit, OnDestroy {

  @ViewChild('tagInput') tagInput: ElementRef;
  constructor(
    public lang: LangService,
    private loginGuard: LoginGuardService,
    private breadcrumbsService: BreadcrumbsService,
    private sidePanel: SidepanelService,
    private router: Router,
    private routes: RoutesService,
    private route: ActivatedRoute,
    private auth: AuthService,
    public myItems: ItemMakerService,
    private scrollService: ScrollService,
    private whitelabel: WhitelabelService,
    private pageModalService: PageModalService,
    private authRoles: AuthRolesService
    ) { }
    
  AuthoringModal = AuthoringModal;
  
  public breadcrumb = [];
  public questionSets:IItemSetDef[] = [];
  public frameworks:IItemSetDef[] = [];
  public groups:IAuthoringGroup[] = [];
  public isCreatingNewQuestionSet:boolean;
  public isCreatingNewTestAuthor:boolean;
  public isCreatingNewTestDesign:boolean;
  public authRoleTypes;

  public contentSearchQuery = new FormControl();
  private routeSub:Subscription;
  private isUserInited:boolean;
  
  isShowDetails: boolean = false;
  isShowDetailsTable: boolean = false;

  public pageModal: PageModalController;
  
  filteredTags: Observable<IItemTag[]>;
  searchTags: IItemTag[] = [];
  compositeAsmtTemplateQsId: number;
  viewArchived: boolean = false;//to set if currently viewing archived items or not
  viewArchivedLoading: boolean;//to lock toggle button to avoid errors
  
  public searchItems: any[] = [];
  public isItemSearchById:boolean;
  public isTestDesignSearchById: boolean;

  isCreatingNewAuthGroup: boolean;
  openAuthGroupOptions: IAuthoringGroup = null;
  isItemSetCreateSaving: boolean;
  isAuthGroupCreateSaving: boolean;
  itemSetCreateError:string;
  testAuthorCreateError:string;
  itemSetCreateForm = {
    slug: new FormControl(),
    name: new FormControl(),
    description: new FormControl(),
    group_id: new FormControl(),
  };
  isTestDesignCreateSaving:boolean;
  testDesignCreateError:string;
  authGroupCreateError:string;
  testDesignCreateForm = {
    slug: new FormControl(),
    name: new FormControl(),
    description: new FormControl(),
    group_id: new FormControl(),
  }
  groupCreateForm = {
    description: new FormControl()
  };
  issues;
  itemBankDescrFilter: string;
  itemBankGroupFilter: string;


  assessmentViews: IMenuTabConfig<ViewTypes>[] = [
    {id: ViewTypes.ACTIVE, caption: 'Active' },
    {id: ViewTypes.ARCHIVED, caption: 'Archived' },
  ];
  outstandingIssues: IMenuTabConfig<ViewTypes>[] = [
    {id: ViewTypes.ACTIVE, caption: 'Active' },
    {id: ViewTypes.ARCHIVED, caption: 'Resolved' },
  ];

  selectedAssessmentTab = ViewTypes.ACTIVE
  selectAssessmentTab(id:ViewTypes){
    this.selectedAssessmentTab = id;
  }

  showActions: {[key: string]: boolean} = {};

  private routeDataSubscription: Subscription;
  view: string;


  ngOnInit() {
    this.authRoleTypes = this.authRoles.getAuthRoles();
    this.loginGuard.activate();
    this.scrollService.scrollToTop();
    this.sidePanel.activate(testAuthPanels)
    this.routeSub = this.route.params.subscribe(routeParams => {
      this.initRouteView();
    });
    this.initBreadcrumb();
    this.routeDataSubscription = this.route.data.subscribe((data: any) => {
      this.view = data.view;
      this.initBreadcrumb();
    });
    this.pageModal = this.pageModalService.defineNewPageModal();
  }

  _filterTag(value:string): IItemTag[] {
    const filterValue = value.toLowerCase();
    return this.myItems.availableTags.filter(option => !this.searchTags.filter(t=>t.id === option.id).length && option.caption.toLowerCase().includes(filterValue));
  }

  isNotGroupView(){
    return this.view !== AuthView.GROUPS
  }

  ngOnDestroy() {
    if (this.routeSub) {
      this.routeSub.unsubscribe();
    }
    this.routeDataSubscription.unsubscribe();
    clearInterval(this.issueReloadInterval);
  }

  initBreadcrumb(){
    this.breadcrumb = [
      this.breadcrumbsService.TESTAUTH_DASHBOARD(),
    ];
    if(this.view){
      this.breadcrumb.push( this.breadcrumbsService._CURRENT(''+this.view, this.route.toString()) )
    }
  }

  initRouteView(){
    
    this.loadData();
  }

  initTables(){
    
  }

  loadData(){
    return this.auth
    .user()
    .subscribe(userInfo => {
      if (userInfo && !this.isUserInited){
        this.isUserInited = true;
        this.myItems
          .loadMyAuthoringGroups()
          .then(() => {
            this.refreshGroupRoles();
            this.myItems.loadAvailableTags();
            this.initIssues();
            this.initTags();
            return Promise.all([
              this.myItems.loadMyItemSets().then((itemSets: IItemSetDef[]) => this.questionSetsFull = this.questionSets = itemSets),
              this.myItems.loadMyFrameworks().then((frameworks: IItemSetDef[]) => this.frameworksFull = this.frameworks = frameworks),
            ])
            .then(() => {
              this.applyItemSetFilters()
            })
          })
          // .then(() => {
          //   //This placement ensures we have the user info as required before loading the issues 
          //   this.loadIssues();
          // });
      }
    })
  }

  groupFilterSet:Set<number> = new Set()
  toggleGroupFilter(groupId:number){
    console.log('toggleGroupFilter', groupId)
    if (this.groupFilterSet.has(groupId)){
      this.groupFilterSet.delete(groupId)
    }
    else {
      this.groupFilterSet.add(groupId)
    }
    this.applyItemSetFilters()
  }
  isGroupToggled(groupId:number){
    return this.groupFilterSet.has(groupId)
  }

  initTags() {
    this.filteredTags = this.contentSearchQuery.valueChanges.pipe(
      startWith(''),
      map(value => this._filterTag(value))
    )
  }
  questionSetsFull:IItemSetDef[] = [];
  frameworksFull:IItemSetDef[] = [];
  applyItemSetFilters(){
    const descrFilter = (this.itemBankDescrFilter || '').toLowerCase();
    const groupFilter = [... this.groupFilterSet]
    // console.log('applyItemSetFilters')
    const filterItemSet = (itemSet) => {
      let slugToggle, nameToggle, descToggle, groupToggle;
      slugToggle = true;
      descToggle = true;
      nameToggle = true;
      groupToggle = true;
      if (descrFilter){
        const slug = (itemSet.slug || '').toLowerCase()
        if (slug.indexOf(descrFilter) === -1){
          slugToggle = false;
        }
        const name = (itemSet.name || '').toLowerCase()
        if (name.indexOf(descrFilter) === -1){
          nameToggle = false;
        }
        const description = (itemSet.description || '').toLowerCase()
        if (description.indexOf(descrFilter) === -1){
          descToggle = false;
        }
      }
      if (groupFilter.length){
        groupToggle = groupFilter.includes(itemSet.group_id)
      }
      return ((slugToggle || nameToggle || descToggle) && groupToggle);
    }
    this.questionSets = this.questionSetsFull.filter(filterItemSet);
    this.frameworks = this.frameworksFull.filter(filterItemSet);
  }
  

  issueReloadInterval;
  /**
   * Initializes the issues list and sets up the interval to reload the issues list
   * @note Auto-reload is only enabled for BCED
   */
  initIssues(){
    if(!this.whitelabel.getSiteFlag('IS_BCED')) {
      return;
    }
    this.loadIssues();
    this.issueReloadInterval = setInterval(() => this.loadIssues(), 5000)
  }

  loadIssues() {
    const uid = this.auth.user().getValue().uid;
    const ASSIGNMENT_SORT = (a,b) => ASSIGNMENT_SORT_UID(a,b,uid);
    this.auth.apiFind('public/test-auth/issues', {})
      .then(issues => {
        issues.data.sort(ASSIGNMENT_SORT);
        this.issues = issues.data;
      })
      .catch(e => {
        console.error(e);
      });
  }

  getAuthoringGroups(){
    return this.myItems.getAuthoringGroups();;
  }

  getGroupNameById(groupId: number) {
    return this.myItems.getGroupNameById(groupId);
  }

  checkQuestionSetSuperRole(questionSet:IItemSetDef){
    const group = this.myItems.getGroupById(questionSet.group_id);
    let single_group; 
    
    if(questionSet.single_group_id) {
      single_group = this.myItems.getGroupById(questionSet.single_group_id);
    }
    return (group && group.isSuper) || (single_group && single_group.isSuper);
  }

  checkPersonalQuestionSet(questionSet:IItemSetDef){
    const group = this.myItems.getGroupById(questionSet.group_id);
    return (group && !!group.isPersonal);
  }

  hasGroupsAsSuper() {
    return this.myItems.hasGroupsAsSuper;
  }

  hasGroupsAsTemplateAuthor() {
    console.log('')
    return this.myItems.hasGroupsAsTemplateAuthor;
  }

  refreshGroupRoles(){
    this.myItems.refreshGroupRoles();
  }

  toggleNewQuestionSetCreator(){
    this.isCreatingNewQuestionSet = !this.isCreatingNewQuestionSet;
    if (this.isCreatingNewQuestionSet){
      this.refreshGroupRoles();
    }
  }

  toggleNewTestDesignCreator(){
    this.isCreatingNewTestDesign = !this.isCreatingNewTestDesign;
    if (this.isCreatingNewTestDesign){
      this.refreshGroupRoles();
    }
  }

  toggleNewAuthGroupCreator() {
    this.isCreatingNewAuthGroup = !this.isCreatingNewAuthGroup;
  }

  openItemBankAccessModal(questionSet: IItemSetDef) {
    const config = {
      itemSetId: questionSet.id,
      singleGroupId: questionSet.single_group_id || -1,
      authoringGroupId: questionSet.group_id,
      groupName: questionSet.name,
      questionSet
    }

    this.pageModal.newModal({
      type: AuthoringModal.MANAGE_ITEM_BANK,
      config,
      finish: this.manageItemBankModalFinish
    })
  }

  manageItemBankModalFinish = () => {
    this.pageModal.closeModal();
  }

  openAuthGroupTagsModal(group: IAuthoringGroup) {
    if(!group.isSuper) {
      return;
    }

    const config = {
      authGroupId: group.group_id,
      groupName: group.isPersonal ? this.lang.tra('test_auth_personal') : group.description
    }
    
    this.pageModal.newModal(
      {
        type: AuthoringModal.MANAGE_GROUP_TAGS,
        config,
        finish: this.manageGroupTagModalFinish
      }
    );
  }

  manageGroupTagModalFinish = () => {
    this.pageModal.closeModal();
  }

  openAuthGroupOptionsModal(group: IAuthoringGroup) {
    if (!group.isSuper || group.isPersonal) {
      return;
    }

    const config = {
      authoringGroupId: group.group_id,
      groupName: group.description
    };
    this.pageModal.newModal({
      type: AuthoringModal.MANAGE_GROUP,
      config,
      finish: this.manageGroupModalFinish
    });
  }
  
  manageGroupModalFinish = () => {
    this.pageModal.closeModal();
  }

  createNewItemBank(){
    this.itemSetCreateError = null;
    const payload:any = {};
    const errors = [];
    Object.keys(this.itemSetCreateForm).forEach(key => {
      const val = this.itemSetCreateForm[key].value;
      payload[key] = val;
      if (!val){
        switch(key){
          case 'slug': errors.push(this.lang.tra('test_auth_short_name')); break;
          case 'name': errors.push(this.lang.tra('test_auth_title')); break;
          case 'description': errors.push(this.lang.tra('test_auth_description')); break;
          case 'group_id': errors.push(this.lang.tra('test_auth_authoring_group')); break;
        }
      }
    });
    if (errors.length > 0){
      this.itemSetCreateError = `${this.lang.tra('required_fields_error')} (${errors.join(', ')})`;
    }
    else{
      this.isItemSetCreateSaving = true;
      this.myItems
        .createNewItemBank(payload)
        .then(res => {
          this.isItemSetCreateSaving = false;
          this.isCreatingNewQuestionSet = false;
        })
    }
  }

  isArchivingItemBank:boolean;
  archiveItemBank(itemBank:IItemSetDef) {
    if (confirm(`${this.lang.tra('item_set_archive_confirmation')} ${itemBank.name}?`)){
      this.isArchivingItemBank = true;
      this.myItems
        .archiveItemBank(itemBank.id)
        .then(res => {
          this.isArchivingItemBank = false;
          this.loginGuard.quickPopup(this.lang.tra('item_set_archive_success'));
        } )
        .catch(e => this.isArchivingItemBank = false )
    }
  }
  recoverItemBank(itemBank:IItemSetDef) {
    if (confirm(`${this.lang.tra('item_set_recovery_confirmation')} ${itemBank.name}?`)){
      this.isArchivingItemBank = true;
      this.myItems
        .recoverItemBank(itemBank.id)
        .then(res => {
          this.isArchivingItemBank = false;
          this.loginGuard.quickPopup(this.lang.tra('item_set_recovery_success'));
        } )
        .catch(e => this.isArchivingItemBank = false )
    }
  }
  archiveTestDesign(itemBank:IItemSetDef) {
    if (confirm(`${this.lang.tra('item_set_archive_confirmation')} ${itemBank.name}?`)){
      this.isArchivingItemBank = true;
      this.myItems
        .archiveTestDesign(itemBank.id)
        .then(res => {
          this.isArchivingItemBank = false;
          this.loginGuard.quickPopup(this.lang.tra('item_set_archive_success'));
        })
        .catch(e => this.isArchivingItemBank = false )
    }
  }

  createNewTestDesign(){
    this.testDesignCreateError = null;
    const payload:any = { };
    const errors = [];
    if (this.compositeAsmtTemplateQsId){
      payload['compositeAsmtTemplateQsId'] = this.compositeAsmtTemplateQsId
    }
    Object.keys(this.testDesignCreateForm).forEach(key => {
      const val = this.testDesignCreateForm[key].value;
      payload[key] = val;
      if (!val){
        switch(key){
          case 'slug': errors.push(this.lang.tra('test_auth_short_name')); break;
          case 'name': errors.push(this.lang.tra('test_auth_title')); break;
          case 'description': errors.push(this.lang.tra('test_auth_description')); break;
          case 'group_id': errors.push(this.lang.tra('test_auth_authoring_group')); break;
        }
      }
    });
    if (errors.length > 0){
      this.testDesignCreateError = `${this.lang.tra('required_fields_error')} (${errors.join(', ')})`;
    }
    else{
      this.isTestDesignCreateSaving = true;
      this.myItems
        .createNewTestDesign(payload)
        .then(res => {
          this.isTestDesignCreateSaving = false;
          this.isCreatingNewTestDesign = false;
        })
    }
  }

  createNewAuthGroup() {
    this.authGroupCreateError = null;
    const payload: any = { };
    const errors = [];
    Object.keys(this.groupCreateForm).forEach(key => {
      const val = this.groupCreateForm[key].value;
      payload[key] = val;
      if (!val) {
        switch (key) {
          case 'description': errors.push(this.lang.tra('ie_name')); break;
        }
      }
    });
    if (errors.length > 0) {
      this.authGroupCreateError = `${this.lang.tra('required_fields_error')} (${errors.join(', ')})`;
    } else {
      if (this.validateGroupName(this.groupCreateForm.description)){
        this.isAuthGroupCreateSaving = true;
        this.myItems
            .createNewAuthGroup(payload)
            .then(res => {
              this.isAuthGroupCreateSaving = false;
              this.isCreatingNewAuthGroup = false;
              this.resetGroupForm();
            });
      }

    }
  }
  
  resetGroupForm(){
    this.groupCreateForm.description.reset();
  }

  validateGroupName(name){
    let groups = this.getAuthoringGroups();
    for (let i = 0; i < groups.length; i++){
        if (groups[i].description === name.value){
          this.authGroupCreateError = 'Group name already exists!';
             return false;
        }
      }
    return true;
  }
  toggleViewArchive( getItemSets = true) {
    this.viewArchivedLoading = true;
    this.viewArchived = !this.viewArchived;
    //check to see if it is an item set or a framework we're looking for in the archived
    if(getItemSets){
      this.myItems.loadMyItemSets(this.viewArchived).then((itemSets: IItemSetDef[]) => {
        this.questionSetsFull = this.questionSets = itemSets;
        this.viewArchivedLoading = false;
      }).catch(err => this.viewArchivedLoading = false);
    } else {
      this.myItems.loadMyFrameworks(this.viewArchived).then((frameworks: IItemSetDef[]) => {
        this.frameworksFull = this.frameworks = frameworks
        this.viewArchivedLoading = false;
      }).catch(err => this.viewArchivedLoading = false);
    }

  }

  getFrameworkRoute(itemSetId:number){
    return `/${this.lang.c()}/test-auth/framework/1/${itemSetId}`;
  }

  getItemRoute(item) {
    return this.getQuestionSetRoute(item.question_set_id) + "/" + encodeURIComponent(item.question_label);
  }

  getQuestionSetRoute(itemSetId:number){
    return `/${this.lang.c()}/test-auth/item-set-editor/${itemSetId}`;
  }

  getCommentsRoute() {
    return `/${this.lang.c()}/test-auth/issues/`
  }

  getGroupAccessRoute(groupId:number){
    return `/${this.lang.c()}/test-auth/group/${groupId}/access`;
  }

  getAssetLibraries() {
    return [];
  }

  // Creating new authors
  toggleNewTestAuthorCreator() {
    this.isCreatingNewTestAuthor = !this.isCreatingNewTestAuthor;
  }

  getNotificationsRouterLink(questionSet) {
    return `/en/test-auth/notifications/${questionSet.id}`
  }

  cModal() { return this.pageModal.getCurrentModal(); }
  cmc() { return this.cModal().config; }

  toggleActionDropdown(id: number, event: any, isGroup: boolean = false) {
    const dropdownKey = isGroup ? `group${id}` : `${id}`;
    const isShowing = !!this.showActions[dropdownKey];
    this.hideAllDropdowns();
    if(!isShowing) {
      this.showActions[dropdownKey] = true;
    }
    event.stopPropagation();
  }

  isActionDropdownActive(id: number) {
    return !!this.showActions[id];
  }

  hideAllDropdowns() {
    for(const key of Object.keys(this.showActions)) {
      this.showActions[key] = false;
    }
  }

  async contentSearch() {
    const queryParams = {
      searchQuery: this.contentSearchQuery.value, 
      isItemSearchById: this.isItemSearchById, 
      isTestDesignSearchById: this.isTestDesignSearchById,
      tagIds: this.searchTags.map( t => t.id)
    }
    this.searchItems = await this.auth.apiFind(this.routes.TEST_AUTH_CONTENT_SEARCH, {query: queryParams})
  }

  isGroupDropdownActive(group_id: number) {
    return !!this.showActions[`group${group_id}`];
  }

  addTag(tag:IItemTag) {
    if(this.searchTags.filter(t => t.id === tag.id).length) {
      return;
    }

    this.searchTags.push(tag);
    this.contentSearchQuery.setValue("");//Refreshes the filtered list
    this.tagInput.nativeElement.blur();
    this.contentSearch();
  }

  deleteTag(tag:IItemTag) {
    const index = this.searchTags.findIndex( t => t.id === tag.id); 
    if(index === -1) {
      return;
    }
    this.searchTags.splice(index, 1);
    this.contentSearchQuery.setValue(this.contentSearchQuery.value);//Refreshes the filtered list
    this.contentSearch();
  }
}


import { DragDropModule } from '@angular/cdk/drag-drop';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { TextFieldModule } from '@angular/cdk/text-field';
import { CdkTreeModule } from '@angular/cdk/tree';
import { CommonModule } from '@angular/common';
import { HttpClientModule } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { NgxJsonViewerModule } from 'ngx-json-viewer';
import { MarkdownModule } from 'ngx-markdown';
import { IoAudioModule } from '../io-audio/io-audio.module';
import { UiAccountMngrModule } from '../ui-account-mngr/ui-account-mngr.module';
import { UiLoginModule } from '../ui-login/ui-login.module';
import { UiPartialModule } from '../ui-partial/ui-partial.module';
import { FileSizePipe } from './file-size.pipe';
import { DropZoneDirective } from './drop-zone.directive';
import { ItemSetEditorComponent } from './item-set-editor/item-set-editor.component';
import { ElementConfigTextComponent } from './element-config-text/element-config-text.component';
import { ElementConfigMathComponent } from './element-config-math/element-config-math.component';
import { ElementConfigImageComponent } from './element-config-image/element-config-image.component';
import { ElementConfigVideoComponent } from './element-config-video/element-config-video.component';
import { ElementConfigMcqComponent } from './element-config-mcq/element-config-mcq.component';
import { ElementConfigInputComponent } from './element-config-input/element-config-input.component';
import { ElementConfigGridDNDComponent } from './element-config-grid-dnd/element-config-grid-dnd.component'
import { ElementConfigPieChart } from './element-config-pie-chart/element-config-pie-chart.component'
import { ElementConfigMcqOptionTextComponent } from './element-config-mcq-option-text/element-config-mcq-option-text.component';
import { ElementConfigMcqOptionMathComponent } from './element-config-mcq-option-math/element-config-mcq-option-math.component';
import { ElementConfigMcqOptionImageComponent } from './element-config-mcq-option-image/element-config-mcq-option-image.component';
import { ElementConfigTextInfoComponent } from './element-config-text-info/element-config-text-info.component';
import { UiTestrunnerModule } from '../ui-testrunner/ui-testrunner.module';
import { WidgetMathModule } from '../widget-math/widget-math.module';
import { AssetLibraryLinkComponent } from './asset-library-link/asset-library-link.component';
import { AssetLibraryComponent } from './asset-library/asset-library.component';
import { CaptureDndStyleComponent } from './capture-dnd-style/capture-dnd-style.component';
import { CaptureImageComponent } from './capture-image/capture-image.component';
import { CaptureUploadFilesComponent } from './capture-upload-files/capture-upload-files.component';
import { CheckboxComponent } from './checkbox/checkbox.component';
import { DelimiterComponent } from './delimiter/delimiter.component';
import { DropdownSearchComponent } from './dropdown-search/dropdown-search.component';
import { DropdownSelectComponent } from './dropdown-select/dropdown-select.component';
import { DropdownSelectButtonComponent } from './dropdown-select-button/dropdown-select-button.component';
import { KatexDisplayComponent } from './katex-display/katex-display.component'
import { SpecialCharacterKeyboardComponent } from './special-character-keyboard/special-character-keyboard.component';
import { ColorPanel } from './color-panel/color-panel.component';
import { CommentDisplayComponent } from './comment-display/comment-display.component';
import { ElementConfigLocAndDims } from './config-loc-and-dims/config-loc-and-dims.component';
import { ConfigMcq } from './config-mcq/config-mcq.component';
import { ElementConfigAnnotation } from './element-config-annotation/element-config-annotation.component';
import { ElementConfigAudioComponent } from './element-config-audio/element-config-audio.component';
import { ElementConfigCameraComponent } from './element-config-camera/element-config-camera.component';
import { ElementConfigCanvasComponent } from './element-config-canvas/element-config-canvas.component';
import { ElementConfigCustomMCQComponent } from './element-config-custom-mcq/element-config-custom-mcq.component';
import { ElementConfigDivComponent } from './element-config-div/element-config-div.component';
import { ElementConfigDndComponent } from './element-config-dnd/element-config-dnd.component';
import { ElementConfigDocLinkComponent } from './element-config-doc-link/element-config-doc-link.component';
import { ElementConfigFrameComponent } from './element-config-frame/element-config-frame.component';
import { ElementConfigGraphicsComponent } from './element-config-graphics/element-config-graphics.component';
import { ElementConfigGraphingComponent } from './element-config-graphing/element-config-graphing.component';
import { ElementConfigGroupingComponent } from './element-config-grouping/element-config-grouping.component';
import { ElementConfigHotspotComponent } from './element-config-hotspot/element-config-hotspot.component';
import { ElementConfigHottextComponent } from './element-config-hottext/element-config-hottext.component';
import { ElementConfigIframeComponent } from './element-config-iframe/element-config-iframe.component';
import { ElementConfigInsertionComponent } from './element-config-insertion/element-config-insertion.component';
import { ElementConfigMatchingComponent } from './element-config-matching/element-config-matching.component';
import { ElementConfigMcqOptionInfoComponent } from './element-config-mcq-option-info/element-config-mcq-option-info.component';
import { ElementConfigMicComponent } from './element-config-mic/element-config-mic.component';
import { ElementConfigMoveableDndComponent } from './element-config-moveable-dnd/element-config-moveable-dnd.component';
import { ScoringRubricComponent } from './marking-rubric/marking-rubric.component';
import { ScoringRubricCodeComponent } from './marking-rubric-code/marking-rubric-code.component';
import { ViewImGroupAccessComponent } from './view-im-group-access/view-im-group-access.component';
import { ViewImAssetLibraryComponent } from './view-im-asset-library/view-im-asset-library.component';
import { CommentSectionComponent } from './comment-section/comment-section.component';
import { ElementConfigOrderComponent } from './element-config-order/element-config-order.component';
import { ElementConfigReaderComponent } from './element-config-reader/element-config-reader.component';
import { ElementConfigResultsPrintComponent } from './element-config-results-print/element-config-results-print.component';
import { ElementConfigSbsComponent } from './element-config-sbs/element-config-sbs.component';
import { ElementConfigSelectTextComponent } from './element-config-select-text/element-config-select-text.component';
import { ElementConfigSelectionTableComponent } from './element-config-selection-table/element-config-selection-table.component';
import { ElementConfigSolutionComponent } from './element-config-solution/element-config-solution.component';
import { ElementConfigTableComponent } from './element-config-table/element-config-table.component';
import { ElementConfigTextLinkComponent } from './element-config-text-link/element-config-text-link.component';
import { ElementConfigUploadComponent } from './element-config-upload/element-config-upload.component';
import { ElementConfigComponent } from './element-config/element-config.component';
import { ElementNotesComponent } from './element-notes/element-notes.component';
import { FrameConfigComponent } from './frame-config/frame-config.component';
import { FrameworkDimensionEditorComponent } from './framework-dimension-editor/framework-dimension-editor.component';
import { FrameworkDimensionInputComponent } from './framework-dimension-input/framework-dimension-input.component';
import { SelectElementComponent } from './select-element/select-element.component';
import { ViewG9SampleComponent } from './view-g9-sample/view-g9-sample.component';
import { ElementConfigBookmarkLinkComponent } from './element-config-bookmark-link/element-config-bookmark-link.component';
import { ViewImDashboardComponent } from './view-im-dashboard/view-im-dashboard.component';
import { ViewImIssuesComponent } from './view-im-issues/view-im-issues.component';
import { ViewImItemSearchComponent } from './view-im-item-search/view-im-item-search.component';
import { WidgetAssignUserComponent } from './widget-assign-user/widget-assign-user.component';
import { WidgetAuditsComponent } from './widget-audits/widget-audits.component';
import { WidgetAuthoringMainComponent } from './widget-authoring-main/widget-authoring-main.component';
import { WidgetElementImportComponent } from './widget-element-import/widget-element-import.component';
import { WidgetElementRestoreComponent } from './widget-element-restore/widget-element-restore.component';
import { WidgetFrameworkMainComponent } from './widget-framework-main/widget-framework-main.component';
import { WidgetFrameworkSettingsComponent } from './widget-framework-settings/widget-framework-settings.component';
import { WidgetItemListingComponent } from './widget-item-listing/widget-item-listing.component';
import { WidgetItemDiffComponent } from './widget-item-diff/widget-item-diff.component';
import { WidgetItemParamsImportComponent } from './widget-item-params-import/widget-item-params-import.component';
import { WidgetLinearFormConstructionComponent } from './widget-linear-form-construction/widget-linear-form-construction.component';
import { WidgetMscatPanelsComponent } from './widget-mscat-panels/widget-mscat-panels.component';
import { WidgetPublishingComponent } from './widget-publishing/widget-publishing.component';
import { WidgetQuadrantsComponent } from './widget-quadrants/widget-quadrants.component';
import { WidgetSampleFormsListingComponent } from './widget-sample-forms-listing/widget-sample-forms-listing.component';
import { WidgetSimplePrintViewComponent } from './widget-simple-print-view/widget-simple-print-view.component';
import { WidgetTestDesignItemBanksComponent } from './widget-test-design-item-banks/widget-test-design-item-banks.component';
import { WidgetTestletsComponent } from './widget-testlets/widget-testlets.component';
import { ElementConfigValidatorComponent } from './element-config-validator/element-config-validator.component';
import { ElementConfigCustomInteractionComponent } from './element-config-custom-interaction/element-config-custom-interaction.component';
import { ElementConfigScaleRadiusComponent } from './element-config-scale-radius/element-config-scale-radius.component';
import { ElementConfigSliderComponent } from './element-config-slider/element-config-slider.component';
import { ElementConfigRotateSpriteComponent } from './element-config-rotate-sprite/element-config-rotate-sprite.component';
import { ViewImManageAuthorComponent } from './view-im-manage-author/view-im-manage-author.component';
import { ViewImParameters } from './view-im-parameters/view-im-parameters.component';
import { ManageAccessComponent } from './manage-access/manage-access.component';
import { ActivityLogComponent } from './activity-log/activity-log.component';
import { NotificationListComponent } from './notification-list/notification-list.component';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatListModule } from '@angular/material/list'
import { MatExpansionModule } from '@angular/material/expansion';
import { AssetDetailsComponent } from './asset-details/asset-details.component';
import { AssetDetailFieldEditorComponent } from './asset-detail-field-editor/asset-detail-field-editor.component';
import { ShareAccessComponent } from './share-access/share-access.component';
import { TooltipModule } from 'ng2-tooltip-directive';
import { ManageTagsComponent } from './manage-tags/manage-tags.component';
import { ItemTagComponent } from './item-tag/item-tag.component';
import {MatAutocompleteModule} from '@angular/material/autocomplete';
import { TextDiffComponent } from './text-diff/text-diff.component';
import { ModeToggleComponent } from './mode-toggle/mode-toggle.component';
import { WidgetJsonEditorComponent } from './widget-json-editor/widget-json-editor.component';
import { NgJsonEditorModule } from 'ang-jsoneditor';
import { ElementConfigPixiGraphComponent } from './element-config-pixi-graph/element-config-pixi-graph.component';
import { ElementConfigGridFillComponent } from './element-config-grid-fill/element-config-grid-fill.component';
import { ElementConfigGridScatterPlotComponent } from './element-config-grid-scatter-plot/element-config-grid-scatter-plot.component';
import { UiStudentModule } from '../ui-student/ui-student.module';
import { WidgetAssembledFormsComponent } from './widget-assembled-forms/widget-assembled-forms.component';
import { SectionEditModalComponent } from './section-edit-modal/section-edit-modal.component';
import { WidgetExpectedAnswersComponent } from './widget-expected-answers/widget-expected-answers.component';
import { MatTableModule } from '@angular/material/table';
import {MatInputModule} from '@angular/material/input';
import {MatFormFieldModule} from '@angular/material/form-field';
import { WidgetItemScoringInfoComponent } from './widget-item-scoring-info/widget-item-scoring-info.component';
import { MatMenuModule } from '@angular/material/menu';
import {ClipboardModule} from '@angular/cdk/clipboard';
import { WidgetChangeLogModalComponent } from './widget-change-log-modal/widget-change-log-modal.component';
import { AgGridModule, } from 'ag-grid-angular';
import { ItemWorkflowSectionComponent } from './item-workflow-section/item-workflow-section.component';
import {MatStepperModule} from '@angular/material/stepper';
import {MatButtonModule} from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import {MatIconModule} from '@angular/material/icon';
import { DisplayConfigDiffComponent } from './widget-change-log-modal/display-config-diff/display-config-diff.component';
import { DisplayAssigneesDiffComponent } from './widget-change-log-modal/display-assignees-diff/display-assignees-diff.component';
import { GraphicsWorkflowSectionComponent } from './graphics-workflow-section/graphics-workflow-section.component';
import { GraphicsRequestComponent } from './graphics-workflow-section/graphics-request/graphics-request.component';
import { ElementConfigPassageComponent } from './element-config-passage/element-config-passage.component';
import { ElementConfigStaticDiagramComponent } from './element-config-static-diagram/element-config-static-diagram.component';
import { ElementConfigTemplateComponent } from './element-config-template/element-config-template.component';
import { CodemirrorModule } from '@ctrl/ngx-codemirror';
import { WorkflowGroupAssignmentModalComponent } from './widget-item-listing/workflow-group-assignment-modal/workflow-group-assignment-modal.component';
import { CommentFileUploadComponent } from './comment-display/comment-file-upload/comment-file-upload.component';
import { TextInputTransformDirective } from './text-input-transform.directive';
import { ProgressBarEditorComponent } from './widget-framework-settings/editors/progress-bar-editor/progress-bar-editor.component';
import { AuditTableComponent } from './widget-audits/audit-table/audit-table.component';
import { ViewImTemplateLibrary } from './view-im-template-library/view-im-template-library.component';
import { TemplateLibraryComponent } from './template-library/template-library.component';
import { TemplateJsonViewerComponent } from './template-json-viewer/template-json-viewer.component';
import { TemplateAdvancedInlineComponent } from './element-config-template/constructions/template-advanced-inline/template-advanced-inline.component';
import { TemplateInputComponent } from './element-config-template/constructions/template-input/template-input.component';
import { TemplateMcqComponent } from './element-config-template/constructions/template-mcq/template-mcq.component';
import { TemplateSelectionComponent } from './element-config-template/constructions/template-selection/template-selection.component';
import { TemplateStimComponent } from './element-config-template/constructions/template-stim/template-stim.component';
import { TemplateTableComponent } from './element-config-template/constructions/template-table/template-table.component';
import { TemplateSolutionComponent } from './element-config-template/constructions/template-solution/template-solution.component';
import { ElementConfigInteractiveDiagramComponent } from './element-config-interactive-diagram/element-config-interactive-diagram.component'
import { ElementConfigDgIntDndTableComponent } from './element-config-interactive-diagram/constructions/dg-int-config-dnd-table/dg-int-config-dnd-table.component'
import { ElementConfigDgIntDndInlineComponent } from './element-config-interactive-diagram/constructions/dg-int-config-dnd-inline/dg-int-config-dnd-inline.component'
import { ElementConfigDgIntDndInline2Component } from './element-config-interactive-diagram/constructions/dg-int-config-dnd-inline-2/dg-int-config-dnd-inline-2.component'
import { ElementConfigDgIntDndGroupComponent } from './element-config-interactive-diagram/constructions/dg-int-config-dnd-group/dg-int-config-dnd-group.component'
import { ElementConfigDgIntDndTallyComponent } from './element-config-interactive-diagram/constructions/dg-int-config-dnd-tally/dg-int-config-dnd-tally.component';
import { ElementConfigDgIntDndSortingComponent } from './element-config-interactive-diagram/constructions/dg-int-config-dnd-sorting/dg-int-config-dnd-sorting.component'
import { ElementConfigDgIntDndNumberlineComponent } from './element-config-interactive-diagram/constructions/dg-int-config-dnd-numberline/dg-int-config-dnd-numberline.component'
import { ElementConfigDgIntDndFreeformComponent } from './element-config-interactive-diagram/constructions/dg-int-config-dnd-freeform/dg-int-config-dnd-freeform.component'
import { ElementConfigDgIntDndVennComponent } from './element-config-interactive-diagram/constructions/dg-int-config-dnd-venn/dg-int-config-dnd-venn.component'
import { ElementConfigDgIntDndClozeMathComponent } from './element-config-interactive-diagram/constructions/dg-int-config-dnd-cloze-math/dg-int-config-dnd-cloze-math.component'
import { ElementConfigDgIntMcqHotspotComponent } from './element-config-interactive-diagram/constructions/dg-int-config-mcq-hotspot/dg-int-config-mcq-hotspot.component'
import { ConfigPreviewContainerComponent } from './element-config-interactive-diagram/common/config-preview-container/config-preview-container.component'
import { DgIntDndHomeLayoutComponent } from './element-config-interactive-diagram/common/dg-int-dnd-home-layout/dg-int-dnd-home-layout.component'
import { DgIntDndColorStyleComponent } from './element-config-interactive-diagram/common/dg-int-dnd-color-style/dg-int-dnd-color-style.component'
import { DgIntDndTargetOptionsComponent } from './element-config-interactive-diagram/common/dg-int-dnd-target-options/dg-int-dnd-target-options.component'
import { DgIntDndAdvancedHomeLayoutEditorComponent } from './element-config-interactive-diagram/common/dg-int-dnd-advanced-home-layout-editor/dg-int-dnd-advanced-home-layout-editor.component'
import { DgIntDndAnswersComponent } from './element-config-interactive-diagram/common/dg-int-dnd-answers/dg-int-dnd-answers.component';
import { ElementConfigScientificNotationComponent } from './element-config-scientific-notation/element-config-scientific-notation.component';
import { ViewImGraphicRequestsComponent } from './view-im-graphic-requests/view-im-graphic-requests.component';
import { ConfigScoreMatrixComponent } from './config-score-matrix/config-score-matrix.component';
import { QuestionRunnerSplitViewComponent } from './question-runner-split-view/question-runner-split-view.component';
import { TextDiffRendererComponent } from './text-diff/text-diff-renderer/text-diff-renderer.component';
@NgModule({
    imports: [
        CommonModule,
        ReactiveFormsModule,
        UiPartialModule,
        HttpClientModule,
        MarkdownModule,
        UiTestrunnerModule,
        UiLoginModule,
        DragDropModule,
        ScrollingModule,
        TextFieldModule,
        CdkTreeModule,
        WidgetMathModule,
        UiAccountMngrModule,
        NgxJsonViewerModule,
        IoAudioModule,
        MatCheckboxModule,
        MatListModule,
        MatExpansionModule,
        MatButtonModule,
        MatStepperModule,
        ReactiveFormsModule,
        MatFormFieldModule,
        MatInputModule,
        MatChipsModule,
        MatIconModule,
        RouterModule.forChild([
          {path: `dashboard`, component: ViewImDashboardComponent},
          {path: `auth/designs`, data: {view: 'designs'}, component: ViewImDashboardComponent},
          {path: `auth/sets`, data: {view: 'sets'}, component: ViewImDashboardComponent},
          {path: `auth/groups`, data: {view: 'groups'}, component: ViewImDashboardComponent},
          {path: `framework/:isFramework/:itemSetId`, component: ItemSetEditorComponent},
          {path: `framework/:isFramework/:itemSetId/preview`, data: {isStudentPreview: true}, component: ItemSetEditorComponent},
          {path: `g9-sample/:itemSetId/:label`, component: ViewG9SampleComponent},
          {path: `public-test-runner/:itemSetId`, component: ViewG9SampleComponent},
          {path: `shared-test-version/:itemSetId/:testVersionId`, component: ViewG9SampleComponent},
          {path: `item-set-editor/:itemSetId`, component: ItemSetEditorComponent},
          {path: `item-set-editor/:itemSetId/:questionLabel`, component: ItemSetEditorComponent},
          {path: `item-editor/:itemSetId/:itemID`, component: ItemSetEditorComponent},
          {path: `group/:groupId/access`, component: ViewImGroupAccessComponent},
          {path: `asset-library`, component: ViewImAssetLibraryComponent},
          {path: `issues`, component: ViewImIssuesComponent},
          {path: `graphic-requests`, component: ViewImGraphicRequestsComponent},
          {path: `parameters`, component: ViewImParameters},
          {path: `graphic-requests`, component: ViewImGraphicRequestsComponent},
          {path: `manage-author/:userId`, component: ViewImManageAuthorComponent},
          {path: `activity-log/:userId`, component: ActivityLogComponent},
          {path: `notifications`, component: NotificationListComponent},
          {path: `notifications/:itemSetId`, component: NotificationListComponent},
          {path: `issues/:filters`, component: ViewImIssuesComponent},
          {path: `graphic-requests/:filters`, component: ViewImGraphicRequestsComponent},
          {path: `template-library`, component: ViewImTemplateLibrary},
        ]),
        FormsModule,
        TooltipModule,
        MatAutocompleteModule,
        NgJsonEditorModule,
        UiStudentModule,
        CodemirrorModule,
        MatTableModule,
        MatMenuModule,
        ClipboardModule,
        MatInputModule,
        MatFormFieldModule,
        AgGridModule,
    ],
  declarations: [
    ItemSetEditorComponent,
    DropdownSearchComponent,
    DropdownSelectComponent,
    DropdownSelectButtonComponent,
    KatexDisplayComponent,
    ElementConfigTextComponent,
    ElementConfigMathComponent,
    ElementConfigImageComponent,
    ElementConfigVideoComponent,
    ElementConfigMcqComponent,
    ElementConfigCustomMCQComponent,
    ElementConfigLocAndDims,
    ElementConfigInputComponent,
    DropZoneDirective,
    ElementConfigMoveableDndComponent,
    ElementConfigGridDNDComponent,
    FileSizePipe,
    ElementConfigMcqOptionTextComponent,
    ElementConfigMcqOptionMathComponent,
    ElementConfigMcqOptionImageComponent,
    ElementConfigAnnotation,
    ElementConfigFrameComponent,
    CaptureImageComponent,
    // RenderMathComponent,
    // RenderImageComponent,
    ElementConfigOrderComponent,
    ElementConfigIframeComponent,
    CheckboxComponent,
    DelimiterComponent,
    ColorPanel,
    ConfigMcq,
    FrameworkDimensionEditorComponent,
    FrameworkDimensionInputComponent,
    ElementConfigMcqOptionInfoComponent,
    CommentSectionComponent,
    ViewImDashboardComponent,
    ElementConfigTableComponent,
    ElementConfigDndComponent,
    SelectElementComponent,
    FrameConfigComponent,
    ElementConfigComponent,
    ElementConfigTextInfoComponent,
    ElementConfigMicComponent,
    ElementConfigPieChart,
    ElementConfigCameraComponent,
    ElementConfigSelectTextComponent,
    ElementConfigUploadComponent,
    ElementConfigHotspotComponent,
    ElementConfigHottextComponent,
    CaptureDndStyleComponent,
    SpecialCharacterKeyboardComponent,
    // MarkingRubricComponent,
    // MarkingRubricCodeComponent,
    ScoringRubricComponent,
    ScoringRubricCodeComponent,
    ViewImGroupAccessComponent,
    AssetLibraryComponent,
    AssetLibraryLinkComponent,
    ViewImAssetLibraryComponent,
    ViewImTemplateLibrary,
    ElementConfigAudioComponent,
    ElementConfigReaderComponent,
    ElementConfigGraphingComponent,
    CommentDisplayComponent,
    CaptureUploadFilesComponent,
    ElementConfigGraphicsComponent,
    ElementConfigSbsComponent,
    ElementConfigSelectionTableComponent,
    ElementConfigMatchingComponent,
    ElementConfigGroupingComponent,
    ElementNotesComponent,
    ViewImIssuesComponent,
    ViewImParameters,
    ViewG9SampleComponent,
    ElementConfigCanvasComponent,
    ElementConfigInsertionComponent,
    ElementConfigTextLinkComponent,
    ElementConfigBookmarkLinkComponent,
    ElementConfigSolutionComponent,
    ElementConfigDocLinkComponent,
    ElementConfigDivComponent,
    ElementConfigResultsPrintComponent,
    ViewImItemSearchComponent,
    WidgetElementRestoreComponent,
    WidgetAssignUserComponent,
    WidgetElementImportComponent,
    WidgetItemParamsImportComponent,
    WidgetItemListingComponent,
    WidgetItemDiffComponent,
    WidgetSampleFormsListingComponent,
    WidgetLinearFormConstructionComponent,
    WidgetSimplePrintViewComponent,
    WidgetAuthoringMainComponent,
    WidgetFrameworkMainComponent,
    WidgetFrameworkSettingsComponent,
    WidgetPublishingComponent,
    WidgetTestDesignItemBanksComponent,
    WidgetMscatPanelsComponent,
    WidgetTestletsComponent,
    WidgetQuadrantsComponent,
    WidgetAuditsComponent,
    ElementConfigValidatorComponent,
    ElementConfigCustomInteractionComponent,
    // ElementConfigGroupingPixiComponent,
    ElementConfigScaleRadiusComponent,
    ElementConfigSliderComponent,
    ElementConfigRotateSpriteComponent,
    ViewImManageAuthorComponent,
    ManageAccessComponent,
    ActivityLogComponent,
    NotificationListComponent,
    AssetDetailsComponent,
    AssetDetailFieldEditorComponent,
    ShareAccessComponent,
    ManageTagsComponent,
    ItemTagComponent,
    TextDiffComponent,
    ModeToggleComponent,
    WidgetJsonEditorComponent,
    ElementConfigPixiGraphComponent,
    ElementConfigGridFillComponent,
    ElementConfigGridScatterPlotComponent,
    WidgetAssembledFormsComponent,
    SectionEditModalComponent,
    WidgetExpectedAnswersComponent,
    WidgetItemScoringInfoComponent,
    WidgetChangeLogModalComponent,
    ItemWorkflowSectionComponent,
    DisplayConfigDiffComponent,
    DisplayAssigneesDiffComponent,
    GraphicsWorkflowSectionComponent,
    GraphicsRequestComponent,
    ElementConfigPassageComponent,
    ElementConfigTemplateComponent,
    WorkflowGroupAssignmentModalComponent,
    CommentFileUploadComponent,
    ViewImGraphicRequestsComponent,
    ConfigScoreMatrixComponent,
    ElementConfigStaticDiagramComponent,
    ElementConfigInteractiveDiagramComponent,
    ElementConfigDgIntDndTableComponent,
    ElementConfigDgIntDndInlineComponent,
    ElementConfigDgIntDndInline2Component,
    ElementConfigDgIntDndGroupComponent,
    ElementConfigDgIntDndSortingComponent,
    ElementConfigDgIntDndTallyComponent,
    ElementConfigDgIntDndNumberlineComponent,
    ElementConfigDgIntDndFreeformComponent,
    ElementConfigDgIntDndVennComponent,
    ElementConfigDgIntDndClozeMathComponent,
    ElementConfigDgIntMcqHotspotComponent,
    DgIntDndHomeLayoutComponent,
    DgIntDndColorStyleComponent,
    DgIntDndTargetOptionsComponent,
    DgIntDndAdvancedHomeLayoutEditorComponent,
    DgIntDndAnswersComponent,
    ConfigPreviewContainerComponent,
    GraphicsWorkflowSectionComponent,
    GraphicsRequestComponent,
    QuestionRunnerSplitViewComponent,
    TextDiffRendererComponent,
    TextInputTransformDirective,
    AuditTableComponent,
    ProgressBarEditorComponent,
    TemplateLibraryComponent,
    TemplateJsonViewerComponent,
    TemplateStimComponent,
    TemplateTableComponent,
    TemplateSolutionComponent, 
    TemplateSelectionComponent,
    TemplateAdvancedInlineComponent,
    TemplateInputComponent,
    TemplateMcqComponent,
    ElementConfigScientificNotationComponent,
  ],
  exports: [
    ItemSetEditorComponent,
    CaptureImageComponent,
    ElementConfigMcqOptionImageComponent,
    WidgetExpectedAnswersComponent
  ]
})
export class UiItemMakerModule { }

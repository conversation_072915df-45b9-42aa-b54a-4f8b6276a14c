<fieldset [disabled]="isReadOnly()">
  <hr/>
  <p>This test design is using the following item sets:</p>
  <table style="width:auto; font-size:0.8em; margin:0.5em 0em;">
    <tr *ngFor="let itemBank of saveLoadCtrl.itemBanksUsed; let i = index">
      <td><code>{{itemBank.id}}</code></td>
      <td><strong>{{itemBank.slug}}</strong></td>
      <td> 
        <a href="/#/{{lang.c()}}/test-auth/item-set-editor/{{itemBank.id}}" target="_blank">{{itemBank.name}} </a>
      </td>
      <td><button (click)="frameworkCtrl.removeItemBank(i)" class="button is-small is-danger" style="font-size: 0.8em;">Remove</button></td>
    </tr>
  </table>
  <div style="margin:0.5em 0em; display: flex; flex-direction:row;">
      <button  class="button is-success is-small"
        [disabled]="saveLoadCtrl.isLoadingItemSets"
        (click)="frameworkCtrl.newItemBankStart()"
      >
        Include another Item Set
      </button>

      <div [class.is-disabled]="isReadOnly()" class="select is-small" *ngIf="frameworkCtrl.isReadyToAddItemSet">
        <select [formControl]="frameworkCtrl.selectedNewItemBank">
          <option *ngFor="let itemSet of frameworkCtrl.availableItemSets" [value]="itemSet.id"> 
            {{itemSet.slug}} : {{itemSet.name}}
          </option>
        </select>
      </div>
      <button *ngIf="frameworkCtrl.isReadyToAddItemSet" class="button is-small" (click)="frameworkCtrl.newItemBankAdd()">Add</button>
  </div>
  <div style="margin-top:1em; font-size:0.8em;" *ngIf="frameworkCtrl.requireItemBankRefresh">
    You have made a change to the selection of item banks that are used to construct the test forms. In order to update the item bank listing below, please <a (click)="reloadThePage()">reload the page</a>.
  </div>
</fieldset>

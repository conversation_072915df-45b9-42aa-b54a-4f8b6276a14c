<div>
      
  <div class="pre-table-strip">
      <div style="display:flex; flex-direction: row; align-items: center;">
        <paginator [model]="testletCtrl.testletsPaginator" [isStrip]="false" [isSmall]="true" ></paginator>
        <fieldset [disabled]="isReadOnly()">
          <label style="margin-left: 1em;">
            <input type="checkbox" [formControl]="testletCtrl.isTestletFiltersActive">
            <span  style="margin-left:0.5em">
              Use Testlet Filters?
            </span>
          </label>
        </fieldset>
      </div>
      <div>
        <button
        [disabled]="isReadOnly()"
        (click)="testletCtrl.toggleTestletColumnsEdit()" 
        class="button is-small has-icon" 
        [class.is-info]="testletCtrl.isTestletEditOpen"
        >
        <span class="icon"><i class="fa fa-columns" aria-hidden="true"></i> </span>
        <span>Select Stats Columns</span>
      </button>
      <button 
      class="button is-small has-icon" 
      [disabled]="true || isReadOnly()"
      >
      <span class="icon"><i class="fa fa-refresh" aria-hidden="true"></i> </span>
      <span>Recompute Stats</span>
    </button>
    <button
    [disabled]="isReadOnly()" 
    class="button is-small has-icon" 
    (click)="testletCtrl.exportTestletTable()"
    >
    <span class="icon"><i class="fa fa-table" aria-hidden="true"></i> </span>
    <span>Export Testlet Table</span>
  </button>
  <button 
  [disabled]="isReadOnly()"
  (click)="testletCtrl.printSelectedTestlets()" 
  class="button is-small has-icon" 
  >
  <span class="icon"><i class="fa fa-print" aria-hidden="true"></i> </span>
  <span>Print Selected Testlets</span>
  </button> 
  </div>
  </div>

  <div *ngIf="testletCtrl.isTestletEditOpen">
    <hr/>
    <table style="width:auto">
      <tr>
        <th>Stats Column</th>
        <th>Show?</th>
      </tr>
      <tr *ngFor="let col of frameworkCtrl.asmtFmrk.testletStatCol">
        <td>
          {{col.key}}
        </td>
        <td style="text-align:center;">
          <button [disabled]="isReadOnly()" class="button is-small is-white" (click)="col.isShown = !col.isShown">
            <span class="checkbox-icon" [ngSwitch]="col.isShown">
              <i *ngSwitchCase="true" class="fa fa-check-square" aria-hidden="true"></i>
              <i *ngSwitchDefault class="far fa-square" aria-hidden="true"></i>
            </span>
          </button>
        </td>
      </tr>
    </table>
    <hr/>
  </div>
  <!-- <button class="button">Calculate Item Exposure</button> -->

  <div class="scroll-table-container">
    <table>
      <tr>
        <th>
          <button [disabled]="isReadOnly()" class="button is-small is-white" (click)="testletCtrl.toggleSelectAllTestlets()">
            <span class="checkbox-icon" [ngSwitch]="testletCtrl.isAllTestletsSelected">
              <i *ngSwitchCase="true" class="fa fa-check-square" aria-hidden="true"></i>
              <i *ngSwitchDefault class="far fa-square" aria-hidden="true"></i>
            </span>
          </button>
        </th>
        <th>
          ID
          <capture-filter-range
            id="id"
            defaultMode="value"
            [isModeSelect]="true"
            [model]="testletCtrl.testletFilters"
            [isActive]="testletCtrl.isTestletFiltersActive.value"
            (change)="testletCtrl.updateTestletFilterThrottled()"
          ></capture-filter-range>
        </th>
        <th>
          Section
          <capture-filter-range
            id="section"
            defaultMode="value"
            [isModeSelect]="true"
            [model]="testletCtrl.testletFilters"
            [isActive]="testletCtrl.isTestletFiltersActive.value"
            (change)="testletCtrl.updateTestletFilterThrottled()"
          ></capture-filter-range>
        </th>
        <th>
          Quadrant
          <capture-filter-range
            id="quadrant"
            defaultMode="value"
            [isModeSelect]="true"
            [model]="testletCtrl.testletFilters"
            [isActive]="testletCtrl.isTestletFiltersActive.value"
            (change)="testletCtrl.updateTestletFilterThrottled()"
          ></capture-filter-range>
        </th>
        <th *ngFor="let stat of frameworkCtrl.asmtFmrk.testletStatCol" [class.is-hidden]="!stat.isShown">
          <div class="stat-header-indic">STAT</div>
          <div>{{stat.key}}</div>
          <capture-filter-range
            [id]="stat.key"
            defaultMode="range"
            [isModeSelect]="true"
            [model]="testletCtrl.testletFilters"
            [isActive]="testletCtrl.isTestletFiltersActive.value"
            (change)="testletCtrl.updateTestletFilterThrottled()"
          ></capture-filter-range>
        </th>
        <th>Review</th>
        <th>Manage</th>
      </tr>
      <tr 
        *ngFor="let testlet of testletCtrl.testletsFilteredDisplay"
        [class.is-reviewed]="testlet.isReviewed"
      >
      <td>
        <button [disabled]="isReadOnly()" class="button is-small is-white" (click)="testletCtrl.toggleTestletSelection(testlet)">
          <span class="checkbox-icon" [ngSwitch]="testletCtrl.checkTestletSelected(testlet)">
            <i *ngSwitchCase="true" class="fa fa-check-square" aria-hidden="true"></i>
            <i *ngSwitchDefault class="far fa-square" aria-hidden="true"></i>
          </span>
        </button>
      </td>
      <td>
        <span class="tag is-dark">{{testlet.id}}</span>
      </td>
      <td>{{testlet.section}}</td>
      <td>{{testlet.quadrant}}</td>
      <td *ngFor="let stat of frameworkCtrl.asmtFmrk.testletStatCol" [class.is-hidden]="!stat.isShown">
        {{testletCtrl.getTestletStat(testlet, stat.key)}}
      </td>
      <td>
        <button [disabled]="isReadOnly()"
          class="button is-small has-icon" 
          (click)="testletCtrl.activateTestletQuestions(testlet)" 
          [class.is-info]="testletCtrl.checkActiveTestlet(testlet)"
        >
          <span class="icon"><i class="fa fa-list" aria-hidden="true"></i> </span>
          <span>List Items</span>
        </button>
      </td>
      <td>
        <div style="white-space: nowrap;">
          <button [disabled]="isReadOnly()"
            class="button is-small has-icon is-warning" 
            (click)="testletCtrl.addTestletComment(testlet)" 
          >
            <span class="icon"><i class="fa fa-comment" aria-hidden="true"></i> </span>
            <span>Comment</span>
          </button>
          <button [disabled]=isReadOnly()
            class="button is-small has-icon" 
            [class.is-success]="testlet.isReviewed"
            (click)="testlet.isReviewed = !testlet.isReviewed"
          >
            <span class="icon"><i class="fa fa-check" aria-hidden="true"></i> </span>
            <span>Mark as Reviewed</span>
          </button>
          <button [disabled]="isReadOnly()"
            class="button is-small has-icon is-danger" 
            [class.is-outlined]="testlet.isDisabled"
            (click)="testlet.isDisabled = !testlet.isDisabled" 
            [ngSwitch]="!!testlet.isDisabled"
          >
            <ng-container *ngSwitchCase="true">
              <span class="icon"><i class="fa fa-eye" aria-hidden="true"></i> </span>
              <span>Enable</span>
            </ng-container>
            <ng-container *ngSwitchCase="false">
              <span class="icon"><i class="fa fa-eye-slash" aria-hidden="true"></i> </span>
              <span>Disable</span>
            </ng-container>
          </button>
          <button [disabled]="isReadOnly()"
            class="button is-small has-icon is-danger" 
            (click)="util.removeArrEl(frameworkCtrl.asmtFmrk.testlets, testlet); testletCtrl.updateTestletFilterThrottled();" 
          >
            <span class="icon"><i class="fa fa-times" aria-hidden="true"></i> </span>
            <span>Discard</span>
          </button>
        </div>
        <div *ngIf="testlet.comments">
          <ul>
            <li *ngFor="let comment of testlet.comments">
              {{comment.caption}}
              <a [class.is-disabled]="isReadOnly()" class="close"  (click)="util.removeArrEl(testlet.comments, comment)">
                <i class="fa fa-times" aria-hidden="true"></i>
              </a>
            </li>
          </ul>
        </div>
      </td>  
      </tr>
    </table>
  </div>

</div>
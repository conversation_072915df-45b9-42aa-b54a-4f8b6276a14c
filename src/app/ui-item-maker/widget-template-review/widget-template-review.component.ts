import { Component, OnInit, Input, Output } from '@angular/core';

// app services
import { AssetLibraryService, IAssetLibraryConfig} from '../services/asset-library.service';
import { AssignedUsersService } from '../assigned-users.service';
import { AuthScopeSettingsService, AuthScopeSetting } from "../auth-scope-settings.service";
import { AuthService } from '../../api/auth.service';
import { DataGuardService } from '../../core/data-guard.service';
import { EditingDisabledService } from '../editing-disabled.service';
import { ItemComponentEditService } from '../item-component-edit.service';
import { ItemMakerService } from '../item-maker.service';
import { LangService } from '../../core/lang.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { PrintAltTextService } from '../print-alt-text.service';
import { PrintModeService } from '../print-mode.service';
import { RoutesService } from '../../api/routes.service';
import { ScriptGenService } from '../script-gen.service';
import { StyleprofileService } from '../../core/styleprofile.service';
import { WhitelabelService } from '../../domain/whitelabel.service';


import { ItemSetPreviewCtrl } from '../item-set-editor/controllers/preview';
import { ItemBankSaveLoadCtrl } from '../item-set-editor/controllers/save-load';
import { AssetLibraryCtrl } from '../item-set-editor/controllers/asset-library';
import { ItemSetFrameworkCtrl } from '../item-set-editor/controllers/framework';
import { ItemBankAuditor } from '../item-set-editor/controllers/audits';
import { ItemEditCtrl } from '../item-set-editor/controllers/item-edit';
import { ItemBankCtrl } from '../item-set-editor/controllers/item-bank';
import { MemberAssignmentCtrl } from '../item-set-editor/controllers/member-assignment';
import { ItemFilterCtrl } from '../item-set-editor/controllers/item-filter';
import { PanelCtrl } from '../item-set-editor/controllers/mscat';
import { ItemSetPrintViewCtrl } from '../item-set-editor/controllers/print-view';
import { ItemSetPublishingCtrl } from '../item-set-editor/controllers/publishing';
import { FrameworkQuadrantCtrl } from '../item-set-editor/controllers/quadrants';
import { TestFormGen } from '../item-set-editor/controllers/testform-gen';
import { TestletCtrl } from '../item-set-editor/controllers/testlets';
import { ItemBankUtilCtrl } from '../item-set-editor/controllers/util';
import { FormControl } from '@angular/forms';
import { IAssessmentPartition, getFormPartitionObj, getPartitionPropValue } from '../item-set-editor/models/assessment-framework';
import * as _ from 'lodash';
import { EventEmitter } from '@angular/core';
import { TemplateGroup, TemplateGroupItem, TemplateGroupProp } from './model';
import { IContentElementTemplate } from 'src/app/ui-testrunner/element-render-template/model';
import { IQuestionConfig } from '../item-set-editor/models';
import { ElementType, IQuestionConfig as IQuestionConfigTR} from 'src/app/ui-testrunner/models';
import { getValueByPath, upgradeTemplateContent } from '../element-config-template/util';

interface ITemplate {
  id: number;
  name: string;
  current_version_id: number;
  newest_version: string | number;
}
interface IQuestionTemplates {
  id: number,
  label: string,
  templates: ITemplate[],
}

@Component({
  selector: 'widget-template-review',
  templateUrl: './widget-template-review.component.html',
  styleUrls: ['./widget-template-review.component.scss']
})
export class WidgetTemplateReviewComponent implements OnInit {
  
  
  @Input() assetLibraryCtrl:AssetLibraryCtrl
  @Input() frameworkCtrl:ItemSetFrameworkCtrl
  @Input() auditCtrl:ItemBankAuditor
  @Input() itemBankCtrl:ItemBankCtrl
  @Input() itemEditCtrl:ItemEditCtrl
  @Input() itemFilterCtrl:ItemFilterCtrl
  @Input() memberAssignmentCtrl:MemberAssignmentCtrl
  @Input() panelCtrl:PanelCtrl
  @Input() previewCtrl:ItemSetPreviewCtrl
  @Input() printViewCtrl: ItemSetPrintViewCtrl
  @Input() publishingCtrl: ItemSetPublishingCtrl
  @Input() quadrantCtrl: FrameworkQuadrantCtrl
  @Input() saveLoadCtrl:ItemBankSaveLoadCtrl
  @Input() testFormGen: TestFormGen
  @Input() testletCtrl: TestletCtrl
  @Output() openSectionEditModal = new EventEmitter();
  
  public util = new ItemBankUtilCtrl();

  templateGroups:TemplateGroup[] = [];
  langCache:string;
  supportedTemplateConfigTypes = [
    "text",
    "number",
    "checkbox",
    "image",
  ]
  
  constructor(
    private editingDisabled: EditingDisabledService,
    public lang: LangService,
    public profile: StyleprofileService
    ) { }

  questionTemplates: IQuestionTemplates[] = [];
  ngOnInit(): void {
    this.loadQuestionTemplates();
  }

  loadQuestionTemplates() {
    this.questionTemplates = this.getQuestionTemplates();
    this.loadQuestionSelectMap();
  }

  questionSelectMap = {};
  loadQuestionSelectMap() {
    this.questionTemplates.forEach((question) => {
      this.questionSelectMap[question.id] = this.hasQuestionOutdatedTemplates(question.templates);
    })
  }

  getQuestionSelected(itemId) {
    return this.questionSelectMap[itemId];
  }

  setQuestionSelectMap(itemId, $event) {
    this.questionSelectMap[itemId] = $event;
  }

  isTemplateOutdated(template: ITemplate) {
    return template.current_version_id != template.newest_version && template.newest_version != 'NA';
  }

  hasQuestionOutdatedTemplates(templates: ITemplate[]) {
    return !!templates.find(template => this.isTemplateOutdated(template))
  }
  getQuestionTemplates() {
    const templateBank = this.profile.templates;
    const questions = this.itemBankCtrl.questions.filter((question) => question.content.find((element: IContentElementTemplate) => element.elementType == ElementType.TEMPLATE && element.templateId));
    const questionTemplateMap = questions.map((question) => {
      const templatesFiltered: IContentElementTemplate[] = question.content.filter((element: IContentElementTemplate) => element.elementType == ElementType.TEMPLATE && element.templateId) as IContentElementTemplate[];
      const templates = templatesFiltered.map((template) => {
        const newestVersion = templateBank.find((templateDef) => {
          return templateDef.id == template.templateId
        });
        return {id: template.templateId, name: template.templateName, current_version_id: template.templateVersion, newest_version: newestVersion?.current_version_id ?? 'NA'}
      });
      return {
        id: question.id,
        label: question.label,
        templates
      }
    })

    return questionTemplateMap;
  }

  getQuestionsToUpgrade() {
    const templateBank = this.profile.templates;
    // Find any question with at least one valid template element
    const questions = this.itemBankCtrl.questions.filter((question) => 
      question.content.find((element: IContentElementTemplate) => 
        element.elementType === ElementType.TEMPLATE && element.templateId
      )
    );

    if(!questions || !questions.length) {
      return [];
    }
        
    // Check all questions with any template element that is outdated.
    return questions.filter((question) => {
      let hasOutdated = false;
      const templatesFiltered: IContentElementTemplate[] = question.content.filter((element: IContentElementTemplate) => element.elementType == ElementType.TEMPLATE && element.templateId) as IContentElementTemplate[];
      for (let template of templatesFiltered) {
        const newestVersion = templateBank.find((templateDef) => {
          return templateDef.id == template.templateId
        });

        if(newestVersion && newestVersion.current_version_id != template.templateVersion) {
          hasOutdated = true;
          break;
        }
      }

      return hasOutdated
    }).filter((question) => this.questionSelectMap[question.id])
  }


  getCompletionPercentage(index, length) {
    if (length === 0) {
      return '0%'; // To avoid division by zero if the length is 0
    }
  
    const percentage = (index / (length - 1)) * 100; // Calculate the percentage
    const clampedPercentage = Math.min(Math.max(percentage, 0), 100); // Ensure it's between 0 and 100
  
    return `${clampedPercentage.toFixed(2)}%`; // Format to two decimal places and append '%'
  }

  bulkUpdateIndex = 0;
  bulkUpdateLength = 0;
  isBulkUpgrading = false;
  upgradeBulkTemplates() {
    try {
      this.isBulkUpgrading = true;
      const questions = this.getQuestionsToUpgrade();
      const templateBank = this.profile.templates;
    
      this.bulkUpdateIndex = 0;
      this.bulkUpdateLength = questions.length;
      let currentQuestion: IQuestionConfigTR;
      const upgradeNextQuestion = () => {
        try {
          if (this.bulkUpdateIndex >= questions.length){
            this.itemBankCtrl.selectQuestion(currentQuestion, true).then(() => {
              this.loadQuestionTemplates();
              this.isBulkUpgrading = false;
              alert('All questions have been upgraded where possible.');
            })
            return;
          }
          const question = questions[this.bulkUpdateIndex];
          currentQuestion = this.itemBankCtrl.getQuestionByLabel(question.label);
          this.itemBankCtrl.selectQuestion(currentQuestion).then(()=>{
            // Upgrade logic
            const templates: IContentElementTemplate[] = currentQuestion.content.filter((element: IContentElementTemplate) => element.elementType == ElementType.TEMPLATE && element.templateId) as IContentElementTemplate[]
            
            templates.forEach((template) => {
              const newestVersion = templateBank.find((templateDef) => {
                return templateDef.id == template.templateId
              });
    
              if(newestVersion && newestVersion.current_version_id != template.templateVersion) {
                const templateConfig = this.profile.templateConfigRef.get(newestVersion.id);
                try {
                  upgradeTemplateContent(newestVersion, templateConfig, template)
                } catch (err) {
                  console.error(err);
                  if(currentQuestion) {
                    alert(`An error occurred while upgrading item: ${currentQuestion.label} - ${(err.message)}`)
                  } else {
                    alert(`An error occurred during upgrading - ${(err.message)}`)
                  }
                }
              }
            })
    
            this.bulkUpdateIndex++;
            upgradeNextQuestion();
          })
        } catch (err){
          alert(`An error occurred during upgrading - ${(err.message)}`)
          console.error(err);
          this.isBulkUpgrading = false;
        }
      }
    
    
      if(questions.length > 0) {
          upgradeNextQuestion();
          return;
      } else {
        this.isBulkUpgrading = false;
        alert("No questions to upgrade.")
      }
    } catch (err) {
      alert(`An error occurred during upgrading - ${err.message}`)
      console.error(err);
      this.isBulkUpgrading = false;
    }
  }
}

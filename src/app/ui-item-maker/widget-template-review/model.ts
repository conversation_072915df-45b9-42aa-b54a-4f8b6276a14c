import { IQuestionConfig } from "../item-set-editor/models";

export interface TemplateGroup {
    templateId: number,
    templateName: string,
    relevantConfigProps: TemplateGroupProp[],
    items: TemplateGroupItem[],
    latestTemplateVersionId?:number,
}
export interface TemplateGroupProp {
    prop_id: string,
    configType: string, 
    configPath: string,
    label: string,
}
export interface TemplateGroupItem {
    item_id: number,
    item_label: string,
    templateVersionId: number,
    templatePath:string,
    itemRef: IQuestionConfig,
}
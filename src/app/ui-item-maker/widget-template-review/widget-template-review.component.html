<div >
  <h3>Version Management</h3>
  <table class="table is-bordered">
    <tr>
      <th></th>
      <th>Item</th>
      <th>Template name</th>
      <th>Current version</th>
      <th>Newest version</th>
    </tr>
    <ng-container *ngFor="let item of questionTemplates">
      <!-- Loop through the templates of the current item -->
      <tr *ngFor="let template of item.templates; let j = index">
        <td *ngIf="j === 0" [attr.rowspan]="item.templates.length"><input [disabled]="!hasQuestionOutdatedTemplates(item.templates)" type="checkbox" [ngModel]="getQuestionSelected(item.id)" (ngModelChange)="setQuestionSelectMap(item.id, $event)"></td>
        <!-- Display the Item label only on the first row of each item -->
        <td *ngIf="j === 0" [attr.rowspan]="item.templates.length">{{ item.label }} ({{ item.id }})</td>
        <td>{{ template.name }}</td>
        <td [class.is-outdated]="isTemplateOutdated(template)">{{ template.current_version_id }}</td>
        <td>{{ template.newest_version }}</td>
      </tr>
    </ng-container>
  </table>
  <button class="button" (click)="upgradeBulkTemplates()" style="margin-top: 1em;" [disabled]="isBulkUpgrading || !getQuestionsToUpgrade().length">
    <span *ngIf="isBulkUpgrading">
      {{getCompletionPercentage(bulkUpdateIndex, bulkUpdateLength)}}
    </span>
    <span *ngIf="!isBulkUpgrading">
      Upgrade
    </span>
  </button>
</div>
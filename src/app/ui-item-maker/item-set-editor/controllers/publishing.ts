// app services
import { AuthService } from '../../../api/auth.service';
import { LangService } from '../../../core/lang.service';
import { RoutesService } from '../../../api/routes.service';
import { ItemSetFrameworkCtrl } from './framework';
import { ItemBankCtrl } from './item-bank';
import { TestFormConstructionMethod } from '../models/assessment-framework';
import { TestFormGen } from './testform-gen';
import { IQuestionConfig } from '../../../ui-testrunner/models';
import { Destroyable } from './destroyable';
import { ItemEditCtrl } from './item-edit';
import { ScriptGenService } from '../../script-gen.service';

export interface IReleaseHistory {
  id: number,
  name: string,
  created_on: string,
  is_public: number,
  num_forms: number,
  tdso_id: number,
  is_approved: number,
  approved_by: number,
  approved_on: string,
  started_on: string,
  started_by: string,
  compared_to_td_id: number,
  twtdars?: any[]
}
export class ItemSetPublishingCtrl implements Destroyable {

  testDesignReleaseHistory: IReleaseHistory[];
  isShowingTestDesignReleaseHistory:boolean;;
  isPublishingTestDesign: boolean;

  constructor(
    public auth: AuthService,
    public lang: LangService,
    public routes: RoutesService,
    public frameworkCtrl: ItemSetFrameworkCtrl,
    public itemBankCtrl: ItemBankCtrl,
    public testFormGen: TestFormGen,
    public itemEditCtrl: ItemEditCtrl,
    private scriptGen: ScriptGenService
  ){

  }
  destroy() {

  }

  lockQuestions(){
    const questions = this.frameworkCtrl.getLinearFormQuestions()
    if (questions) {
      questions.forEach((quest)=>{
        const itemBankQuest:IQuestionConfig = this.itemBankCtrl.getQuestionById(quest.id)
        if (itemBankQuest["isLocked"]==true) {
          return;
        }
        this.itemBankCtrl.toggleQuestionLock(itemBankQuest)
        this.frameworkCtrl.saveLoadCtrl.saveTargetQuestionData(itemBankQuest, true)
      })
    }
  }

  getQuestionWeightMapping() {
    const questionWeightMap: {questionId: number, weight: number | null}[] = []
    this.itemBankCtrl.itemFilterCtrl.filteredQuestions.forEach((question) => {
      if(this.itemBankCtrl.hasExpectedAnswer(question.id)) {
        const weight = this.itemBankCtrl.getEAWeight(question.id);
        questionWeightMap.push({
          questionId: question.id,
          weight
        })
      } else {
        questionWeightMap.push({
          questionId: question.id,
          weight: null
        })
      }
    })

    return questionWeightMap;
  }

  isPublishingBlockedByItemRegisterLoad(){
    if (this.itemBankCtrl.isEaLoading){ return true }
    return false
  }
  
  async publishTestDesign(test_design_id?:number) {
    let lang = this.lang.c();
    if (!this.itemBankCtrl.isLangEnabled('en')){
      lang = 'fr'
    }

    if (this.isPublishingBlockedByItemRegisterLoad()){
      alert('Item register background variables load in progress. These must be resolved before publishing can proceed.')
      return;
    }

    // if (this.itemBankCtrl.isQLockAvail()) {
    //   const lock = confirm("Do you want to lock the test and all items in it from being edited?")
    //   if (lock) {
    //      this.lockQuestions();
    //   }
    // }
  
    this.isPublishingTestDesign = true;
    try {

      if (test_design_id){
        const numCopies = +prompt('How many forms?')
        if (numCopies){
          const len = Math.ceil(numCopies/10);
          for (let i=0; i<len; i++){
            await this._publishTestDesign(null, lang, test_design_id);
          }
        }
      }
      else{
        const name = prompt('Enter a name for this test design');
        if (!name) {
          this.isPublishingTestDesign = false;
          return;
        }
        await this._publishTestDesign(name, lang);
        await this.loadTestDesignReleaseHistory();
      }
      this.isPublishingTestDesign = false;
    }
    catch(e){
      console.error('Publishing failed', e)
      this.isPublishingTestDesign = false;
    }
  }

  isMultiForm(){
    switch (this.frameworkCtrl.asmtFmrk.testFormType) {
      case TestFormConstructionMethod.TLOFT:  
      case TestFormConstructionMethod.MSCAT:  
      case TestFormConstructionMethod.BUCKETS:  
        return true;
      case TestFormConstructionMethod.LINEAR: 
      default: 
        return false;
    }
  }

  // async updateCaptionVoiceovers(lang?: string) {
  //   //Generate and upload any missing or outdated question title voiceovers
  //   //Ensures that changes in question captions due to positioning or framework settings
  //   //are taken into account on publish without using the magic wand icon.
  //   const questionTitleMap = await this.itemEditCtrl.genQuestionTitleMap();
  //   const tuples = Array.from(questionTitleMap.keys());
  //   await Promise.all(tuples.map((tupleKey)=> {
  //     const tuple = JSON.parse(tupleKey);
  //     const sId = tuple[0];
  //     const qId: number = tuple[1];
  //     let question = this.itemBankCtrl.getQuestionById(qId);
  //     if(lang === 'fr') {
  //       question = question.langLink;
  //     }
  //     return this.scriptGen.autoGenQuestionCaptionVoiceover(sId, question, questionTitleMap.get(tupleKey), lang).then(()=> {
  //       this.frameworkCtrl.saveLoadCtrl.saveTargetQuestionData(question, true)
  //     });
  //   }))
  // }

  private mapToObject(map) {
    const obj = {};
    map.forEach((value, key) => {
      obj[key] = value;
    });
    return obj;
  }

  async _publishTestDesign(name?:string, lang?:string, test_design_id?:number){
    // await this.updateCaptionVoiceovers(lang);

    let forms:string[] = await this.getTestForms();
    // let generator:() => PromiseLike<string[]>;
    // switch (this.frameworkCtrl.asmtFmrk.testFormType) {
    //   case TestFormConstructionMethod.TLOFT:  
    //     forms = await this.testFormGen.generateLOFTTestFormsForTestDesign(true); break;
    //   case TestFormConstructionMethod.MSCAT:  
    //     forms = await this.testFormGen.generateMscatTestFormsForTestDesign(); 
    //     // const formsFirstQSummary = forms.map(s => JSON.parse(s).panelModules[0].questions.sort(function(a, b) {
    //     //   return a - b;
    //     // }))
    //     // console.log('formsFirstQSummary', formsFirstQSummary);
    //     // const firstCol = formsFirstQSummary[0] 
    //     // const csv = []; // to check questions in first module
    //     // for (let j=0; j<firstCol.length; j++){
    //     //   const row = []
    //     //   for (let i=0; i<formsFirstQSummary.length; i++){
    //     //     row.push(formsFirstQSummary[i][j]);
    //     //   }
    //     //   csv.push(row.join('\t'))
    //     // }
    //     // console.log(csv.join('\n'));
    //     break;
    //   case TestFormConstructionMethod.LINEAR: 
    //   default: 
    //     forms = await this.testFormGen.generateLinearTestFormsForTestDesign(); break;
    // }
    const removedQuestionsInForms = this.frameworkCtrl.getRemovedQuestionInFramework()
    if (Object.keys(removedQuestionsInForms).length){
      this.frameworkCtrl.alertRemovedQuestions(removedQuestionsInForms)
      console.error(removedQuestionsInForms);
      return;
    }

    const source_item_set_id = this.itemBankCtrl.customTaskSetId;
    const framework = JSON.stringify(this.frameworkCtrl.asmtFmrk);

    const question_weight_mapping = this.getQuestionWeightMapping();

    const newTestDesign = await this.auth.apiCreate(this.routes.TEST_AUTH_TEST_DESIGNS, {
      source_item_set_id,
      framework,
      name,
      lang,
      forms,
      test_design_id,
      question_weight_mapping,
      item_version_mapping: this.itemBankCtrl.versionIdMap,
    });

    alert('Test design is now publishing. Please refresh the Publishing History to view its progress.');

    return test_design_id
  }

  async getTestForms() {
    switch (this.frameworkCtrl.asmtFmrk.testFormType) {
      case TestFormConstructionMethod.TLOFT:  
        return await this.testFormGen.generateLOFTTestFormsForTestDesign(true);
      case TestFormConstructionMethod.MSCAT:  
        return await this.testFormGen.generateMscatTestFormsForTestDesign(); 
        // const formsFirstQSummary = forms.map(s => JSON.parse(s).panelModules[0].questions.sort(function(a, b) {
        //   return a - b;
        // }))
        // console.log('formsFirstQSummary', formsFirstQSummary);
        // const firstCol = formsFirstQSummary[0] 
        // const csv = []; // to check questions in first module
        // for (let j=0; j<firstCol.length; j++){
        //   const row = []
        //   for (let i=0; i<formsFirstQSummary.length; i++){
        //     row.push(formsFirstQSummary[i][j]);
        //   }
        //   csv.push(row.join('\t'))
        // }
        // console.log(csv.join('\n'));
      case TestFormConstructionMethod.BUCKETS: 
          return await this.testFormGen.generateBucketTestFormsForTestDesign();
      case TestFormConstructionMethod.LINEAR: 
      default: 
        return await this.testFormGen.generateLinearTestFormsForTestDesign();
    }
  }

  async loadFormsForPublishedTestDesign(test_design_id:number){
    return this.auth
      .apiFind(
        this.routes.TEST_AUTH_TEST_DESIGN_FORMS, 
        { 
          query: {
            test_design_id
          } 
        }
      )
  }
  async revokeForm(test_form_id:number){
    await this.auth
      .apiRemove(
        this.routes.TEST_AUTH_TEST_DESIGN_FORMS, 
        test_form_id,
      )
  }
  async unrevokeForm(test_form_id:number){
    await this.auth
      .apiPatch(
        this.routes.TEST_AUTH_TEST_DESIGN_FORMS, 
        test_form_id, 
        { },
      )
  }
  async toggleTestDesignIsPublic(testDesignRecord:{id:number, is_public:number}){
    if ( (testDesignRecord.is_public === 0) || !testDesignRecord.is_public){
      await this.auth.apiPatch(this.routes.TEST_AUTH_TEST_DESIGNS_IS_PUBLIC, testDesignRecord.id, { })
      testDesignRecord.is_public = 1;
    }
    else {
      this.auth.apiRemove(this.routes.TEST_AUTH_TEST_DESIGNS_IS_PUBLIC, testDesignRecord.id);
      testDesignRecord.is_public = 0;
    }    
  }

  twtars = [];
  selectedTwtar;
  td_id = null;
  async loadTestDesignReleaseHistory() {
    const source_item_set_id = this.itemBankCtrl.customTaskSetId;
    // console.log('source_item_set_id', source_item_set_id);
    this.testDesignReleaseHistory = await this.auth
      .apiFind(
        this.routes.TEST_AUTH_TEST_DESIGNS, 
        {query: {source_item_set_id}}
      )

    if (this.testDesignReleaseHistory?.length > 0) {
      this.twtars = this.testDesignReleaseHistory[0].twtdars || [];
      this.td_id = this.testDesignReleaseHistory[0].id
      this.selectedTwtar = this.twtars[0];
    }
  }

  togglePwdProtected(val: boolean) {
    const newVal = !val;
    this.auth.apiPatch(this.routes.TEST_AUTH_PUB_PWD_PROTECTED, this.itemBankCtrl.customTaskSetId, {public_pwd_protected: newVal}).then( (res) => {
        this.itemBankCtrl.publicPwdProtected = newVal;
      }
    );
  }

}

import { IContentElementAnnotatedText } from "src/app/ui-testrunner/element-render-annotation/model";
import { IContentElementCanvas, IContentElementCanvasPage } from "src/app/ui-testrunner/element-render-canvas/model";
import { IContentElementCustomMCQ, IContentElementCustomMcqOption } from "src/app/ui-testrunner/element-render-custom-mcq/model";
import { DndTargetType, IContentElementDnd, IContentElementDndTarget } from "src/app/ui-testrunner/element-render-dnd/model";
import { IContentElementFrame, IContentElementFrameStateStyle } from "src/app/ui-testrunner/element-render-frame/model";
import { IContentElementGroup } from "src/app/ui-testrunner/element-render-grouping/model";
import { defaultImageScaleFactor, IContentElementConditionalImage, IContentElementDynamicImage, IContentElementImage, IContentElementImageSubText } from "src/app/ui-testrunner/element-render-image/model";
import { IContentElementInput, InputFormat } from "src/app/ui-testrunner/element-render-input/model";
import { IContentElementInsertion } from "src/app/ui-testrunner/element-render-insertion/model";
import { IContentElementMath, IEquationBlockMode } from "src/app/ui-testrunner/element-render-math/model";
import { IContentElementMcq, IContentElementMcqOption, IContentElementMcqTableColEachCell, McqDisplay } from "src/app/ui-testrunner/element-render-mcq/model";
import { IContentElementLocAndDims, IContentElementMoveableDragDrop } from "src/app/ui-testrunner/element-render-moveable-dnd/model";
import { IContentElementOrder, IContentElementOrderOption } from "src/app/ui-testrunner/element-render-order/model";
import { IContentElementResultsPrint } from "src/app/ui-testrunner/element-render-results-print/model";
import { IContentElementSbs } from "src/app/ui-testrunner/element-render-sbs/model";
import { IContentElementSelectableText, IContentElementTextSelection } from "src/app/ui-testrunner/element-render-select-text/model";
import { IContentElementSelectionTable } from "src/app/ui-testrunner/element-render-selection-table/model";
import { IContentElementSolution } from "src/app/ui-testrunner/element-render-solution/model";
import { IContentElementTable } from "src/app/ui-testrunner/element-render-table/model";
import { IContentElementTextLink } from "src/app/ui-testrunner/element-render-text-link/model";
import { IContentElementText, TextParagraphStyle } from "src/app/ui-testrunner/element-render-text/model";
import { IContentElementVideo } from "src/app/ui-testrunner/element-render-video/model";
import { ElementType, IContentElement, IVoiceover } from "../../../ui-testrunner/models";
import { elementTypes } from "../../../ui-testrunner/models/ElementTypeDefs";
import { CustomButtonPos, ItemType } from "../../models";
import { getCurrentVersion, indexOf } from "../../services/util";
import { IContentElementPassage, PASSAGE_LIN_COUNT_INTERVAL_DEFAULT_SYS, PassageCounterType } from "src/app/ui-testrunner/element-render-passage/model";
import { IContentElementTemplate } from "src/app/ui-testrunner/element-render-template/model";
import { IContentElementStaticDiagram } from "src/app/ui-testrunner/element-render-static-diagram/model";
import { IContentElementInteractiveDiagram } from "src/app/ui-testrunner/element-render-interactive-diagram/model";
import { DiagramaticsImageQuality, LATEST_DIAGRAMATICS_VERSION, DiagramaticsMode, DiagramaticsVersion } from "src/assets/lib/diagramatics/config";
import { generateDefaultElementInteractiveDiagram } from "src/app/ui-testrunner/element-render-interactive-diagram/generator";
import { IContentElementNumberline, NUMBERLINE_SCORING_METHOD } from "src/app/ui-testrunner/element-render-numberline/model";
import { IContentElementScientificNotation } from "src/app/ui-testrunner/element-render-scientific-notation/model";
import { DgIntConstructionType } from "src/app/ui-testrunner/element-render-interactive-diagram/common";
import { IDgIntElementDnDTable } from "src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-table";
import { IDgIntElementDnDTarget } from "src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-common";
import { IDgIntElementDnDInline2 } from "src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-inline-2";
import { IDgIntElementDnDGroup } from "src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-group";
import { IDgIntElementDnDFreeform } from "src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-freeform";
import { IDgIntElementDnDVenn } from "src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-venn";
import { IDgIntElementDnDClozeMath } from "src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-cloze-math";
import { IDgIntElementDnDInline } from "src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-inline";
import { IDgIntElementDnDNumberline } from "src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-numberline";
import { IDgIntElementDnDTally } from "src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-tally";
import { IDgIntElementDnDSorting } from "src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-sorting";
import { DG_INT_MATCHING_TYPE } from "src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-common";
import { DG_INT_TALLY_TYPE } from "src/app/ui-testrunner/element-render-interactive-diagram/constructions/dnd-tally";
import { IDgIntElementMcqHotspot } from "src/app/ui-testrunner/element-render-interactive-diagram/constructions/mcq-hotspot";

export interface ICustomTaskSet { 
  __id?: string,
  __backup__id?: string,
  __backup__userDisplayName?: string,
  name?: string,
  order?: number,
  taskId?: string, // cross link to collection `tasks`
  project?: string,
  isReady?: boolean,
  labelCounter?:number,
  isArchived?: boolean,
  questions?:IQuestionConfig[] | string,
  creatorUid?: string,
  timeCreated?: number,
  timeLastSaved?: number,
  schemaVersion?: number,
  lastTouchedBy?: string,
  assessmentFrameworkId?:string,
  isProtected?:boolean,
}

export interface ICustomTaskTag {
  __id?:string,
  caption: string,
  customTaskSetId: string,
  localTaskId: number,
  createdBy: string,
  timestamp: number,
}

export interface ICustomTaskComment {
  __id?:string,
  caption: string,
  customTaskSetId: string,
  localTaskId: number,
  createdByName: string,
  createdByUid: string,
  timestamp: number,
}
export interface IQuestionRun {
  content: IContentElement[],
  // voiceover?: {url:string},
  entryOrder?: number[],
  isReadingSelectionPage?: boolean,
  readingSelectionCaption?: string,
  test_question_version_id?:number,
  label?: string
}
export interface IQuestionConfig extends IQuestionRun {
  id?: number,
  question_set_id?: number,
  label: string,
  isReady?: boolean,
  readSel?:string,
  caption?:string,
  points?:string,
  ishidePoints?: boolean;
  // captionVoiceover?: IVoiceover;
  readSelections?:string[],
  isInfoSlide?: boolean,
  notes?: string,
  taskId?: string,
  localTaskId: number,
  entryIdCounter?: number,
  testLabel?: string,
  isReqFill?:boolean,
  reqFillEntries?:string,
  reqFillMsg?:string,
  isReadingSelectionsAlwaysShown?: boolean,
  isStartHalf?: boolean,
  backgroundImage?: IContentElementImage,
  bannerImage?: IContentElementImage,
  bannerSubtitle?: IContentElementText,
  bannerSubtitleMarginBottom?: number,
  bannerTitle?: IContentElementText,
  showBannerHr?: boolean,
  bannerHrColor?: string,
  bannerOverlay?: IContentElementImage,
  useCustomPrev?: boolean,
  useCustomNext?: boolean,
  customPrevBgColor?: string,
  customNextBgColor?: string,
  customPrevFgColor?: string,
  customNextFgColor?: string,
  customPrevText?: string,
  customNextText?: string,
  customPrevBold?: boolean,
  customNextBold?: boolean,
  customButtonPos?: CustomButtonPos,
  customButtonIndent?: number,
  type?: ItemType;
  langLink?: IQuestionConfig;
  enableQuestionLevelZoom?: boolean;
}

export interface ExpectedAnswer {
  total?: number;
  formatted_response?: string;
  score?: number;
  weight?: number;
  entries?: any[];
  lang?: string;
  is_no_submission?: boolean;
}

export interface IElementChildrenConfig {
  includeSolution?: boolean;
  includeResultsPrint?: boolean;
  includeDnDTargets?: boolean;
  includeDnDMoveableImages?: boolean;
  includeVoiceover?: boolean
}

export const generateDefaultElementText = (elementType):IContentElementText => {
  return {
    elementType,
    caption: '',
    simpleList: [],
    advancedList: [],
    paragraphStyle: TextParagraphStyle.REGULAR,
    isNoLineHeight: true,
    block_version_id: getCurrentVersion(ElementType.TEXT)
  }
}

export const generateDefaultElementTextSelection = (elementType):IContentElementTextSelection => {
  return {
    elementType,
    texts: [],
    isAutoLineWrap: true,
    block_version_id: getCurrentVersion(ElementType.SELECT_TEXT)
  }
}

export const generateDefaultElementAnnotatedText = ():IContentElementAnnotatedText => {
  const obj = {
    ...generateDefaultElementText(ElementType.TEXT),
    text: generateDefaultElementText(ElementType.TEXT),
    annotation: ""
  }
  obj.elementType = ElementType.ANNOTATION
  return obj;
}


export const generateDefaultFrameStateStyle = ():IContentElementFrameStateStyle => {
  console.log('generateDefaultElementTable')
  return {
    backGroundImg: generateDefaultElementImage(ElementType.IMAGE),
    dropShadowX: 0,
    dropShadowY: 0,
    blurRadius: 0,
    shadowColor: "#ffffff",
    backgroundColor: "#ffffff",
    padding: 0,
  }
}

export const generateDefaultElementTable = (elementType):IContentElementTable => {
  console.log('generateDefaultElementTable')

  let grid = [];
  const defaultRows = 3;
  const defaultCols = 2;
  for(let r = 0; r < defaultRows; r++) {
    const cols = [];
    for(let c = 0; c < defaultCols; c++) {
      const el = createDefaultElement(ElementType.TABLE_TEXT)
      cols.push(el)
    }
    grid.push(cols);
  }

  return {
    elementType,
    grid: grid,
    isHeaderRow: false,
    isHeaderCol: false,
    isColWidthConst: false,
    isTableOfValues: true,
    colWidthConst: 4,
    block_version_id: getCurrentVersion(ElementType.TABLE)
  }
}
export const generateDefaultElementMath = (elementType):IContentElementMath => {
  return {
    elementType,
    latex: '',
    _changeCounter: 0,
    paragraphStyle: TextParagraphStyle.REGULAR,
    elementMode: IEquationBlockMode.MATH,
    block_version_id: getCurrentVersion(ElementType.MATH)
  }
}
export const generateDefaultElementImage = (elementType):IContentElementImage => {
  return {
    elementType,
    url: null,
    scale: 100,
    scaleFactor: defaultImageScaleFactor,
    block_version_id: getCurrentVersion(ElementType.IMAGE)
  }
}

export const generateOldDefaultImage = (element:IContentElementDynamicImage, condition:string) => {
  const image = { 
    elementType: ElementType.IMAGE,
    url: element.url, 
    fileType: element.fileType,
    altText: element.altText,
    scale: element.scale,
    outline: element.outline
  }
  return { condition: condition, image };
}

export const generateDefaultElementVideo = (elementType):IContentElementVideo => {
  return {
    elementType,
    url: null,
    urls: [],
    block_version_id: getCurrentVersion(ElementType.VIDEO)
  }
}


export const generateDefaultElementMcq = (elementType):IContentElementMcq => {
  return {
    elementType,
    displayStyle: McqDisplay.VERTICAL,
    options: [
      {elementType:ElementType.TEXT, optionType: ElementType.MCQ_OPTION, content: 'Option 1', isCorrect:false, optionId: 1, link: generateDefaultElementTextLink()},
      {elementType:ElementType.TEXT, optionType: ElementType.MCQ_OPTION, content: 'Option 2', isCorrect:true,  optionId: 2, link: generateDefaultElementTextLink()},
      {elementType:ElementType.TEXT, optionType: ElementType.MCQ_OPTION, content: 'Option 3', isCorrect:false, optionId: 3, link: generateDefaultElementTextLink()},
      {elementType:ElementType.TEXT, optionType: ElementType.MCQ_OPTION, content: 'Option 4', isCorrect:false, optionId: 4, link: generateDefaultElementTextLink()},
    ],
    block_version_id: getCurrentVersion(ElementType.MCQ)
  }
}

export const generateDefaultElementTextLink = ():IContentElementTextLink => {
  return { elementType: "text_link", caption: ""};
}

export const generateDefaultElementOrder = (elementType):IContentElementOrder => {
  return {
    elementType,
    displayStyle: McqDisplay.HORIZONTAL,
    isScrambled: false,
    delimeter: '',
    options: [
    ],
    block_version_id: getCurrentVersion(ElementType.ORDER)
  }
}

export const generateDefaultElementDnd = (elementType):IContentElementDnd => {
  return {
    elementType,
    targetType: DndTargetType.TARGET,
    defaultTargetStyle: null,
    draggableCounter:0,
    targetCounter:0,
    width: 360,
    height: 200,
    backgroundElements: [], 
    draggables: [],
    targets: [],
    groups:[],
    block_version_id: getCurrentVersion(ElementType.DND)
  } 
}

export const generateDefaultElementGrouping = (elementType) => {
  return {
    elementType,
    targetType: DndTargetType.TARGET,
    defaultTargetStyle: null,
    draggableCounter:0,
    targetCounter:0,
    width: 360,
    height: 200,
    draggables: [],
    targets: [],
    block_version_id: getCurrentVersion(ElementType.GROUPING)
  }
}

export const generateDefaultElementPassage = (elementType:string):IContentElementPassage => {
  return {
    elementType,
    text: '\n\n\n\n\n\n\n\n\n',
    images: [],
    counterType: PassageCounterType.LINE,
    lineCountInterval: PASSAGE_LIN_COUNT_INTERVAL_DEFAULT_SYS,
    isNoLineHeight: true,
    block_version_id: getCurrentVersion(ElementType.PASSAGE)
  }
}

export const generateDefaultElementStaticDiagram = (elementType:string):IContentElementStaticDiagram => {
  return {
    elementType,
    diagram: null,
    version: LATEST_DIAGRAMATICS_VERSION,
    mode: DiagramaticsMode.YAML,
    isGenerateOnTextChange: false,
    // isStoreAsSVG: false,
    imageSize: 1000,
    imageQuality: DiagramaticsImageQuality.MEDIUM,
    svgCode:
`- type: Diagram
  elements:
  - name : sq
    object : square
    methods:
    - method : fill
      params : [lightblue]`,
    isShowBackgroundConfiguration: false,
    isAddBackground: false,
    padding: { top: 3.0, right: 3.0, bottom: 3.0, left: 3.0, },
    boundingBox : {
        scaleWidth : 100,
        scaleHeight : 100,
    },
    block_version_id: getCurrentVersion(ElementType.STATIC_DIAGRAM)
  }
}

export const generateDefaultElementNumberline = (elementType:string):IContentElementNumberline => {
  return {
    elementType,
    min: 0,
    max: 10,
    target: 5,
    scoreWeight: 2,
    toleranceScoreMap: [{tolerance: 10, score: 2}, {tolerance: 20, score: 1}],
    style: {
      fontSize: 20,
      fontColor: '#000000'
    },
    scoringMethod: NUMBERLINE_SCORING_METHOD.ACCURACY_PERCENT,
    block_version_id: getCurrentVersion(ElementType.NUMBERLINE)
  }
}

export const generateDefaultElementScientificNotation = (elementType: string): IContentElementScientificNotation => {
  return {
    elementType,
    whole: "",
    fractional: "",
    exponent: "",
    block_version_id: getCurrentVersion(ElementType.SCIENTIFIC_NOTATION)
  }
}

export const generateDefaultElementTemplate = (elementType:string):IContentElementTemplate => {
  return {
    elementType,
    templateName: undefined, 
    templateId: undefined, // invalid values, indicate that a template needs to be selected
    templateVersion: undefined, // invalid values, indicate that a template needs to be selected
    content: [],
    configs: {},
    templateConfigs: [],
    migrationOptions: [],
    block_version_id: getCurrentVersion(ElementType.TEMPLATE)
  }
}

export const generateDefaultElementFrame = (elementType):IContentElementFrame => {
  return {
    elementType,
    content: [],
    // activatedStyles: [],
    // styleRaw:{},
    block_version_id: getCurrentVersion(ElementType.FRAME)
  }
}

export const generateDefaultElementCanvas = (elementType):IContentElementCanvas => {
  return {
    elementType,
    pages: [],
    width: 1,
    height: 1,
    block_version_id: getCurrentVersion(ElementType.CANVAS)
  }
}

export const generateDefaultElementInput = (elementType):IContentElementInput => {
  return {
    elementType,
    format: null,
    alternativeValues: [],
    ratioTerms: [],
    defaultText: "",
    block_version_id: getCurrentVersion(ElementType.INPUT)
  }
}
export const getElementChildrenText = (element:IContentElementText, config?: {includeVoiceover?: boolean}) => {
  const {includeVoiceover} = config || {};
  let elements = [];
  if (element.advancedList && Array.isArray(element.advancedList)){
    elements = elements.concat(element.advancedList.map(el => el));
  }

  if(element.link) {
    elements.push(element.link);
  }

  if(element.paragraphList) {
    elements = elements.concat(element.paragraphList.map(el => el));
  }

  if (includeVoiceover && element.voiceover) {
    elements.push(element.voiceover);
  }

  return elements;
}

export const getElementChildrenSelectText = (element:IContentElementTextSelection) => {
  const elements:IContentElement[] = [];
  for(const text of element.texts) {
    if(text.elementType) {
      elements.push(text);
    }
  }

  for(const p of (element.paragraphs || [])) {
    for(const par of p) {
      if(par.elementType) {
        elements.push(par);
      }
    }
  }
  return elements;
}
export const getElementChildrenTable = (element:IContentElementTable) => {
  const elements:IContentElement[] = [];
  if (element.grid){
    for(const row of element.grid) {
      for(const cell of row) {
        if (cell.elementType){
          elements.push(<IContentElement> cell);
        }
      }
    }
  }
  return elements;
}
export const getElementChildrenSbs = (element:IContentElementSbs) => {
  return element.left.concat(element.right);
}
export const getElementChildrenMcq = (element:IContentElementMcq) => {
  const elements:Array<IContentElement | IContentElementMcqTableColEachCell> = []
  if (element.options){
    element.options.forEach(option => {
      if (option.elementType){ // this is not handling the simple text properly
        elements.push(option);
      }
    })
  }
  if (element.tableCols){
    element.tableCols.forEach(col => {
      elements.push(col);
    })
  }
  return elements;
}
export const getElementChildrenOrder = (element:IContentElementOrder) => {
  const elements:IContentElement[] = []
  if (element.options){
    element.options.forEach(option => {
      if (option.elementType){ // this is not handling the simple text properly
        elements.push(option);
      }
    })
  }
  return elements;
}

export const getElementChildrenFrame = (element:IContentElementFrame)=>{
  return element.content.map( el => el )
}

export const getElementChildrenTemplate = (element:IContentElementTemplate)=>{
  // console.log('getting template children')
  const content = element.content || []; // content might be undefined
  return content.map( el => el )
}

export const getElementChildrenCanvas = (element:IContentElementCanvas)=>{
  const elements:IContentElement[] = []
  element.pages.forEach((page:IContentElementCanvasPage)=>{
    page.displayList.forEach(el => {
      if (el.elementType){
        elements.push(el);
      }
    });
  })
  return elements;
}

export const getElementChildrenSelectTable = (element:IContentElementSelectionTable)=> {
  const elements:IContentElement[] = []
  element.leftCol.forEach(cell => {
    if (cell.content && cell.content.elementType) {
      elements.push(cell.content)
    }
    if (cell.audio) {
      elements.push(cell.audio)
    }
  });

  for(const cell of element.topRow) {
    if(cell.content && cell.content.elementType) {
      elements.push(cell.content)
    }
    if(cell.audio) {
      elements.push(cell.audio);
    }
  }
  
  const propNames = ['mathCellElement', 'textCellElement', 'imgCellElement', 'mathCellSelectedElement', 'textCellSelectedElement', 'imgCellSelectedElement']; 
  for(const row of element.checkBoxRows) { 
    for(const col of row) {
      for(const prop of propNames) {
        if(col[prop]) {
          elements.push(col[prop]);
        }
      }
    }
  }

  if(element.topLeftText) {
    elements.push(element.topLeftText);
  }
  // console.log(elements);
  return elements
}

export const getElementChildrenImage = (element: any, config? : {includeVoiceover?:boolean}) => {
  const {includeVoiceover} = config
  const elements:IContentElement[] = [];
  //Could be IContentElementDynamicImage
  if(element.images) {
    for(const condImage of Object.values(element.images)) {
      if(condImage){
        const image = (<IContentElementConditionalImage>condImage).image;
        if(image.elementType) {
          elements.push(image);
        }
      }
    }
  }

  if(element.subtexts) {
    for(const subtext of element.subtexts) {
      if(subtext.elementType) {
        elements.push(<IContentElementImageSubText>subtext);
      }
    }
  }

  if(element.hiContrastImg) {
    elements.push(element.hiContrastImg);
  }

  if(element.frameState?.bgdImg) {
    elements.push(element.frameState.bgdImg);
  }

  if (includeVoiceover && element.voiceover) elements.push(element.voiceover)

  return elements;
}

export const getElementChildrenInsertion = (element: IContentElementInsertion) => {
  const elements: IContentElement[] = [];

  for(const draggable of element.draggables.concat(element.textBlocks)) {
    const el = draggable.element;
    if(el.elementType) {
      elements.push(el);
    }
  }

  return elements;
}

export const getElementChildrenMoveableDnd = (element:IContentElementMoveableDragDrop, config?: {includeDnDMoveableImages?:boolean, includeDnDTargets?:boolean, includeVoiceover?:boolean}) => {
  const {includeDnDMoveableImages, includeDnDTargets, includeVoiceover} = config
  const elements = [];
  for(const draggable of element.draggables) {
    const el = draggable.element;
    if(el.elementType) {
      elements.push(el);
    }
  }

  if (includeDnDMoveableImages){
    for(const el of element.moveableImages) {
      if(el.elementType) elements.push(el);
    }
  }

  if(element.backgroundImg) {
    elements.push(element.backgroundImg);
  }

  if (includeDnDTargets){
    for(const el of element.targets) {
      if(el.elementType) elements.push(el);
    }
  }
  
  if(includeVoiceover && element.draggables.length > 0) {
    for(const draggable of element.draggables) {
      if(draggable.voiceover) elements.push(draggable.voiceover);
    }
  }
    
  return elements;
}

export const getElementChildrenGrouping = (element:IContentElementGroup, config?: {includeVoiceover?:boolean}) => {
  const {includeVoiceover} = config;
  const elements: IContentElement[] = [];
  
  if(element.separatorText && element.separatorText.elementType) {
    elements.push(element.separatorText);
  }

  if(element.separatorImage && element.separatorImage.elementType) {
    elements.push(element.separatorImage);
  }

  if(element.separatorCanvas && element.separatorCanvas.elementType) {
    elements.push(element.separatorCanvas);
  }

  if(includeVoiceover && element.draggables.length > 0) {
    for(const draggable of element.draggables) {
      if(draggable.voiceover) elements.push(draggable.voiceover);
    }
  }

  if(includeVoiceover && element.targets.length > 0) {
    for(const target of element.targets) {
      if(target.voiceover) {
        elements.push(target.voiceover as unknown as IContentElement);
      }
    }
  }

  for(const draggable of element.draggables) {
    const el = draggable.element;
    if(el.elementType) {
      elements.push(el);
    }
  }  

  for(const target of element.targets) {
    const el = target.element;
    if(el && el.elementType) {
      elements.push(el);
    }
    const bgdImg = target.backgroundImg;
    if(bgdImg && bgdImg.elementType) {
      elements.push(bgdImg);
    }
  }
  return elements;
}

export const getElementChildrenResultsPrint = (element:IContentElementResultsPrint) => {
  const elements: IContentElement[] = [];
  for(const el of element.content) {
    if(el.elementType) {
      elements.push(el);
    }
  }
  return elements;
}

export const getElementChildrenSolution =(element:IContentElementSolution, config?: {includeVoiceover?:boolean}) => {
  const {includeVoiceover} = config
  const elements: (IContentElement | IVoiceover)[] = [];
  for(const el of element.content) {
    if(el.elementType) {
      elements.push(el);
    }
  }
  if (includeVoiceover && element.voiceover) elements.push(element.voiceover)
  return elements;
}

export const getElementChildrenCustomMcq = (element:IContentElementCustomMCQ) => {
  return getElementChildrenMcq(element);
}

export const getElementChildrenDnd = (element:IContentElementDnd) => {
  const elements: IContentElement[] = [];

  for(const bgdEl of element.backgroundElements) {
    const el = bgdEl.element;
    if(el.elementType) {
      elements.push(el);
    }
  }

  for(const draggable of element.draggables) {
    const el = draggable.element;
    if(el.elementType) {
      elements.push(el);
    }
  }

  //note: targets are not technically elements, so not included.

  return elements;
}

export const getElementChildrenInput = (element: IContentElementInput) => {
  if(element.alternativeValues) {
    return element.alternativeValues.map(el => el);
  }
  return [];
}

export const getElementChildrenTextLink = (element: IContentElementTextLink) => {
  const propNames = ['thumbNail', 'bookmarkImg'];
  const elements = [];

  for(const prop of propNames) {
    if(element[prop]) {
      elements.push(element[prop]);
    }
  }

  return elements;
}


export const getElementChildrenCustomMcqOption = (element: IContentElementCustomMcqOption) => {
  if(element.dynamicImage) {
    return [element.dynamicImage];
  }

  return [];
}

export const getElementChildrenOrderOption = (element: IContentElementOrderOption, config?: {includeVoiceover?:boolean}) => { 
  const {includeVoiceover} = config
  const elements = []
  if(element.labelImg) elements.push(element.labelImg)
  if (includeVoiceover && element.voiceover) elements.push(element.voiceover)
  return elements;
}

export const getElementChildrenMcqOption = (element: IContentElementMcqOption, config?: {includeVoiceover?:boolean}) => {
  const {includeVoiceover} = config
  let elements = []
  if(element.link) elements.push(element.link);
  if (includeVoiceover && element.voiceover) elements.push(element.voiceover)
  if (element.cols){
    elements = elements.concat(element.cols.map(el => el));
  }
  return elements;
}

export const getElementChildrenSelectableText = (element: IContentElementSelectableText) => {
  return [];
}

export const getElementChildrenInteractiveDiagram = (element: IContentElementInteractiveDiagram, config?: {includeVoiceover?:boolean}) => {
  const includeVoiceover = config?.includeVoiceover;
  
  const constructionElements = element.constructionElement;
  switch (constructionElements.constructionType) {
    // standard constructions
    case DgIntConstructionType.DND_TABLE:
      return getElementChildrenInteractiveDiagramTargets((<IDgIntElementDnDTable>constructionElements).dndTargets, config);
    case DgIntConstructionType.DND_INLINE_2:
      return getElementChildrenInteractiveDiagramTargets((<IDgIntElementDnDInline2>constructionElements).dndTargets, config);
    case DgIntConstructionType.DND_GROUP:
      return getElementChildrenInteractiveDiagramTargets((<IDgIntElementDnDGroup>constructionElements).dndTargets, config);
    case DgIntConstructionType.DND_FREEFORM:
      return getElementChildrenInteractiveDiagramTargets((<IDgIntElementDnDFreeform>constructionElements).dndTargets, config);
    case DgIntConstructionType.DND_VENN:
      return getElementChildrenInteractiveDiagramTargets((<IDgIntElementDnDVenn>constructionElements).dndTargets, config);
    case DgIntConstructionType.DND_INLINE:
      return getElementChildrenInteractiveDiagramTargets((<IDgIntElementDnDInline>constructionElements).dndTargets, config);
    case DgIntConstructionType.DND_CLOZE_MATH:
      return getElementChildrenInteractiveDiagramTargets((<IDgIntElementDnDClozeMath>constructionElements).dndTargets, config);
    // non standard constructions
    case DgIntConstructionType.DND_NUMBERLINE: {
      let elements = []
      const el = <IDgIntElementDnDNumberline>constructionElements;
      el.elements.forEach(numberlineEl => {
        if (numberlineEl.isOption && numberlineEl.voiceover && typeof numberlineEl.voiceover === 'object') {
          elements.push(numberlineEl.voiceover);
        }
      })
      return elements;
    }
    case DgIntConstructionType.DND_TALLY: {
      let elements = []
      const el = <IDgIntElementDnDTally>constructionElements;
      el.options.forEach(option => {
        if (option.voiceover && typeof option.voiceover === 'object') {
          elements.push(option.voiceover);
        }
        if (option.image) {
          elements.push(option.image)
        }
      })
      return elements
    }
    case DgIntConstructionType.DND_SORTING: {
      let elements = []
      const el = <IDgIntElementDnDSorting>constructionElements;
      el.options.forEach(option => {
        if (option.voiceover && typeof option.voiceover === 'object') {
          elements.push(option.voiceover);
        }
        if (option.image) {
          elements.push(option.image)
        }
      })
      el.extraOptions.forEach(option => {
        if (option.voiceover && typeof option.voiceover === 'object') {
          elements.push(option.voiceover);
        }
        if (option.image) {
          elements.push(option.image)
        }
      })
      return elements
    }
    case DgIntConstructionType.MCQ_HOTSPOT: {
      let elements = []
      const el = <IDgIntElementMcqHotspot>constructionElements;
      el.hotspots.forEach(hotspot => {
        if (hotspot.voiceover && typeof hotspot.voiceover === 'object') {
          elements.push(hotspot.voiceover);
        }
      })
      return elements;
    }
    default:
      return [];
  }
}
function getElementChildrenInteractiveDiagramTargets(dndTargets: IDgIntElementDnDTarget[], config?: {includeVoiceover?:boolean}) {
  const includeVoiceover = config?.includeVoiceover;
  let elements = []
  dndTargets.forEach((target) => {
    target.content.forEach((option) => {
      if (includeVoiceover && option.voiceover && typeof option.voiceover === 'object') {
        elements.push(option.voiceover);
      }
      if (option.image) {
        elements.push(option.image)
      }
    })
  })
  return elements;
}


export const elementIconById:Map<string, string> = new Map();
elementTypes.forEach(elementType => {
  elementIconById.set(elementType.id, elementType.icon)
});

export const createDefaultElementWithDims = (elementType:string):IContentElementLocAndDims => {
  return {
    x: 0,
    y: 0,
    width: 0,
    height: 0,
    padding: 0,
    ...createDefaultElement(elementType)
  }
}

export const createDefaultElement = (elementType:string):IContentElement => {
  switch(elementType){
    case ElementType.TEXT:     return generateDefaultElementText(elementType);
    case ElementType.TABLE:    return generateDefaultElementTable(elementType);
    case ElementType.MATH:     return generateDefaultElementMath(elementType);
    case ElementType.IMAGE:    return generateDefaultElementImage(elementType);
    case ElementType.MCQ:      return generateDefaultElementMcq(elementType);
    case ElementType.ORDER:    return generateDefaultElementOrder(elementType);
    case ElementType.DND:      return generateDefaultElementDnd(elementType);
    case ElementType.VIDEO:    return generateDefaultElementVideo(elementType);
    case ElementType.INPUT:    return generateDefaultElementInput(elementType);
    case ElementType.FRAME:    return generateDefaultElementFrame(elementType);
    case ElementType.GROUPING: return generateDefaultElementGrouping(elementType);
    case ElementType.MATCHING: return generateDefaultElementGrouping(elementType);
    case ElementType.PASSAGE: return generateDefaultElementPassage(elementType);
    case ElementType.TEMPLATE: return generateDefaultElementTemplate(elementType);
    case ElementType.CANVAS:   return generateDefaultElementCanvas(elementType);
    case ElementType.TABLE_TEXT: return generateDefaultElementTableText(elementType);
    case ElementType.SELECT_TEXT: return generateDefaultElementTextSelection(elementType);
    case ElementType.STATIC_DIAGRAM: return generateDefaultElementStaticDiagram(elementType);
    case ElementType.INTERACTIVE_DIAGRAM: return generateDefaultElementInteractiveDiagram(elementType);
    case ElementType.NUMBERLINE: return generateDefaultElementNumberline(elementType);
    case ElementType.MCQ_TABLE_TEXT:     return generateDefaultElementMcqTableText(elementType);
    case ElementType.MCQ_TABLE_COL:     return generateDefaultElementMcqTableCol(elementType);
    case ElementType.SCIENTIFIC_NOTATION: return generateDefaultElementScientificNotation(elementType);
    default: return { elementType }
  }
}

export const generateDefaultElementTableText = (elementType) => {
  return {
    elementType,
    val: '',
    block_version_id: getCurrentVersion(ElementType.TABLE_TEXT)
  }
}

export const generateDefaultElementMcqTableText = (elementType) => {
  return {
    elementType,
    content: '',
    block_version_id: getCurrentVersion(ElementType.MCQ_TABLE_TEXT)
  }
}

export const generateDefaultElementMcqTableCol = (elementType) => {
  return {
    elementType,
    label: '',
    block_version_id: getCurrentVersion(ElementType.MCQ_TABLE_COL)
  }
}
  
export const ensureDefaultElementProps = (element:any, elementType:string) => {
  const baseElement = createDefaultElement(elementType);
  element.elementType = elementType;
  Object.keys(baseElement).forEach(prop =>{
    if (!element[prop]){
      element[prop] = baseElement[prop]
    }
  })
}

export const frameElement = (content:any[], element:any) => {
  if (window.confirm('Frame this element?')){
    let i = indexOf(content, element);
    const replacementElement = <IContentElementFrame> createDefaultElement(ElementType.FRAME);
    replacementElement['x'] = element.x;
    replacementElement['y'] = element.y;
    replacementElement.content.push(element)
    content[i] = replacementElement;
  }
}

export const getAllTextElements = (elements:IContentElement[]) : string[] => {
  let allTexts = []
  if (elements) {
    elements.forEach(element => {
      if (element.elementType == ElementType.TEXT) {
        const el = <IContentElementText>element
        const style = el.paragraphStyle
        switch (style) {
          case TextParagraphStyle.LINK:
            console.log(el)
            if (el.link?.caption) allTexts = allTexts.concat(el.link.caption)
            break;
          case TextParagraphStyle.NUMBERED:
          case TextParagraphStyle.BULLET:
          case TextParagraphStyle.ADVANCED_INLINE:
            allTexts = allTexts.concat(getAllTextElements(el.advancedList))
            break;
          case TextParagraphStyle.PARAGRAPHS:
            el.paragraphList.forEach((word)=>{
              allTexts.push(word.caption)
            })
            break;
          case TextParagraphStyle.ANNOTATION:
            allTexts = allTexts.concat(getAllTextElements([el["text"]]))
            break;
          default: 
            allTexts.push(el.caption)
        }
        if (style==TextParagraphStyle.ADVANCED_INLINE) {
        } else if (style==TextParagraphStyle.PARAGRAPHS) {
          
        } else if (el.paragraphStyle==TextParagraphStyle.ANNOTATION) {
          
        } else {
          
        }
        
      } else {
        const childrenText = getElementChildren(element, {includeDnDMoveableImages: true, includeDnDTargets: true, includeSolution: true, includeVoiceover: true})
        allTexts = allTexts.concat(getAllTextElements(childrenText))
      }
    });
  }
  return allTexts
}

export const getElementChildren = (element:IContentElement, config: IElementChildrenConfig = {}) : IContentElement[] => {
  const {includeSolution, includeResultsPrint, includeDnDMoveableImages, includeDnDTargets, includeVoiceover} = config
  let elements = [];
  
  switch(element.elementType){
    case ElementType.TEXT:          elements = getElementChildrenText   (<IContentElementText>   element, {includeVoiceover}); break;
    case ElementType.TEXT_LINK:     elements = getElementChildrenTextLink (<IContentElementTextLink> element); break;
    case ElementType.TABLE:         elements = getElementChildrenTable  (<IContentElementTable>  element); break;
    case ElementType.MCQ:           elements = getElementChildrenMcq    (<IContentElementMcq>    element); break;
    case ElementType.ORDER:         elements = getElementChildrenOrder  (<IContentElementOrder>  element); break;
    case ElementType.SBS:           elements = getElementChildrenSbs    (<IContentElementSbs>    element); break;
    case ElementType.SELECT_TABLE:  elements = getElementChildrenSelectTable (<IContentElementSelectionTable> element); break;
    case ElementType.CANVAS:        elements = getElementChildrenCanvas (<IContentElementCanvas> element); break;
    case ElementType.FRAME:         elements = getElementChildrenFrame  (<IContentElementFrame>  element); break;
    case ElementType.TEMPLATE:      elements = getElementChildrenTemplate(<IContentElementTemplate>  element); break;
    case ElementType.SELECT_TEXT:   elements = getElementChildrenSelectText (<IContentElementTextSelection> element); break;
    case ElementType.INSERTION:     elements = getElementChildrenInsertion (<IContentElementInsertion> element); break;
    case ElementType.IMAGE:         elements = getElementChildrenImage (<IContentElementImage> element, {includeVoiceover}); break;
    case ElementType.MOVEABLE_DND:  elements = getElementChildrenMoveableDnd (<IContentElementMoveableDragDrop> element, {includeDnDMoveableImages, includeDnDTargets, includeVoiceover}); break;
    case ElementType.GROUPING:      elements = getElementChildrenGrouping(<IContentElementGroup> element, {includeVoiceover}); break;
    case ElementType.CUSTOM_MCQ:    elements = getElementChildrenCustomMcq(<IContentElementCustomMCQ> element); break;
    case ElementType.RESULTS_PRINT: elements = (includeResultsPrint ? getElementChildrenResultsPrint(<IContentElementResultsPrint> element) : []); break;
    case ElementType.SOLUTION:      elements = (includeSolution ? getElementChildrenSolution(<IContentElementSolution> element, {includeVoiceover}) : []); break;
    case ElementType.DND:           elements = getElementChildrenDnd (<IContentElementDnd> element); break;     
    case ElementType.INPUT:         elements = getElementChildrenInput(<IContentElementInput> element); break;
    case ElementType.INTERACTIVE_DIAGRAM: elements = getElementChildrenInteractiveDiagram(<IContentElementInteractiveDiagram> element, {includeVoiceover}); break;
    default: elements = []; 
  }

  const optionType = (<any>element).optionType;
  if(optionType) {
    let optionElements = [];
    switch(optionType) {
      case ElementType.CUSTOM_MCQ_OPTION:
        optionElements = getElementChildrenCustomMcqOption(<IContentElementCustomMcqOption>element); break;
      case ElementType.MCQ_OPTION:
        optionElements = getElementChildrenMcqOption(<IContentElementMcqOption>element, {includeVoiceover}); break;
      case ElementType.SELECTABLE_TEXT:
        optionElements = getElementChildrenSelectableText(<IContentElementSelectableText>element); break;
      case ElementType.ORDER_OPTION:
        optionElements = getElementChildrenOrderOption(<IContentElementOrderOption>element, {includeVoiceover}); break;
      default:
        break;
    }

    elements = elements.concat(optionElements);
  }
  return elements;
}

export const checkElementIsEntry = (element:IContentElement, isAutoScoreable?:boolean, typePropName = 'elementType') : boolean => {
  if (element["isScoringDisabled"] && element[typePropName] && String(element[typePropName]).startsWith(ElementType.INPUT)) {
    return false
  }
  if (element[typePropName]==ElementType.INPUT && (
    element["format"]==InputFormat.NUMBER ||
    element["format"]==InputFormat.NUMBER_LIST ||
    element["format"]==InputFormat.RATIO ||
    element["format"]==InputFormat.ALGEBRA ||
    element["format"]==InputFormat.FRACTION)) {
    return true;
  } else if (
    element[typePropName]==ElementType.INPUT+'-'+InputFormat.NUMBER ||
    element[typePropName]==ElementType.INPUT+'-'+InputFormat.NUMBER_LIST ||
    element[typePropName]==ElementType.INPUT+'-'+InputFormat.ALGEBRA ||
    element[typePropName]==ElementType.INPUT+'-'+InputFormat.FRACTION ||
    element[typePropName]==ElementType.INPUT+'-'+InputFormat.RATIO
  ) {
    return true;
  } else if (
    element[typePropName]==DG_INT_MATCHING_TYPE.GROUP ||
    element[typePropName]==DG_INT_MATCHING_TYPE.SINGLE ||
    element[typePropName]==DG_INT_MATCHING_TYPE.MCQ ||
    element[typePropName]==DG_INT_TALLY_TYPE.SINGLE ||
    element[typePropName]==DG_INT_TALLY_TYPE.MULTIPLE
  ) {
    // interactive diagram
    return true;
  } else if (
    element[typePropName]=='input-scientific-notation'
  ) {
    return true;
  }
  switch(element[typePropName]){
    case ElementType.CAMERA:
    case ElementType.GRAPHING:
    case ElementType.UPLOAD:
    case ElementType.MIC:
    case ElementType.VIRTUAL_TOOLS:
    return !isAutoScoreable;
    case ElementType.INPUT:
    return !isAutoScoreable;
    case ElementType.DND:
    case ElementType.MOVEABLE_DND:
    case ElementType.MCQ:
    case ElementType.CUSTOM_MCQ:
    // case ElementType.HOTSPOT:   //The hotspot is a sub (option) type within the mcq block, so I don't think it should be a scorable entry by itself.
    case ElementType.HOTTEXT:
    case ElementType.MATCHING:
    case ElementType.SELECT_TEXT:
    case ElementType.ORDER:
    case ElementType.INSERTION:
    case ElementType.SELECT_TABLE:
    case ElementType.GROUPING:
      if (element["isScoringDisabled"]) {
        return false
      }
    case ElementType.VALIDATOR:
    case ElementType.CUSTOM_INTERACTION:
    case ElementType.NUMBERLINE:
    case ElementType.SCORE_ENTRY:
    case ElementType.INTERACTIVE_DIAGRAM:
    case ElementType.SCIENTIFIC_NOTATION:
    return true;
    default: 
    return false;
  }
}



export const applyDefaultElementProps = (target:any, elementType:string) => {
  const ref = createDefaultElement(elementType);
  Object.keys(ref).forEach(key => {
    target[key] = ref[key];
  })
}

export const special2DGridCaseFields = [
  {elementType: ElementType.TABLE, field: 'grid'}
]

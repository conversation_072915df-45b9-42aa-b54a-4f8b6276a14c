import { Component, OnInit, Input, Output } from '@angular/core';

// app services
import { AssetLibraryService, IAssetLibraryConfig} from '../services/asset-library.service';
import { AssignedUsersService } from '../assigned-users.service';
import { AuthScopeSettingsService, AuthScopeSetting } from "../auth-scope-settings.service";
import { AuthService } from '../../api/auth.service';
import { DataGuardService } from '../../core/data-guard.service';
import { EditingDisabledService } from '../editing-disabled.service';
import { ItemComponentEditService } from '../item-component-edit.service';
import { ItemMakerService } from '../item-maker.service';
import { LangService } from '../../core/lang.service';
import { LoginGuardService } from '../../api/login-guard.service';
import { PrintAltTextService } from '../print-alt-text.service';
import { PrintModeService } from '../print-mode.service';
import { RoutesService } from '../../api/routes.service';
import { ScriptGenService } from '../script-gen.service';
import { StyleprofileService } from '../../core/styleprofile.service';
import { WhitelabelService } from '../../domain/whitelabel.service';


import { ItemSetPreviewCtrl } from '../item-set-editor/controllers/preview';
import { ItemBankSaveLoadCtrl } from '../item-set-editor/controllers/save-load';
import { AssetLibraryCtrl } from '../item-set-editor/controllers/asset-library';
import { ItemSetFrameworkCtrl } from '../item-set-editor/controllers/framework';
import { ItemBankAuditor } from '../item-set-editor/controllers/audits';
import { ItemEditCtrl } from '../item-set-editor/controllers/item-edit';
import { ItemBankCtrl } from '../item-set-editor/controllers/item-bank';
import { MemberAssignmentCtrl } from '../item-set-editor/controllers/member-assignment';
import { ItemFilterCtrl } from '../item-set-editor/controllers/item-filter';
import { PanelCtrl } from '../item-set-editor/controllers/mscat';
import { ItemSetPrintViewCtrl } from '../item-set-editor/controllers/print-view';
import { ItemSetPublishingCtrl } from '../item-set-editor/controllers/publishing';
import { FrameworkQuadrantCtrl } from '../item-set-editor/controllers/quadrants';
import { TestFormGen } from '../item-set-editor/controllers/testform-gen';
import { TestletCtrl } from '../item-set-editor/controllers/testlets';
import { ItemBankUtilCtrl } from '../item-set-editor/controllers/util';
import { FormControl } from '@angular/forms';
import { IAssessmentPartition, getFormPartitionObj, getPartitionPropValue } from '../item-set-editor/models/assessment-framework';
import { partition } from 'cypress/types/lodash';
import { EventEmitter } from '@angular/core';
import { indexOf } from '../services/util';

export const getPartitionIndex = (partitions: IAssessmentPartition[], currentPartition: IAssessmentPartition) => {
  // This is so that when you move sections up and down, the order stays descending. Previously we would match the
  // number to the partition id which is made upon creation.
  return partitions.findIndex(partition => partition.id === currentPartition.id) + 1;
}

@Component({
  selector: 'widget-linear-form-construction',
  templateUrl: './widget-linear-form-construction.component.html',
  styleUrls: ['./widget-linear-form-construction.component.scss']
})
export class WidgetLinearFormConstructionComponent implements OnInit {
  
  
  @Input() assetLibraryCtrl:AssetLibraryCtrl
  @Input() frameworkCtrl:ItemSetFrameworkCtrl
  @Input() auditCtrl:ItemBankAuditor
  @Input() itemBankCtrl:ItemBankCtrl
  @Input() itemEditCtrl:ItemEditCtrl
  @Input() itemFilterCtrl:ItemFilterCtrl
  @Input() memberAssignmentCtrl:MemberAssignmentCtrl
  @Input() panelCtrl:PanelCtrl
  @Input() previewCtrl:ItemSetPreviewCtrl
  @Input() printViewCtrl: ItemSetPrintViewCtrl
  @Input() publishingCtrl: ItemSetPublishingCtrl
  @Input() quadrantCtrl: FrameworkQuadrantCtrl
  @Input() saveLoadCtrl:ItemBankSaveLoadCtrl
  @Input() testFormGen: TestFormGen
  @Input() testletCtrl: TestletCtrl
  @Output() openSectionEditModal = new EventEmitter();
  @Output() openQuestionBulkLockModal = new EventEmitter();
  
  public util = new ItemBankUtilCtrl();
  getPartitionIndex = getPartitionIndex;
  
  constructor(
    private editingDisabled: EditingDisabledService,
    public lang: LangService,
    public profile: StyleprofileService
    ) { }

  ngOnInit(): void {
    this.checkIdLabelMapping()
  }

  isReadOnly = () => this.editingDisabled.isReadOnly(true);

  isBuckets(){
    return this.frameworkCtrl.isTestBuckets();
  }

  checkIdLabelMapping(){
    const changesMade = []
    const asmtFmrk = this.frameworkCtrl.asmtFmrk;
    for (let partition of asmtFmrk.partitions){
      const {questions} = this.frameworkCtrl.getSectionQuestionsLinear(asmtFmrk, partition);
      for (let question of questions){
        if (question.id){
          const questionRef = this.itemBankCtrl.getQuestionById(question.id)
          // console.log('checkIdLabelMapping', questionRef)
          if (questionRef && question.label !== questionRef.label){
            changesMade.push({from:question.label, to: questionRef.label})
            question.label = questionRef.label
          }
        }
      }
    }
    if (changesMade.length > 0){
      console.log('labelChangesToSave', changesMade)
      alert('It looks like some of the item labels for items that were assigned to the form have changed. These have been updated in the form and reflected in the framework the next time you save. List is available in the console');
    }
  }

  removeElement(content:any[], element:any){
    if (window.confirm('Remove this option?')){
      let i = indexOf(content, element);
      if (i !== -1){
        content.splice(i, 1)
      }
    }
  }

  getConditionOnOptionVal = (partition: IAssessmentPartition) => +(this.getPartitionPropValue(partition, 'conditionOnOption')) + 1 

  updatePartitionProp = (partition: IAssessmentPartition, prop: keyof IAssessmentPartition, forceReplace?: boolean, onlyForLang?: string) => {
    this.util.replaceProp(this.getFormPartitionObj(partition), prop, forceReplace, onlyForLang)
  }

  getPartitionPropValue = <K extends keyof IAssessmentPartition>(
    partition: IAssessmentPartition,
    prop: K
  ): IAssessmentPartition[K] => this.frameworkCtrl.getPartitionPropValue(partition, prop);

  getFormPartitionObj = (partition: IAssessmentPartition) => this.frameworkCtrl.getFormPartitionObj(partition);

  questionBulkLockModalEmit() {
    this.openQuestionBulkLockModal.emit();
  }

  getItemNumber(questionIdentifier: any) {
    return questionIdentifier['QN'];
  }

  asmtFmrkContainsRefDocs(){
    return this.frameworkCtrl.asmtFmrk.referenceDocumentPages && this.frameworkCtrl.asmtFmrk.referenceDocumentPages.length > 0;
  }
}

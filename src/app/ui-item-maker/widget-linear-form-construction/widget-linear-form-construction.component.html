<div >
    <div class="pre-table-strip">
      <div>
        <button 
          class="button  has-icon" 
          (click)="previewCtrl.previewLinearTestForm();" 
        >
          <span class="icon"><i class="fa fa-desktop" aria-hidden="true"></i> </span>
          <span><tra slug="ie_view_as_test_taker"></tra></span>
      </button>
      <button
          [attr.disabled]="isReadOnly() ? 'disabled' : null"
          class="button  has-icon" 
          (click)="printViewCtrl.gotoLinearPrintView();" 
        >
          <span class="icon"><i class="fa fa-print" aria-hidden="true"></i> </span>
          <span><tra slug="auth_print_view"></tra></span>
      </button>
      <button 
          class="button  has-icon" 
          [class.is-info]="itemFilterCtrl.activeTestFormFilter"
          (click)="itemBankCtrl.activateLinearFormQuestions();" 
        >
          <span class="icon"><i class="fa fa-filter" aria-hidden="true"></i> </span>
          <span><tra slug="auth_filter_assessment_to_used_list"></tra></span>
      </button>
      <button 
        *ngIf="itemBankCtrl.isQLockAvail()"
        class="button has-icon is-info"
        [class.is-outlined]="itemBankCtrl.hasUnlockedQuestion()"
        (click)="questionBulkLockModalEmit()"
      >
        <span class="icon">
          <i 
            class="fas" 
            [class.fa-lock-open]="itemBankCtrl.hasUnlockedQuestion()" 
            [class.fa-lock]="!itemBankCtrl.hasUnlockedQuestion()"
          ></i>
        </span>
        <span style="padding-left: 3em; padding-right: 4em;"><tra slug="All Items"></tra></span>
      </button>
    </div>
      </div>
    <div *ngIf="isBuckets()">
      Bucket Param: 
      <input [(ngModel)]="frameworkCtrl.asmtFmrk.bucketParam" style="width:6em;">
    </div>
    <div style="display:flex; flex-direction:row; ">
      <div style="max-height:90vh; overflow:auto">
        <table class="table is-bordered" style="width:auto; margin-bottom:2em;" cdkDropListGroup>
          <tr>
            <th style="width: 18em; max-width:20em"><tra slug="ie_section"></tra></th>
            <th style="width: 32em;"><tra slug="ie_questions"></tra></th> <!-- ensure wide enough for wider labels -->
          </tr>
          <tr *ngFor="let partition of frameworkCtrl.asmtFmrk.partitions; let index = index">
            <td>
              <div class="space-between">
                <div>
                  <a [class.is-disabled]="isReadOnly()" (click)="updatePartitionProp(partition, 'description')">{{getPartitionPropValue(partition, 'description') || '(Description)'}}</a>
                  <span *ngIf="getPartitionPropValue(partition, 'isConditional')">Path {{ getConditionOnOptionVal(partition) }}</span>
                </div>
                <div style="display: flex; flex-direction: row; justify-content: flex-end">
                  <!-- passing simple partition as section-edit-modal already get the right partition lang object to avoid circular deps -->
                  <button class="button is-small" (click)="openSectionEditModal.emit(partition)"><i class="fas fa-ellipsis-h"></i></button>
                  <code>
                    {{getPartitionIndex(frameworkCtrl.asmtFmrk.partitions, partition)}}
                  </code>
                </div>
              </div>
              <twiddle caption="Options" (change)="getFormPartitionObj(partition).isConfigExpanded = $event"></twiddle>
              <div *ngIf="getPartitionPropValue(partition, 'isConfigExpanded')">
                <button (click)="util.moveArrElUp(partition, frameworkCtrl.asmtFmrk.partitions)"><tra slug="auth_move_up"></tra></button>
                <button (click)="util.moveArrElDown(partition, frameworkCtrl.asmtFmrk.partitions)"><tra slug="auth_move_down"></tra></button>
                <div  class="section-settings-container">
                  <div class="section-setting"><span class="first-el"><tra slug="ie_preamble"></tra>:</span> <a [class.is-disabled]="isReadOnly()" (click)="updatePartitionProp(partition, 'preambleQuestionLabel', true)"> {{getPartitionPropValue(partition, 'preambleQuestionLabel') || lang.tra('ie_no_preamble')}} </a></div>
                  <div class="section-setting"><span class="first-el">Module Map Item Meta:</span> <a [class.is-disabled]="isReadOnly()" (click)="updatePartitionProp(partition, 'mapMetaItemId', true)"> {{getPartitionPropValue(partition, 'mapMetaItemId') || '(No Map Meta Surrogate)'}} </a></div>
                  <div class="section-setting"><span class="first-el">Info Caption:</span> <a [class.is-disabled]="isReadOnly()" (click)="updatePartitionProp(partition, 'infoCaption', true)"> {{getPartitionPropValue(partition, 'infoCaption') || '(No Caption)'}} </a></div>
                  <div class="section-setting"><span class="first-el">Submission Text: <a [class.is-disabled]="isReadOnly()" (click)="updatePartitionProp(partition, 'submissionText', true)"> <tra [slug]="getPartitionPropValue(partition, 'submissionText') || 'btn_review_submit'"></tra> </a></span></div>
                  <div class="section-setting"><span class="first-el">Notepad Text: <a [class.is-disabled]="isReadOnly()" (click)="updatePartitionProp(partition, 'notepadText', true)"> {{getPartitionPropValue(partition, 'notepadText') || '(None)'}} </a></span></div>
                  <div class="section-setting"><span class="first-el">Image:</span> <a [class.is-disabled]="isReadOnly()" (click)="updatePartitionProp(partition, 'preambleThumbnail', true)"> {{getPartitionPropValue(partition, 'preambleThumbnail') || '(No url)'}} </a></div>
                  <div class="section-setting"><span class="first-el">EN Thumbnail:</span> <a [class.is-disabled]="isReadOnly()" (click)="updatePartitionProp(partition, 'sidebarThumbnailEn', true)"> {{getPartitionPropValue(partition, 'sidebarThumbnailEn') || '(No url)'}} </a></div>
                  <div class="section-setting"><span class="first-el">FR Thumbnail:</span> <a [class.is-disabled]="isReadOnly()" (click)="updatePartitionProp(partition, 'sidebarThumbnailFr', true)"> {{getPartitionPropValue(partition, 'sidebarThumbnailFr') || '(No url)'}} </a></div>
                  <div class="section-setting"><span class="first-el"><tra slug="auth_image_text"></tra></span> <a [class.is-disabled]="isReadOnly()" (click)="updatePartitionProp(partition, 'preambleThumbnailText', true)"> {{getPartitionPropValue(partition, 'preambleThumbnailText') || '(No translation slug)'}} </a></div>
                  <div class="section-setting"><span class="first-el"><tra slug="auth_image_seleted"></tra></span> <a [class.is-disabled]="isReadOnly()" (click)="updatePartitionProp(partition, 'preambleThumbnailSelected', true)"> {{getPartitionPropValue(partition, 'preambleThumbnailSelected') || '(No url)'}} </a></div>
                  <div class="section-setting"><check-toggle class="first-el" [disabled]="isReadOnly()" [isChecked]="getPartitionPropValue(partition, 'isShuffled')" (toggle)="getFormPartitionObj(partition).isShuffled = !getFormPartitionObj(partition).isShuffled"></check-toggle><span><tra slug="ie_shuffle_order_q"></tra></span>        </div>
                  <div class="section-setting"><check-toggle class="first-el" [disabled]="isReadOnly()" [isChecked]="getPartitionPropValue(partition, 'isCalculatorEnabled')" (toggle)="getFormPartitionObj(partition).isCalculatorEnabled = !getFormPartitionObj(partition).isCalculatorEnabled"></check-toggle> <span><tra slug="ie_enable_calculator_q"></tra></span>    </div>
                  <div class="section-setting"><check-toggle class="first-el" [disabled]="isReadOnly()" [isChecked]="getPartitionPropValue(partition, 'isNotepadDisabled')" (toggle)="getFormPartitionObj(partition).isNotepadDisabled = !getFormPartitionObj(partition).isNotepadDisabled"></check-toggle> <span>Disable Notepad?</span>    </div>
                  <div class="section-setting"><check-toggle class="first-el" [disabled]="isReadOnly()" [isChecked]="getPartitionPropValue(partition, 'isFormulaSheetEnabled')" (toggle)="getFormPartitionObj(partition).isFormulaSheetEnabled = !getFormPartitionObj(partition).isFormulaSheetEnabled"></check-toggle> <span><tra slug="ie_enable_formula_sheet_q"></tra></span> </div>
                  <div class="section-setting"><check-toggle class="first-el" [disabled]="isReadOnly()" [isChecked]="getPartitionPropValue(partition, 'isTimeLimit')" (toggle)="getFormPartitionObj(partition).isTimeLimit = !getFormPartitionObj(partition).isTimeLimit"></check-toggle> <span><tra slug="ie_apply_time_limit_q"></tra></span> </div>
                  <div class="section-setting-sub" *ngIf="getPartitionPropValue(partition, 'isTimeLimit')">
                    <a [class.is-disabled]="isReadOnly()" (click)="updatePartitionProp(partition, 'timeLimitMinutes', true)"> {{getPartitionPropValue(partition, 'timeLimitMinutes') || '--'}} </a> minutes
                  </div>
                  <!-- #TODO: check this is not in partition but at the framework level  -->
                  <div class="section-setting"><check-toggle class="first-el" [disabled]="isReadOnly()" [isChecked]="getPartitionPropValue(partition, 'isShuffled')" (toggle)="partition.useQuestionLabel = !partition.useQuestionLabel"></check-toggle><span><tra slug="auth_use_item_label"></tra></span>        </div>
                  <div class="section-setting"><check-toggle [disabled]="isReadOnly()" class="first-el" [isChecked]="getPartitionPropValue(partition, 'isConditional')" (toggle)="getFormPartitionObj(partition).isConditional = !getFormPartitionObj(partition).isConditional"></check-toggle> <span><tra slug="ie_activate_by_decision_q"></tra></span> </div>
                  <div class="section-setting-sub" *ngIf="getPartitionPropValue(partition, 'isConditional')">
                    <tra slug="ie_item_id"></tra>: <a [class.is-disabled]="isReadOnly()" (click)="updatePartitionProp(partition, 'conditionOnItem', true)"> {{getPartitionPropValue(partition, 'conditionOnItem') || '--'}} </a>,
                    Option Index: <a [class.is-disabled]="isReadOnly()" (click)="updatePartitionProp(partition, 'conditionOnOption', true)"> {{getPartitionPropValue(partition, 'conditionOnOption') || '--'}} </a>
                  </div>
                  <div class="section-setting"><check-toggle class="first-el" [disabled]="isReadOnly()" [isChecked]="getPartitionPropValue(partition, 'disableScoring')" (toggle)="getFormPartitionObj(partition).disableScoring = !getFormPartitionObj(partition).disableScoring"></check-toggle> <span><tra slug="auth_disable_scoring"></tra></span> </div>
                  <div class="section-setting"><check-toggle class="first-el" [disabled]="isReadOnly()" [isChecked]="getPartitionPropValue(partition, 'disableFlagging')" (toggle)="getFormPartitionObj(partition).disableFlagging = !getFormPartitionObj(partition).disableFlagging"></check-toggle> <span><tra slug="auth_disable_flagging"></tra></span> </div>
                  <div class="section-setting"><check-toggle class="first-el" [disabled]="isReadOnly()" [isChecked]="getPartitionPropValue(partition, 'disableLeftBar')" (toggle)="getFormPartitionObj(partition).disableLeftBar = !getFormPartitionObj(partition).disableLeftBar"></check-toggle> <span><tra slug="auth_disable_left_bar"></tra></span> </div>
                  <div class="section-setting">
                    <div>
                      <tra slug="auth_entry_warning"></tra>
                      <button *ngIf="!getPartitionPropValue(partition, 'msgReqFill')" (click)="getFormPartitionObj(partition).msgReqFill='new message'"><tra slug="auth_override"></tra></button>
                      <div><textarea *ngIf="getPartitionPropValue(partition, 'msgReqFill')" [(ngModel)]="getFormPartitionObj(partition).msgReqFill"></textarea></div>
                    </div> 
                  </div>
                  <div class="section-setting"><check-toggle class="first-el" [disabled]="isReadOnly()" [isChecked]="getPartitionPropValue(partition, 'customSectionPopup')" (toggle)="getFormPartitionObj(partition).customSectionPopup = !getFormPartitionObj(partition).customSectionPopup"></check-toggle> <span>Custom Section Popup</span> </div>
                  <div *ngIf="getPartitionPropValue(partition, 'customSectionPopup')">
                    <div class="section-setting">
                        <div [class.no-pointer-events]="isReadOnly()" class="select">
                          <select [(ngModel)]="getFormPartitionObj(partition).customSectionPopupSlug">
                            <option [value]=undefined></option>
                            <option *ngFor="let option of profile.getSectionPopupSlugs()" [value]="option">
                              {{option}}
                            </option>
                          </select>
                        </div>
                    </div>          
                  </div>  
                  <div class="section-setting"><check-toggle class="first-el" [disabled]="isReadOnly() || !asmtFmrkContainsRefDocs()" [isChecked]="getPartitionPropValue(partition, 'autoOpenRefDocs')" (toggle)="getFormPartitionObj(partition).autoOpenRefDocs = !getFormPartitionObj(partition).autoOpenRefDocs"></check-toggle> <span><tra slug="Auto Open Documents?"></tra></span> </div>
                </div>
                <div>
                  <button [disabled]="isReadOnly()" class="button is-small is-danger" (click)="frameworkCtrl.removeSectionFromTestForm(partition)"><tra slug="auth_discard_section"></tra> {{getPartitionIndex(frameworkCtrl.asmtFmrk.partitions, partition)}}</button>
                </div>
              </div>
            </td>


            <td >
              <ng-container *ngIf="!isBuckets()">
                <div 
                  cdkDropList  
                  [cdkDropListDisabled]="isReadOnly()"
                  [cdkDropListData]="frameworkCtrl.getSectionQuestionsLinear(frameworkCtrl.asmtFmrk, partition).questions"
                  (cdkDropListDropped)="util.dropBtwn($event)"
                >    
                <div 
                *ngFor="let questionIdentifier of frameworkCtrl.getSectionQuestionsLinear(frameworkCtrl.asmtFmrk, partition).questions" 
                cdkDrag 
                class="section-question-row"
                [class.is-question-nav-open]="itemBankCtrl.isCurrentQuestionNavigationOpen(questionIdentifier)"
                >
                <button [disabled]="isReadOnly()" [class.no-pointer-events]="isReadOnly()" class="stack-edit-element-header" cdkDragHandle>
                  <div class="icon"><i class="fa fa-bars" aria-hidden="true"></i></div>
                </button>
                <div class="question-label">
                  {{questionIdentifier.label}}
                  <code *ngIf="getItemNumber(questionIdentifier)">
                    {{getItemNumber(questionIdentifier)}}
                  </code>
                </div>
                <button [disabled]="isReadOnly()" (click)="frameworkCtrl.removeQuestionFromTestform(questionIdentifier, partition)" [title]="lang.tra('ie_remove_item_from_tf')" class="button is-danger">
                  <i class="fa fa-minus" aria-hidden="true"></i>
                </button>
                <button 
                  (click)="itemBankCtrl.openQuestionNavigation(questionIdentifier)" 
                  [title]="lang.tra('ie_view_question')" 
                  class="button"
                  [class.is-info]="itemBankCtrl.isQuestionSetForNavigation(questionIdentifier)"
                >
                  <i class="fa fa-compass" aria-hidden="true"></i>
                </button>
                <button (click)="itemBankCtrl.selectSectionQuestion(questionIdentifier)" [title]="lang.tra('ie_view_question')" class="button " [class.is-info]="itemBankCtrl.isSectionQuestionSelected(questionIdentifier) && !itemBankCtrl.isBulkLocking">
                  <i class="fa fa-eye" aria-hidden="true"></i>
                </button>
              </div>
                </div>
                <div style="margin:0.5em 0em;">
                  <button [disabled]="isReadOnly()" class="button has-icon is-info is-small is-fullwidth is-outlined" (click)="itemBankCtrl.selectSection(partition)" *ngIf="!itemBankCtrl.isSelectedSection(partition)">
                    <span class="icon"> <i class="fa fa-circle-thin" style="color:#ccc;" aria-hidden="true" ></i> </span>
                    <span> <tra slug="ie_add_question_to_sec"></tra> </span>
                  </button>
                  <button [disabled]="isReadOnly()" class="button has-icon is-small is-fullwidth is-info " (click)="frameworkCtrl.scrollToQuestionListing()" *ngIf="itemBankCtrl.isSelectedSection(partition)">
                    <span class="icon">
                      <i class="fa fa-plus-circle" aria-hidden="true" ></i>
                    </span>
                    <span> <tra slug="ie_adding_question_to_sect"></tra> {{getPartitionIndex(frameworkCtrl.asmtFmrk.partitions, partition)}} </span>
                  </button>
                </div>
              </ng-container>
              <ng-container *ngIf="isBuckets()">
                <button *ngIf="!getFormPartitionObj(partition).buckets" (click)="getFormPartitionObj(partition).buckets = []">Initialize buckets</button>
                <div *ngIf="getFormPartitionObj(partition).buckets">
                  <div *ngFor="let bucket of getFormPartitionObj(partition).buckets; let index=index;">
                    <input [(ngModel)]="bucket.label" (change)="frameworkCtrl.refreshBucketCount(bucket)" placeholder="Bucket Label" style="width:12em;">
                    <span class="tag is-info">{{bucket.count}} items</span>
                    <button class="button is-small is-danger" (click)="removeElement(getFormPartitionObj(partition).buckets, bucket)">remove</button>
                  </div>
                </div>
                <div style="margin:0.5em 0em;" >
                  <button (click)="getFormPartitionObj(partition).buckets.push({})" class="button">Create Bucket</button>
                </div>
              </ng-container>  
            </td>
      </tr>
    </table>
    </div>
    <div class="section-question-navigation" *ngIf="itemBankCtrl.currentQuestionNavigation">
      <h4>
        Dropdown Navigation
        <span>({{itemBankCtrl.currentQuestionNavigation.label}})</span>
      </h4>
      <div class="config-container">
        <div class="flex-row control center">
          <div class="flex-col">
            <span>Set as Anchor</span>
          </div>
          <div class="flex-col">
            <input 
              type="checkbox" 
              [(ngModel)]="itemBankCtrl.currentQuestionNavigation.isAnchor" 
              (change)="itemBankCtrl.setQuestionNavigationAsAnchor($event.target.checked, itemBankCtrl.currentQuestionNavigation)"
            >
          </div>
        </div>
        <div *ngIf="itemBankCtrl.currentQuestionNavigation.isAnchor">
          <div class="flex-row control center">
            <div class="flex-col">
              <span>Anchor Label (EN)</span>
            </div>
            <div class="flex-col">
              <input 
                class="input" 
                type="text" 
                style="width:15em; text-align:center" 
                (change)="itemBankCtrl.setQuestionNavigationAsAnchor(true, itemBankCtrl.currentQuestionNavigation)"
                [formControl]="itemBankCtrl.currentQuestionNavigationLabel"
              >
            </div>
          </div>
          <div class="flex-row control center">
            <div class="flex-col">
              <span>Anchor Label (FR)</span>
            </div>
            <div class="flex-col">
              <input 
                class="input" 
                type="text" 
                style="width:15em; text-align:center" 
                (change)="itemBankCtrl.setQuestionNavigationAsAnchor(true, itemBankCtrl.currentQuestionNavigation)"
                [formControl]="itemBankCtrl.currentQuestionNavigationLabelFr"
              >
            </div>
          </div>
          <div class="flex-row control center">
            <div class="flex-col">
              <span>Lock By</span>
            </div>
            <div class="flex-col">
              <div class="select is-small">
                <select 
                  [(ngModel)]="itemBankCtrl.currentQuestionLockBy" 
                  (change)="itemBankCtrl.cleanQuestionNavigationState()"
                >
                  <option [value]="null" selected>None</option>
                  <option value="section">Section Id</option>
                  <option value="item">Item Label</option>
                  <option value="choice">Choice Id</option>
                </select>
              </div>
            </div>
          </div>
          <div *ngIf="itemBankCtrl.currentQuestionLockBy" [ngSwitch]="itemBankCtrl.currentQuestionLockBy">
            <div class="flex-row control center" *ngSwitchCase="'section'">
              <div class="flex-col">
                <span>Section ID</span>
              </div>
              <div class="flex-col">
                <input 
                  class="input" 
                  type="text" 
                  (change)="itemBankCtrl.setQuestionNavigationAsAnchor(true, itemBankCtrl.currentQuestionNavigation)"
                  [(ngModel)]="itemBankCtrl.currentQuestionLockBySectionId"
                >
              </div>
            </div>
            <div class="flex-col control center" *ngSwitchCase="'item'">
              <div class="flex-col" style="padding-bottom: .5em">
                <span>Item Labels</span>
              </div>
              <div class="flex-col">
                <div *ngFor="let item of itemBankCtrl.currentQuestionLockByItems; let index = index; trackBy: itemBankCtrl.trackByIndex;">
                  <div class="flex-row item-label" *ngIf="itemBankCtrl.currentQuestionLockByItems[index]">
                    <input 
                      class="input is-small" 
                      placeholder="Item Label" 
                      type="text" 
                      style="width:15em; text-align:center" 
                      [(ngModel)]="itemBankCtrl.currentQuestionLockByItems[index].label"
                      disabled
                    >
                    <button class="button is-small is-danger" (click)="itemBankCtrl.removeQuestionLockyByItemLabel(index, itemBankCtrl.currentQuestionNavigation)">
                      <tra slug="ie_delete_param"></tra>
                    </button>
                  </div>
                </div>
              </div>
              <div class="flex-col">
                <div class="flex-row item-label">
                  <input 
                      class="input is-small" 
                      placeholder="Item Label" 
                      type="text" 
                      style="width:15em; text-align:center" 
                      [formControl]="itemBankCtrl.currentQuestionLockByNewItemLabel"
                  >
                  <button class="button is-small" (click)="itemBankCtrl.addQuestionLockByItemLabel(itemBankCtrl.currentQuestionLockByNewItemLabel, itemBankCtrl.currentQuestionNavigation)">
                    <tra slug="ie_add_item_bank"></tra>
                  </button>
                </div>
              </div>
            </div>
            <div class="flex-col control center" *ngSwitchCase="'choice'">
              <div class="flex-row center">
                <div class="flex-col">
                  <span>Item Label</span>
                </div>
                <div class="flex-col">
                  <input 
                    class="input" 
                    type="text" 
                    [formControl]="itemBankCtrl.currentQuestionLockByNewItemLabel"
                    (change)="itemBankCtrl.addQuestionLockByItemLabelToChoiceLock(itemBankCtrl.currentQuestionLockByNewItemLabel, itemBankCtrl.currentQuestionNavigation)"
                    [(ngModel)]="itemBankCtrl.currentQuestionLockByItem && itemBankCtrl.currentQuestionLockByItem.label"
                  >
                </div>
              </div>
              <div class="flex-row center">
                <div class="flex-col">
                  <span>Entry ID</span>
                </div>
                <div class="flex-col">
                  <input 
                    class="input" 
                    type="text" 
                    (change)="itemBankCtrl.setQuestionNavigationAsAnchor(true, itemBankCtrl.currentQuestionNavigation)"
                    [(ngModel)]="itemBankCtrl.currentQuestionLockByEntryId"
                  >
                </div>
              </div>
              <div class="flex-row center">
                <div class="flex-col">
                  <span>Choice ID</span>
                </div>
                <div class="flex-col">
                  <input 
                    class="input" 
                    type="text" 
                    (change)="itemBankCtrl.setQuestionNavigationAsAnchor(true, itemBankCtrl.currentQuestionNavigation)"
                    [(ngModel)]="itemBankCtrl.currentQuestionLockByChoiceId"
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="section-question-review" *ngIf="itemBankCtrl.isFormQuestionSelected() && !itemBankCtrl.isBulkLocking">
      <h4>
        <tra slug="ie_item_preview"></tra>
        <button class="button is-small" (click)="itemBankCtrl.switchToEditorView()"><tra slug="ie_edit"></tra></button>
      </h4>
      <question-runner 
        [currentQuestion]="itemBankCtrl.getCurrentQuestionContent()" 
        [questionState]="itemBankCtrl.activeQuestionState"
        [isSubmitted]="itemEditCtrl.isLocked" 
        [isPrintMode]="printViewCtrl.isResultsPrint"
      ></question-runner>
    </div>
    </div>
    <div style="margin-top:1em;">
      <button [disabled]="isReadOnly()" (click)="frameworkCtrl.createNewSection()" class="button is-success"><tra slug="ie_create_new_section"></tra></button>
      <button [disabled]="isReadOnly()" class="button has-icon" [class.is-success]="saveLoadCtrl.isSaveChangedRequired" (click)="saveLoadCtrl.saveChanges()">
        <span class="icon"><i class="fa fa-floppy-o" aria-hidden="true"></i></span>
        <span><tra slug="ie_save_changes"></tra></span>
      </button> 
    </div>
    <hr/>

</div>
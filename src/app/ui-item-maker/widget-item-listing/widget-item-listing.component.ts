import {Component, ElementRef, Input, On<PERSON><PERSON>roy, OnInit, ViewChild} from '@angular/core';

// app services
import {EditingDisabledService} from '../editing-disabled.service';


import {ItemSetPreviewCtrl} from '../item-set-editor/controllers/preview';
import {ItemBankSaveLoadCtrl} from '../item-set-editor/controllers/save-load';
import {AssetLibraryCtrl} from '../item-set-editor/controllers/asset-library';
import {ItemSetFrameworkCtrl} from '../item-set-editor/controllers/framework';
import {ItemBankAuditor} from '../item-set-editor/controllers/audits';
import {ItemEditCtrl} from '../item-set-editor/controllers/item-edit';
import {ItemBankCtrl} from '../item-set-editor/controllers/item-bank';
import {MemberAssignmentCtrl} from '../item-set-editor/controllers/member-assignment';
import {ItemFilterCtrl} from '../item-set-editor/controllers/item-filter';
import {PanelCtrl} from '../item-set-editor/controllers/mscat';
import {ItemSetPrintViewCtrl} from '../item-set-editor/controllers/print-view';
import {ItemSetPublishingCtrl} from '../item-set-editor/controllers/publishing';
import {FrameworkQuadrantCtrl} from '../item-set-editor/controllers/quadrants';
import {TestFormGen} from '../item-set-editor/controllers/testform-gen';
import {TestletCtrl} from '../item-set-editor/controllers/testlets';
import {ItemBankUtilCtrl} from '../item-set-editor/controllers/util';
import {ItemType} from '../models';
import {
  DimensionType,
  IAssessmentFrameworkDimensionDetail,
  IAssessmentParameterViewFilter
} from "../item-set-editor/models/assessment-framework";
import { LangService } from '../../core/lang.service';
import { IItemTag } from '../item-tag/item-tag.component';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { AuthScopeSettingsService } from '../auth-scope-settings.service';
import { ItemParameterType, PARAM_SPECIAL_FLAGS } from '../framework-dimension-editor/model';
import { IHistoricalItemRegisterModelTw, IHistoricalTd } from 'src/app/ui-testrunner/models';
import { Subscription } from 'rxjs';
import { getBatchAllocID, getBatchAllocItemSlug, getHistoricalTdSelected, getHumanScored, getHistoricalPropVal, isHistoricalTdSelected } from '../services/util';

@Component({
  selector: 'widget-item-listing',
  templateUrl: './widget-item-listing.component.html',
  styleUrls: ['./widget-item-listing.component.scss']
})
export class WidgetItemListingComponent implements OnInit, OnDestroy {

  
  @Input() assetLibraryCtrl:AssetLibraryCtrl
  @Input() frameworkCtrl:ItemSetFrameworkCtrl
  @Input() auditCtrl:ItemBankAuditor
  @Input() itemBankCtrl:ItemBankCtrl
  @Input() itemEditCtrl:ItemEditCtrl
  @Input() itemFilterCtrl:ItemFilterCtrl
  @Input() memberAssignmentCtrl:MemberAssignmentCtrl
  @Input() panelCtrl:PanelCtrl
  @Input() previewCtrl:ItemSetPreviewCtrl
  @Input() printViewCtrl: ItemSetPrintViewCtrl
  @Input() publishingCtrl: ItemSetPublishingCtrl
  @Input() quadrantCtrl: FrameworkQuadrantCtrl
  @Input() saveLoadCtrl:ItemBankSaveLoadCtrl
  @Input() testFormGen: TestFormGen
  @Input() testletCtrl: TestletCtrl
  @ViewChild('tagInput') tagInput: ElementRef;

  @ViewChild('questionListing', { static: false }) questionListingEl: ElementRef;
  
  public util = new ItemBankUtilCtrl();

  ItemType = ItemType
  showSavedFilters: boolean = false;
  excludeHidden: boolean = false;
  activeParameterTypes:Map<ItemParameterType, boolean> = new Map();
  ItemParameterType = ItemParameterType;
  subscription: Subscription = new Subscription();
  
  parameterTypes = [
    {slug: ItemParameterType.STRUCT,    caption: 'Structural'},
    {slug: ItemParameterType.CONTENT,    caption: 'Content'},
    {slug: ItemParameterType.HISTORICAL, caption: 'Historical I.R.'},
    {slug: ItemParameterType.STATS,      caption: 'Psych. Stats'},
  ]

  constructor(
    private editingDisabled: EditingDisabledService,
    public lang: LangService,
    private authScopeSettings: AuthScopeSettingsService,
    private whitelabel: WhitelabelService
  ) { }

  ngOnInit(): void {
    this.activeParameterTypes.set(ItemParameterType.STRUCT, true);
    this.activeParameterTypes.set(ItemParameterType.CONTENT, true);
    // As of ********, the single implementation of this component is currently in widget-framework-main, and this component is not loaded until itemBankCtrl.itemList is initialized, so this is safe to run
    this.reloadExpectedAnswers(); 
    this.subscription.add(this.lang.languageInitialized.subscribe(() => this.reloadExpectedAnswers()));
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
  }

  isReadOnly = () => this.editingDisabled.isReadOnly(true);

  isParamTypeActive(paramTypeSlug:ItemParameterType){
    return !! this.activeParameterTypes.get(paramTypeSlug)
  }
  toggleParamTypeActive(paramTypeSlug:ItemParameterType){
    this.activeParameterTypes.set(paramTypeSlug, !this.isParamTypeActive(paramTypeSlug) )
  }

  getHistoricalItemProps(item_id:number, td_slug:string, propSlug:string) : number | string | null {
    const byTdSlug = this.itemBankCtrl.historicalItemRegister.get(item_id);
    if (byTdSlug){
      const itemParams = byTdSlug.get(td_slug);
      if (itemParams){
        return itemParams[propSlug]
      }
    }
    return;
  }

  getHistoricalTw(){
    return this.itemBankCtrl.itemRegisterSummary.test_windows;
  }

  getHistoricalTdSelected(){
    return getHistoricalTdSelected(this.itemBankCtrl);
  }

  isHistoricItemInTd(item_id:number, tdSlug:string){
    const item = this.itemBankCtrl.historicalItemRegister.get(item_id);
    if (item){
      const tdItemProps = item.get(tdSlug)
      if (tdItemProps){
        return true
      }
    }
    return false;
  }

  getHistoricalPropVal(item_id:number, tdSlug:string, propSlug:string){
    return getHistoricalPropVal(item_id, tdSlug, propSlug, this.itemBankCtrl);
  }

  isHistoricalTdSelected(td_slug:string){
    return isHistoricalTdSelected(td_slug, this.itemBankCtrl)
  }

  toggleHistoricalTdSelection(td_slug:string){
    const selected_td_slugs = this.itemBankCtrl.itemRegisterSummary.selected_td_slugs;
    selected_td_slugs.set(td_slug, !selected_td_slugs.get(td_slug));
  }

  toggleHistoricalTwSelection(tw:IHistoricalItemRegisterModelTw){
    tw.is_selected = !tw.is_selected;
    // then check to ensure that all remaining selected test designs are from available test windows
    const selected_td_slugs = this.itemBankCtrl.itemRegisterSummary.selected_td_slugs;
    for (let td of tw.test_designs){
      selected_td_slugs.set(td.slug, false);
    }
  }

  selectedTW: {
    title: string;
    isSelected: boolean;
    testDesigns: Map<number, {
        slug: string;
        psychStats: any;
        isSelected: boolean;
    }>;
  };
  selectedTD: {
    slug: string;
    psychStats: any;
    isSelected: boolean;
}
  togglePsychStatTwSelection(tw_id: number) {
    const selectedTW = this.itemBankCtrl.historicalPsychStatSummary.get(tw_id);
    const isSelected = !selectedTW.isSelected;
    this.selectedTW = null;
    this.selectedTD = null;

    if(isSelected) {
      this.itemBankCtrl.historicalPsychStatSummary.forEach((val) => {
        val.isSelected = false;
      });
      this.selectedTW = this.itemBankCtrl.historicalPsychStatSummary.get(tw_id);
    }

    selectedTW.isSelected = isSelected;
  }

  togglePsychStatTdSelection(td_id: number) {
    const selectedTD = this.selectedTW.testDesigns.get(td_id);
    const isSelected = !selectedTD.isSelected;
    this.selectedTD = null;

    if(isSelected) {
      this.selectedTW.testDesigns.forEach((td) => {
        td.isSelected = false;
      });
      this.selectedTD = this.selectedTW.testDesigns.get(td_id);
    }

    selectedTD.isSelected = isSelected;
  }

  filterParams(param: IAssessmentFrameworkDimensionDetail) {
    const prefix = this.getParamPrefix(param);
    const isLangIndependent = param.config.special && param.config.special[PARAM_SPECIAL_FLAGS.LANG_INDEPENDENT];
    if (param.type === DimensionType.NUMERIC) {
      this.util.replaceRangeProp(this.itemFilterCtrl.filterSettings, prefix+param.code, true, isLangIndependent ? this.lang.c() : undefined);  
    } else {
      this.util.replaceProp(this.itemFilterCtrl.filterSettings, prefix+param.code, true, isLangIndependent ? this.lang.c() : undefined);
    }
    this.itemFilterCtrl.updateItemFilter();    
  }

  renderFilter(param: IAssessmentFrameworkDimensionDetail): string {
    const prefix = this.getParamPrefix(param);
    let filterValue = this.itemFilterCtrl.filterSettings[prefix+param.code]; 
    if(param.config.special && param.config.special[PARAM_SPECIAL_FLAGS.LANG_INDEPENDENT]) {
      filterValue = filterValue?.val;
    }
    if (param.type === DimensionType.NUMERIC) {
      if (typeof filterValue !== 'undefined') {
        const values = filterValue.split('->');
        if (values.length === 2) {
          if (values[0] !== '' && values[1] !== '') {
            filterValue = filterValue.replace('->','-');
          } else if (values[0] === '') {
            filterValue = filterValue.replace('->',' < ');
          } else {
            filterValue = filterValue.replace('->',' > ');
          }
        }
      }
    }
    return filterValue;
  }

  getParamPrefix(param: IAssessmentFrameworkDimensionDetail) {
    if(param.config.special && param.config.special[PARAM_SPECIAL_FLAGS.LANG_INDEPENDENT]) {
      if(this.lang.c() === 'fr') {
        return 'langLink.meta.'
      }
    }
    return 'meta.'
  }

  async reloadExpectedAnswers(){
    await this.itemBankCtrl.loadExpectedAnswers()
    this.frameworkCtrl.calculateTestFormParams();
  }

  saveFilter() {
    const filterName = prompt(`Name this filter`);
    if (filterName) {
      let savedFilters: IAssessmentParameterViewFilter[] = this.frameworkCtrl.asmtFmrk.filters;
      if (!savedFilters) {
        savedFilters = [];
        this.frameworkCtrl.asmtFmrk.filters = savedFilters;
      }
      const filter: IAssessmentParameterViewFilter = {
        name: filterName,
        filter: Object.assign({},this.itemFilterCtrl.filterSettings)
      }
      savedFilters.push(filter);
      this.showSavedFilters = true;
      this.saveLoadCtrl.saveChanges();
    }
  }

  removeSavedFilter(index: number) {
    const filters = this.frameworkCtrl.asmtFmrk.filters;
    if (filters && filters.length) {
      filters.splice(index, 1);
    }
    this.saveLoadCtrl.saveChanges();
  }
  
  applySavedFilter(savedFilter: IAssessmentParameterViewFilter) {
    this.itemFilterCtrl.filterSettings = Object.assign({},savedFilter.filter);
    this.itemFilterCtrl.updateItemFilter();    
  }
  
  viewSavedFilters() {
    this.showSavedFilters = !this.showSavedFilters; 
  }

  hasSavedFilters() {
    const filters = this.frameworkCtrl.asmtFmrk.filters;
    return filters && filters.length;
  }
  
  paramsOrDimensionsExist(): boolean {
    const exist = 
      !!(this.frameworkCtrl.asmtFmrk.primaryDimensions && this.frameworkCtrl.asmtFmrk.primaryDimensions.length) ||
      !!(this.frameworkCtrl.asmtFmrk.secondaryDimensions && this.frameworkCtrl.asmtFmrk.secondaryDimensions.length);
    return exist;
  }


getParamDisplay(param) {
  return param.isHidden || !this.isViewable(param) ? 'none' : ''
}

addTag(tag: IItemTag) {
    this.itemFilterCtrl.addTag(tag);
    this.tagInput.nativeElement.blur();
  }

  isViewable(param) {
    return this.authScopeSettings.isItemParamViewable(param)
  }

  /** Prompt user for filter value for a given column, then apply it */
  addNewUserFilter(filterObj, filterProp:string){
    this.util.replaceProp(filterObj, filterProp, true);
    this.itemFilterCtrl.updateItemFilter();
  }

  getItemNumber(questionToFind: any) {
    this.itemBankCtrl.getItemNumber(questionToFind)
  }

  getResponseEntries(questionToFind: any) {
    const sectionItems = this.frameworkCtrl.asmtFmrk.sectionItems;
    if(!sectionItems) {
      return;
    }

    let responseEntry: string | null = null;

    Object.keys(sectionItems).forEach(key => {
      sectionItems[key].questions.forEach(question => {
        if ('label' in question && 'id' in question && question.id == questionToFind.id) {
          responseEntry = question['response_entry_types'];
        }
      })
    });

    return responseEntry;
  }

  getHumanScored(qId: number) {
    return getHumanScored(qId, this.itemBankCtrl, this.lang);
  }

  getBatchAllocID(qId: number) {
    return getBatchAllocID(qId, this.itemBankCtrl);
  }

  getBatchAllocItemSlug(qId: number) {
    return getBatchAllocItemSlug(qId, this.itemBankCtrl);
  }

  getHistoricalTDColor(td_id: number) {
    const selectedTD = this.getHistoricalTdSelected();
    const colorMap = {}

    selectedTD.forEach((td, i) => {
      const color = this.getIndexColor(i, true);
      colorMap[td.td_id] = color;
    })

    return colorMap[td_id] || null;
  }

  getIndexColor(i: number, isToggle = false) {
    if(i == 0) {
      return isToggle ? '#e3e3e3' : 'whitesmoke'
    } else if(i == 1) {
      return '#ffdc88'
    } else if(i == 2) {
      return '#a3fa72'
    }
    
    return '#fca5a1';
  }

  getTextColor(i: number, questionId: number, propSlug: string) {
    if(i == 0 || !this.getHistoricalTdSelected() || !this.getHistoricalTdSelected().length) {
      return null;
    }
    const firstSelected = this.getHistoricalTdSelected()[0];
    const currSelected = this.getHistoricalTdSelected()[i];

    if(!firstSelected || !currSelected) {
      return null;
    }

    const firstSelProp = this.getHistoricalPropVal(questionId, firstSelected.slug, propSlug)
    const currSelProp = this.getHistoricalPropVal(questionId, currSelected.slug, propSlug)
    if(firstSelProp != currSelProp) {
      return 'red';
    }

    return null;
  }

  showWorkflowColumns() {
    return !this.whitelabel.getSiteFlag('AUTH_IS_HIDE_WORKFLOW_COLUMNS')
  }
}

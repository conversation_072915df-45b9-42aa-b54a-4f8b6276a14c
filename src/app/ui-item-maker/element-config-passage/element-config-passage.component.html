<p>
    Enter your passage below. Lines will flow based on number on the left (line returns must be made explicitly). 
</p>

<div *ngIf="problematicLines && problematicLines.length>0">
    The following lines are too long
    <code *ngFor="let problem of problematicLines">{{problem.lineNumber}}</code>
    <button (click)="autoReflow()">Auto-Fix</button>
</div>

<div class="code-mirror-sizer">
    <ngx-codemirror
      #codeeditor
      textInputTransform [codemirrorInstance]="codeMirror" 
      [(ngModel)]="element.text"
      [options]="editorConfig"
      (drop)="onImageDrop($event)"
    ></ngx-codemirror>
</div>

<p>
    The following tags are supported: 
    <code>&lt;b&gt;</code>,
    <code>&lt;i&gt;</code>,
    <code>&lt;sc&gt;</code> (for small caps),
    <code>&lt;c&gt;</code> (center a line),
    <code>&lt;skip/&gt;</code> (to skip line count),
    <code>&lt;bookmark id="x"&gt;</code> (so that other items can reference the passage).
</p>

<div>
    Count by 
    <select [(ngModel)]="element.counterType" (change)="updateChangeCounter()">
        <option *ngFor="let counterType of counterTypes" [value]="counterType">{{counterType}}</option>
    </select>
    <div *ngIf="element.counterType == 'LINE'">
        every <input type="number" [(ngModel)]="element.lineCountInterval" (change)="updateChangeCounter()"> lines
        <label style="font-size:0.8em">
            (<input type="checkbox" [(ngModel)]="element.isLineCountSkipBlank" (change)="updateChangeCounter()"> skip blank lines?)
        </label>
    </div>
    <div>
        Align: <select [(ngModel)]="element.counterAlignment" (change)="updateChangeCounter()">
            <option>(default)</option>
            <option value="left">left</option>
            <option value="right">right</option>
        </select>
    </div>
    <div style="margin-top: 0.5em">
        <label>
            <input type="checkbox"  [(ngModel)]="element.isNoLineHeight"> Superscripts and subscripts shouldn't affect line height
        </label>
    </div>   
    <div>
      <span>Column break after line:&nbsp;</span>
      <input type="number" style="max-width: 5em;" 
        [(ngModel)]="element.columnBreakPos" (input)="detectMaxWidthExceedingLines();updateChangeCounter()">
    </div>
    <div>
      <label>
          <input type="checkbox" [(ngModel)]="element.isFitContentWidth" (change)="updateChangeCounter()">
          <span> Set width to fit content</span>
      </label>
    </div>
    <div>
      <label>
          <input type="checkbox" [(ngModel)]="element.isDropCap" (change)="ensureDropCapStyle(); updateChangeCounter()">
          <span> Enable Drop Cap</span>
      </label>
    </div>
    <div *ngIf="element.isDropCap">
      <label>
          <span> Drop Cap Style </span>
          <select [(ngModel)]="element.dropCapStyle" (change)="updateChangeCounter()">
            <option value="dropped">Dropped</option>
            <option value="in-margin">In Margin</option>
          </select>
      </label>
    </div>
    <div *ngIf="element.isDropCap">
      <label>
          <span> Drop Cap Size: </span>
          <input type="number" [(ngModel)]="element.dropCapSize" (change)="updateChangeCounter()">
      </label>
    </div>
</div>

<div style="margin-top: 0.5em">
    <label>
        <input type="checkbox"  [(ngModel)]="element.isTransparentBackground"><tra slug="auth_transparent_background"></tra>
    </label>
</div>   

<div style="margin-top:1em; padding:0.5em; border: 1px solid #ccc;">
    <button class="button" (click)="insertParagraphStyle()">Define Paragraph Style</button>
    <div *ngIf="element.paragraphStyles">
        <div *ngFor="let paragraphStyle of element.paragraphStyles">
            <hr/>
            <code>&lt;pstyle id="{{paragraphStyle.id}}"/&gt;</code>
            <ul>
                <li *ngFor="let tab of paragraphStyle.tabs">
                    tab spacing <input type="number" [(ngModel)]="tab.sizeEm" (change)="updateChangeCounter()"/>
                </li>
            </ul>
            <button class="button is-small" (click)="addPsTab(paragraphStyle)">Add Tab Indent</button>
        </div>
    </div>
</div>


<div style="margin-top:1em; padding:0.5em; border: 1px solid #ccc;">
    <!-- <button class="button" (click)="insertImage()">Apply Bookmark</button> -->
    <button class="button" (click)="insertImage()">Insert Image</button>
    <div *ngIf="element.images">
        <div *ngFor="let image of element.images">
            <hr/>
            <code>&lt;img id="{{image.id}}"&gt;</code>
            <div>Align: <select [(ngModel)]="image.alignment" (change)="updateChangeCounter()">
                <option></option>
                <option>left</option>
                <option>right</option>
                <option>center</option>
                <option>none</option>
            </select></div>
            <element-config-image [element] = "image.el" (change)="updateChangeCounter()"></element-config-image>
        </div>
    </div>
</div>


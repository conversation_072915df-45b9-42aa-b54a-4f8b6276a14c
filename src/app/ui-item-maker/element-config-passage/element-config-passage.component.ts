import { Component, ElementRef, Input, OnInit, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { AuthScopeSetting, AuthScopeSettingsService } from '../auth-scope-settings.service';
import { EditingDisabledService } from '../editing-disabled.service';
import { IContentElementPassage, PassageCounterType } from '../../ui-testrunner/element-render-passage/model';
import * as CodeMirror from 'codemirror';
import { generateDefaultElementImage } from '../item-set-editor/models';


export const PASSAGE_WIDTH = 38;
export const DEFAULT_PSTYLE_TAB = 4;
export const HALF_PASSAGE_WIDTH = PASSAGE_WIDTH/2 - 2;

@Component({
  selector: 'element-config-passage',
  templateUrl: './element-config-passage.component.html',
  styleUrls: ['./element-config-passage.component.scss']
})
export class ElementConfigPassageComponent implements OnInit, <PERSON><PERSON><PERSON><PERSON> {

  @ViewChild('codeeditor') private codeEditor;

  @Input() element: IContentElementPassage;

  editorConfig = { 
    lineWrapping: false,
    smartIndent: false,
    indentWithTabs: false,
    lineNumbers: true,
    // mode: 'passage' // todo: implement more precise tagging for xml
    mode: 'xml' 
  };

  counterTypes = [
    PassageCounterType.LINE,
    PassageCounterType.PARAGRAPH,
    PassageCounterType.NONE,
  ]

  codeMirror:{cm: any}; // CodeMirror
  problematicLines:{lineNumber:number}[] = [];

  constructor(
    private authScopeSettings: AuthScopeSettingsService,
    private editingDisabled: EditingDisabledService
  ) { }

  ngOnInit(): void {

    // todo: shouldnt this be getting re-initialized at each step like this
    // CodeMirror.defineMode("passage", function(config, parserConfig) {
    //   var customOverlay = {
    //       token: function(stream, state) {
    //           var ch;
    //           if (stream.match("<img")) {
    //               while ((ch = stream.next()) != null)
    //                   if (ch == ">" && !stream.eat(">")) break;
    //               return "tag";
    //           }
    //           if (stream.match("<bookmark")) {
    //               while ((ch = stream.next()) != null)
    //                   if (ch == ">" && !stream.eat(">")) break;
    //               if (stream.match(" id=")) return "attribute";
    //               return "tag";
    //           }
    //           while (stream.next() != null && !stream.match("<img", false) && !stream.match("<bookmark", false)) {}
    //           return null;
    //       }
    //   };
    //   return CodeMirror.overlayMode(CodeMirror.getMode(config, parserConfig.backdrop || "text/html"), customOverlay);
    // });

  }

  ngAfterViewInit(){
    const editor = this.codeEditor.codeMirror;
    this.codeMirror = editor.getDoc();
    this.codeMirror.cm.on('change', (instance) => {
      this.detectMaxWidthExceedingLines()
    });
  }

  ngOnDestroy(): void {
  }
  
  isTwoColumn(){
    return !!this.element.columnBreakPos;
  }

  detectMaxWidthExceedingLines(){
    const lines = this.element.text.split('\n');
    this.problematicLines = []
    this.withWidthtester(checkLine => {
      for (let i=0; i<lines.length; i++){
        const line = lines[i]
        const isOverWidth = checkLine(line)
        if (isOverWidth){
          this.problematicLines.push({lineNumber: i+1})
        }
      }
    })
  }

  autoReflow(){
    const MAX_TRIES = 100000;
    const lines = this.element.text.split('\n');
    this.withWidthtester(checkLine => {
      let _whileTotal = 0;
      for (let i=0; i<lines.length; i++){
        _whileTotal ++;
        let isOverWidth = false;
        let isCuttingExhausted = false;
        for (let _while=0; _while<MAX_TRIES; _while++){
          _while ++;
          isOverWidth = checkLine(lines[i])
          if (isOverWidth){
            const lineWords = lines[i].split(' ');
            if (lineWords.length < 2){
              isCuttingExhausted = true
            }
            else {
              const lastWord = lineWords.pop();
              if (lines[i+1] && lines[i+1].trim() === ''){ // if the next line is already a blank line, add a new line underneath before sticking spill-over words into it.
                lines.splice(i+1,0, '')
              }
              lines[i+1] = lines[i+1] || ''; // creating the next line if it does not yet exist
              lines[i+1] = lastWord + ' ' + lines[i+1]
              lines[i] = lineWords.join(' ')
            }
            if (_while > MAX_TRIES-10){
              console.log('1::_while', lineWords.length, lines.length)
            }
          }
          if (_whileTotal > MAX_TRIES){
            break;
          }
          if (!isOverWidth || isCuttingExhausted){
            console.log('2::_while', _while, lines.length)
            break;
          }
        }
        if (_whileTotal > MAX_TRIES){
          break;
        }
      }
    })
    this.element.text = lines.join('\n')
    this.detectMaxWidthExceedingLines()
  }

  private withWidthtester( runChecks:(checkLine:(line:string)=>boolean)=>void ){
    let columnWidth = `${PASSAGE_WIDTH}em`;
    if (this.isTwoColumn()) columnWidth = `${HALF_PASSAGE_WIDTH}em`;
    
    const testContainer = document.createElement('div');
    document.body.appendChild(testContainer);
    testContainer.style.width = columnWidth;
    testContainer.style.position = 'fixed';
    testContainer.style.visibility = 'hidden';
    testContainer.style.whiteSpace = 'pre';
    testContainer.style.padding = '0em';
    testContainer.innerText = '-'
    const maxWidthPx = testContainer.clientWidth;
    testContainer.style.width = 'auto'
    const checkLine = (line:string) => {
      testContainer.innerText = line
      const widthPx = testContainer.clientWidth;
      return (widthPx > maxWidthPx)
    }
    runChecks(checkLine);
    document.body.removeChild(testContainer);
  }


  getNextImageId(){
    let id = 1;
    for (let image of this.element.images){
      if (+image.id >= id){
        id = (+image.id)+1
      }
    }
    return id
  }

  getNextParagraphStyleId(){
    let id = 1;
    for (let ps of this.element.paragraphStyles){
      if (+ps.id >= id){
        id = (+ps.id)+1
      }
    }
    return id
  }

  insertParagraphStyle(){
    if (!this.element.paragraphStyles){
      this.element.paragraphStyles = [];
    }
    const id = this.getNextParagraphStyleId();
    this.element.paragraphStyles.push({
      id,
      tabs: [{sizeEm: DEFAULT_PSTYLE_TAB}]
    })
  }

  addPsTab(paragraphStyle:{tabs:{sizeEm:number}[]}){
    paragraphStyle.tabs.push({sizeEm: DEFAULT_PSTYLE_TAB})
  }

  insertImage(){
    if (!this.element.images){
      this.element.images = [];
    }
    const id = this.getNextImageId();
    this.element.images.push({
      id,
      el: generateDefaultElementImage('image')
    })
  }

  updateChangeCounter(){
    this.element._changeCounter = (this.element._changeCounter || 0) +1
  }

  onImageDrop($event){
    
  }
  
  ensureDropCapStyle() {
    if (this.element.isDropCap) {
      if (!this.element.dropCapStyle) this.element.dropCapStyle = 'dropped';
      if (!this.element.dropCapSize) this.element.dropCapSize = 3;
    }
  }

}

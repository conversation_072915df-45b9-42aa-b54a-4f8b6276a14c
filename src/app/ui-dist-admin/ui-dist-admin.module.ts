import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { UiDistAdminRoutingModule } from './ui-dist-admin-routing.module';
import { UiTestadminModule } from '../ui-testadmin/ui-testadmin.module';
import { SbBoardComponent } from './sb-board/sb-board.component';
import { UiPartialModule } from '../ui-partial/ui-partial.module';
import { SbTechReadinessComponent } from './sb-tech-readiness/sb-tech-readiness.component';
import { SbItContactComponent } from './sb-it-contact/sb-it-contact.component';
import { ViewTrackingComponent } from './view-tracking/view-tracking.component';
import { SbSessionsComponent } from './sb-sessions/sb-sessions.component';
import { ViewDistrictadminDashboardComponent } from './view-districtadmin-dashboard/view-districtadmin-dashboard.component';
import { UiMinistryadminModule } from '../ui-ministryadmin/ui-ministryadmin.module';
import { DaAccountsStudentsComponent } from './da-accounts-students/da-accounts-students.component';
import { MatRadioModule } from '@angular/material/radio';
import { NgxChartsModule } from '@swimlane/ngx-charts';
import { UiTeacherModule } from '../ui-teacher/ui-teacher.module';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { DaAccountsComponent } from './da-accounts/da-accounts.component';
import { DaStudentDetailsComponent } from './da-student-details/da-student-details.component';
import { DaResultsTabComponent } from './da-student-details/da-results-tab/da-results-tab.component';
import { DaSpecialFormatsTabComponent } from './da-student-details/da-special-formats-tab/da-special-formats-tab.component';
import { DaStudentInfoTabComponent } from './da-student-details/da-student-info-tab/da-student-info-tab.component';
import { DaAssessmentsComponent } from './da-assessments/da-assessments.component';
import { DaCurrentAssessmentsComponent } from './da-current-assessments/da-current-assessments.component';
import { DaFinalResultsComponent } from './da-final-results/da-final-results.component';
import { DaIndividualReportsComponent } from './da-final-results/da-individual-reports/da-individual-reports.component';
import { DaAdminSessionComponent } from './da-admin-session/da-admin-session.component';
import { UiSchooladminModule } from '../ui-schooladmin/ui-schooladmin.module';
import { UiNotificationsModule } from '../ui-notifications/ui-notifications.module';
import { SbReportComponent } from './sb-report/sb-report.component';

@NgModule({
  declarations: [
    DaAdminSessionComponent,
    DaCurrentAssessmentsComponent,
    DaAssessmentsComponent,
    SbBoardComponent,
    SbTechReadinessComponent,
    SbItContactComponent,
    ViewTrackingComponent,
    SbSessionsComponent,
    ViewDistrictadminDashboardComponent,
    DaAccountsStudentsComponent,
    DaAccountsComponent,
    DaStudentDetailsComponent,
    DaResultsTabComponent,
    DaSpecialFormatsTabComponent,
    DaStudentInfoTabComponent,
    DaFinalResultsComponent,
    DaIndividualReportsComponent,
    SbReportComponent
  ],
  imports: [
    CommonModule,
    UiDistAdminRoutingModule,
    UiPartialModule,
    ReactiveFormsModule,
    NgxChartsModule,
    FormsModule,
    UiTeacherModule,
    MatAutocompleteModule,
    MatRadioModule,
    UiPartialModule,
    UiTestadminModule,
    FormsModule,
    ReactiveFormsModule,
    UiMinistryadminModule,
    MatRadioModule,
    UiSchooladminModule,
    UiNotificationsModule,
  ],
  exports: [
    SbSessionsComponent,
    SbReportComponent
  ]
})
export class UiDistAdminModule { }

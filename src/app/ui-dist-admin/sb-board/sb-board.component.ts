import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService, getFrontendDomain } from 'src/app/api/auth.service';
import { IMenuTabConfig } from '../../ui-partial/menu-bar/menu-bar.component';
import { LoginGuardService } from 'src/app/api/login-guard.service';
import { RoutesService } from 'src/app/api/routes.service';
import { BreadcrumbsService } from 'src/app/core/breadcrumbs.service';
import { LangService } from 'src/app/core/lang.service';
import { SidepanelService } from 'src/app/core/sidepanel.service';
import { AccountType } from '../../constants/account-types';
import { SchoolBoardView, SCHOOL_BOARD_VIEWS } from './data/views';
import { downloadFromExportData, downloadCSVFromExportData, IExportColumn } from "../../ui-testctrl/tc-table-common/tc-table-common.component";
import { formatDate } from '@angular/common';
import { NtfMessageComponent } from 'src/app/ui-notifications/ntf-message/ntf-message.component';
import { ActivatedRoute } from '@angular/router';
import { combineLatest } from 'rxjs';
import { tap } from 'rxjs/operators';
import { WhitelabelService } from 'src/app/domain/whitelabel.service';
import { csvOSSLTResultDataService } from 'src/app/ui-partial/isr-reports/csv-osslt-result-data.service';

interface IView<T> extends IMenuTabConfig<T> {
  imgUrl: string,
  description: string,
  hasIndicator?: boolean,
}
export const OSSLTResultColumns =  [ 
        "SchMident", "SchName", "Grouping", "StudentOEN", "SASN"
        , "FirstName", "LastName", "EligibilityStatus", "HasStarted", "HasSubmitted", "StartedOn"
        , "SubmittedOn", "HasReport", "Result", "OSSLTScaleScore", 'ScaleScoreDescription', "Note"
];
@Component({
  selector: 'sb-board',
  templateUrl: './sb-board.component.html',
  styleUrls: ['./sb-board.component.scss']
})
export class SbBoardComponent implements OnInit {
  
  breadcrumb = [];
  
  constructor(
    public lang: LangService,
    private auth: AuthService,
    private routes: RoutesService,
    private route: ActivatedRoute,
    private loginGuard: LoginGuardService,
    private sidePanel: SidepanelService,
    private breadcrumbsService: BreadcrumbsService,
    private router: Router,
    public whitelabel: WhitelabelService,
    public csvOssltService: csvOSSLTResultDataService
  ) { }

  isShowSebPass: boolean;
  isShowKioskPass: boolean;
  hasAccess: boolean;
  schoolBoardInfo;
  SchoolBoardView = SchoolBoardView;
  selectedView: SchoolBoardView;
  views: IView<SchoolBoardView>[] = [];

  currentAdminWindow: any;
  csvTestWindows: any;
  schlDistGroupId: string;
  isSecreteUser;

  ngOnInit(): void {
    this.loginGuard.activate([]);
    this.sidePanel.activate();
    this.sidePanel.unexpand();

    this.route.queryParams.subscribe((queryParams) => {
      if (queryParams.isSecreteUser) {
        this.isSecreteUser = queryParams.isSecreteUser 
      }
      const distGroupId  = queryParams['district'];
      if (distGroupId){
        this.schlDistGroupId = distGroupId
      }
    });

    if(!this.schlDistGroupId){
      this.router.navigate([this.lang.c(), 'login-educator']);
      return
    }

    this.createSchoolBoardBreadcrumb()

    this.selectedView = this.SchoolBoardView.TECH_READI;
    // The API called should be trigger when both user and district group id are fetched
    const combinedObservable = combineLatest([this.auth.user().asObservable(), this.route.queryParams]);
    combinedObservable.pipe(
      tap(() => {
        const user = this.auth.user().value;
        if (!user) {
          return;
        }
        this.auth
        .apiGet(this.routes.DIST_ADMIN_SUMMARY, user.uid, { query: {clientDomain: getFrontendDomain(), schlDistGroupId: this.schlDistGroupId, isSecreteUser:this.isSecreteUser}})
          .then(res => {
            this.schoolBoardInfo = res;
            this.hasAccess = true;
            this.csvTestWindows = res.csvTestWindows
        })
      })
    ).subscribe();
    this.views = [];
    SCHOOL_BOARD_VIEWS.forEach(view => {
      if(view.id!=SchoolBoardView.REPORTS || this.isAllowBypassDomain()){
        this.views.push( Object({
          ...view,
          caption: this.lang.tra(view.caption),
          description: this.lang.tra(view.description),
        }))
      }
    })
  }

  createSchoolBoardBreadcrumb() { 
    const urlTree = this.router.parseUrl(this.router.url);
    const params = urlTree.queryParams; 
    
    urlTree.queryParams = {}; 
    urlTree.fragment = null; // optional
    const route = urlTree.toString()

    this.breadcrumb = [
      this.breadcrumbsService._CURRENT(
        this.lang.tra('eqao_sample'),
        route,
        params
      )   
    ] 
  }

  selectView(id: SchoolBoardView) {
    //console.log('selectView', id)
    //this.router.navigate([this.getViewRoute(id)]);
    // this.updateBreadcrumb();
    this.selectedView = id;
    if(this.selectedView == SchoolBoardView.REPORTS && this.schoolBoardInfo){
      const boardLang = this.schoolBoardInfo["brd_lang"];
      this.lang.setCurrentLanguage(boardLang);
    }  
  }
  getBaseRoute() {
    return `/${this.lang.c()}/${AccountType.DIST_ADMIN}`;
  }
  getViewRoute(viewSlug: SchoolBoardView) {
    return this.getBaseRoute() + '/' + viewSlug
  }

  onCurrentAdminWindowChange(newAdminWindow: any) {
    this.currentAdminWindow = newAdminWindow;
  }

  configureQueryParams(){
    return { query: {schl_dist_group_id: this.schoolBoardInfo.group_id, clientDomain: getFrontendDomain(), testWindowId: this.currentAdminWindow.tw_id, isSecreteUser:this.isSecreteUser }}
  }

  isAllowBypassDomain(){
    // const clientDomain = getFrontendDomain()
    // const allowBypassDomain = ["http://localhost:4200/","http://localhost:4401/", "https://d3h4m0g2lmrmq6.cloudfront.net/"]
    // //const allowBypassDomain = ["https://d3h4m0g2lmrmq6.cloudfront.net/"]
    // const isBypassDomain = allowBypassDomain.indexOf(clientDomain) > -1
    // return isBypassDomain;
    return true;
  }
}

.district-school-overview {
  .report-header {
    .header-title {
      font-style: normal;
      font-weight: 500;
      font-size: 24px;
      line-height: 28px;
      color: #000000;
    }
    .session-selector {
      display: flex;
      align-items: center;
      padding-top: 15px;
      .session-selector-label {
        font-style: normal;
        font-weight: normal;
        font-size: 18px;
        line-height: 21px;
        color: #000000;

      }
      select {
        font-style: normal;
        font-weight: normal;
        font-size: 15px;
        line-height: 21px;
        color: #000000;
        padding-right: 40px;
        margin-left: 10px;
        cursor: pointer;
        background: #FFFFFF;
        border: 1px solid #C9C9C9;
        box-sizing: border-box;
        border-radius: 4px;
        padding-left: 3px;
        height: 26px;
      }
    }
    .intro {
      font-style: normal;
      font-weight: normal;
      font-size: 15px;
      line-height: 18px;
      color: #000000;
      width: 760px;
      padding-top: 10px;
    }
  }
  .report-body {
    background-color: white;
    padding: 20px 20px 50px 20px;
    max-width: 1145px;
    margin-top: 30px;
    .top-section {
      padding: 0 5px;
      .title-section {
        max-width: 1070px;
        display: flex;
        align-items: center;
        .report-title {
          font-style: normal;
          font-weight: 500;
          font-size: 24px;
          line-height: 28px;
          color: #000000;
        }
        button.export {
          width: 87px;
          height: 22px;
          background: #E3F2FD;
          border-radius: 4px;
          font-style: normal;
          font-weight: 500;
          font-size: 14px;
          line-height: 16px;
          color: #3E3E3E;
          border: 1px solid #c7c7c7;
          margin-left: 35px;
        }
        .back-link {
          flex: 1;
          display: flex;
          justify-content: flex-end;
          a {
            font-style: normal;
            font-weight: normal;
            font-size: 15px;
            line-height: 18px;
            text-decoration-line: underline;
            color: #0085FF;
            margin-left: 194px;
          }
        }
      }
      .controls-section {
        display: flex;
        margin-top: 30px;
        flex-wrap: wrap;
        .selector {
          display: flex;
          align-items: center;
          margin-right: 20px;
          .selector-label {
            font-style: normal;
            font-weight: 600;
            font-size: 15px;
            line-height: 16px;
            color: #000000;
            margin-right: 7px;
          }
          select {
            background: #FFFFFF;
            border: 1px solid #C9C9C9;
            box-sizing: border-box;
            border-radius: 4px;
            height: 26px;
            width: 172px;
            padding-left: 5px;
            padding-bottom: 1px;
            cursor: pointer;
          }
          &.district {

          }
          &.school {
            margin-right: 90px;
          }
        }
        .toggles {
          .mat-slide-toggle-content {
            color: black;
            font-weight: 600;
            font-size: 15px;
            font-family: "Source Sans Pro", sans-serif;
          }
          .showProgressBars {
            margin-right: 20px;
          }
        }
      }
    }
    .table-section {
      .table-paginator-container {
        background-color: white !important;
        margin-top: 20px;
        margin-bottom: 5px;
      }
      .scrollable-table-container {
        overflow: auto;
      }
      table {
        font-style: normal;
        font-weight: normal;
        font-size: 15px;
        line-height: 18px;
        color: #000000;
        width: fit-content;
        position: relative;
        th {
          font-style: normal;
          font-weight: 600;
          font-size: 15px;
          line-height: 18px;
          color: #000000;
          vertical-align: bottom;
          &.assessment {
            text-align: left;
            min-width: 175px;
            max-width: 175px;
          }
        }
        td {
          &.school {
            min-width: 155px;
          }
          &.assessment {
            min-width: 175px;
            max-width: 175px;
            vertical-align: middle;
            .assessment-container {
              width: 100%;
              display: flex;
              justify-content: space-around;
              align-items: center;
              .progress-bar-container {
                width: 105px;
                height: 12px;
                background-color: #ABABAB;
                border: none;
                border-radius: 6px;
                position: relative;
                .progress-bar {
                  position: absolute;
                  top: 0;
                  left: 0;
                  height: 100%;
                  border-radius: 6px;
                  background-color: #7AD35B;
                }
              }
              .percentage {
                font-size: 14px;
                color: #7AD35B;
              }
              &.partial {
                justify-content: center;
              }
            }
          }
        }
      }
    }
  }
}
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import { UiPartialModule } from '../ui-partial/ui-partial.module';
import { FullCalendarModule } from '@fullcalendar/angular';
import { MarkdownModule } from 'ngx-markdown';
import { ViewSResetProgressComponent } from './view-s-reset-progress/view-s-reset-progress.component';
import { ViewSDashboardComponent } from './view-s-dashboard/view-s-dashboard.component';
import { ViewSTabularizeRolesComponent } from './view-s-tabularize-roles/view-s-tabularize-roles.component';
import { ViewSReminderEmailsComponent } from './view-s-reminder-emails/view-s-reminder-emails.component';
import { ViewSAttemptKeyComponent } from './view-s-attempt-key/view-s-attempt-key.component';
import { ViewSBookingCancelComponent } from './view-s-booking-cancel/view-s-booking-cancel.component';
import { ViewSUpcomingSessionsComponent } from './view-s-upcoming-sessions/view-s-upcoming-sessions.component';
import { ViewSUpcomingLangReqComponent } from './view-s-upcoming-lang-req/view-s-upcoming-lang-req.component';
import { ClipboardModule } from 'ngx-clipboard';
import { PrintTestFormComponent } from './print-test-form/print-test-form.component';
import { UiTestrunnerModule } from '../ui-testrunner/ui-testrunner.module';
import { ViewSSysFlagsComponent } from './view-s-sys-flags/view-s-sys-flags.component';
import { ViewSCertBodyApiTestAttemptComponent } from './view-s-cert-body-api-test-attempt/view-s-cert-body-api-test-attempt.component';
import { ViewSAttemptEmailsComponent } from './view-s-attempt-emails/view-s-attempt-emails.component';
import { ViewSExtendCodeExpiryComponent } from './view-s-extend-code-expiry/view-s-extend-code-expiry.component';
import { AccessSummaryComponent } from './access-summary/access-summary.component';
import {UiLoginModule} from '../ui-login/ui-login.module';
import { ViewSUsersComponent } from './view-s-users/view-s-users.component';
import { ViewSMarkingComponent } from './view-s-marking/view-s-marking.component';
import { ViewSSchoolUsersComponent } from './view-s-school-users/view-s-school-users.component';
import { WidgetSearchComponent } from './widget-search/widget-search.component';
import { WidgetQueryOutputComponent } from './widget-query-output/widget-query-output.component';
import { ViewSSchoolsComponent } from './view-s-schools/view-s-schools.component';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { ViewSSysAccountsComponent } from './view-s-sys-accounts/view-s-sys-accounts.component';
import { ViewSBulkEmailsCustomComponent } from './view-s-bulk-email-custom/view-s-bulk-emails-custom.component';
import { ViewSUserRolesComponent } from './view-s-user-roles/view-s-user-roles.component';
import { ViewSMinistryStudentTransfersComponent } from './view-s-ministry-student-transfers/view-s-ministry-student-transfers.component';
import { ViewSRecentEmailsComponent } from './view-s-recent-emails/view-s-recent-emails.component';
import { ViewSStudentLookupsComponent } from './view-s-student-lookups/view-s-student-lookups.component';
import { ViewSSupportReportingComponent } from './view-s-support-reporting/view-s-support-reporting.component';
import { NgxJsonViewerModule } from 'ngx-json-viewer';
import { ViewSReopenSessionsComponent } from './view-s-reopen-sessions/view-s-reopen-sessions.component';
import { ViewSPrivateG9AccessComponent } from './view-s-private-g9-access/view-s-private-g9-access.component';
import { ViewSResetTestAttemptComponent } from './view-s-reset-test-attempt/view-s-reset-test-attempt.component';
import { ViewSResetPasswordAttemptComponent } from './view-s-reset-password-attempt/view-s-reset-password-attempt.component';
import { ViewSSwapAttemptComponent } from './view-s-swap-attempt/view-s-swap-attempt.component';
import { AgGridModule } from 'ag-grid-angular';
import { ViewAddStuOldTwComponent } from './view-add-stu-old-tw/view-add-stu-old-tw.component';
import { ViewSRedisStatsComponent } from './view-s-redis-stats/view-s-redis-stats.component';
import { ValidateStuTaTassCountComponent } from './validate-stu-ta-tass-count/validate-stu-ta-tass-count.component';
import { ViewSValidateStuPaymentComponent } from './view-s-validate-stu-payment/view-s-validate-stu-payment.component';
import { ViewSSessionWarmingComponent } from './view-s-session-warming/view-s-session-warming.component';
import { ViewPjScanningComponent } from './view-pj-scanning/view-pj-scanning.component';
import { ViewSStudentMetaUpdateComponent } from './view-s-student-meta-update/view-s-student-meta-update.component';

@NgModule({
    imports: [
        CommonModule,
        FullCalendarModule,
        RouterModule,
        ReactiveFormsModule,
        UiPartialModule,
        MarkdownModule,
        ClipboardModule,
        UiTestrunnerModule,
        AgGridModule,
        FormsModule,
        NgxJsonViewerModule,
        RouterModule.forChild([
            {path: ``, component: ViewSDashboardComponent},
            {path: `dashboard`, component: ViewSDashboardComponent},
            {path: `dashboard/:loginAs`, component: ViewSDashboardComponent},
            {path: `reset-progress`, component: ViewSResetProgressComponent},
            {path: `reminders`, component: ViewSReminderEmailsComponent},
            {path: `cancel-booking`, component: ViewSBookingCancelComponent},
            {path: `access-summary`, component: AccessSummaryComponent},
            {path: `attempt-key`, component: ViewSAttemptKeyComponent},
            {path: `upcoming-sessions`, component: ViewSUpcomingSessionsComponent},
            {path: `reopen-sessions`, component: ViewSReopenSessionsComponent},
            {path: `upcoming-lang-req`, component: ViewSUpcomingLangReqComponent},
            {path: `print-test-form/:testFormId`, component: PrintTestFormComponent},
            {path: `sys-flags`, component: ViewSSysFlagsComponent},
            {path: `cert-body-attempt-api`, component: ViewSCertBodyApiTestAttemptComponent},
            {path: `attempt-emails`, component: ViewSAttemptEmailsComponent},
            {path: `bulk-emails-custom`, component: ViewSBulkEmailsCustomComponent},
            {path: `extend-email-code-expiry`, component: ViewSExtendCodeExpiryComponent},
            {path: `school-users`, component: ViewSSchoolUsersComponent},
            {path: `users`, component: ViewSUsersComponent},
            {path: `marking`, component: ViewSMarkingComponent},
            {path: `schools`, component: ViewSSchoolsComponent},
            {path: `private-g9-access`, component: ViewSPrivateG9AccessComponent},
            {path: `user-roles`, component: ViewSUserRolesComponent},
            {path: `recent-sys-emails`, component: ViewSRecentEmailsComponent},
            {path: `ministry-student-transfers`, component: ViewSMinistryStudentTransfersComponent},
            {path: `sys-accounts`, component: ViewSSysAccountsComponent},
            {path: `student-lookups`, component: ViewSStudentLookupsComponent},
            {path: `validate-stu-ta-tass-count`, component: ValidateStuTaTassCountComponent},
            {path: `validate-stu-payment`, component: ViewSValidateStuPaymentComponent},
            {path: `reset-test-attempt`, component: ViewSResetTestAttemptComponent},
            {path: `reset-password-attempt`, component: ViewSResetPasswordAttemptComponent},
            {path: `support-reporting`, component: ViewSSupportReportingComponent},
            {path: `swap-attempt`, component: ViewSSwapAttemptComponent},
            {path: `add-stu-to-tw`, component: ViewAddStuOldTwComponent},
            {path: `redis-stats`, component: ViewSRedisStatsComponent},
            {path: `session-warming`, component: ViewSSessionWarmingComponent},
            {path: `pj-scanning`, component: ViewPjScanningComponent},
            {path: `student-meta-update`, component: ViewSStudentMetaUpdateComponent},
        ]),
        UiLoginModule,
        MatSlideToggleModule,
    ],
  declarations: [
    ViewSResetProgressComponent,
    ViewSDashboardComponent,
    ViewSTabularizeRolesComponent,
    ViewSReminderEmailsComponent,
    ViewSAttemptKeyComponent,
    ViewSBookingCancelComponent,
    ViewSUpcomingSessionsComponent,
    ViewSUpcomingLangReqComponent,
    PrintTestFormComponent,
    ViewSSysFlagsComponent, ViewSCertBodyApiTestAttemptComponent, ViewSAttemptEmailsComponent, ViewSExtendCodeExpiryComponent,
    ViewSBulkEmailsCustomComponent,
    AccessSummaryComponent,
    ViewSUsersComponent,
    ViewSMarkingComponent,
    ViewSSchoolUsersComponent,
    WidgetSearchComponent,
    WidgetQueryOutputComponent,
    ViewSSchoolsComponent,
    ViewSSysAccountsComponent,
    ViewSUserRolesComponent,
    ViewSMinistryStudentTransfersComponent,
    ViewSRecentEmailsComponent,
    ViewSStudentLookupsComponent,
    ViewSSupportReportingComponent,
    ViewSReopenSessionsComponent,
    ViewSPrivateG9AccessComponent,
    ViewSResetTestAttemptComponent,
    ViewSResetPasswordAttemptComponent,
    ViewSSwapAttemptComponent,
    ViewAddStuOldTwComponent,
    ViewSRedisStatsComponent,
    ValidateStuTaTassCountComponent,
    ViewSValidateStuPaymentComponent,
    ViewSSessionWarmingComponent,
    ViewPjScanningComponent,
    ViewSStudentMetaUpdateComponent,
  ],
})
export class UiSupportModule { }

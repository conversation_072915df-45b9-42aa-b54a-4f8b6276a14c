<div class="page-body">
  <div>
    <header
    [breadcrumbPath]="breadcrumb"
    ></header>
    <div class="page-content is-fullpage">
      <div *ngIf="!this.isLoaded && !this.isLoadFailed">
        Loading
      </div>
      <div *ngIf="this.isLoadFailed">
        You do not have the required roles to access this page.
      </div>
      <div *ngIf="this.isLoaded">
        <ul>
          <li><a routerLink="/en/support/sys-flags">System Flags</a></li>
          <li><a routerLink="/en/support/reset-progress">Account Resets</a></li>
          <li><a routerLink="/en/support/reminders">Reminder Emails</a></li>
          <li><a routerLink="/en/support/cancel-booking">Applicant Bookings</a></li>
          <li><a routerLink="/en/support/upcoming-sessions">Upcoming Sessions</a></li>
          <li><a routerLink="/en/support/reopen-sessions">Reopen Sessions</a></li>
          <li><a routerLink="/en/support/upcoming-lang-req">Upcoming Language Requirements (French)</a></li>
          <li><a routerLink="/en/support/cert-body-attempt-api">cert body attempt api</a></li>
          <!-- <li><a routerLink="/en/support/attempt-key">Attempt Key Emails</a></li> -->
          <li><a routerLink="/en/support/attempt-emails">Completed Attempt Emails</a></li>
          <li><a routerLink="/en/support/access-summary">Access Summary</a></li>
          <li><a routerLink="/en/support/schools">Schools and Boards</a></li>
          <li><a routerLink="/en/support/private-g9-access">Private Schools G9 Access</a></li>
          <li><a routerLink="/en/support/user-roles">User Roles</a></li>
          <li><a routerLink="/en/support/sys-accounts">System Roles</a></li>
          <li><a routerLink="/en/support/student-lookups">Student lookups</a></li>
          <li><a routerLink="/en/support/validate-stu-ta-tass-count">Validate Student TA/TASS count</a></li>
          <li><a routerLink="/en/support/validate-stu-payment">Validate Student Payment</a></li>
          <li><a routerLink="/en/support/reset-test-attempt">Reset/Unlink Test Attempt</a></li>
          <li><a routerLink="/en/support/reset-password-attempt">Reset Password Attempt / Link</a></li>
          <li><a routerLink="/en/support/support-reporting">Support Reporting</a></li>
          <li><a routerLink="/en/support/swap-attempt">Swap/Pass Test Attempt</a></li>
          <li><a routerLink="/en/support/add-stu-to-tw">Add Existing Student to Old Test Window</a></li>
          <li><a routerLink="/en/support/redis-stats">Redis Stats</a></li>
          <li><a routerLink="/en/support/session-warming">Session Warming</a></li>
          <li><a routerLink="/en/support/pj-scanning">PJ Scanning</a></li>
          <li><a routerLink="/en/support/student-meta-update">Student Meta Update</a></li>
        </ul>
      </div>
    </div>
  </div>
</div>

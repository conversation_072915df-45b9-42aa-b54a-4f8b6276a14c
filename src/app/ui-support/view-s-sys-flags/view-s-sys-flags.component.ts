import { Component, OnInit } from '@angular/core';
import { AuthService } from '../../api/auth.service';
import { RoutesService } from '../../api/routes.service';
import { AccountType } from '../../constants/account-types';
import { LoginGuardService } from '../../api/login-guard.service';
import { BreadcrumbsService } from '../../core/breadcrumbs.service';
import { LangService } from '../../core/lang.service';
import { Router } from '@angular/router';

interface ISysFlag {
  id: number;
  key: string;
  value: number;
}

interface ISysFlagConfig {
  isOnOff: boolean;
  description?: string;
  max?: number;
  min?: number;
}

const DEFAULT_FLAG_CONFIG: ISysFlagConfig = {
  isOnOff: undefined,
  description: '',
}

enum SysFlagType {
  NUMERIC = 'numeric',
  STRING = 'string',
}
@Component({
  selector: 'view-s-sys-flags',
  templateUrl: './view-s-sys-flags.component.html',
  styleUrls: ['./view-s-sys-flags.component.scss']
})
export class ViewSSysFlagsComponent implements OnInit {

  constructor(
    private auth: AuthService,
    private routes: RoutesService,
    private loginGuard: LoginGuardService, 
    private breadcrumbsService: BreadcrumbsService,
    private lang: LangService,
    private router: Router,
  ) { }

  isLoaded: boolean;
  isLoadFailed: boolean;
  breadcrumb = [];
  systemFlagsNumeric: ISysFlag[] = [];
  systemFlagsString: ISysFlag[] = [];
  SysFlagType = SysFlagType;

  // Keep this here instead of db since it's only needed on this page
  systemFlagsNumericInfo: { [key:string]: ISysFlagConfig }= {
    'SHOW_APPLICANT_REG': {isOnOff: true, description: ''},
    'BYPASS_OCT_VALIDATOR': {isOnOff: true, description: ''},
    'SHOW_APPLICANT_RESULTS': {isOnOff: true, description: ''},
    'SHOW_APPEALS': {isOnOff: true, description: ''},
    'ENABLE_CREDIT_SYSTEM': {isOnOff: true, description: ''},
    'ENABLE_PAYMENTS_WITH_CREDITS': {isOnOff: true, description: ''},
    'ENABLE_WAITLIST': {isOnOff: true, description: ''},
    'DISABLE_G9_REPORTS': {isOnOff: true, description: ''},
    'DISABLE_SCORING': {isOnOff: true, description: ''},
    'DISABLE_SCHOOLS': {isOnOff: true, description: ''},
    'SCAN_BATCH_SIZE': {isOnOff: false, description: ''},
    'SCAN_CHECK_COUNT': {isOnOff: false, description: ''},
    'ENABLE_DAILY_REPORTING': {isOnOff: true, description: ''},
    'ENABLE_DAILY_SCORING_POOLING': {isOnOff: true, description: ''},
    'BYPASS_SCAN_REVIEW': {isOnOff: true, description: ''},
    'STUDENT_HEARTBEAT_CTRL': {isOnOff: false, description: ''},
    'ENABLE_WS_MULTITAB_BLOCK': {isOnOff: true, description: ''},
    'IS_MFA_DISABLED': {isOnOff: true, description: ''},
    'ALLOW_FORCE_REDIS_LOAD_ON_INIT': {isOnOff: true, description: ''},
    'ENFORCE_STU_1_TW_CLASS': {isOnOff: true, description: ''},
    'SUPPORT_RESET_PWS_EXPIRE_TIME': {isOnOff: false, description: ''},
    'DIST_ADMIN_TR_SEB_KIOSK_S3_EXPIRE_TIME': {isOnOff: false, description: ''},
    'DISABLE_NEW_EXPORTS': {isOnOff: true, description: ''},
    'BULK_CLASS_SCAN_UPLOAD_MAX_RUNNING': {isOnOff: false, max: 150, min: 1, description: 'Maximum number of bulk class scan uploads that can run at the same time. Used to limit load on scanning service.'},
    'SCOR_POOL_ONGOING_OFFSET_HOUR': {isOnOff: false, min: 0, description: 'Count an unfinished pooling started a maximum of X hours ago as ongoing - prevent starting other poolings from the UI to avoid duplication.'},
    'SCOR_POOL_UNCOMPLETED_OFFSET_HOUR': {isOnOff: false, min: 1, description: 'Count an unfinished pooling started a minimum of X hours ago as uncompleted - flag it in system alerts.'},
  }
  

  ngOnInit() {
    this.loginGuard.activate([AccountType.SUPPORT]);
    this.breadcrumb = [
      this.breadcrumbsService.SUPPORT_DASHBOARD(),
      this.breadcrumbsService._CURRENT(this.lang.tra('System Flags'), this.router.url),
    ]
    this.loadReq();
  }

  isSending:boolean;
  isSent:boolean;
  isFailed:boolean;
  failReason:string;

  loadReq(){
    this.auth
      .apiFind(
        this.routes.SUPPORT_SYS_FLAGS,
        {}
      )
      .then(res => {
        const {systemFlagsNumeric, systemFlagsString} = res;
        this.systemFlagsNumeric = systemFlagsNumeric;
        this.systemFlagsString = systemFlagsString;
        this.isLoaded = true;
      })
  }

  updateFlag(flag: ISysFlag, value:number, type = SysFlagType.NUMERIC){

    // Enforce max and min
    if (type == SysFlagType.NUMERIC){
      const sysFlagConfig = this.getSysFlagConfig(flag.key);
      const maxAllowed = sysFlagConfig.max;
      const minAllowed = sysFlagConfig.min
      if (maxAllowed!==undefined && value > maxAllowed){
        return this.loginGuard.quickPopup(`Error: value cannot be above ${sysFlagConfig.max}.`)
      } else if (minAllowed!==undefined && value < minAllowed) {
        return this.loginGuard.quickPopup(`Error: value cannot be below ${sysFlagConfig.min}.`)
      }
    }

    this.isSent = false;
    this.isSending = true;
    this.auth
      .apiPatch(
        this.routes.SUPPORT_SYS_FLAGS,
        flag.id,
        { value, key: flag.key, type }
      )
      .then(res => {
        flag.value = value;
        this.isSent = true;
        this.isSending = false;
      })
      .catch(e =>{
        alert('failed to update')
      })
  }

  getSysFlagConfig(key: string) {
    return this.systemFlagsNumericInfo[key] || DEFAULT_FLAG_CONFIG;
  }
}

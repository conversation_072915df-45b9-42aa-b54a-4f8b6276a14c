
<div class="page-body is-offwhite">
  <div>
    <header
      [breadcrumbPath]="breadcrumb"
      [hasSidebar]="true"
      [hasTechnicalSupportAction]="true"
      (technicalSupportAction)="reportIssue(true, 'Technical Support')"

    ></header>
    <div style="padding:4em; padding-top:1em;">
      <div *ngIf="isShowHeaderInstr">
        <tra-md slug="sa_educators_assesment_header"></tra-md>
      </div>
      <div  *ngIf="session_id" class="active-classroom">
        <div class="pre-table-strip">
          <div>
            <div style="margin-bottom:1em;"> <!-- todo:STYLES -->
              <div class="session-name" style="font-weight:600; font-size: 1.3em;"> <!-- todo:STYLES -->
                <tra-md [slug]="activeSession.name"></tra-md>
              </div>
              <div class="session-timing" [ngStyle]="{visibility: isLoaded ? 'visible' : 'hidden'}">
                <ng-container *ngIf="isSessionOpened"> 
                  <div>
                    <tra [slug]="getStartedOnSlug()"></tra> {{activeSession.dateTimeStartLong}}
                  </div>
                  <div *ngIf="isDurationEnforced">
                    <div class="text-w-btns is-gapped">
                      <div>
                        This exam has a running time of <strong>{{duration_m}} minute(s)</strong> 
                        and has been extended for all students by {{time_ext_m}} minutes. 
                      </div>
                      <widget-time-ext (updateExt)="updateSessionTimeExt($event)" ></widget-time-ext>
                    </div>
                    <div *ngIf="tw_hardstop_offset_h">
                      There are <strong>{{tw_hardstop_offset_h}} hours</strong> to administer this scheduled assessment. If you are accessing this assessment after the stated date, please cancel and start a new session.
                    </div>
                    <div *ngIf="hasIndivExtensions()">
                      There are {{numIndivExtensions()}} student(s) who have an individual extension. 
                    </div>
                    <div *ngIf="IS_MAX_TIME_CHECK_ACTIVE"> 
                      <span *ngIf="maxTimeRemaining() > 0">
                        Based on student start times, the last student will be locked in {{maxTimeRemaining()}} minute(s).
                      </span>
                      <span *ngIf="maxTimeRemaining() == 0">
                        All students have completed their available time.
                      </span>
                    </div>
                  </div>
                  <!-- <div *wlCtx="'IS_EQAO'"> -->
                </ng-container>
                <ng-container  *ngIf="!isSessionOpened">
                  <ng-container *wlCtx="'IS_DIRECT_SCHED'">
                    <tra-md slug="lbl_pj_scheduled" [props]="{date: activeSession.hourDirectStart}"></tra-md>
                  </ng-container>
                  <ng-container *ngIf="isG9OrOsslt()">
                    Session Scheduled for {{activeSession.timeDirectStart}} at {{activeSession.hourDirectStart}}
                  </ng-container>
                  <ng-container *ngIf="isPrimaryOrJunior()">
                    Session Scheduled for {{activeSession.timeDirectStart}}
                  </ng-container>
                </ng-container>
              </div>
            </div>
            <div [ngStyle]="{visibility: isLoaded ? 'visible' : 'hidden'}" [ngClass]="isSessionOpened ? 'button-group' : null">
              <button *ngIf="isSessionOpened && !isScoreEntrySession" class="button is-small  has-icon head-btn" [class.is-success]="!didSessionStart" [class.is-dark]="isSessionPaused && didSessionStart" (click)="togglePauseSession()" [disabled]="!isSessionOpened">
                <span *ngIf="didSessionStart && !isSessionPaused" class="icon"><i class="far fa-pause-circle"></i></span>
                <span *ngIf="!didSessionStart || isSessionPaused" class="icon"><i class="far fa-play-circle"></i></span>
                <span *ngIf="didSessionStart">
                  <span *ngIf="!isSessionPaused"><tra [slug]="getPauseSessionSlug()"></tra></span>
                  <span *ngIf="isSessionPaused"><tra [slug]="getResumeSessionSlug()"></tra></span>
                </span>
                <span *ngIf="!didSessionStart"><tra slug="start_session"></tra></span>
              </button>
              <button *ngIf="false" class="button is-small has-icon" [disabled]="!isOpenSessionEnabled">
                <span class="icon"><i class="fas fa-lock-open"></i></span>
                <span><tra slug="open_session"></tra></span>
              </button>
              <button *ngIf="!isScoreEntrySession" class="button is-small has-icon " (click)="changeScheduledSessionStart()">
                <!-- *ngIf="!isSessionOpened" -->
                <span class="icon"><i class="fas fa-calendar"></i></span>
                <span><tra slug="btn_change_date"></tra></span>
              </button>
              <div *ngIf="!getAllowDisableLockAfterNotification()">
                <button 
                class="button is-small has-icon" 
                *wlCtx="'IS_INVIG_SLOCK_CTRL'" 
                [ngSwitch]="isSoftLockEnabled()"
                (click)="toggleSoftLockEnabled()"
                [class.is-warning]="isSoftLockEnabled()"
              >
                <ng-container *ngSwitchCase="true">
                  <span class="icon"><i class="fas fa-lock"></i></span>
                  <span>Disable Softlock</span>
                </ng-container>
                <ng-container *ngSwitchCase="false">
                  <span class="icon"><i class="fas fa-lock-open"></i></span>
                  <span>Enable Softlock</span>
                </ng-container>
              </button>
              </div>
              <button 
                class="button is-small has-icon" 
                *ngIf="getAllowDisableLockAfterNotification() && (isUserSoftLockNotified || !isSoftLockEnabled())"
                [ngSwitch]="isSoftLockEnabled()"
                (click)="toggleSoftLockEnabled()"
                [class.is-warning]="isSoftLockEnabled()"
              >
                <ng-container *ngSwitchCase="true">
                  <span class="icon"><i class="fas fa-lock"></i></span>
                  <span>Disable Softlock</span>
                </ng-container>
                <ng-container *ngSwitchCase="false">
                  <span class="icon"><i class="fas fa-lock-open"></i></span>
                  <span>Enable Softlock</span>
                </ng-container>
              </button>
              <button *ngIf="isSessionOpened" class="button is-small is-danger has-icon" (click)="reportIssue()">
                <span class="icon"><i class="far fa-flag"></i></span>
                <span><tra slug="btn_report_issue"></tra></span>
              </button>
              <button *ngIf="canEditDate()" (click)="editModalStart()" class="button is-small is-info has-icon">
                <span class="icon"><i class="fas fa-pen"></i></span>
                <span *ngIf="!isPrimaryOrJunior()"><tra slug="btn_edit_session_date_time"></tra></span>
                <span *ngIf="isPrimaryOrJunior()"><tra slug="btn_edit_session_date_pj"></tra></span>
              </button>
              <div *ngIf="isSessionOpened" style = "display: flex; flex-direction: column;">
                <!-- #MERGE_20220524 : refactor  -->
                <button *ngIf="showButton && isSessionOpened && !isPJOperationalTest()" (click)="triggerSubmitAssessmentSessions()" class="button is-small is-success has-icon" [disabled]="!didSessionStart">
                  <span class="icon"><i class="fas fa-upload"></i></span>
                  <span><tra [slug]="getTeacherSubmitResponsesSlug()"></tra></span>
                </button>

                <button *ngIf="!showButton && isSessionOpened && !isPJOperationalTest()" (click)="reopenAssessment()" class="button is-small is-success has-icon" [disabled]="!didSessionStart">
                  <span class="icon"><i class="fas fa-upload"></i></span>
                  <span><tra slug="eya_reopen_score_entry"></tra></span>
                </button>
                
                <button *ngIf="isSessionOpened && isPJOperationalTest()" (click)="reviewSubmissionsModalStart()" class="button is-small is-success has-icon" [disabled]="!didSessionStart">
                  <span class="icon"><i class="fas fa-upload"></i></span>
                  <span><tra slug="pj_teacher_submit_responses"></tra></span>
                </button>
                <div 
                  *ngIf="!isABED()"
                  style="color: red; padding-top: 0.5em; padding-left: 0.5em;">	
                  <tra-md slug='txt_pj_submit_note'></tra-md>
                </div>
              </div>
              <button *ngIf="!isSessionOpened"  class="button is-small has-icon is-danger" (click)="cancelScheduledSession()">
                <span class="icon"><i class="fas fa-times"></i></span>
                <span><tra slug="g9_btn_cancel_session"></tra></span>
              </button>
              <button *ngIf="displayUnsubmitButton()" (click)="isUnsubmitBtnVisiable = !isUnsubmitBtnVisiable" class="button is-small unsubmit-btn">
                <span>{{ getUnsubmitBtnText() }}</span>
              </button>
            </div>
          </div>
          <div *ngIf="isLoaded && activeClassroom && !isScoreEntrySession" [ngClass]="isSessionOpened ? 'class-code' : null">
            <!-- [classCode]="activeSession.access_code" -->
            <class-code-indicator
              [classCode]="activeClassroom.classCode"
              [accessScope]="getAccessCodeSlug()"
            ></class-code-indicator>
          </div>
        </div>
        <div *ngIf="!isScoreEntrySession">
          <div class='one-em-padding'>
            <span *ngIf="!numUnlockedStudents"><tra slug="lbl_student_no_acess"></tra></span>
            <span *ngIf="numUnlockedStudents"><tra-md slug="lbl_student_no_acess_param" [props]="{numUnlockedStudents: numUnlockedStudents}"></tra-md></span>
          </div>
          <ng-container *ngIf="getCustomMessage()">
            <div class="notification is-warning">
              <tra-md [slug]="getCustomMessage()"></tra-md>
            </div>
          </ng-container>
          <ng-container *ngIf="showScanInfo() && getCustomMessageScanSecurity()">
            <div class="notification is-warning">
              <tra-md [slug]="getCustomMessageScanSecurity()"></tra-md>
            </div>
          </ng-container>
          <div class='one-em-padding'>
            <tra-md [slug]='getStudentPostSlug()'></tra-md>
          </div>
          <div class='one-em-padding'>
            <tra [slug]="'txt_ldb_invigilation_instructions'"></tra>&nbsp;
            <a (click)="showLDBProctorPassword()"><tra [slug]="'lbl_ldb_show_password'"></tra></a>
          </div>
          <div *wlCtxNot="'IS_INVIG_LOCKDOWN_EDIT_DISABLED'" class="one-em-padding">
            <tra [slug]="'txt_ldb_invigilation_config'"></tra>&nbsp;
            <a (click)="openLDBConfigModal()"><tra [slug]="'ldl_ldb_show_config'"></tra></a>
          </div>
          <div class="one-em-padding display-red" *ngIf="getPrintUploadButtonDisplayStatus().isPrintDisabled">
            <tra [slug]="'Printing will be available after'"></tra>&nbsp;{{formatPrintUploadDate(printBeforedateEnd)}}
          </div>
          <div class="one-em-padding display-red" *ngIf="g9demoService.getIsFromSchoolAdmin() && getPrintUploadButtonDisplayStatus().isUploadDisabled && isSessionOpened && isBulkUploadAllowed">
            <tra [slug]="'Bulk Upload is no longer available, please use individual student upload.'"></tra>
          </div>
          <div *ngIf="!isNBED() && !isMBED() &&!isABED()" class='one-em-padding'>
            <tra-md slug='txt_submit_results_warning'></tra-md>
            <tra-md slug='txt_clarify_password_warning'></tra-md>
          </div>
          <div *ngIf="isBulkPrint()" class="notification is-warning">
            Printing...
          </div>
          <div *ngIf="isBulkUpload()" class="notification is-warning">
            Uploading response sheets...<br>
            Time until next check for upload completion: {{ getBulkUploadTime() }}s
          </div>
          <div *ngIf="isSessionInitializing || isRefreshingSessionsInfo">
            <div class="notification" style="text-align: center;">
              <tra slug="init_assessment_session"></tra>
              <div *ngIf="isAllowLoadRetry" style="margin-top:2em;">
                <button class="button is-small" (click)="loadSessionInfo()">Retry</button>
              </div>
            </div>
          </div>
          <div class='one-em-padding horizontal-container' *ngIf="!isRefreshingSessionsInfo">
            <scan-button-bar *ngIf="showScanInfo()" 
              (reviewClicked)="sampleModalStart(0)" 
              [testSessionId]="session_id" 
              [asmtSlug]="asmtSlug" 
              [classroomId]="classroomId"
              [studentList]="studentList"
              [currentQueryParams]="currentQueryParams"
              (showPrintScanInstruction)="instructionModalStart()" 
              (showPrintStudentInfo)="studentInfoModalStart()" 
              [invigilateView]="true"
              [isFIClass]="isFIOptionC"
              [printUploadStatus]="getPrintUploadButtonDisplayStatus()"
              [isBulkUploadAllowed]="isBulkUploadAllowed"
            ></scan-button-bar>
            <div style="margin-top: 0.5rem;" *ngIf="isLoaded">
              <student-list class="dont-print"
                [studentList]="studentList"
                [studentSocketState]="studentSocketState"
                [isScanSession]="isScanSession"
                [isStudentDetailAccess]="getStudentDetailAccess()"
                [printUploadStatus]="getPrintUploadButtonDisplayStatus()"
                [questionsForScan]="questionsForScan"
                [testSessionId]="session_id"
                [studentStates]="studentStates"
                [closeAllSubSessions]="closeAllSubSessions"
                (isAllSubSessionsClosed)="finalizeSessionSubmission($event)"
                [subSessions]="subSessions"
                [completedSubSessions]="completedSubSessions"
                [activeSubSessions]="activeSubSessions"
                (selectionUpdate)="onStudentSelectionUpdate($event)"
                (locksUpdate)="onStudentLockUpdate($event)"
                (studentLockActionEmitter)="onLockActionsUpdate($event)"
                (questionId)="onQuestionIdChange($event)"
                (openStudentInfo)="sampleModalStart($event)"
                (resetSoftLock)="onResetStudentSoftLock($event)"
                [classId]="classroomId"
                [activeSession]="activeSession"
                [isDurationEnforced]="isDurationEnforced"
                [duration_m]="duration_m"
                [time_ext_m]="time_ext_m"
                [isSessionOpened]="isSessionOpened"
                (openSoftlockModal)="toggleSoftLockModal($event)"
                [activeClassroom]="activeClassroom"
                [isNoTdOrder]="isNoTdOrder"
                (reloadSessionInfo) = "reloadSessionInfo($event)"
                [isUnsubmitBtnVisiable] = "isUnsubmitBtnVisiable"
                [isForceUnsubmitAbility]="isForceUnsubmitAbility"
                [isShowTimeRemaining]="isDurationEnforced"
                [isSecreteUser] ="isSecreteUser"
                [asmtSlug] = "asmtSlug"
                [testAttemptSoftLockStatus] = "testAttemptSoftLockStatus"
                [isSasnLogin] = "isSasnLogin"
                [studentScanInfo]="getScanInfoMap()"
                (disableSoftLock)= "disableSoftLock()"
                (notifyUserSoftLock) = "notifyUserSoftLock()"
                [isUserSoftLockNotified] = "isUserSoftLockNotified"
              ></student-list>
            </div>
          </div>
        </div>
        <div *ngIf="isScoreEntrySession && isScoreEntryLoaded">
          <score-entry [test_designs]="test_designs" [testSessionId]="session_id" [classId]="classroomId" [scoreEntryPatch]="onScoreEntryPatch"></score-entry>
        </div>
        <div style="padding: 0.5em 1em 0.5em 1em;" *ngIf="isLoaded && walkinStudents.length">
          <student-list class="dont-print"
            (acceptAllWalkStudents)="acceptAllWalkStudents()"
            (rejectAllWalkStudents)="rejectAllWalkStudents()"
            (acceptWalkStudent)="acceptWalkStudent($event)"
            (rejectWalkStudent)="rejectWalkStudent($event)"
            [isWalkinList]="true"
            [studentList]="walkinStudents"
            [studentSocketState]="studentSocketState"
            [testSessionId]="session_id"
            [studentStates]="studentStates"
            [closeAllSubSessions]="closeAllSubSessions"
            (isAllSubSessionsClosed)="finalizeSessionSubmission($event)"
            [subSessions]="subSessions"
            [completedSubSessions]="completedSubSessions"
            [activeSubSessions]="activeSubSessions"
            (selectionUpdate)="onStudentSelectionUpdate($event)"
            (locksUpdate)="onStudentLockUpdate($event)"
            (studentLockActionEmitter)="onLockActionsUpdate($event)"
            (openStudentInfo)="sampleModalStart($event, true)"
            (resetSoftLock)="onResetStudentSoftLock($event)"
            [classId]="classroomId"
            [activeSession]="activeSession"
            [isDurationEnforced]="isDurationEnforced"
            [duration_m]="duration_m"
            [time_ext_m]="time_ext_m"
            [isSessionOpened]="isSessionOpened"
            (openSoftlockModal)="toggleSoftLockModal($event)"
            [activeClassroom]="activeClassroom"
            [isNoTdOrder]="isNoTdOrder"
            (reloadSessionInfo) = "reloadSessionInfo($event)"
            [(isUnsubmitBtnVisiable)] = "isUnsubmitBtnVisiable"
            [isForceUnsubmitAbility]="isForceUnsubmitAbility"
            [isSecreteUser] ="isSecreteUser"
            [asmtSlug] = "asmtSlug"
            [testAttemptSoftLockStatus] = "testAttemptSoftLockStatus"
            [isSasnLogin] = "isSasnLogin"
          ></student-list>
        </div>
      </div>
    </div>
    <div *ngIf="isLoaded && false" class="video-icon-container">
      <i (click)="introVideoModalStart()" class="video-icon fas fa-question-circle"></i>
    </div>
  </div>
</div>

<div class="custom-modal" *ngIf="cModal()">
  <div class="modal-contents">
    <div [ngSwitch]="cModal().type">
      <div *ngSwitchCase="TeacherModal.INVIG_VIDEO_INTRO_SEEN">
        <invig-intro-vid></invig-intro-vid>
      </div>
      <div *ngSwitchCase="TeacherModal.CHANGE_SESSION_DATE">
        <h2><tra slug="btn_change_date"></tra></h2>
        <t-modal-session-date-change
          [testSessionId]="session_id"
          [config]="cmc()"
        ></t-modal-session-date-change>
      </div>
      <div *ngSwitchCase="TeacherModal.SAMPLE_MODAL">
        <!-- <t-student-data [savePayload]="cmc()" saveProp="payload"></t-student-data> -->
        <t-modal-student-info-chooser     
          [allowPASIUpdates]="allowPASIUpdates"
          [accommodationList]="accommodationList"
          [questionsForScan]="questionsForScan"          
          [testSessionId]="session_id"
          [classId]="classroomId"
          [asmtSlug] = "asmtSlug"
          [isScanSession] = 'isScanSession'
          [config]="cmc()"
          [pageModal]="pageModal"
          [isScanSession]="isScanSession"
          [isSingleUploadAllowed]="isSingleUploadAllowed"
          [printUploadStatus]="getPrintUploadButtonDisplayStatus()"
          [showRADropdown]="false"
          [isSasnLogin] = "isSasnLogin"
          [isFIClass]="isFIOptionC"
        ></t-modal-student-info-chooser>
      </div>
      <div *ngSwitchCase="TeacherModal.EDIT_MODAL" style="width: 40em;">
        <t-modal-edit-session 
          [savePayload]="cModal().config" 
          saveProp="payload"  
          [canEditSessionA]="showSessionAEdit" 
          [canEditSessionB]="showSessionBEdit" 
          [isShowSessionB]="isShowSessionB"
          [canEditSessionLang]="showSessionLangEdit"
          [canEditSessionMath]="showSessionMathEdit"
          [asmtSlug]="asmtSlug"
          [isFIClass]="isFIOptionC"
        ></t-modal-edit-session>
      </div>
      <div *ngSwitchCase="TeacherModal.REPORT_MODAL" style="width: 40em;">
        <t-modal-report-issue 
          [studentList]="studentList" 
          [reportIssuesCategoryList]="reportIssuesCategories"
          [customReportHeader]="customReportHeader"
          [savePayload]="cModal().config"
          (reportClicked)="reportModalFinish(cModal().config)"
          (cancelClicked)="pageModal.closeModal()"
        ></t-modal-report-issue>
      </div>
      <div *ngSwitchCase="TeacherModal.REVIEW_SUBM_MODAL" style="width: 40em;">
        <t-modal-review-student-submissions 
          [testSessionId]="session_id" 
          (studentMapEmitter)="initStudentReviewedMap($event)" 
          [studentList]="studentList" 
          [completedSubsessions]="completedSubSessions" 
          [subSessions]="subSessions" 
          [studentSubsessionStates]="studentStates" 
          [studentScanInfo]="getScanInfoMap()"
          [isFIClass]="isFIOptionC"
        ></t-modal-review-student-submissions>
      </div>
      <div *ngSwitchCase="TeacherModal.SOFT_LOCK_MODAL" style="width: 40em;">
        <div class="modal-scrollpane">
          <div><tra-md slug="msg_student_navigation_warning_choice" [props]="getNumTimesOffScreenProps()"></tra-md></div>
        </div>     
      </div>
      <div *ngSwitchCase="PrintScanModal.INSTRUCTION_MODAL" style="width: 50em;">
        <t-modal-print-scan-instruction></t-modal-print-scan-instruction>
      </div>
      <div *ngSwitchCase="PrintScanModal.STUDENT_INFO" style="width: 40em;">
        <t-modal-print-student-info [config]="cmc()" [isSasnLogin] = "isSasnLogin"></t-modal-print-student-info>
      </div>
      <div *wlCtxNot="'IS_INVIG_LOCKDOWN_EDIT_DISABLED'">
        <div *ngSwitchCase="LDBModal.LDB_CONFIG_MODAL" style="width: 80em;">
          <modal-ldb-config 
            [config]="cmc()"
            [pageModal]="pageModal"
          ></modal-ldb-config>
        </div>
      </div>
    </div>
    <modal-footer 
      *ngIf="!(cModal().type === TeacherModal.SAMPLE_MODAL || cModal().type === TeacherModal.REPORT_MODAL || cModal().type === LDBModal.LDB_CONFIG_MODAL)" 
      [isEditDisable]="checkIfOkDisabled()" 
      class="dont-print" 
      [pageModal]="pageModal"
    ></modal-footer>
  </div>
</div>


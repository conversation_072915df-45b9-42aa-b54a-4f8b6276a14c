@import './core/main-nav/style/common.scss';

$infoButtonBackColor: rgb(0, 145, 255);

@media only print {
    .hide-on-paper {
        display:none;
    }
}

.left-panel-container {
    display:none;
    width: $leftPanelWidth;
    position:fixed;
    top:0em;
    bottom:0em;
    left:0em;
    background-color: #fff;
    box-shadow: 0em 0em 1em rgba(0,0,0,0.05);
    // box-shadow: 0em 0em 0.3em rgba(0,0,0,0.2);
    transition: width 400ms;
}

.page-content {
    transition: padding-left 400ms;
    min-height:100%;
    margin-bottom: 0em;
}
.is-panel-visible {
    .left-panel-container {
        display:block;
        width: $leftPanelWidth;
    }
    .page-content { 
        padding-left: $leftPanelWidth; 
    }
    &.is-panel-expanded {
        .left-panel-container{
            width: $leftPanelWidthExpanded;
        }
        .page-content {
            padding-left: $leftPanelWidthExpanded;
        }    
    }
}

.content-shadow {
    cursor: pointer;
    background-color: rgba(50,50,50, 0.4);
    position:fixed;
    top:0em;
    bottom:0em;
    left:0em;
    right:0em;
    display:none;
}

.mobile-overhead {
    .mobile-overhead-bar {
        background-color: #fff;
        padding:0.3em 0.8em;
        border-radius:1em;
        display: flex;
        flex-direction: row;
        width:auto;
        margin:auto;
        box-shadow: 0px 0px 10px rgba(0,0,0,0.3);
        .btn-menu{
            cursor: pointer;
            margin-right:0.5em;
        }
    }
    position:fixed;
    top:1em;
    left:1em;
    right:1em;
    display: none;
}

.info-button {
    background-color: $infoButtonBackColor;
    height: 100%;
    width: 2.5em;
    border-top-left-radius: 1em;
    display: flex;
    justify-content: center;
    align-items: center;
    img {
        height: 50%;
    }
}

@media only screen and (max-width: 600px) {
    
    .is-panel-visible {
        .mobile-overhead {
            display: block;
        }
        .page-content {
            padding-top:5em;
        }
        .left-panel-container {
            display:none;
            width: $leftPanelWidth;
        }
        .page-content { 
            padding-left: 0px; 
        }
        &.is-panel-mobile-visible {
            .mobile-overhead {
                display: none;
            }
            .left-panel-container {
                display:block;
            }
            .content-shadow {
                display:block;
            }
        }
        &.is-panel-expanded {
            .left-panel-container{
                display:block;
                width: $leftPanelWidthExpanded;
            }
            .content-shadow{
                display:block;
            }
            .page-content {
                padding-left: 0px; 
            }    
        }
    }
}

@media only print {
    .left-panel-container {
        display:none !important;
    }
    .page-content {
        padding-left: 0px !important; 
    } 
}



.connection-display {
    position: fixed;
    top: 0px;
    right: 0px;
    .tag {
        margin: 0.4em;
    }
}



.mobile-overhead {
    .mobile-overhead-bar {
        background-color: #fff;
        padding: 0.3em 0.8em;
        border-radius: 1em;
        display: flex;
        flex-direction: row;
        width: auto;
        margin: auto;
        box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3);
        .btn-menu {
            cursor: pointer;
            margin-right: 0.5em;
        }
    }
    position:fixed;
    top:1em;
    left:1em;
    right:1em;
    display: none;
}

.text-checkbox{
    :hover{
        cursor: pointer;
    }
}


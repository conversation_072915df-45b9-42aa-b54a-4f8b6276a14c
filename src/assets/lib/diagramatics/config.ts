export enum DiagramaticsVersion {
  V1_2_12_1 = 'v1.2.12-1',
  V1_2_12_2 = 'v1.2.12-2',
  V1_3_8_e0 = 'v1.3.8-ext.0',
  V1_8_2_e0 = 'v1.8.2-ext.0',
}
export const LATEST_DIAGRAMATICS_VERSION = DiagramaticsVersion.V1_8_2_e0;

type Awaited<T> = T extends Promise<infer U> ? U : T;
export type DgLib = Awaited<ReturnType<typeof loadLatestDiagramaticsLibrary>>;
export type DgInt = DgLib['Interactive']['prototype']
export type DgDiagram = DgLib['Diagram']['prototype']
export type DgVector2 = DgLib['Vector2']['prototype']
export type DgAlignment = 'top-left' | 'top-center' | 'top-right' | 'center-left' | 'center-center' | 'center-right' | 'bottom-left' | 'bottom-center' | 'bottom-right';;

export enum DiagramaticsMode {
  JS = 'javascript',
  YAML = 'yaml',
}

export enum DiagramaticsImageQuality {
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low',
  CUSTOM = 'custom',
}

export const DIAGRAMATICS_IMAGE_QUALITY_SIZE = {
  [DiagramaticsImageQuality.HIGH] : 3000,
  [DiagramaticsImageQuality.MEDIUM] : 2500,
  [DiagramaticsImageQuality.LOW] : 1000,
}

export async function loadLatestDiagramaticsLibrary() {
  try {
    return await import('src/assets/lib/diagramatics/diagramatics-ext-1.8.2-ext.0.js');
  } catch (e) {
    console.error('Error loading diagramatics library: ', e);
    return undefined;
  }
}

export async function loadDiagramaticsLibrary(version : DiagramaticsVersion) {
  // 
  try {
    switch (version) {
      case DiagramaticsVersion.V1_2_12_1 : return await import('src/assets/lib/diagramatics/diagramatics-ext-1.2.12-1.js');
      case DiagramaticsVersion.V1_2_12_2 : return await import('src/assets/lib/diagramatics/diagramatics-ext-1.2.12-2.js');
      case DiagramaticsVersion.V1_3_8_e0 : return await import('src/assets/lib/diagramatics/diagramatics-ext-1.3.8-ext.0.js');
      case DiagramaticsVersion.V1_8_2_e0 : return await import('src/assets/lib/diagramatics/diagramatics-ext-1.8.2-ext.0.js');
      default: {
        console.warn('Unknown diagramatics version: ', version);
        return await loadLatestDiagramaticsLibrary();
      }
    }
  } catch (e) {
    console.error('Error loading diagramatics library: ', e);
    return undefined;
  }
}


export enum PERUSAL_TYPES {
  STATIC_TIME = "STATIC_TIME",
  RELATIVE_SESSION_START = "RELATIVE_SESSION_START",
  RELATIVE_FIRST_STUDENT_START = "RELATIVE_FIRST_STUDENT_START",
  NO_LIMIT = "NO_LIMIT"
}

export const TW_STATEMENT_ASSESSMENT_COMPONENT_RECORD = `
WITH ts as ( 
	select 
    ts.id
    , ts.date_time_start
    , scts.slug 
    , min(ta.started_on) as first_ta_started_on
    from test_sessions ts
	join school_class_test_sessions scts
	 on ts.id = scts.test_session_id
	left join test_attempts ta
		on ta.test_session_id = ts.id
        and ta.is_invalid = 0
	where ts.schl_group_id = :schl_group_id
	  and ts.test_window_id  = :test_window_id
	  and ts.is_cancelled = 0
	group by ts.id
)
select 
  twtt.*
  , ts.id sample_test_session_id
from     
( select twtt.id
        , twtt.type_slug asmt_type_slug
        , twtt.caption_short 
        , meta 
        , twtt.is_perusal_allow
        , twtt.perusal_configs
        , twtar.perusal_type
        , twtar.perusal_offset_hours
        , twtar.perusal_duration_hours
        , twtar.perusal_date_start
        , twtar.perusal_date_end
        , twtar.test_design_id
        , td.source_item_set_id
        , min(ts.date_time_start) as ts_date_time_start
        , min(ta.started_on) as first_ta_started_on
        , CASE WHEN (tw.is_active = 1 and tw.date_start < now() and tw.date_end > now()) THEN 1 ELSE 0 END is_tw_current
    from test_window_td_alloc_rules twtar
    join test_windows tw
      on tw.id = twtar.test_window_id
    join test_window_td_types twtt 
      on twtt.type_slug = twtar.type_slug
      and twtt.test_window_id is null
      and twtt.is_revoked = 0
    join test_designs td
      on td.id = ifnull(twtar.tqr_ovrd_td_id, twtar.test_design_id)
    left join school_class_test_sessions scts
      on twtar.type_slug = scts.slug  
    left join test_sessions ts
      on ts.schl_group_id = :schl_group_id
      and ts.test_window_id  = :test_window_id
      and scts.test_session_id = ts.id
      and ts.is_cancelled = 0
    left join test_attempts ta
      on ta.test_session_id = ts.id
      and ta.is_invalid = 0
    where twtar.test_window_id  = :test_window_id
      and twtar.is_sample = 0
      and (twtar.is_active = 1 or twtar.is_active_previously)
    group by twtt.id
) twtt
left join ts
on (
	(twtt.perusal_type = "${PERUSAL_TYPES.RELATIVE_FIRST_STUDENT_START}" and ts.first_ta_started_on = twtt.first_ta_started_on) 
	or (twtt.perusal_type = "${PERUSAL_TYPES.RELATIVE_SESSION_START}" and ts.date_time_start = twtt.ts_date_time_start)
  or ((twtt.perusal_type is null or twtt.perusal_type not in ("${PERUSAL_TYPES.RELATIVE_FIRST_STUDENT_START}", "${PERUSAL_TYPES.RELATIVE_SESSION_START}")) and ts.id is not null)
)
and twtt.asmt_type_slug = ts.slug  
group by twtt.id
;
`

export const TW_STATEMENT_STUDENT_ASMT_REG_STATUS = `
    select tum.uid, twtt.type_slug 
    from tw_user_metas tum 
    join assessment_courses ac 
        on ac.course_code_foreign = tum.asmt_type_slug
        and ac.is_revoked = 0
    join test_window_td_types twtt 
        on twtt.course_code = ac.course_code 
        and twtt.test_window_id is null
        and twtt.is_revoked = 0
    where tum.key in ('StudentDiplomaExamInfo') -- todo:hardcoded
        and tum.value = 1
        and tum.uid IN (:uids)
        and tum.test_window_id = :test_window_id
`

export const TW_STATEMENT_STUDENT_TW_META = (isProvince: boolean| undefined) => {
    return `
    select ur.uid
          , tum.asmt_type_slug 
          , tum.key_namespace 
          , tum.key
          , tum.value 
          , s.foreign_id as s_code
      from schools s 
      join school_classes sc 
        on sc.schl_group_id = s.group_id 
        and sc.is_active = 1
      join school_semesters ss 
        on ss.id = sc.semester_id 
      join user_roles ur 
        on ur.group_id = sc.group_id 
        and ur.role_type = 'schl_student'
        and ur.is_revoked = 0
      join tw_user_metas tum 
        on tum.uid = ur.uid 
      where ss.test_window_id  = :test_window_id
      ${isProvince ? '' : 'and s.group_id = :schl_group_id'}`
}

export const TW_STATEMENT_REPORTED_ISSUE = `
    select ric.id
      , ric.description  
    from reported_issues_common ric
    join reported_issues ri
      on ri.reported_issues_common_id = ric.id
      and ri.testtaker_uid = :uid
      and ric.test_window_id = :test_window_id
    join school_class_test_sessions scts 
      on scts.test_session_id = ric.test_session_id
      and scts.slug = :asmt_type_slug
    group by ric.id;
`

export const TW_STUDENTS_ASMT_STATUS = (isProvince: boolean) => {
  return `
  select ta.uid
          , s.foreign_id as s_code
          , twtt.type_slug asmt_type_slug 
          , 1 is_in_ts
          , case when max(ta.started_on)  is not null then 1 else 0 end is_submitted
          , count(distinct ri.id) report_num
      from schools s 
      join school_classes sc 
        on sc.schl_group_id = s.group_id 
      join school_semesters ss 
        on ss.id = sc.semester_id 
      join school_class_test_sessions scts 
        on scts.school_class_id  = sc.id 
      join test_sessions ts 
        on ts.id = scts.test_session_id 
      -- 	and ts.is_cancelled = 0
      join test_window_td_types twtt
        on twtt.type_slug  = scts.slug 
        and twtt.test_window_id is null
        and twtt.is_revoked = 0
      join test_attempts ta 
        on ta.test_session_id  = ts.id 
        and ta.is_invalid = 0
        and ta.started_on  is not null 
      left join reported_issues_common ric
        on ric.test_window_id = ss.test_window_id 
        and sc.id = ric.school_class_id
        -- and ric.test_session_id = ts.id
      left join reported_issues ri
        on ri.reported_issues_common_id = ric.id
        and ri.testtaker_uid = ta.uid
      where ss.test_window_id  = :test_window_id
      ${isProvince ? '' : 'and s.group_id = :schl_group_id'}
      group by ta.uid, twtt.type_slug
  `
}
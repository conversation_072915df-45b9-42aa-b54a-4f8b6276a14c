import { DagScope } from './assets';


export interface DagJobDef {
  assets: string[];
  scope: string;
  pipeline_config: {[key:string]: boolean | number | string | number[] | string[] }
  pipeline_config_req: {[key:string]: string }
}

export const TEMP_DAG_JOBS:any = {

  "tc-attempts": {
    "isDisabled": true,
    "description": "",
    "scope": "schools",
    "assets": ["load_ta_detail", "load_ts_detail"],
    "pipeline_config": {
      "include_sample_assessments": false,
      "include_questionnaire": true,
      "only_secured_twtdar": false,
    },
    "pipeline_config_req": {
      "test_window_ids": "number[]",
    }
  },
  "tc-attempts-school": {
    "description": "Test controller tables for school-based assessments",
    "assets": [
      "load_ta_detail",
      "load_ts_detail",
      "trfm_attempts_by_form_code",
    ],
    "scope": "schools",
    "pipeline_config": {
      "include_sample_assessments": false,
      "include_questionnaire": true,
      "only_secured_twtdar": false,
    },
    "pipeline_config_req": {
      "test_window_ids": "number[]",
    }
  },
  "tc-attempts-testcentre": {
    "description": "Test controller tables for test-centre-based assessments",
    "assets": [
      "load_ta_from_tw__testcentres",
      "trfm_item_responses",
      "load_test_reports",
      "trfm_result_distribution",
      "trfm_item_response_value_stats",
      "trfm_item_scale_stats",
    ],
    "scope": "test-centres",
    "pipeline_config": {
      "include_questionnaire": true,
      "only_secured_twtdar": false,
    },
    "pipeline_config_req": {
      "test_window_ids": "number[]",
    }
  },
  "tc-attempts-responses": {
    "isDisabled": true,
    "description": "",
    "assets": ["load_ta_detail", "load_ts_detail", "trfm_item_responses"],
    "scope": "schools",
    "pipeline_config": {
      "include_sample_assessments": false,
      "include_questionnaire": false,
      "only_secured_twtdar": false,
    },
    "pipeline_config_req": {
      "test_window_ids": "number[]",
    }
  },
  "tc-attempts-responses-testcentre": {
    "isDisabled": true,
    "description": "",
    "assets": ["load_ta_detail", "load_ts_detail", "trfm_item_responses", "test_reports", "result_distribution"],
    "scope": "test-centres",
    "pipeline_config": {
      "include_sample_assessments": false,
      "include_questionnaire": true,
      "only_secured_twtdar": false,
    },
    "pipeline_config_req": {
      "test_window_ids": "number[]",
    }
  },
  "tw-marking-pool-readiness": {
    "isDisabled": false,
    "description": "",
    "assets": [
      // "load_test_attempts_from_uid",
      "load_db_marking_pooled_attempts_by_asmt_code",
      "load_db_marking_not_pooled_attempts_with_no_withhold_by_asmt_code",
      "load_db_marking_not_pooled_attempts_with_no_withhold",
      "load_db_marking_not_pooled_attempts", // interim
      // "load_db_marking_pooled_attempts", // interim
      // "load_db_marking_window_asmt_code", // interim
      // "load_tw_student_exceptions", // interim
      // "trfm_test_attempts_exceptions_applied",  // uses load_test_attempts_from_uid... need to confirm method before relying on it
      // "trfm_students_withhold_applied", // does not apply attempt level withholds
      // "trfm_check_students_mult_attempts", // not directly relevant, but a useful check
      // "trfm_invalid_attempts", // not directly relevant, but a useful check

    ],
    "scope": "schools",
    "pipeline_config": {
      "only_human_marked_twtdar": true,
    },
    "pipeline_config_req": {
      "test_window_ids": "number[]",
    }
  },

  "pkg_abed_administration_minus_marking_wip": {
    "scope": "schools",
    "description": "Work in progress of abed administration exports",
    "assets": [
      // Codebook
      "load_codebook_tables",
      "load_codebook_table_fields",
      // Key Outputs
      "load_db_td",
      "load_db_form_designs",
      "load_twtar",
      "trfm_item_register_consolidated",
      "trfm_item_expected_answers",
      "trfm_students_withhold_applied",
      "load_db_schools",
      "load_db_school_groupings",
      "load_db_students",
      "load_db_student_grouping_registrations",
      "load_db_aw_accommodation_options",
      "trfm_student_accommodations_consolidated",
      "load_tw_student_exceptions_withhold",
      "load_tw_item_exceptions",
      "load_tw_student_item_exceptions",
      "trfm_test_attempts_exceptions_applied",
      "load_ta_detail",
      "load_db_response_type_pattern_item_type",
      "trfm_item_responses_nf_omit",
      "trfm_blank_responses",
      "trfm_item_scale_stats",
      "trfm_item_response_value_stats",
      "trfm_cutoff_points",
      "trfm_form_stats",
      "load_db_student_admin_meta_booklets",
      "trfm_response_stats",
      "trfm_response_stats_summary",
      "trfm_response_stats_summary_min_10_words",
      "trfm_student_attempts_consolidated",
      // Check assets
      "trfm_check_tqr_fields",
      "check_tqer_weight_discrepancies",
      "trfm_check_discrepant_tqers", // a bit redundant with the weight discrepancies
      "trfm_check_missing_correct_response",
      "trfm_check_attempts_from_uid",
      "trfm_check_multiple_coded_responses",
      "trfm_check_multiple_item_expected_answers",
      "trfm_check_expected_answers_empty_columns",
      "trfm_check_principal_kit_students",
    ],
    "pipeline_config": {
      "include_sample_assessments": false,
      "include_questionnaire": true,
      "only_secured_twtdar": false,
      "include_sample_schools": false,
    },
    "pipeline_config_req": {
      "test_window_ids": "number[]",
    },
    "pipeline_config_opt": {
      "twtar_type_slugs": "string[]",
    }
  },
  "item-analysis": {
    "scope": "schools",
    "description": "This job is used specifcally to update/insert new item analysis data ",
    "assets": [
      // Codebook
      "load_codebook_tables",
      "load_codebook_table_fields",
      // Key Outputs
      "trfm_item_scale_stats",
      "trfm_item_response_value_stats",
      "trfm_form_stats",
      // Check assets
      "trfm_check_tqr_fields",
      "check_tqer_weight_discrepancies",
      "trfm_check_discrepant_tqers", // a bit redundant with the weight discrepancies
      "trfm_check_missing_correct_response",
      // Extra, but pretty closely related
      "load_db_tqer",
      "trfm_item_register_consolidated",
      "trfm_cutoff_points",
    ],
    "pipeline_config": {
      "include_sample_assessments": false,
      "include_questionnaire": true,
      "only_secured_twtdar": false,
      "include_sample_schools": false,
    },
    "pipeline_config_req": {
      "test_window_ids": "number[]",
    },
    "pipeline_config_opt": {
      "twtar_type_slugs": "string[]",
    }
  },
  "pkg_abed_administration_minus_marking": {
    "isDisabled": true,
    "scope": "schools",
    "description": "Legacy version of abed administration exports",
    "assets": [
      // "load_test_attempts_from_uid",
      "load_db_students",
      "load_tw_student_exceptions",
      "load_tw_item_exceptions",
      "load_tw_student_item_exceptions",
      "trfm_test_attempts_exceptions_applied",
            // "trfm_item_responses",
      "trfm_check_items_discrep_response_type_summary",
      "trfm_pre_check_nr_response_discrepancies",
      "trfm_principal_kit_school_asmt",
      "trfm_student_attempts_consolidated",
      "trfm_item_responses_nf_omit",
      "trfm_check_students_mult_attempts",
      "trfm_invalid_attempts",
      "trfm_item_register_consolidated",
      "trfm_automated_messages",
      "load_db_student_admin_meta_booklets",
      "load_db_twtar",
      "trfm_check_items_discrep_response_type",
      "load_db_item_expected_answers",
      "trfm_check_score_max_discrepancies",
      "trfm_resp_stats_summary",
      "trfm_check_numeric_responses",
      "load_db_response_type_pattern_item_type",
      "trfm_check_tqr_unassigned_items",
      "trfm_student_accommodations_aggregated",
      "trfm_item_responses_fixed_filled",
      "trfm_cutoff_points",
      "trfm_check_tw_exceptions_students_applied",
      "trfm_tw_exceptions_items_flagged",
      "trfm_check_discrepant_tqers",
      "trfm_check_tqer_tei_score_discrepancies",
      "trfm_principal_kit_students",
      "trfm_check_taqr_discrepant_scores_grouped",
      "load_db_form_designs",
      "trfm_check_tfmi",
      "trfm_blank_responses",
      "trfm_item_responses_fixed",
      "load_db_tc_student_attempts",
      "spec_threshold_values",
      "trfm_check_principal_kit_students",
      "load_db_tw_exceptions_student_items_applied",
      "spec_exclusion_threshold",
      "trfm_check_nr_response_discrepancies",
      "load_db_item_scale_register_consolidated",
      "trfm_check_part_marks_summary",
      "load_db_aw_accommodation_options",
      "trfm_check_taqr_discrepant_scores",
      "trfm_item_response_value_stats",
      "trfm_exclusion_threshold",
      "trfm_student_accommodations_consolidated",
      "trfm_test_attempt_total_scores",
      "trfm_principal_kit_meta",
      "trfm_check_max_scores",
      "trfm_pre_check_score_max_discrepancies",
      "load_db_td",
      "load_db_tw_exceptions_items_applied",
      "trfm_check_taqr_nr_score",
      "check_cutoff_points",
      "trfm_resp_stats_summary_min_10_words",
      "trfm_resp_stats",
      "trfm_check_part_marks",
      "load_db_school_groupings",
      "trfm_item_scale_stats",
      "trfm_form_stats",
      "trfm_taqr_discrepant_scores",
      "load_db_tw_exceptions_students_applied",
      "load_db_students",
    ],
    "pipeline_config": {
      "include_sample_assessments": false,
      "include_questionnaire": true,
      "only_secured_twtdar": false,
    },
    "pipeline_config_req": {
      "test_window_ids": "number[]",
    }
  },
  "reports-interim-marker": {
    "scope": "schools",
    "description": "Data for generating interim marker reports",
    "assets": [
      // Codebook
      "load_codebook_tables",
      "load_codebook_table_fields",
      // Key Outputs
      "trfm_marker_data_histogram",
      "trfm_average_scores_histogram",
      "trfm_average_scale_score_box_and_whisker",
      "trfm_marker_third_read_rate_hist",
      "trfm_marker_scoring_category",
      "trfm_scores_by_scoring_category"
    ],
    "pipeline_config": {
      "include_sample_assessments": false,
    },
    "pipeline_config_req": {
      "test_window_ids": "number[]",
      "marking_window_id": "number",
    }
  },
  "item-register": {
    "description": "The item register and test designs for a test window",
    "assets": [
      // Codebook
      "load_codebook_tables",
      "load_codebook_table_fields",
      // Key Outputs
      "load_db_td",
      "load_db_form_designs",
      "load_twtar",
      "trfm_item_register_consolidated",
      "trfm_item_expected_answers",
      // Check assets
      "trfm_check_tqr_fields",
      "check_tqer_weight_discrepancies",
      "trfm_check_discrepant_tqers", // a bit redundant with the weight discrepancies
      "trfm_check_missing_correct_response",
      "trfm_check_multiple_item_expected_answers",
      "trfm_check_expected_answers_empty_columns"
    ],
    "pipeline_config": {
      "include_sample_assessments": false,
      "include_questionnaire": true,
      "only_secured_twtdar": false,
    },
    "pipeline_config_req": {
      "test_window_ids": "number[]",
    }
  },
  "pkg_abed_student_registrations": {
    "description": "Student registrations",
    "assets": [
      // Codebook
      "load_codebook_tables",
      "load_codebook_table_fields",
      "load_db_schools", // Schools with students registered
      "load_db_students", // Students registered in an active school
      "load_db_school_groupings", // The groupings or classes
      "load_db_student_grouping_registrations",
      "trfm_students_withhold_applied",
      "load_tw_student_exceptions",
    ],
    "scope": "schools",
    "pipeline_config": {
      "include_sample_assessments": false,
      "include_questionnaire": true,
      "only_secured_twtdar": false,
      "include_sample_schools": false,
    },
    "pipeline_config_req": {
      "test_window_ids": "number[]",
    }
  },
  "school-administrators": {
    "description": "Pulls all of the schools and the associated school administrators.",
    "assets": [
      "load_db_schools",
      "load_db_school_administrators"
    ],
    "scope": "schools",
    "pipeline_config": {
      "include_sample_schools": false
    },
    "pipeline_config_req": {}
  },
  "principal-kits": {
    "description": "Only the principal kits assets",
    "assets": [
      // Codebook
      "load_codebook_tables",
      "load_codebook_table_fields",
      // Key Outputs
      "trfm_principal_kit_meta",
      "trfm_principal_kit_school_asmt",
      "trfm_principal_kit_students",
      "trfm_check_pk_uids_in_students",
      "trfm_check_principal_kit_students",
    ],
    "pipeline_config": {
      "include_sample_schools": false,
    },
    "pipeline_config_req": {
      "test_window_ids": "number[]",
    }
  },
  "example_school_reports": {
    "scope": "schools",
    "description": "Data for generating example school reports (legacy)",
    "assets": [
      // Codebook
      "load_codebook_tables",
      "load_codebook_table_fields",
      // Key outputs
      "trfm_individual_student_exceptions",
      "trfm_students_with_standards",
      "trfm_outcome_percentage_distribution",
      "trfm_students_with_standards_by_gender",
      "trfm_outcome_percentage_distribution_by_gender",
    ],
    "pipeline_config": {
      "include_sample_assessments": false,
    },
    "pipeline_config_req": {
      "test_window_ids": "number[]",
    }
  },
  "tc-scan-detail": {
    "description": "Scan information",
    "assets": [
      "scan_detail_by_assessment",
      "scan_detail_by_school",
      "scan_detail_by_student",
      "scan_detail_by_session",
      "scan_count_by_date"
    ],
    "scope": "schools",
    "pipeline_config": {
      "include_sample_assessments": true,
      "include_questionnaire": true,
      "include_not_reported_schools": true,
      "include_sample_schools": true,
      "only_secured_twtdar": false,
    },
    "pipeline_config_req": {
      "test_window_ids": "number[]",
    }
  },
  "authoring-recent-edits": {
    "description": "Monitoring of Authoring Activity",
    "assets": [
      "load_tw_ts_forms_inactive",
      "load_sc_cf_slug_mismatch",
      "trfm_tw_ta_forms_superceded_past",
      "trfm_tw_ta_forms_superceded_upcoming",
      "load_sessions_superceded_forms",
      "load_sessions_superceded_forms_to_attempts",
      "trfm_tw_ta_forms_superceded_summary",
      "trfm_tw_ta_forms_superceded_window_summary",
      "load_items_created",
      "load_items_deleted",
      "load_items_modified",
      "load_item_answers_added",
      "load_item_answers_removed",
      "load_frameworks",
      "load_publishings",
      "load_form_allocations",
      "load_recently_allocated",
      "load_recently_ready_for_struct_reviews",
      "load_tw_active",
    ],
    "scope": "schools",
    "pipeline_config": {
      "include_sample_assessments": false,
      "include_questionnaire": true,
      "include_not_reported_schools": true,
      "include_sample_schools": false,
      "only_secured_twtdar": false,
    },
    "pipeline_config_req": {

    },
    "pipeline_config_opt": {
      // "test_window_ids": "number[]",
      // "days_back": "number",
      // "auth_group_name": "string",
    }
  },
  "local-scoring-missing-scores": {
    "description": "Monitoring missing score after pooling local scoring into central scoring",
    "isDisabled": false,
    "scope": "schools",
    "assets": [
      "load_db_marking_window_local_marking_missing_score"
    ],
    "pipeline_config": {

    },
    "pipeline_config_req": {
      "test_window_ids": "number[]",
    },
    "pipeline_config_opt": {

    }
  }

}




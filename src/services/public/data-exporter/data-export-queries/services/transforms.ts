import { Application } from "@feathersjs/express";
import { Errors } from '../../../../../errors/general';
import { DataJobLogger } from "./logger";
import { IRecord } from "../types/type"

import { inner_join, left_join } from "../transforms/merge"
import { filter_col } from "../transforms/filter-col"
import { group_by } from "../transforms/group-by"
import { pivot, aggregators } from "../transforms/pivot"
import { restrict_cols } from "../transforms/restrict-cols"
import { sort_by } from "../transforms/sort-by"
import { map_col } from "../transforms/map-col"
import { drop_duplicates } from "../transforms/drop-duplicates"
import { aggregate } from "../transforms/aggregate";
import { normed_histogram } from "../transforms/normed-histogram";
import replaceWhere from "../transforms/replace-where"
import { renameCols, renameColsInplace } from "../transforms/rename-cols"
import { concatDataFrames } from "../transforms/concat"
import { fillNaMulti, fillNaMultiInplace } from "../transforms/fill-na"
import { cutoffs } from "../transforms/cutoffs"
import { applyCutscores } from "../transforms/apply-cutscores"
import { setWhere, setWhereInplace } from "../transforms/set-where"
import { pointBiserial } from "../transforms/correlations"
import { wr_stats } from "../transforms/wr-stats";
import { cast_col } from "../transforms/cast-col";

type IDataFrame = IRecord[];

export interface IServiceTransformJoin {
  how: "inner" | "left" | "right",
  left: string,
  right: string,
  left_on: string,
  right_on?: string,
  logger: DataJobLogger,
}

function checkInSchema(df: IDataFrame, cols: string[]) {
  const missing: string[] = [];
  // All transforms should work with empty dataframes
  if (df.length == 0) {
    return missing;
  }
  for (let col of cols) {
    if (df[0][col] === undefined) {
      missing.push(col)
    }
  }
  return missing;
}

export const serviceTransformJoin = async (app: Application, config: IServiceTransformJoin, sequenceData: any) => {

  let {
    how,
    left,
    right,
    left_on,
    right_on,
  } = config;
  const left_data: IDataFrame = sequenceData[left];
  const right_data: IDataFrame = sequenceData[right];

  if (!left_data) {
    throw new Error(`Configuration error: Dataframe "${left}" not found for join`)
  }
  if (!right_data) {
    throw new Error(`Configuration error: Dataframe "${right}" not found for join`)
  }
  if (!left_on) {
    throw new Error(`Configuration error: missing left_on in join config`)
  }

  // TODO: better to be explicit everywhere than coerce undefined values
  if (!right_on) {
    right_on = left_on;
  }
  let l_on = (typeof left_on == 'string') ? [left_on] : left_on
  let r_on = (typeof right_on == 'string') ? [right_on] : right_on

  if (r_on.length != l_on.length) {
    throw new Error(`Configuration error: left_on and right_on must have the same length`)
  }

  let missing = checkInSchema(left_data, l_on);
  if (missing.length > 0) {
    throw new Error(`Configuration error: left_on column(s) ${missing} not found in dataframe ${left}`)
  }
  missing = checkInSchema(right_data, r_on as string[]);
  if (missing.length > 0) {
    throw new Error(`Configuration error: right_on column(s) ${missing} not found in dataframe ${right}`)
  }

  const timeStart = +(new Date())

  let records;
  switch (how) {
    case "inner":
      records = inner_join(left_data, right_data, l_on, r_on)
      break;
    case "left":
      // Check the schema
      records = left_join(left_data, right_data, l_on, r_on)
      break;
    case "right":
      throw new Errors.NotImplemented(`exporter: ${how} join not yet implemented`)
    default:
      throw new Errors.Unprocessable(`exporter: join method ${how} not recognized`)
  }

  const timeDiff = ((+(new Date()) - timeStart) / 1000).toFixed(3)
  console.log('transform-step ::', how, "join", 'end', timeDiff)

  return records
}

export async function serviceTransformFilterCol(app: Application, config: any, sequenceData: any) {
  const {
    col,
    comparison,
    value,
    value_src,
  } = config;
  const df_input = sequenceData[config.df_input];

  // TODO: Add config validation

  let _value:(string | number)[] = value
  if (value_src){
    if (typeof value_src == 'string') {
      _value = sequenceData[value_src];
      if (!_value) {
        throw new Error(`Configuration error: asset-col "${value_src}" not found for filter-col`)
      }
      if (_value){
        // make unique version of the array
        _value = [... new Set(_value.map(v => ''+v))]
      }
    } else {
      let {df, col} = value_src;
      let records: IDataFrame = sequenceData[df];
      if (!records) {
        throw new Error(`Configuration error: dataframe "${df}" not found for filter-col`)
      }
      if (records.length > 0 && !(col in records[0])) {
        throw new Error(`Configuration error: column "${col}" not found in dataframe "${df}" for filter-col`)
      }
      _value = [... new Set(records.map( r => ''+r[col]))]
    }
  }

  return filter_col(df_input, col, comparison, _value);
}

export async function serviceTransformDropDuplicates(app: Application, config: any, sequenceData: any) {
  let {
    subset,
  } = config;
  const df_input = sequenceData[config.df_input];
  if (!df_input) {
    throw new Error(`Configuration error: Dataframe "${config.df_input}" not found for drop-duplicates`)
  }
  // TODO: Global config validation should enforce a list, but we can still coerce it here
  if (typeof subset === 'string') {
    subset = [subset]
  }
  const result = drop_duplicates(df_input, subset);

  return result;
}

export async function serviceTransformGroupBy(app: Application, config: any, sequenceData: any) {
  const {
    group_by: cols,
    agg,
  } = config;
  const df_input = sequenceData[config.df_input];

  if (!df_input) {
    throw new Error(`Configuration error: Dataframe "${config.df_input}" not found for group-by`)
  }
  if (!cols) {
    throw new Error(`Configuration error: missing group_by in group-by config`)
  }

  let by_cols = typeof cols === 'string' ? [cols] : cols

  return group_by(df_input, by_cols, agg);
}

export async function serviceTransformPivot(app: Application, config: any, sequenceData: any) {
  const {
    group_by: rowHeader,
    index: indexCol,
    value: valueCol,
    default_value: defaultValue,
    col_prefix,
    agg,
  } = config;
  const df_input = sequenceData[config.df_input];

  const aggFunc = aggregators[<string> agg];
  if (!aggFunc) {
    throw new Error(`aggregation ${agg} not supported by pivot transform`);
  }
  return pivot(df_input, rowHeader, indexCol, valueCol, aggFunc, col_prefix, defaultValue);
}

export async function serviceTransformMapCol(app: Application, config: any, sequenceData: any) {
  const {
    col_output,
    source_cols,
    operation,
    value_true,
    value_false,
  } = config;
  const df_input = sequenceData[config.df_input];

  return map_col(df_input, col_output, source_cols, operation, value_true, value_false);
}

export async function serviceTransformRestrictCols(app: Application, config: any, sequenceData: any) {
  const {
    cols,
  } = config;
  const df_input = sequenceData[config.df_input];

  if (!df_input) {
    throw new Error(`Configuration error: Dataframe "${config.df_input}" not found for restrict-cols`)
  }
  if (!cols) {
    throw new Error(`Configuration error: missing cols in restrict-cols config`)
  }
  return restrict_cols(df_input, cols);
}

export async function serviceTransformCastCol(app: Application, config: any, sequenceData: any) {
  const {
    col_target,
    target_type
  } = config;
  const df_input = sequenceData[config.df_input];
  return cast_col(df_input, col_target, target_type);
}


export async function serviceTransformSortBy(app: Application, config: any, sequenceData: any) {
  const {
    cols,
    ascending,
  } = config;
  const df_input = sequenceData[config.df_input];
  return sort_by(df_input, cols, ascending);
}

export async function serviceTransformAggregate(app: Application, config: any, sequenceData: any) {
  const {
    agg,
  } = config;
  const df_input = sequenceData[config.df_input];

  return aggregate(df_input, agg);
}

export async function serviceTransformNormedHistogram(app: Application, config: any, sequenceData: any) {
  const {
    count,
    mean,
    stdev,
    n_bins,
  } = config;
  const df_input = sequenceData[config.df_input];

  return normed_histogram(df_input, n_bins);
}

export async function serviceTransformReplaceWhere(app: Application, config: any, sequenceData: any) {
  let {
    source_key,
    target_key,
    source_col,
    target_col,
    indicator_col,
  } = config;
  const df_target = sequenceData[config.df_target];
  const df_source = sequenceData[config.df_source];
  // Configuration validation
  if (!df_target) {
    throw new Error(`Configuration error: Dataframe "${df_target}" not found`)
  }
  if (!df_source) {
    throw new Error(`Configuration error: Dataframe "${df_source}" not found`)
  }
  if (!source_col) {
    throw new Error(`Configuration error: missing source_col for replace-where`)
  }
  if (!target_col) {
    throw new Error(`Configuration error: missing target_col for replace-where`)
  }
  if (typeof indicator_col !== 'string') {
    throw new Error(`Configuration error: indicator_col must be a string for replace-where`)
  }
  if (typeof source_key === 'string') {
    source_key = [source_key]
  }
  if (typeof target_key === 'string') {
    target_key = [target_key]
  }
  return replaceWhere(df_target, df_source, target_key, source_key, target_col, source_col, indicator_col);
}


export async function serviceTransformRenameCols(app: Application, config: any, sequenceData: any) {
  const {
    new_names,
    inplace,
  } = config;
  const df_input = sequenceData[config.df_input];
  if (!df_input) {
    throw new Error(`Configuration error: Dataframe "${config.df_input}" not found for rename-cols`)
  }
  if (!new_names) {
    throw new Error(`Configuration error: missing new_names for rename-cols`)
  }
  if (typeof new_names !== 'object') {
    throw new Error(`Configuration error: new_names must be an object {old_name: new_name} for rename-cols`)
  }

  if (inplace) {
    return renameColsInplace(df_input, new_names);
  }
  return renameCols(df_input, new_names);
}

export async function serviceTransformConcat(app: Application, config: any, sequenceData: any) {
  const {
    df_inputs
  } = config;
  if (!df_inputs || df_inputs.length == 0) {
    throw new Error(`Configuration error: missing df_inputs for concat`)
  }
  let dfs = df_inputs.map((df: string) => sequenceData[df]);
  return concatDataFrames(dfs);
}

export async function serviceTransformCutoffs(app: Application, config: any, sequenceData: any) {
  const {
    cols,
    group_by,
    col_target,
    upper_bounds,
  } = config;
  const df_input = sequenceData[config.df_input];

  // TODO: check that the group and target fields are in the schema
  // A warning would be preferred rather than a full error, or just producing null values
  return cutoffs(df_input, group_by, col_target, upper_bounds);
}

export async function serviceTransformApplyCutscores(app: Application, config: any, sequenceData: any) {
  const {
    group_by,
    score_column,
    label_column,
    cutscore_defs,
  } = config;
  const df_input = sequenceData[config.df_input];
  const cutDefs = sequenceData[cutscore_defs];

  // TODO: check cutDefs schema
  // TODO: check the df_input schema

  return applyCutscores(df_input, group_by, score_column, cutDefs, label_column);
}

export async function serviceTransformFillNa(app: Application, config: any, sequenceData: any) {
  const { values, inplace } = config;
  const df_input = sequenceData[config.df_input];

  if (!df_input) {
    throw new Error(`Configuration error: Dataframe "${config.df_input}" not found for fill-na`)
  }
  if (typeof values !== 'object') {
    throw new Error(`Configuration error: values must be an object {column: value} for fill-na`)
  }
  // TODO: technically, we can go a bit faster for single columns using fillNa
  if (inplace) {
    return fillNaMultiInplace(df_input, values);
  }
  return fillNaMulti(df_input, values);
}

export async function serviceTransformSetWhere(app: Application, config: any, sequenceData: any) {
  const { condition, values, inplace, values_false } = config;
  const df_input = sequenceData[config.df_input];

  if (!df_input) {
    throw new Error(`Configuration error: Dataframe "${config.df_input}" not found for set-where`)
  }
  if (typeof values !== 'object') {
    throw new Error(`Configuration error: values must be an object {column: value} for set-where`)
  }
  if (typeof condition !== 'object') {
    // TODO: proper schema check on conditions
    throw new Error(`Configuration error: condition must be an object {comparison: string, col: string, value: any} for set-where`)
  }

  if (inplace) {
    return setWhereInplace(df_input, condition, values, values_false);
  }
  return setWhere(df_input, condition, values, values_false);
}

export async function serviceTransformPointBiserial(app: Application, config: any, sequenceData: any) {
  const { keep_cols } = config;
  const df_input = sequenceData[config.df_input];

  if (!df_input) {
    throw new Error(`Configuration error: Dataframe "${config.df_input}" not found for set-where`)
  }
  if ( keep_cols !== undefined && !Array.isArray(keep_cols) ) {
    throw new Error(`Configuration error: keep_cols must be an array for point-biserial`)
  }

  return pointBiserial(df_input, keep_cols);
}

type CheckFn = (config: any, paramNames: string[]) => {errors: string[], warnings: string[]};

export const checkConfigs : { [key: string]: CheckFn } = {
  'group-by': checkConfigGroupBy,
  'join': checkConfigJoin,
}

function checkConfigJoin(config: any, paramNames: string[]) {
  const errors: string[] = [];
  const warnings: string[] = [];
  const {
    how,
    left,
    right,
    left_on,
    right_on,
  } = config;

  // Check required fields
  for (let field of ['how', 'left', 'right', 'left_on']) {
    if (!config[field]) {
      errors.push(`missing ${field} in join config`)
    }
  }
  if (!right_on) {
    warnings.push(`right_on not defined in join config, defaulting to left_on`)
  }
  // Check dependencies exist
  if (!left || !paramNames.includes(left)) {
    errors.push(`missing left dataframe ${left} not found in dependencySourcings`)
  }
  if (!right || !paramNames.includes(right)) {
    errors.push(`missing right dataframe ${right} not found in dependencySourcings`)
  }
  // TODO: check for types/values of fields

  return {errors, warnings};
}

function checkConfigGroupBy(config: any, paramNames: string[]) {
  const {
    group_by,
    agg,
  } = config;
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!group_by) {
    errors.push(`missing group_by in group-by config`)
  }

  // Check aggregation configs
  if (!agg) {
    errors.push(`missing agg in group-by config`)
  }
  else if (!Array.isArray(agg)) {
    errors.push(`agg must be an array of objects in group-by config`)
  }
  else {
    for (let aggDef of agg) {
      if (!aggDef['col_new']) {
        errors.push(`missing col_new in "agg" definition for group-by config`)
      }
      if (!aggDef['agg_type']) {
        errors.push(`missing agg_type in "agg" definition for group-by config`)
      }
      if (!aggDef['col_target'] && aggDef['agg_type'] !== 'count') {
        errors.push(`missing col_target in "agg" definition for group-by config`)
      }
      // TODO: warn if extra fields are present
    }
  }

  // Make sure dependencies exist
  if (!config.df_input) {
    errors.push(`missing df_input in group-by config`)
  }
  else if (!paramNames.includes(config.df_input)) {
    errors.push(`dataframe "${config.df_input}" not found in parameters for group-by`)
  }
  return {errors, warnings};
}

export async function serviceTransformWrStats(app: Application, config: any, sequenceData: any) {
  const {
    column
  } = config;
  const df_input = sequenceData[config.df_input];

  return wr_stats(df_input, column);
}

/** Cast column data type algorithm for the API-DAG
 */

// Type definitions
type value = number | string | any
type IRow = {[key: string]: value}

/**
 * cast_col transform definition
 */
export function cast_col(df_input: IRow[], col_target: string, target_type: string): IRow[] {

  // Input Validations
  if (df_input.length == 0) {
    return []
  }

  return df_input.map(row => {
    const newRow = { ...row };
    if (newRow[col_target] !== null && newRow[col_target] !== undefined) {
      switch (target_type) {
        case "string":
          newRow[col_target] = String(newRow[col_target]);
          break;
        case "number":
          newRow[col_target] = Number(newRow[col_target]);
          if (isNaN(newRow[col_target])) {
            throw new Error(`Invalid value for number conversion: ${row[col_target]}`);
          }
          break;
        default:
          throw new Error(`Unsupported target type: ${target_type}`);
      }
    }
    return newRow;
  });
}

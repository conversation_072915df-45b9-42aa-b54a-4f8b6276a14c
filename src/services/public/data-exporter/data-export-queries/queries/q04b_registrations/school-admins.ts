import { IExportQueryDef } from "../../types/type";

interface IQueryConfig {
    s_gids: number[];
}

export const SQL_04B_SCHOOL_ADMINISTRATORS: IExportQueryDef = {
    requiredInputs: [
        's_gids'
    ],
    queryGen: (config: IQueryConfig) => `
        SELECT
              ur.uid
            , ur.group_id AS s_gid
            , s.foreign_id AS s_code
            , a.email
            , u.first_name
            , u.last_name
            , ur.created_on
        FROM
            schools s
        JOIN
            user_roles ur ON ur.group_id = s.group_id
        JOIN
            users u ON ur.uid = u.id
        JOIN
            auths a ON u.id = a.uid
        WHERE
            ur.role_type = 'schl_admin'
            AND ur.is_revoked = 0
            AND s.group_id IN (:s_gids)
    `
};
import { IExportQueryDef } from "../types/type";

/* q01_meta */
import { SQL_00_META_CODEBOOK_TABLES } from "./q00_meta/codebook-tables";
import { SQL_00_META_CODEBOOK_TABLE_FIELDS } from "./q00_meta/codebook-table-fields";

/* q01_sys */
import { SQL_01_SYS_TEST_WINDOWS } from "./q01_sys/test-windows";
import { SQL_01_SYS_TEST_WINDOWS_ACTIVE } from "./q01_sys/test-windows-active";

/* q02_asmt_specs */
import { SQL_02_ASMT_SPECS_TEST_WINDOW_ALLOC_RULES } from "./q02_asmt_specs/test-window-alloc-rules";
import { SQL_02_ASMT_SPECS_ACCOMMODATION_OPTIONS } from "./q02_asmt_specs/accommodation-options";
import { SQL_02_ASMT_SPECS_RESPONSE_TYPE_PATTERN_ITEM_TYPE } from "./q02_asmt_specs/response-type-pattern-item-type";

/* q03_asmt_content */
import { SQL_03_ASMT_CONTENT_ITEM_REGISTER_CONSOLIDATED } from "./q03_asmt_content/item-register-consolidated";
import { SQL_03_ASMT_CONTENT_ITEM_REGISTER } from "./q03_asmt_content/item-register";
import { SQL_03_ASMT_CONTENT_ITEM_EXPECTED_RESPONSES } from "./q03_asmt_content/item-expected-responses";
import { SQL_03_ASMT_CONTENT_TEST_DESIGNS } from "./q03_asmt_content/test-designs";
import { SQL_03_ASMT_CONTENT_FORM_DESIGNS } from "./q03_asmt_content/form-designs";
import { SQL_03_ASMT_CONTENT_TQR_GENERIC_PARAM_MAP } from "./q03_asmt_content/tqr-generic-param-map";
import { SQL_03_ASMT_CONTENT_TQR_PARAMS } from "./q03_asmt_content/tqr-params";
/* q03b_authoring_monitor */
import { SQL_03B_AUTH_MONIT_PUBLISHINGS } from "./q03b_authoring_monitor/publishings";
import { SQL_03B_AUTH_MONIT_ITEMS_MODIFIED } from "./q03b_authoring_monitor/items-modified";
import { SQL_03B_AUTH_MONIT_ITEMS_DELETED } from "./q03b_authoring_monitor/items-deleted";
import { SQL_03B_AUTH_MONIT_ITEMS_CREATED } from "./q03b_authoring_monitor/items-created";
import { SQL_03B_AUTH_MONIT_ITEM_ANSWERS_REMOVED } from "./q03b_authoring_monitor/item-answers-removed";
import { SQL_03B_AUTH_MONIT_ITEM_ANSWERS_ADDED } from "./q03b_authoring_monitor/item-answers-added";
import { SQL_03B_AUTH_MONIT_FRAMEWORKS } from "./q03b_authoring_monitor/frameworks";
import { SQL_03B_AUTH_MONIT_FORM_ALLOCATIONS } from "./q03b_authoring_monitor/form-allocations";
import { SQL_03B_AUTH_MONIT_RECENTLY_ALLOCATED } from "./q03b_authoring_monitor/recently-allocated";
import { SQL_03B_AUTH_MONIT_RECENTLY_READY_FOR_STRUCT_REVIEWS } from "./q03b_authoring_monitor/recently-ready-for-struct-reviews";


/* q04a_groups */
import { SQL_SCHL_CODE_TO_GID } from "./q04a_groups/school-code-to-gid";
import { SQL_SCHOOL_SC_PLACHOLDER } from "./q04a_groups/school-sc-placeholder";
import { SQL_04A_GROUPS_MARKER_USER_ROLES } from "./q04a_groups/marker-user-roles";
import { SQL_04A_GROUPS_SCHOOLS } from "./q04a_groups/schools";

/* q04b_registrations */
import { SQL_SCHOOL_STUDENT_ENROLMENTS } from "./q04b_registrations/school-student-enrolments";
import { SQL_STUDENT_GOV_ID_TO_UID } from "./q04b_registrations/student-gov-id-to-uid";
import { SQL_04B_REGISTRATIONS_SCHOOL_GROUPINGS } from "./q04b_registrations/school-groupings";
import { SQL_04B_REGISTRATIONS_STU_G_REGSTRATIONS  } from "./q04b_registrations/student-grouping-registrations";
import { SQL_04B_REGISTRATIONS_STUDENTS  } from "./q04b_registrations/students";
import { SQL_04B_REGISTRATIONS_STUDENT_ACCOMMODATIONS  } from "./q04b_registrations/student-accomodations";
import { SQL_04B_REGISTRATIONS_STUDENT_ADMIN_META  } from "./q04b_registrations/student-admin-meta";
import { SQL_04B_REGISTRATIONS_STUDENT_ADMIN_META_BOOKLETS  } from "./q04b_registrations/student-admin-meta-booklets";
import { SQL_04B_INDIVIDUAL_STUDENT_EXCEPTIONS } from "./q04b_registrations/individual-student-exceptions"
import { SQL_04B_STU_GOV_ID_FROM_UID } from "./q04b_registrations/stu-gov-id-from-uids";
import { SQL_04B_REGISTRATIONS_SCHOOL_CLASSES_GUEST } from "./q04b_registrations/school_classes_guest";


/* q05a_attempts */
import { SQL_05A_ATTEMPTS_FROM_TW_ID, SQL_05A_ATTEMPTS_FROM_TW_ID_TESTCENTRE } from "./q05a_attempts/attempts-from-tw-id";
import { SQL_05A_ATTEMPTS_DETAIL_FROM_TA_ID } from "./q05a_attempts/attempt-detail-from-ta-id";
import { SQL_05A_REPORT_ATTEMPTS_DETAIL_FROM_TA_ID } from "./q05a_attempts/report-attempt-detail-from-ta-id";
import { SQL_05A_ATTEMPTS_TS_DETAIL } from "./q05a_attempts/ts-detail";
import { SQL_05A_ATTEMPTS_TESTCENTRES } from "./q05a_attempts/attempts-testcenters";
import { SQL_05A_ATTEMPTS_TEST_REPORTS } from "./q05a_attempts/test-reports";
import { SQL_04B_STUDENT_EXCEPTIONS } from "./q04b_registrations/student-exceptions";
import { SQL_05A_ATTEMPTS_FROM_UID } from "./q05a_attempts/load-attempts-from-uid";
import { SQL_05A_SCHOOL_CLASS_TEST_SESSION } from "./q05a_attempts/school-class-test-session";
import { SQL_05A_TS_DETAIL_FROM_TA_ID } from "./q05a_attempts/ts-detail-from-ta-ids";
import { SQL_05A_TA_FORMS_SUPERCEDED } from "./q05a_attempts/ta-forms-superceded";
import { SQL_05A_TA_FORMS_INACTIVE } from "./q05a_attempts/ts-forms-inactive";
import { SQL_05A_SESSIONS_SUPERCEDED_FORMS } from "./q05a_attempts/sessions-superceded-forms";
import { SQL_05A_SESSIONS_SUPERCEDED_FORMS_TO_ATTEMPTS } from "./q05a_attempts/sessions-superceded-forms-to-attempts";

/* q05b_responses */
import { SQL_05B_TAQR_FROM_TA_ID } from "./q05b_responses/taqr-from-ta-id";
import { SQL_05B_ITEM_EXCEPTIONS } from "./q05b_responses/item-exceptions";
import { SQL_05B_STUDENT_ITEM_EXCEPTIONS } from "./q05b_responses/student-item-exceptions";
import { SQL_05B_TQER_ITEM_EXCEPTIONS } from "./q05b_responses/tqer-item-exceptions";

/* q6_marking */
import { SQL_06_MARKING_WINDOWS } from "./q06_marking/marking-windows";
import { SQL_06_MARKING_WINDOW_ASMT_CODE } from "./q06_marking/marking-window-asmt-code";
import { SQL_06_MARKING_WINDOW_POOLED_ATTEMPTS } from "./q06_marking/marking-window-pooled-attempts";
import { SQL_06_MARKING_WINDOW_LOCAL_MARKING_MISSING_SCORE } from "./q06_marking/marking-window-local-marking-missing-score";


/** q7_scans */
import { SQL_07_SCAN_INFO_FROM_TA_ID } from "./q07_scans/scan-info-from-ta-id";
import { SQL_07_SCAN_COUNT_BY_DATE_FROM_TA_ID } from "./q07_scans/scan-count-by-date-from-ta-ids";
import { SQL_04B_SC_CF_SLUG_MISMATCH } from "./q04b_registrations/class-form-lock-code-mismatch";
import { SQL_04B_SCHOOL_ADMINISTRATORS } from "./q04b_registrations/school-admins";



/////////////////////////
/////////////////////////
/////////////////////////

interface IExporterQueryRef {
    [key:string]: IExportQueryDef
}

export const ExporterQueryRef:IExporterQueryRef = {
    'SQL_00_META_CODEBOOK_TABLES': SQL_00_META_CODEBOOK_TABLES,
    'SQL_00_META_CODEBOOK_TABLE_FIELDS': SQL_00_META_CODEBOOK_TABLE_FIELDS,
    'SQL_01_SYS_TEST_WINDOWS': SQL_01_SYS_TEST_WINDOWS,
    'SQL_STUDENT_GOV_ID_TO_UID': SQL_STUDENT_GOV_ID_TO_UID,
    'SQL_SCHL_CODE_TO_GID': SQL_SCHL_CODE_TO_GID,
    'SQL_SCHOOL_STUDENT_ENROLMENTS': SQL_SCHOOL_STUDENT_ENROLMENTS,
    'SQL_SCHOOL_SC_PLACHOLDER': SQL_SCHOOL_SC_PLACHOLDER,
    'SQL_05A_ATTEMPTS_FROM_TW_ID': SQL_05A_ATTEMPTS_FROM_TW_ID,
    'SQL_05A_ATTEMPTS_DETAIL_FROM_TA_ID': SQL_05A_ATTEMPTS_DETAIL_FROM_TA_ID,
    'SQL_05A_REPORT_ATTEMPTS_DETAIL_FROM_TA_ID': SQL_05A_REPORT_ATTEMPTS_DETAIL_FROM_TA_ID,
    'SQL_05A_ATTEMPTS_TS_DETAIL': SQL_05A_ATTEMPTS_TS_DETAIL,
    'SQL_05B_TAQR_FROM_TA_ID': SQL_05B_TAQR_FROM_TA_ID,
    'SQL_02_ASMT_SPECS_TEST_WINDOW_ALLOC_RULES': SQL_02_ASMT_SPECS_TEST_WINDOW_ALLOC_RULES,
    'SQL_02_ASMT_SPECS_ACCOMMODATION_OPTIONS': SQL_02_ASMT_SPECS_ACCOMMODATION_OPTIONS,
    'SQL_02_ASMT_SPECS_RESPONSE_TYPE_PATTERN_ITEM_TYPE': SQL_02_ASMT_SPECS_RESPONSE_TYPE_PATTERN_ITEM_TYPE,
    'SQL_03_ASMT_CONTENT_ITEM_REGISTER_CONSOLIDATED': SQL_03_ASMT_CONTENT_ITEM_REGISTER_CONSOLIDATED,
    'SQL_03_ASMT_CONTENT_ITEM_REGISTER': SQL_03_ASMT_CONTENT_ITEM_REGISTER,
    'SQL_03_ASMT_CONTENT_ITEM_EXPECTED_RESPONSES': SQL_03_ASMT_CONTENT_ITEM_EXPECTED_RESPONSES,
    'SQL_03_ASMT_CONTENT_TEST_DESIGNS': SQL_03_ASMT_CONTENT_TEST_DESIGNS,
    'SQL_03_ASMT_CONTENT_FORM_DESIGNS': SQL_03_ASMT_CONTENT_FORM_DESIGNS,
    'SQL_05A_ATTEMPTS_TESTCENTRES': SQL_05A_ATTEMPTS_TESTCENTRES,
    'SQL_05A_ATTEMPTS_FROM_TW_ID_TESTCENTRE': SQL_05A_ATTEMPTS_FROM_TW_ID_TESTCENTRE,
    'SQL_05A_ATTEMPTS_TEST_REPORTS': SQL_05A_ATTEMPTS_TEST_REPORTS,
    'SQL_05A_TS_DETAIL_FROM_TA_ID': SQL_05A_TS_DETAIL_FROM_TA_ID,
    'SQL_05A_SCHOOL_CLASS_TEST_SESSION': SQL_05A_SCHOOL_CLASS_TEST_SESSION,
    'SQL_04A_GROUPS_MARKER_USER_ROLES': SQL_04A_GROUPS_MARKER_USER_ROLES,
    // student registrations assets
    "SQL_04A_GROUPS_SCHOOLS": SQL_04A_GROUPS_SCHOOLS,
    "SQL_04B_REGISTRATIONS_SCHOOL_GROUPINGS": SQL_04B_REGISTRATIONS_SCHOOL_GROUPINGS,
    "SQL_04B_REGISTRATIONS_SCHOOL_CLASSES_GUEST": SQL_04B_REGISTRATIONS_SCHOOL_CLASSES_GUEST,
    "SQL_04B_REGISTRATIONS_STU_G_REGSTRATIONS": SQL_04B_REGISTRATIONS_STU_G_REGSTRATIONS,
    "SQL_04B_REGISTRATIONS_STUDENTS": SQL_04B_REGISTRATIONS_STUDENTS,
    "SQL_04B_REGISTRATIONS_STUDENT_ACCOMMODATIONS": SQL_04B_REGISTRATIONS_STUDENT_ACCOMMODATIONS,
    "SQL_04B_REGISTRATIONS_STUDENT_ADMIN_META": SQL_04B_REGISTRATIONS_STUDENT_ADMIN_META,
    "SQL_04B_REGISTRATIONS_STUDENT_ADMIN_META_BOOKLETS": SQL_04B_REGISTRATIONS_STUDENT_ADMIN_META_BOOKLETS,
    "SQL_04B_SCHOOL_ADMINISTRATORS": SQL_04B_SCHOOL_ADMINISTRATORS,
    // added 2024-12-09
    'SQL_04B_STUDENT_EXCEPTIONS': SQL_04B_STUDENT_EXCEPTIONS,
    'SQL_05B_ITEM_EXCEPTIONS': SQL_05B_ITEM_EXCEPTIONS,
    'SQL_05B_STUDENT_ITEM_EXCEPTIONS': SQL_05B_STUDENT_ITEM_EXCEPTIONS,
    'SQL_05B_TQER_ITEM_EXCEPTIONS': SQL_05B_TQER_ITEM_EXCEPTIONS,
    'SQL_05A_ATTEMPTS_FROM_UID': SQL_05A_ATTEMPTS_FROM_UID,
    'SQL_04B_INDIVIDUAL_STUDENT_EXCEPTIONS': SQL_04B_INDIVIDUAL_STUDENT_EXCEPTIONS,
    // marking
    'SQL_06_MARKING_WINDOWS': SQL_06_MARKING_WINDOWS,
    'SQL_06_MARKING_WINDOW_ASMT_CODE': SQL_06_MARKING_WINDOW_ASMT_CODE,
    'SQL_06_MARKING_WINDOW_POOLED_ATTEMPTS': SQL_06_MARKING_WINDOW_POOLED_ATTEMPTS,
    'SQL_06_MARKING_WINDOW_LOCAL_MARKING_MISSING_SCORE': SQL_06_MARKING_WINDOW_LOCAL_MARKING_MISSING_SCORE,
    //scans
    'SQL_07_SCAN_INFO_FROM_TA_ID': SQL_07_SCAN_INFO_FROM_TA_ID,
    'SQL_07_SCAN_COUNT_BY_DATE_FROM_TA_ID': SQL_07_SCAN_COUNT_BY_DATE_FROM_TA_ID,
    'SQL_04B_STU_GOV_ID_FROM_UID': SQL_04B_STU_GOV_ID_FROM_UID,
    // tqr generic params
    'SQL_03_ASMT_CONTENT_TQR_GENERIC_PARAM_MAP': SQL_03_ASMT_CONTENT_TQR_GENERIC_PARAM_MAP,
    'SQL_03_ASMT_CONTENT_TQR_PARAMS': SQL_03_ASMT_CONTENT_TQR_PARAMS,
    // authoring monitoring
    'SQL_01_SYS_TEST_WINDOWS_ACTIVE': SQL_01_SYS_TEST_WINDOWS_ACTIVE,
    'SQL_05A_TA_FORMS_SUPERCEDED': SQL_05A_TA_FORMS_SUPERCEDED,
    'SQL_05A_TA_FORMS_INACTIVE': SQL_05A_TA_FORMS_INACTIVE,
    'SQL_03B_AUTH_MONIT_PUBLISHINGS': SQL_03B_AUTH_MONIT_PUBLISHINGS,
    'SQL_03B_AUTH_MONIT_ITEMS_MODIFIED': SQL_03B_AUTH_MONIT_ITEMS_MODIFIED,
    'SQL_03B_AUTH_MONIT_ITEMS_DELETED': SQL_03B_AUTH_MONIT_ITEMS_DELETED,
    'SQL_03B_AUTH_MONIT_ITEMS_CREATED': SQL_03B_AUTH_MONIT_ITEMS_CREATED,
    'SQL_03B_AUTH_MONIT_ITEM_ANSWERS_REMOVED': SQL_03B_AUTH_MONIT_ITEM_ANSWERS_REMOVED,
    'SQL_03B_AUTH_MONIT_ITEM_ANSWERS_ADDED': SQL_03B_AUTH_MONIT_ITEM_ANSWERS_ADDED,
    'SQL_03B_AUTH_MONIT_FRAMEWORKS': SQL_03B_AUTH_MONIT_FRAMEWORKS,
    'SQL_03B_AUTH_MONIT_FORM_ALLOCATIONS': SQL_03B_AUTH_MONIT_FORM_ALLOCATIONS,
    'SQL_03B_AUTH_MONIT_RECENTLY_ALLOCATED': SQL_03B_AUTH_MONIT_RECENTLY_ALLOCATED, // todo: is this a duplicate of SQL_03B_AUTH_MONIT_FORM_ALLOCATIONS?
    'SQL_03B_AUTH_MONIT_RECENTLY_READY_FOR_STRUCT_REVIEWS': SQL_03B_AUTH_MONIT_RECENTLY_READY_FOR_STRUCT_REVIEWS,
    'SQL_05A_SESSIONS_SUPERCEDED_FORMS': SQL_05A_SESSIONS_SUPERCEDED_FORMS,
    'SQL_05A_SESSIONS_SUPERCEDED_FORMS_TO_ATTEMPTS': SQL_05A_SESSIONS_SUPERCEDED_FORMS_TO_ATTEMPTS, // todo: this should probably be handled as just a transform if we have access to all of attempts
    'SQL_04B_SC_CF_SLUG_MISMATCH': SQL_04B_SC_CF_SLUG_MISMATCH,

}


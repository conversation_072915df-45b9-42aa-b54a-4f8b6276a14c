import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { Knex } from 'knex';
import { dbRawRead } from '../../../../util/db-raw';

interface Data { 
  type: SysFlagType,
  key: string,
  value?:number|string,
  value_en?: string,
  value_fr?: string,
 }

 enum SysFlagType {
  NUMERIC = 'numeric',
  STRING = 'string',
  MD_TRANSLATIONS = 'md_translations'
}

interface ServiceOptions {}

export class SysFlags implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }


  async find (params?: Params): Promise<any | Paginated<any>> {
    const systemFlagsNumeric = await dbRawRead(this.app, {}, `select * from sys_constants_numeric;`)
    const systemFlagsString = await dbRawRead(this.app, {}, `select * from sys_constants_string;`)
    const systemFlagsMdTranslations = await dbRawRead(this.app, {}, `select * from sys_constants_md_translations;`)

    return {systemFlagsNumeric, systemFlagsString, systemFlagsMdTranslations};
  }

  async get (id: Id, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<any> {
    const {value, value_en, value_fr, type, key} = data;
    if (!type || !key) {
      throw new Errors.BadRequest('ERR_MISSING_DATA');
    }
    if (type === SysFlagType.NUMERIC) {
      if (value == undefined) throw new Errors.BadRequest('ERR_MISSING_DATA');
      return this.app.service('db/write/sys-constants-numeric').patch(id, {value});
    } else if (type === SysFlagType.STRING) {
      if (value == undefined) throw new Errors.BadRequest('ERR_MISSING_DATA');
      return this.app.service('db/write/sys-constants-string').patch(id, {value});
    } else if (type === SysFlagType.MD_TRANSLATIONS){
      if (value_en == undefined && value_fr == undefined) throw new Errors.BadRequest('ERR_MISSING_DATA');
      return this.app.service('db/write/sys-constants-md-translations').patch(id, {value_en, value_fr});
    }
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}

import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import {Errors} from "../../../../errors/general";
import logger from '../../../../logger';
import {dbRawRead,dbRawWrite } from '../../../../util/db-raw';
import { getSysConstNumeric } from '../../../../util/sys-const-numeric';
import { range } from 'lodash';
import { currentUid } from '../../../../util/uid';
import { dbDateNow } from '../../../../util/db-dates';

interface Data {
  newRate:any,
  newMaxBatchClaim:any,
  newBatchSize: any,
  newDurationHours:any,
  rangeTagIds: string,
  newDescription: string,
  is_paired_marking: number,
  paired_marking_target_group_size: number, 
  paired_marking_backup_group_size: number,
  allow_batch_next_navigate: number,
  allow_score_clear: number
}

interface ServiceOptions {}

export class BatchAllocPolicies implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query) {
      const { markingWindowId } = params.query
      if(markingWindowId){
        const records = await dbRawRead(this.app, [markingWindowId], `
          select mbap.*
            from marking_batch_alloc_policies mbap
          join marking_window_items mwi 
          on mwi.batch_alloc_policy_id = mbap.id
          where mwi.marking_window_id = ?
          AND mbap.is_revoked = 0
          group by mbap.id
          ;
       `);
       return records;
      }
      else{
        const records = await dbRawRead(this.app, [], `
          select *
            from marking_batch_alloc_policies
       `);
       
       return records;
      }
    }
    throw new Errors.BadRequest("MISSING_PARAMS")
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<any> {
    return await dbRawRead(this.app, [id], `
      SELECT mbap.id, mbap.read_rules, mbap.created_on, mbap.order 
      FROM marking_batch_alloc_policies mbap
      JOIN marking_window_items mwi 
        ON mwi.batch_alloc_policy_id = mbap.id
      WHERE mwi.marking_window_id = ?
      AND mbap.is_revoked = 0
      group by mbap.id
      ;
    `)
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params: Params): Promise<Data> {
    const {newRate, newDurationHours, newMaxBatchClaim, newBatchSize, rangeTagIds, newDescription, is_paired_marking, paired_marking_target_group_size, paired_marking_backup_group_size, allow_batch_next_navigate, allow_score_clear } = data;
    if( id == null || newRate == null || newDurationHours == null || newMaxBatchClaim == null || newBatchSize == null ){
      throw new Errors.BadRequest("MISSING_PARAMS");
    }
    const max_batch_size = await getSysConstNumeric(this.app, "MAX_BATCH_SIZE", true);
    if(newBatchSize > max_batch_size){
      throw new Errors.BadRequest("EXCEED_MAX_BATCH_SIZE");
    }
    const updated_by_uid = await currentUid(this.app, params);
    try{
      await this.app.service('db/write/marking-batch-alloc-policies').patch(id, {
        same_sch_lim_rate: newRate,
        max_batch_num_claim: newMaxBatchClaim,
        claim_dur_hours: newDurationHours,
        batch_size: newBatchSize,
        standards_range_tag_ids: rangeTagIds,
        is_paired_marking,
        paired_marking_target_group_size,
        paired_marking_backup_group_size,
        allow_score_clear,
        allow_batch_next_navigate,
        description: newDescription,
        updated_on: dbDateNow(this.app),
        updated_by_uid: updated_by_uid

      })
    }catch(e){
      logger.error('error patching batch-alloc-policies', e);
    }
    return <any> [];
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    throw new Errors.MethodNotAllowed();
  }
}

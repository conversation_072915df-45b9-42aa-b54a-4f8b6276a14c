import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../../declarations';
import { Knex } from 'knex';
import { Errors } from '../../../../../errors/general';
import { dbRawRead } from '../../../../../util/db-raw';
import { CourseType, renderSampleAssessmentSlug } from '../summary/summary.class';
type NumberHash = { [key: number]: boolean };
interface Data {}
interface ISchoolBoardInfo{
  brdMident: number,
  brdName: string,
  is_sample:number,
  lang: string,
}
interface ServiceOptions {}

export class Boards implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    const { tc_group_id,order } = (<any> params).query;
    if (params && params.query) {
      const order = params.query.order;
      const foreign_scope_id = params.query.projectId;
      const courseType : CourseType = params.query.courseType;
      if(order){
        switch (order) {
          case 'Registered': return await this.getRecordsById(await dbRawRead(this.app, [], `
          ${this.app.service('public/test-ctrl/schools/summary').renderDistrictsJoinQuery(foreign_scope_id)}
        `));
          case 'CompletedTech': return await this.getRecordsFromGroupId(await this.getCompletedTech(foreign_scope_id,courseType));
          case 'CompletedSample': return await this.getRecordsFromGroupId(await this.getCompletedSample(foreign_scope_id, courseType));
          case 'CompletedSampleAndTech': return await this.getRecordsFromGroupId(await this.app.service('public/test-ctrl/schools/summary').getCompletedSampleAndTechRecords(foreign_scope_id,'Districts', courseType));
          case 'StartedOper': return await this.getRecordsFromGroupId(await this.app.service('public/test-ctrl/schools/summary').getStartedOperRecords(foreign_scope_id,'Districts', courseType));
          case 'CompletedOper': return await this.getRecordsFromGroupId(await this.app.service('public/test-ctrl/schools/summary').getCompletedOperRecords(foreign_scope_id, 'Districts', courseType));
          case 'StuReportsAccessed': return await this.getRecordsFromGroupId(await this.app.service('public/test-ctrl/schools/summary').getStuReportsAccessedRecords(foreign_scope_id,'Districts', courseType));
        }

    }
    else{
      const db:Knex = this.app.get('knexClientRead');
      //const query = "select `t2`.`id` AS `id`,`t2`.`group_id` AS `group_id`,`t2`.`foreign_id` AS `foreign_id`,`t2`.`is_sample` AS `is_sample`,`t2`.`name` AS `name`,`t2`.`brd_lang` AS `brd_lang`,`t2`.`num_schools` AS `num_schools`,`t2`.`num_teachers` AS `num_teachers`,count(`urs`.`id`) AS `num_students` from (((select `t`.`id` AS `id`,`t`.`group_id` AS `group_id`,`t`.`foreign_id` AS `foreign_id`,`t`.`name` AS `name`,`t`.`brd_lang` AS `brd_lang`,`t`.`is_sample` AS `is_sample`,`t`.`num_schools` AS `num_schools`,count(`urt`.`id`) AS `num_teachers` from (((select `sd`.`id` AS `id`,`sd`.`group_id` AS `group_id`,`sd`.`foreign_id` AS `foreign_id`,`sd`.`name` AS `name`,`sd`.`brd_lang` AS `brd_lang`,`sd`.`is_sample` AS `is_sample`,count(`s`.`id`) AS `num_schools` from (`mpt_dev`.`school_districts` `sd` left join `mpt_dev`.`schools` `s` on((`s`.`schl_dist_group_id` = `sd`.`group_id`))) where (`sd`.`is_active` = 1) group by `sd`.`id`)) `t` left join `mpt_dev`.`user_roles` `urt` on(((`urt`.`group_id` = `t`.`group_id`) and (`urt`.`role_type` = 'schl_teacher') and (`urt`.`is_revoked` != 1)))) group by `t`.`id`)) `t2` left join `mpt_dev`.`user_roles` `urs` on(((`urs`.`group_id` = `t2`.`group_id`) and (`urs`.`role_type` = 'schl_student') and (`urs`.`is_revoked` != 1)))) group by `t2`.`id`"
      const records = await dbRawRead(this.app, [], `
      select t2.id AS id,t2.group_id AS group_id,t2.foreign_id AS foreign_id,t2.is_sample AS is_sample,t2.name AS name,t2.brd_lang AS brd_lang,t2.num_schools AS num_schools,t2.num_teachers AS num_teachers,count(urs.id) AS num_students
      from (((select t.id AS id,t.group_id AS group_id,t.foreign_id AS foreign_id,t.name AS name,t.brd_lang AS brd_lang,t.is_sample AS is_sample,t.num_schools AS num_schools,count(urt.id) AS num_teachers
      from (((select sd.id AS id,sd.group_id AS group_id,sd.foreign_id AS foreign_id,sd.name AS name,sd.brd_lang AS brd_lang,sd.is_sample AS is_sample,count(s.id) AS num_schools
      from (mpt_dev.school_districts sd left join mpt_dev.schools s on((s.schl_dist_group_id = sd.group_id)))
      where (sd.is_active = 1) group by sd.id)) t
      left join mpt_dev.user_roles urt on(((urt.group_id = t.group_id) and (urt.role_type = 'schl_teacher') and (urt.is_revoked != 1))))
      group by t.id)) t2
      left join mpt_dev.user_roles urs on(((urs.group_id = t2.group_id) and (urs.role_type = 'schl_student') and (urs.is_revoked != 1))))
      group by t2.id
      ;`);
      return records;
    }

  }
  throw new Errors.BadRequest();
    // return this.app.service('db/read/school-districts-summary').find({query:{$limit:50000}})
  }

  async getCompletedTech(foreign_scope_id: number,courseType:CourseType) {
    const records = await dbRawRead(this.app, [], `
      ${this.app.service('public/test-ctrl/schools/summary').renderCompletedTechQuery(foreign_scope_id,courseType)}
    `);
    return this.parseUnionRecordsToUserGroupCount(records,foreign_scope_id)
  }

  async parseUnionRecordsToUserGroupCount(records: any[], foreign_scope_id: number) {
    const districtsHash: NumberHash = {};
    records.forEach(record => {
      districtsHash[record.schl_dist_group_id] = true;
    });
    const districts = Object.keys(districtsHash);
    return districts
  }
  async getCompletedSample(foreign_scope_id: number, courseType: CourseType) {
    const records = await dbRawRead(this.app, [], this.app.service('public/test-ctrl/schools/summary').renderCompletedAssessmentQuery(foreign_scope_id, renderSampleAssessmentSlug(courseType)));
    return this.parseRecordsToUserGroupCount(records, true);
  }
  async parseRecordsToUserGroupCount(records: any[], lookupTeacherByClasses: boolean = false) {
    const districtsHash: NumberHash = {};
    records.forEach(record => {
      districtsHash[record.schl_dist_group_id] = true;
    });
    const districts = Object.keys(districtsHash);
    return  districts
  }
  async getRecordsFromGroupId(records:any[]){
    // const districts = await this.app.service('db/read/school-districts').find({
    //   query: {
    //     group_id: {
    //       $in: records
    //     }
    //   }
    // });
    const districts = await dbRawRead(this.app, [], `
    select t2.id AS id,t2.group_id AS group_id,t2.foreign_id AS foreign_id,t2.is_sample AS is_sample,t2.name AS name,t2.brd_lang AS brd_lang,t2.num_schools AS num_schools,t2.num_teachers AS num_teachers,count(urs.id) AS num_students
    from (((select t.id AS id,t.group_id AS group_id,t.foreign_id AS foreign_id,t.name AS name,t.brd_lang AS brd_lang,t.is_sample AS is_sample,t.num_schools AS num_schools,count(urt.id) AS num_teachers
    from (((select sd.id AS id,sd.group_id AS group_id,sd.foreign_id AS foreign_id,sd.name AS name,sd.brd_lang AS brd_lang,sd.is_sample AS is_sample,count(s.id) AS num_schools
    from (mpt_dev.school_districts sd left join mpt_dev.schools s on((s.schl_dist_group_id = sd.group_id)))
    where (sd.is_active = 1) and (sd.group_id in (${records.join()})) group by sd.id)) t
    left join mpt_dev.user_roles urt on(((urt.group_id = t.group_id) and (urt.role_type = 'schl_teacher') and (urt.is_revoked != 1))))
    group by t.id)) t2
    left join mpt_dev.user_roles urs on(((urs.group_id = t2.group_id) and (urs.role_type = 'schl_student') and (urs.is_revoked != 1))))
    group by t2.id
    ;`);
    return districts
  }
  async getRecordsById(records:any[]){
    const ids = records.map(record => record.id)
    // const districts = <Paginated<any>> await this.app.service('db/read/school-districts').find({
    //   query: {
    //     id: {
    //       $in: ids
    //     }
    //   }
    // });
    const districts = await dbRawRead(this.app, [], `
    select t2.id AS id,t2.group_id AS group_id,t2.foreign_id AS foreign_id,t2.is_sample AS is_sample,t2.name AS name,t2.brd_lang AS brd_lang,t2.num_schools AS num_schools,t2.num_teachers AS num_teachers,count(urs.id) AS num_students
    from (((select t.id AS id,t.group_id AS group_id,t.foreign_id AS foreign_id,t.name AS name,t.brd_lang AS brd_lang,t.is_sample AS is_sample,t.num_schools AS num_schools,count(urt.id) AS num_teachers
    from (((select sd.id AS id,sd.group_id AS group_id,sd.foreign_id AS foreign_id,sd.name AS name,sd.brd_lang AS brd_lang,sd.is_sample AS is_sample,count(s.id) AS num_schools
    from (mpt_dev.school_districts sd left join mpt_dev.schools s on((s.schl_dist_group_id = sd.group_id)))
    where (sd.is_active = 1) and (sd.id in (${ids.join()})) group by sd.id)) t
    left join mpt_dev.user_roles urt on(((urt.group_id = t.group_id) and (urt.role_type = 'schl_teacher') and (urt.is_revoked != 1))))
    group by t.id)) t2
    left join mpt_dev.user_roles urs on(((urs.group_id = t2.group_id) and (urs.role_type = 'schl_student') and (urs.is_revoked != 1))))
    group by t2.id
    ;`);
    return districts;
  }

  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  async create (data: ISchoolBoardInfo, params?: Params): Promise<Data> {
    const userInfo = await this.app.service('public/auth/user-info-core').parseUserinfoJWT(params);
    const created_by_uid = <number> userInfo.uid;
    const { tc_group_id } = (<any> params).query;
    const {
      brdMident,
      brdName,
      lang,
      is_sample
    } = data;

    const existingSchoolDistrict = await this.checkExistingSchoolBoard(brdMident);
    if (existingSchoolDistrict.total > 0) {
      throw new Errors.BadRequest(`School Board with mident ${brdMident} already exists`)
      }
      else{
        const schoolDistrictGroupRecord = await this.app
        .service('db/write/u-groups')
        .create({
          group_type:'school_district',
          description:'School Board'
        })
        const schoolDistrictRecord = await this.app
        .service('db/write/school-districts')
        .create({
          group_id: schoolDistrictGroupRecord.id,
          foreign_id:brdMident,
          name:brdName,
          brd_lang:lang,
          is_active:1,
          is_sample
        })
      }

    return data;
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async patch (id: NullableId, data: ISchoolBoardInfo, params?: Params): Promise<Data> {
    const {
      brdMident,
      brdName,
      lang,
      is_sample
    } = data;
    const { tc_group_id } = (<any> params).query;
    const school_district_id = <number> id
    const currentSchoolDistrict = await this.app.service('db/read/school-districts').get(school_district_id);
    const existingSchoolDistrict = await this.checkExistingSchoolBoard(brdMident);
    if (existingSchoolDistrict.total > 0 && currentSchoolDistrict.foreign_id !== brdMident) {
      throw new Errors.BadRequest(`School Board with mident ${brdMident} already exists`)
      }
      else{
    const schoolDistrictRecord = await this.app
        .service('db/write/school-districts')
        .patch(id,{
          foreign_id:brdMident,
          name:brdName,
          brd_lang:lang,
          is_sample
        })
      }
        return data
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    return await this.app
    .service('db/write/school-districts')
    .patch(id,{
      is_active:0
    })
  }

  async checkExistingSchoolBoard(brdMident:number){
    return <Paginated<any>> await this.app
    .service('db/read/school-districts')
    .find({
      query:{
        foreign_id: brdMident
      }
    });
  }
}

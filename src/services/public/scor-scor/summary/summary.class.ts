import { Id, NullableId, <PERSON>ginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { currentUid } from '../../../../util/uid';
import { IScorerSummary, IAssignedItem, AssignedItemComponentType, AssignedItemStatus, IDashboardDoc } from './types/assigned-tasks';
import { IMarkingItemMarkerTasks } from './types/db';
import { boolAsNum, numAsBool, arrToMap, arrToAggrMap } from '../../../../util/param-sanitization';
import { Errors } from '../../../../errors/general';
import { dbRawRead, dbRawReadReporting, dbRawReadSingle } from '../../../../util/db-raw';
import { SQL_ASSIGNMENT_FLAGS, SQL_SCORER_SCORING_TASK, S<PERSON>_ITEM_PAIR_MARKING_STATUS, SQL_ITEM_PAIR_USERS, SQL_ITEM_PAIR_DRAFT_STATUS as SQL_ITEM_PAIRED_DRAFT_STATUS} from '../../../../sql/scoring';
import { ensureLockedDomain } from '../../../../util/domain-lock';
const _ = require('lodash');

const DASHBOARD_DOCS_SLUG = 'scorer_dashboard_doc';

interface Data {
}

interface ServiceOptions {}

interface IMarkingClaimedBatchPairedDraft {
  mcb_id: number,
  is_marked: number,
  uid: number,
}

export class Summary implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params){
      // identify
      let uid = await currentUid(this.app, params);

      if(params.query?.scorer_uid) {
        uid = params.query.scorer_uid;
      }

      // fetch
      const taskRecords = <Paginated<IMarkingItemMarkerTasks>> await this.app
        .service('db/read/marking-item-marker-tasks')
        .find({query: {
            uid,
            is_revoked: 0,
            is_removed: 0,
            $limit: 120,
        }})
      // allocate

      let windowItemIdsUnfiltered = taskRecords.data.map(r => r.marking_window_item_id);
      if(windowItemIdsUnfiltered.length == 0) windowItemIdsUnfiltered = [-1];
      // get item info
      const windowItemUnfiltered = <any[]> await dbRawReadReporting(this.app, {windowItemIdsUnfiltered, uid}, `
        -- < 0.1 sec
        select
              ur.role_type elevated_role_type
            , mwi.*
            , mw.short_differ_name
            , mw.is_score_profile_single_focus
            , mw.is_scorer_summary_view_allowed
            , mw.is_group_marking
            , mspg.order mspg_order
            , mspg.group_name
            , mspg.group_prefix
            , msp.order msp_order
            , msp.short_name as scale_slug
            , msp.is_non_scored_profile
            , mbap.allow_score_clear
            , mbap.allow_batch_next_navigate
            , now() < mw.start_on as mw_is_not_open
            , now() > mw.end_on as mw_is_closed
        from marking_window_items mwi
        join marking_score_profiles msp
          on mwi.score_profile_id = msp.id
        join marking_windows mw 
          on mw.id = mwi.marking_window_id 
        join marking_batch_alloc_policies mbap
          on mbap.id = mwi.batch_alloc_policy_id
        left join marking_score_profile_groups mspg
          on mwi.score_profile_group_id = mspg.id
        left join user_roles ur 
          on ur.uid = :uid 
          and ur.group_id = mw.group_id 
          and ur.is_revoked = 0
          and ur.role_type not in ('mrkg_mrkr')
        where mwi.id in (:windowItemIdsUnfiltered)
        group by mwi.id, mspg.id, msp.id
        order by mwi.slug, mspg.group_name, msp.order
      ;`) 

      const scorerDashboardDocs = <IDashboardDoc[]> [];
      
      // const itemsRef = arrToMap(windowItems, 'item_id');
      const windowItemsRef = arrToMap(windowItemUnfiltered, 'id');
      const windowIds = windowItemUnfiltered.map(wi => wi.marking_window_id);

      // get marker number
      const markerMetaRecords = <any[]> await dbRawReadReporting(this.app, {uid, windowIds: [-1].concat(windowIds)}, `
        select
            mw.id mw_id
          , mwum.value swum_marker_number
          , mwum_mr.value swum_max_read_level
        from marking_windows mw
        left join marking_window_user_meta mwum 
          on mwum.marker_uid = :uid
          and mwum.marking_window_id  = mw.id 
          and mwum.meta_key  = 'Marker_Number'
          and mwum.is_revoked  = 0
        left join marking_window_user_meta mwum_mr 
          on mwum_mr.marker_uid = :uid
          and mwum_mr.marking_window_id  = mw.id 
          and mwum_mr.meta_key  = 'Max_Read_Level'
          and mwum_mr.is_revoked  = 0
        where mw.id in (:windowIds)
        group by mw.id
      `)
      const markerMetaRef = arrToMap(markerMetaRecords, 'mw_id'); 

      const windows = <any[]> await this.app
        .service('db/read/marking-windows')
        .find({
          query: {
            id: { $in: windowIds},
            is_hidden_for_scorers: 0,
            is_active: 1,
            is_archived: 0
          }
          , paginate: false,
        })
      const windowRef = arrToMap(windows, 'id');
      for (let window of windows){
        if (window.is_active == 1){
          ensureLockedDomain(params, window.domain_lock); // throws an error if currently assigned to any items that have a domain lock
        }
      }

      // strip inactive window items
      const windowItems:any[] = []
      const windowItemRefById = new Map();
      for (let wi of windowItemUnfiltered){
        const w = windowRef.get(wi.marking_window_id);
        if (w){
          windowItems.push(wi);
          windowItemRefById.set(wi.id, true)
        }
      }
      const windowItemIds = windowItems.map(wi => wi.id);

      // strip tasks (to do: clean this up)
      taskRecords.data = taskRecords.data.filter(mt => windowItemRefById.get(mt.marking_window_item_id))
      taskRecords.total = taskRecords.data.length
      
      
      if(windowItemIds && windowItemIds.length <= 0){
        throw new Errors.Forbidden('MISSING_MWI_ID');
      }
      // get available batches
      const claimedBatches = await this.app.service('public/scor-scor/batches/available').identifyBatchesClaimed(uid, windowItemIds)
      const itemToClaimedBatches = arrToAggrMap(claimedBatches, 'marking_window_item_id')

      const mwiFlags = await dbRawRead(this.app, {windowItemIds}, SQL_ASSIGNMENT_FLAGS);
      const itemToFlags = arrToAggrMap(mwiFlags, 'mwi_id');
      
      const assignedItemById:Map<number, IAssignedItem> = new Map();
      let assignedItems:IAssignedItem[] = [];
      for (let i=0; i<taskRecords.data.length; i++){
        const taskRecord = taskRecords.data[i];
        const window_item_id = +taskRecord.marking_window_item_id;
        
        let assignedItem = assignedItemById.get(window_item_id);
        if (!assignedItem){
          const dateTimeAssigned = taskRecord.created_on;
          const windowItem = windowItemsRef.get(window_item_id);
          // meta
          const markerMeta = markerMetaRef.get(windowItem.marking_window_id);
          const {swum_marker_number, swum_max_read_level} = markerMeta;

          const scoringTasks = <any[]> await this.app
          .service('db/read/marking-item-marker-tasks')
          .find({
            paginate: false,
            query: {
              $sort: { id: -1 },
              uid, 
              marking_window_item_id: window_item_id,
              component_type: AssignedItemComponentType.SCORING,
              is_removed: 0
              // is_revoked: ANY // we have to include revoked cases here because scorers will often be revokd but still needing to be able to see their last stat
            }
          })
          let latestScoringTask = scoringTasks[0] || {is_revoked: 1};
          const {isPairedMarking, activeUid } = await this.getPairedMarkingStatus([window_item_id], latestScoringTask.pair_id, uid)
          const { isPairedDraftStageCompleted, isPairedDraftCompleted } = await this.getPairedDraftStatus(isPairedMarking, [window_item_id], uid, activeUid)

          let claimedBatches;
          if (isPairedMarking && (activeUid != uid)){
            claimedBatches = await this.app.service('public/scor-scor/batches/available').identifyBatchesClaimed(activeUid, [window_item_id])

            const scoringTasksActiveUid = <any[]> await this.app
            .service('db/read/marking-item-marker-tasks')
            .find({
              paginate: false,
              query: {
                $sort: { id: -1 },
                uid: activeUid, 
                marking_window_item_id: window_item_id,
                component_type: AssignedItemComponentType.SCORING,
                is_removed: 0
              }
            })

            latestScoringTask = scoringTasksActiveUid[0] || {is_revoked: 1};

          } else {
            claimedBatches = itemToClaimedBatches.get(window_item_id);
          }

          // claimed
          const pendingBatches = [];
          let numBatchesClaimed = 0;
          let numBatchesScored = 0
          if (claimedBatches){
            claimedBatches.forEach((batch: any) => {
              if (batch.is_marked){
                numBatchesScored ++;
              }
              else{
                pendingBatches.push(batch) ;
              }
            })
          }
          numBatchesClaimed = pendingBatches.length;
          const window = windowRef.get(windowItem.marking_window_id);
          const {
            is_cache_roll_val_current,
            cache_roll_val_exact,
            cache_roll_val_adj,
            cache_batches_rem,
          } = latestScoringTask;

          let isScoringEligible = true;
          if (cache_batches_rem === 0 || (latestScoringTask.is_revoked == 1)){
            isScoringEligible = false;
          }

          let rollingValidityStats:any = {
            isRollValCurr: (is_cache_roll_val_current == 1),
            cache_roll_val_exact,
            cache_roll_val_adj,
          };
          // avail to claim
          const scorerStats = await this.app
            .service('public/scor-scor/batches/available')
            .getScorerStats(uid, window_item_id, isScoringEligible);
          const flags = itemToFlags.get(window_item_id)?.map((flags) => flags.slug_code) || [];

          const {
            max_batch_num_claim, 
            // batchesRemaining,
            batch_size,
            num_batches_claimed, 
            num_responses_scored, 
            num_validity_total, 
            num_validity_exact, 
            num_validity_adj,
            num_validity_over,
            num_validity_under
          } = scorerStats;
          // -----------
          const assignItemPayload = {
            id: window_item_id,
            swum_marker_number, 
            swum_max_read_level,
            elevated_role_type: windowItem.elevated_role_type,
            name: windowItem.caption,
            mw_short_differ_name: windowItem.short_differ_name,
            mw_is_score_profile_single_focus: !!windowItem.is_score_profile_single_focus,
            mw_is_scorer_summary_view_allowed: !!windowItem.is_scorer_summary_view_allowed,
            mw_is_group_marking: !!windowItem.is_group_marking,
            allow_score_clear: !!windowItem.allow_score_clear,
            allow_batch_next_navigate: !!windowItem.allow_batch_next_navigate,
            mw_is_not_open: !!windowItem.mw_is_not_open,
            mw_is_closed: !!windowItem.mw_is_closed,
            mw_is_scoring_disabled: !!window.is_scoring_disabled,
            item_id: windowItem.item_id,
            is_revoked: latestScoringTask.is_revoked,
            item_slug: windowItem.slug,
            scale_slug: windowItem.scale_slug,
            is_non_scored_profile: windowItem.is_non_scored_profile,
            sync_batches_to_wmi_id: windowItem.sync_batches_to_wmi_id,
            group_to_mwi_id: windowItem.group_to_mwi_id,
            score_profile_group_id: windowItem.score_profile_group_id,
            score_profile_group_prefix: windowItem.group_prefix,
            score_profile_group_name: windowItem.group_name,
            msp_order: windowItem.msp_order,
            mspg_order: windowItem.mspg_order,
            components: [],
            isAvailable: true,
            dateTimeAssigned,
            window_id: window.id,
            dateWindowStart: window.start_on,
            dateWindowEnd: window.end_on,
            numBatchesAvail: cache_batches_rem, // batchesRemaining, // int
            numBatchesClaimed, // int
            numBatchesScored,
            claimedBatches,
            maxBatchNumClaim: max_batch_num_claim,
            batch_size,
            num_batches_claimed_total: num_batches_claimed, 
            num_responses_scored, 
            num_validity_total, 
            num_validity_exact, 
            num_validity_adj,
            num_validity_over,
            num_validity_under,
            flags,
            ... rollingValidityStats,
            is_paired_marking_active: isPairedMarking && (activeUid == uid),
            is_paired_marking_read_only: isPairedMarking && (activeUid != uid),
            is_paired_draft_stage_completed: isPairedDraftStageCompleted,
            is_paired_draft_completed: isPairedDraftCompleted,
            marking_pair_id: latestScoringTask.pair_id
          }
          assignedItems.push(assignItemPayload);
          assignedItemById.set(window_item_id, assignItemPayload);
          assignedItem = assignItemPayload;
        }
        if (assignedItem){
          const status = (assignedItem.is_revoked == 1) ? AssignedItemStatus.NOT_AVAILABLE :  taskRecord.status;
          assignedItem.components.push({
            id: taskRecord.id,
            order: taskRecord.order,
            componentType: taskRecord.component_type,
            caption: taskRecord.caption,
            status,
            allowRevisit: numAsBool(taskRecord.allow_revisit),
            isPassFail: numAsBool(taskRecord.is_pass_fail),
          })
        }
      };
      // sort
      assignedItems.forEach(assignedItem => {
        assignedItem.components = _.orderBy(assignedItem.components, 'order')
      })
      assignedItems = _.orderBy(assignedItems, ['mspg_order','item_slug', 'score_profile_group_name', 'msp_order', 'group_to_mwi_id', 'sync_batches_to_wmi_id'])
      // package
      const summary:IScorerSummary = {
        assignedItems,
        scorerDashboardDocs
      }
      return <any> summary
    }
    return <any> {};
  }

  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  async create (data: Data, params?: Params): Promise<Data> {
    if (Array.isArray(data)) {
      return Promise.all(data.map(current => this.create(current, params)));
    }

    return data;
  }

  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    if (params && data){
      const {newStatus} = <any> data;
      const uid = await currentUid(this.app, params);
      const itemComponentId = <number> id;
      const record = await this.app
        .service('db/read/marking-item-marker-tasks')
        .get(itemComponentId);
      if (record.uid !== uid){
        throw new Errors.Forbidden('NOT_YOUR_TASK')
      }
      await this.app
        .service('db/read/marking-item-marker-tasks')
        .patch(id, {
          status: newStatus,
        });
      // unlocking the scoring
      if (record.component_type === 'QUALIFYING'){
        await this.unblockScoring(record.uid, record.marking_window_item_id);
      }
    }
    return data;
  }

  async unblockScoring(uid:number, marking_window_item_id:number){
    const records = <any[]> await this.app
        .service('db/read/marking-item-marker-tasks')
        .find({
          query: {
            uid,
            marking_window_item_id,
            is_revoked:0,
            is_removed: 0,
          },
          paginate: false
        });
    const record = records[0];
    if (record.is_block_pending){
      await this.app
        .service('db/read/marking-item-marker-tasks')
        .patch(record.id, {
          is_block_pending: 0,
        });
    }
  }

  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }

  async getPairedMarkingStatus(window_item_ids: number[], marking_pair_id: number, current_uid: number): Promise<{isPairedMarking: boolean, activeUid: number, readOnlyUids: number[]}>{
    const notPairedStatus = { isPairedMarking: false, activeUid: current_uid, readOnlyUids: []};
    if (!marking_pair_id){
      return notPairedStatus
    }
    // Check if paired marking is still turned on in both window and batch alloc policies
    const itemsPairMarkingStatusRecords = await dbRawRead(this.app, {window_item_ids}, SQL_ITEM_PAIR_MARKING_STATUS)
    const isPairMarking = itemsPairMarkingStatusRecords.every(r => r.is_paired_marking)
    if (!isPairMarking){
      return notPairedStatus
    } 
    const pairActiveUidRecords = await dbRawRead(this.app, {pair_ids: [marking_pair_id]}, SQL_ITEM_PAIR_USERS)
    const activeUid = pairActiveUidRecords.find(u => !u.is_read_only)?.uid
    const readOnlyUids = pairActiveUidRecords.filter(u => u.is_read_only).map(u => u.uid)
    return { isPairedMarking: !!activeUid, activeUid: activeUid || current_uid, readOnlyUids}
  }

  async getPairedDraftStatus(isPairedMarking: boolean, window_item_ids: number[], current_uid: number, activeUid: number){
    let isPairedDraftStageCompleted = false;
    let isPairedDraftCompleted = false;
    if(!isPairedMarking){
      return {isPairedDraftStageCompleted, isPairedDraftCompleted}
    }
    if(!current_uid) {
      throw new Errors.BadRequest('MISSING_UID');
    }
    if(!window_item_ids || window_item_ids.length == 0) {
      throw new Errors.BadRequest('MISSING_WINDOW_ITEM_IDS')
    }
    const mcbpds: IMarkingClaimedBatchPairedDraft[] = await dbRawRead(this.app, {window_item_ids, uid: current_uid, activeUid}, SQL_ITEM_PAIRED_DRAFT_STATUS);
    const incompleted_paired_drafts = mcbpds.filter(mcbpd => mcbpd.is_marked == 0);
    const incompleted_self_paired_drafts = mcbpds.filter(mcbpd => mcbpd.is_marked == 0 && mcbpd.uid == current_uid);
    
    return {isPairedDraftStageCompleted: incompleted_paired_drafts.length == 0, isPairedDraftCompleted: incompleted_self_paired_drafts.length == 0}
  }
}

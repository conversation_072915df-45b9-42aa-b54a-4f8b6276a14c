import { Id, NullableId, Paginated, Params, ServiceMethods } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { Errors } from '../../../../errors/general';
import { dbDateNow } from '../../../../util/db-dates';
import { dbRawRead } from '../../../../util/db-raw';

interface Data {}

interface ServiceOptions {}

const THIRTY_MINUES_IN_MILISECONDS = 1800000;

export class ProcessData implements ServiceMethods<Data> {
  app: Application;
  options: ServiceOptions;

  constructor (options: ServiceOptions = {}, app: Application) {
    this.options = options;
    this.app = app;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async find (params?: Params): Promise<Data[] | Paginated<Data>> {
    if (params && params.query){
      const limit: number = Number(params.query.limit);
      if (!limit){
        throw new Errors.BadRequest();
      }
      return dbRawRead(this.app, [limit], `
        SELECT id, start_timestamp, end_timestamp
        FROM process_data
        ORDER BY id DESC
        LIMIT ?
      `);
    }
    throw new Errors.BadRequest();
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async get (id: Id, params?: Params): Promise<Data> {
    return {
      id, text: `A new message with ID: ${id}!`
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async create (data: Data, params?: Params): Promise<Data> {
    const {
      new_start_timestamp,
      log_group_name,
      s3_location
    } = <any> data;
  
    const new_end_timestamp = Number(new_start_timestamp) + THIRTY_MINUES_IN_MILISECONDS;
    const new_start_date = new Date(Number(new_start_timestamp)).toISOString().replace('T', ' ').substring(0, 19);;
    const new_end_date = new Date(Number(new_end_timestamp)).toISOString().replace('T', ' ').substring(0, 19);;

    await this.app.service('db/write/process-data').create({
      start_timestamp: new_start_timestamp,
      end_timestamp: new_end_timestamp,
      start_date: new_start_date,
      end_date: new_end_date,
      log_group_name: log_group_name,
      s3_location: s3_location + '/' + 'logs',
      created_on: dbDateNow(this.app)
    }) 
    
    return {
      new_start_date,
      new_end_date
    };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async update (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async patch (id: NullableId, data: Data, params?: Params): Promise<Data> {
    return data;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  async remove (id: NullableId, params?: Params): Promise<Data> {
    return { id };
  }
}

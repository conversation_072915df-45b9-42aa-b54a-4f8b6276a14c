// Initializes the `process-data-exports` service on path `/public/cron/process-data-exports`
import { ServiceAddons } from '@feathersjs/feathers';
import { Application } from '../../../../declarations';
import { ProcessDataExports } from './process-data-exports.class';
import hooks from './process-data-exports.hooks';

// Add this service to the service type index
declare module '../../../../declarations' {
  interface ServiceTypes {
    'public/cron/process-data-exports': ProcessDataExports & ServiceAddons<any>;
  }
}

export default function (app: Application): void {
  const options = {
    paginate: app.get('paginate')
  };

  // Initialize our service with any options it requires
  app.use('/public/cron/process-data-exports', new ProcessDataExports(options, app));

  // Get our initialized service so that we can register hooks
  const service = app.service('public/cron/process-data-exports');

  service.hooks(hooks);
}

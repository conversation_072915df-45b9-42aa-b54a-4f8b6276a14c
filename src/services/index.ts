import { Application } from '../declarations';
import { dbConfigure } from './db/table-services';
import abedSchoolsStudentValidate from './private/schools/student/validate/abed/validate.service';
import allItemResponses from './public/mrkg-lead/all-item-responses/all-item-responses.service';
import authFailedLogin from './auth/failed-login/failed-login.service';
import authInvitation from './auth/invitation/invitation.service';
import authOctCap from './auth/oct-cap/oct-cap.service';
import authPasswordValidation from './auth/password-validation/password-validation.service';
import authUserRoleActions from './auth/user-role-actions/user-role-actions.service';
import connectedUsers from './public/educator/websocket/connected-users/connected-users.service';
import constants from './constants/constants.service';
import creditsCredits from './credits/credits/credits.service';
import creditsCreditTransactions from './credits/credit-transactions/credit-transactions.service';
import currentBatch from './public/mrkg-mrkr/current-batch/current-batch.service';
import dataExportSlots from './public/data-exporter/data-export-slots/data-export-slots.service';
import dbAuths from './db/read/auths/auths.service';
import dbWriteAuths from './db/write/auths/auths.service';
import educatorResponsibilityAgreement from "./public/educator/responsibility-agreement/responsibility-agreement.service";
import eqaoSdcApi from './third-party/eqao/eqao-sdc-api.service';
import exemplarSelectionResponse from './public/mrkg-lead/exemplarSelectionResponse/exemplarSelectionResponse.service';
import exemplarTrainingReliabilitySet from './public/mrkg-lead/exemplar-training-reliability-set/exemplar-training-reliability-set.service';
import irt from './irt/irt.service';
import itemImpressions from './public/test-auth/item-impressions/item-impressions.service';
import leaders from './public/mrkg-lead/leaders/leaders.service';
import mailCore from './mail/core/core.service';
import mailInviteTestAdmin from './mail/invite/test-admin/test-admin.service';
import mailInviteTestCert from './mail/invite/test-cert/test-cert.service';
import mailInviteTestCtrl from './mail/invite/test-ctrl/test-ctrl.service';
import mailNotificationAccommReq from './mail/notification/accomm-req/accomm-req.service';
import mailNotificationSessionAssign from './mail/notification/session-assign/session-assign.service';
import mailNotificationSessionCancellation from './mail/notification/session-cancellation/session-cancellation.service';
import mailNotificationSessionNew from './mail/notification/session-new/session-new.service';
import mailNotificationSessionResults from './mail/notification/session-results/session-results.service';
import mailNotificationSessionStatus from './mail/notification/session-status/session-status.service';
import mailNotificationSessionTransferReq from './mail/notification/session-transfer-req/session-transfer-req.service';
import mailNotificationSessionUnassign from './mail/notification/session-unassign/session-unassign.service';
import mailNotificationWaitlistUpgrade from './mail/notification/waitlist-upgrade/waitlist-upgrade.service';
import mailRegistrationConfirmation from './mail/registration-confirmation/registration-confirmation.service';
import mailResetPassword from './mail/reset-password/reset-password.service';
import markedCountByMarker from './public/mrkg-mrkr/marked-count-by-marker/marked-count-by-marker.service';
import markedResponsesOfItems from './public/mrkg-lead/marked-responses-of-items/marked-responses-of-items.service';
import nbedSchoolsStudentValidate from './private/schools/student/validate/nbed/validate.service';
import pairs from './public/mrkg-lead/pairs/pairs.service';
import policy from './public/school-board/policy/policy.service';
import privateSchoolsStudentValidate from './private/schools/student/validate/validate.service';
import PsychConfigHistory from "./public/data-exporter/psych-config-history/psych-config-history.service"
import PsychPipelineConfigs from "./public/data-exporter/psych-pipeline-configs/psych-pipeline-configs.service"
import publicAbedDataImport from './public/abed-data-import/abed-data-import.service';
import publicAbedPasi from './public/abed-pasi/abed-pasi.service';
import publicAbedPed from './public/abed-ped/abed-ped.service';
import publicAnonSampleTestDesignForm from './public/anon/sample-test-design-form/sample-test-design-form.service';
import publicAssessmentsInfo from './public/assessments-info/assessments-info.service';
import publicAuthRegisterMfa from './public/auth/register-mfa/register-mfa.service';
import publicBcAdminCoordinatorAccounts from './public/bc-admin-coordinator/accounts/accounts.service';
import publicBcAdminCoordinatorActivityLogs from './public/bc-admin-coordinator/activity-logs/activity-logs.service';
import publicBcAdminCoordinatorAssessmentComponent from './public/bc-admin-coordinator/assessment-component/assessment-component.service';
import publicBcAdminCoordinatorBcgradStudentsAssessment from './public/bc-admin-coordinator/bcgrad-students/assessment/assessment.service';
import publicBcAdminCoordinatorBcgradStudentsRegister from './public/bc-admin-coordinator/bcgrad-students/register/register.service';
import publicBcAdminCoordinatorBcgradStudentsRequests from './public/bc-admin-coordinator/bcgrad-students/requests/requests.service';
import publicBcAdminCoordinatorBcgradStudentsStudents from './public/bc-admin-coordinator/bcgrad-students/students/students.service';
import publicBcAdminCoordinatorBcgradStudentsTestSessions from './public/bc-admin-coordinator/bcgrad-students/test-sessions/test-sessions.service';
import publicBcAdminCoordinatorConfirmationCodes from './public/bc-admin-coordinator/confirmation-codes/confirmation-codes.service';
import publicBcAdminCoordinatorDistricts from './public/bc-admin-coordinator/districts/districts.service';
import publicBcAdminCoordinatorEdfmc2 from './public/bc-admin-coordinator/edfmc2/edfmc2.service';
import publicBcAdminCoordinatorExemption from './public/bc-admin-coordinator/exemption/exemption.service';
import publicBcAdminCoordinatorIrtReady from './public/bc-admin-coordinator/irt-ready/irt-ready.service';
import publicBcAdminCoordinatorIrtScore from './public/bc-admin-coordinator/irt-score/irt-score.service';
import publicBcAdminCoordinatorPrintOrders from './public/bc-admin-coordinator/print-orders/print-orders.service';
import publicBcAdminCoordinatorPrintPackages from './public/bc-admin-coordinator/print-packages/print-packages.service';
import publicBcAdminCoordinatorRegistration from './public/bc-admin-coordinator/registration/registration.service';
import publicBcAdminCoordinatorReports from './public/bc-admin-coordinator/reports/reports.service';
import publicBcAdminCoordinatorSchoolParticipation from './public/bc-admin-coordinator/school-participation/school-participation.service';
import publicBcAdminCoordinatorSchools from './public/bc-admin-coordinator/schools/schools.service';
import publicBcAdminCoordinatorScoreEntryLogs from './public/bc-admin-coordinator/score-entry-logs/score-entry-logs.service';
import publicBcAdminCoordinatorSessionPasswords from './public/bc-admin-coordinator/session-passwords/session-passwords.service';
import publicBcAdminCoordinatorSimulation from './public/bc-admin-coordinator/simulation/simulation.service';
import publicBcAdminCoordinatorSpecialMaterialRequests from './public/bc-admin-coordinator/special-material-requests/special-material-requests.service';
import publicBcAdminCoordinatorSrResults from './public/bc-admin-coordinator/sr-results/sr-results.service';
import publicBcAdminCoordinatorStudents from './public/bc-admin-coordinator/students/students.service';
import publicBcAdminCoordinatorSubmissionProgress from './public/bc-admin-coordinator/submission-progress/submission-progress.service';
import publicBcAdminCoordinatorTestAttemptMetas from './public/bc-admin-coordinator/test-attempt-metas/test-attempt-metas.service';
import publicBcAdminCoordinatorTestWindow from './public/bc-admin-coordinator/test-window/test-window.service';
import publicBcAdminCoordinatorUnsubmissions from './public/bc-admin-coordinator/unsubmissions/unsubmissions.service';
import publicBcAdminCoordinatorValidationCode from './public/bc-admin-coordinator/validation-code/validation-code.service';
import publicBcgScanConstructedResponse from './public/bcg-scan/constructed-response/constructed-response.service';
import publicBcgScanGenpdf from './public/bcg-scan/genpdf/genpdf.service';
import publicBoardCurrLeadSchools from './public/board-curr-lead/schools/schools.service';
import publicClSysExamExamineeKeys from './public/cl-sys/exam/examinee-keys/examinee-keys.service';
import publicCreditSystem from './public/credits/credit-system/credit-system.service';
import publicCronComputeReliability from './public/cron/compute-reliability/compute-reliability.service';
import publicCronDailyScoringPooling from './public/cron/daily-scoring-pooling/daily-scoring-pooling.service';
import publicCronEQAOScheduledSessions from './public/cron/eqao-scheduled-sessions/eqao-scheduled-sessions.service';
import publicCronExpiredBatchesRollingValidity from './public/cron/expired-batches-rolling-validity/expired-batches-rolling-validity.service';
import publicCronInvigilationAttemptChecklist from './public/cron/invigilation/attempt-checklist/attempt-checklist.service';
import publicCronMarkingItemScoringSummary from './public/cron/marking-item-scoring-summary/marking-item-scoring-summary.service';
import publicCronProcessData from './public/cron/process-data/process-data.service';
import publicCronProcessDataExports from './public/cron/process-data-exports/process-data-exports.service';
import publicCronProcessDataExportQueue from './public/cron/process-data-export-queue/process-data-export-queue.service';
import publicCronProcessPsychRunQueue from './public/cron/process-psych-run-queue/process-psych-run-queue.service';
import publicCronProcessResponseBuffer from './public/cron/process-response-buffer/process-response-buffer.service';
import publicCronPtSummaryByItem from './public/cron/pt-summary-by-item/pt-summary-by-item.service';
import publicCronQtSummaryByItem from './public/cron/qt-summary-by-item/qt-summary-by-item.service';
import publicCronReliabilityResponsesAlloc from './public/cron/reliability-responses-alloc/reliability-responses-alloc.service';
import publicCronReporting from './public/cron/reporting/reporting.service';
import publicCronResponsesNonResponseCheck from './public/cron/responses/non-response-check/non-response-check.service';
import publicCronScorerStatusUpdate from './public/cron/scorer-status-update/scorer-status-update.service';
import publicCronScoringBatchExpiry from './public/cron/scoring-batch-expiry/scoring-batch-expiry.service';
import publicCronTestAuthAssetCopyright from './public/cron/test-auth/asset-copyright/asset-copyright.service';
import publicCronTestCtrlGenerateSummaries from './public/cron/test-ctrl-generate-summaries/test-ctrl-generate-summaries.service';
import publicCronValiditySummaryByItem from './public/cron/validity-summary-by-item/validity-summary-by-item.service';
import publicCronWeeklyResetScorerTime from './public/cron/weekly-reset-scorer-time/weekly-reset-scorer-time.service';
import publicDataExportDataExportSlots from './public/data-exporter/data-export-slots/data-export-slots.service';
import publicDataExporterDataExport from './public/data-exporter/data-export/data-export.service';
import publicDataExporterPsychRun from './public/data-exporter/data-psych-run/data-psych-run.service';
import publicDistAdminContact from './public/dist-admin/contact/contact.service';
import publicDistAdminDistrict from './public/dist-admin/district/district.service';
import publicDistAdminDistrictRoster from './public/dist-admin/district-roster/district-roster.service';
import publicDistAdminInvite from './public/dist-admin/invite/invite.service';
import publicDistAdminOssltReport from './public/dist-admin/osslt-report/osslt-report.service';
import publicDistAdminPenLookup from './public/dist-admin/pen-lookup/pen-lookup.service';
import publicDistAdminRoles from './public/dist-admin/roles/roles.service';
import publicDistAdminSchool from './public/dist-admin/school/school.service';
import publicDistAdminSchoolAdmin from './public/dist-admin/school-admin/school-admin.service';
import publicDistAdminSchoolClass from './public/dist-admin/school-class/school-class.service';
import publicDistAdminSchoolRoster from './public/dist-admin/school-roster/school-roster.service';
import publicDistAdminScoreEntry from './public/dist-admin/score-entry/score-entry.service';
import publicDistAdminSessions from './public/dist-admin/sessions/sessions.service';
import publicDistAdminStudent from './public/dist-admin/student/student.service';
import publicDistAdminStudentsReporting from './public/dist-admin/students/reporting/reporting.service';
import publicDistAdminSummary from './public/dist-admin/summary/summary.service';
import publicDistAdminTechReadiChecklist from './public/dist-admin/tech-readi/checklist/checklist.service';
import publicDistAdminTechReadiLockDownInfo from './public/dist-admin/tech-readi/lock-down-info/lock-down-info.service';
import publicEducatorChecklist from './public/educator/checklist/checklist.service';
import publicEducatorClassAssessments from './public/educator/class-assessments/class-assessments.service';
import publicEducatorClassScanInfo from './public/educator/class-scan-info/class-scan-info.service';
import publicEducatorConfidentialityAgreement from './public/educator/confidentiality-agreement/confidentiality-agreement.service';
import publicEducatorEducatorQuestionnaireUnsubmit from './public/educator/educator-questionnaire-unsubmit/educator-questionnaire-unsubmit.service';
import publicEducatorGuestStudents from './public/educator/guest-students/guest-students.service';
import publicEducatorHealthCheck from './public/educator/health-check/health-check.service';
import publicEducatorInvigilators from './public/educator/invigilators/invigilators.service';
import publicEducatorPasiStatuses from './public/educator/pasi_statuses/pasi_statuses.service';
import publicEducatorReportIssuesCategories from './public/educator/report-issues-categories/report-issues-categories.service';
import publicEducatorResponseSheetConfirmation from './public/educator/response-sheet-confirmation/response-sheet-confirmation.service';
import publicEducatorRespSheetUploadQrcodeContents from './public/educator/resp-sheet-upload/qrcode-contents/qrcode-contents.service';
import publicEducatorRespSheetUploadUploadResponseSheets from './public/educator/resp-sheet-upload/upload-response-sheets/upload-response-sheets.service';
import publicEducatorSchool from './public/educator/school/school.service';
import publicEducatorSchoolSemesterTwDates from './public/educator/school-semester-tw-dates/school-semester-tw-dates.service';
import publicEducatorSession from './public/educator/session/session.service';
import publicEducatorSessionQuestionnaire from './public/educator/session-questionnaire/session-questionnaire.service';
import publicEducatorSessionReport from './public/educator/session-report/session-report.service';
import publicEducatorSessionReportedIssue from './public/educator/session-reported-issue/session-reported-issue.service';
import publicEducatorSessionSub from './public/educator/session-sub/session-sub.service';
import publicEducatorSoftLock from './/public/educator/soft-lock/soft-lock.service';
import publicEducatorStudentReport from './public/educator/student-report/student-report.service';
import publicEducatorStudents from './public/educator/students/students.service';
import publicEducatorStudentSession from './public/educator/student/session/session.service';
import publicEducatorStudentSessionAbsence from './public/educator/student/session-absence/session-absence.service';
import publicEducatorStudentSessionExtraTime from './public/educator/student/session-extra-time/session-extra-time.service';
import publicEducatorStudentTestAttempt from './public/educator/student-test-attempt/student-test-attempt.service';
import publicEducatorSurveySession from './public/educator/survey/session/session.service';
import publicEducatorSurveySessionQuestion from './public/educator/survey/session-question/session-question.service';
import publicEducatorTestAttemptUnsubmissionReasons from './public/educator/test-attempt-unsubmission-reasons/test-attempt-unsubmission-reasons.service';
import publicEducatorUpdateTestForm from './public/educator/update-test-form/update-test-form.service';
import publicEducatorUserMetas from './public/educator/user-metas/user-metas.service';
import publicEducatorWalkInStudents from './public/educator/walk-in-students/walk-in-students.service';
import publicEducatorWebsocketConnectedUsers from './public/educator/websocket/connected-users/connected-users.service';
import publicEducatorWebsocketStudentSoftlock from './public/educator/websocket/student-softlock/student-softlock.service';
import publicEducatorWebsocketStudentSubsessionNotification from './public/educator/websocket/student-subsession-notification/student-subsession-notification.service';
import publicFieldTesterSession from './public/field-tester/session/session.service';
import publicFieldTesterSessionQuestion from './public/field-tester/session-question/session-question.service';
import publicGenResponseSheets from './public/educator/gen-response-sheets/gen-response-sheets.service';
import publicHealth from './public/health/health.service';
import publicLandingApplicantRegistration from './public/landing/applicant-registration/applicant-registration.service';
import publicLandingConfirmUserRole from './public/landing/confirm-user-role/confirm-user-role.service';
import publicLandingKnowledgeBase from './public/landing/knowledge-base/knowledge-base.service';
import publicLog from './public/log/log.service';
import publicMrkgCandCertTest from './public/mrkg-cand/cert-test/cert-test.service';
import publicMrkgCandCertTests from './public/mrkg-cand/cert-tests/cert-tests.service';
import publicMrkgCandProfile from './public/mrkg-cand/profile/profile.service';
import publicMrkgCoordApplicantInfo from './public/mrkg-coord/applicant-info/applicant-info.service';
import publicMrkgCoordCredentialingSetup from './public/mrkg-coord/credentialing-setup/credentialing-setup.service';
import publicMrkgCoordMarkingSessionSettings from './public/mrkg-coord/marking-session-settings/marking-session-settings.service';
import publicMrkgCtrlMarkingPoolItems from './public/mrkg-ctrl/marking-pool/items/items.service';
import publicMrkgCtrlMarkingPoolMarkingItems from './public/mrkg-ctrl/marking-pool/marking/items/items.service';
import publicMrkgCtrlMarkingWindow from './public/mrkg-ctrl/marking-window/marking-window.service';
import publicMrkgCtrlMarkingWindows from './public/mrkg-ctrl/marking-windows/marking-windows.service';
import publicMrkgCtrlMarkingWindowsAccountsMrkrs from './public/mrkg-ctrl/marking-windows/accounts/mrkrs/mrkrs.service';
import publicMrkgCtrlMarkingWindowsAccountsSuprs from './public/mrkg-ctrl/marking-windows/accounts/suprs/suprs.service';
import publicMrkgCtrlMarkingWindowsAllocate from './public/mrkg-ctrl/marking-windows/allocate/allocate.service';
import publicMrkgCtrlMarkingWindowsCertTests from './public/mrkg-ctrl/marking-windows/cert-tests/cert-tests.service';
import publicMrkgCtrlMarkingWindowsDate from './public/mrkg-ctrl/marking-windows/date/date.service';
import publicMrkgCtrlMarkingWindowsItemOptions from './public/mrkg-ctrl/marking-windows/item-options/item-options.service';
import publicMrkgCtrlMarkingWindowsName from './public/mrkg-ctrl/marking-windows/name/name.service';
import publicMrkgCtrlMarkingWindowsStatus from './public/mrkg-ctrl/marking-windows/status/status.service';
import publicMrkgCtrlMrkrs from './public/mrkg-ctrl/mrkrs/mrkrs.service';
import publicMrkgCtrlProfile from './public/mrkg-ctrl/profile/profile.service';
import publicMrkgCtrlSuprs from './public/mrkg-ctrl/suprs/suprs.service';
import publicMrkgLeadAssignedMarkingSession from './public/mrkg-lead/assigned-marking-session/assigned-marking-session.service';
import publicMrkgLeadCalculateMarkerStats from './public/mrkg-lead/calculate-marker-stats/calculate-marker-stats.service';
import publicMrkgLeadCalculateReliabilityRelease from './public/mrkg-lead/calculate-reliability-release/calculate-reliability-release.service';
import publicMrkgLeadChatContacts from './public/mrkg-lead/chat-contacts/chat-contacts.service';
import publicMrkgLeadConstructedResponseScores from './public/mrkg-lead/constructed-response-scores/constructed-response-scores.service';
import publicMrkgLeadDocument from './public/mrkg-lead/document/document.service';
import publicMrkgLeadEcasImportExport from './public/mrkg-lead/ecas-import-export/ecas-import-export.service';
import publicMrkgLeadExemplarSelectionOverview from './public/mrkg-lead/exemplarSelectionOverview/exemplarSelectionOverview.service';
import publicMrkgLeadExemplarSelectionTable from './public/mrkg-lead/exemplar-selection-table/exemplar-selection-table.service';
import publicMrkgLeadExemplarSetPdf from './public/mrkg-lead/exemplar-set-pdf/exemplar-set-pdf.service';
import publicMrkgLeadExportFieldTestData from './public/mrkg-lead/export-field-test-data/export-field-test-data.service';
import publicMrkgLeadFsaDiscrepancyReport from './public/mrkg-lead/fsa-discrepancy-report/fsa-discrepancy-report.service';
import publicMrkgLeadFsaScanRequests from './public/mrkg-lead/fsa-scan-requests/fsa-scan-requests.service';
import publicMrkgLeadHasExtendedAccess from './public/mrkg-lead/has-extended-access/has-extended-access.service';
import publicMrkgLeadHistoricalPacing from './public/mrkg-lead/historical-pacing/historical-pacing.service';
import publicMrkgLeadInspectionOverview from './public/mrkg-lead/inspection-overview/inspection-overview.service';
import publicMrkgLeadItem from './public/mrkg-lead/item/item.service';
import publicMrkgLeadItemTemplate from './public/mrkg-lead/item-template/item-template.service';
import publicMrkgLeadItemThresholds from './public/mrkg-lead/item-thresholds/item-thresholds.service';
import publicMrkgLeadLeaderSendInvitationEmail from './public/mrkg-lead/leader-send-invitation-email/leader-send-invitation-email.service';
import publicMrkgLeadManageLeaders from './public/mrkg-lead/manage-leaders/manage-leaders.service';
import publicMrkgLeadManageMarkers from './public/mrkg-lead/manage-markers/manage-markers.service';
import publicMrkgLeadMarkerEventPacing from './public/mrkg-lead/marker-event-pacing/marker-event-pacing.service';
import publicMrkgLeadMarkersAssignedItem from './public/mrkg-lead/markers-assigned-item/markers-assigned-item.service';
import publicMrkgLeadMarkerSendAccessKeys from './public/mrkg-lead/marker-send-access-keys/marker-send-access-keys.service';
import publicMrkgLeadMarkingChat from './public/mrkg-lead/marking-chat/marking-chat.service';
import publicMrkgLeadMarkingExemplarSets from './public/mrkg-lead/marking-exemplar-sets/marking-exemplar-sets.service';
import publicMrkgLeadMarkingLeaderAssignment from './public/mrkg-lead/marking-leader-assignment/marking-leader-assignment.service';
import publicMrkgLeadMarkingPairs from './public/mrkg-lead/marking-pairs/marking-pairs.service';
import publicMrkgLeadMarkingPairsUsers from './public/mrkg-lead/marking-pairs-users/marking-pairs-users.service';
import publicMrkgLeadMarkingReliabilitySetResponses from './public/mrkg-lead/marking-reliability-set-responses/marking-reliability-set-responses.service';
import publicMrkgLeadMarkingSession from './public/mrkg-lead/markingSession/markingSession.service';
import publicMrkgLeadMarkingSessionAssignmentJson from './public/mrkg-lead/marking-session-assignment-json/marking-session-assignment-json.service';
import publicMrkgLeadMarkingSessionParticipants from './public/mrkg-lead/marking-session-participants/marking-session-participants.service';
import publicMrkgLeadMarkingSessionPens from './public/mrkg-lead/marking-session-pens/marking-session-pens.service';
import publicMrkgLeadMarkingTriggerRemark from './public/mrkg-lead/marking-trigger-remark/marking-trigger-remark.service';
import publicMrkgLeadNewDemoData from './public/mrkg-lead/new-demo-data/new-demo-data.service';
import publicMrkgLeadOverwritePairs from './public/mrkg-lead/overwrite-pairs/overwrite-pairs.service';
import publicMrkgLeadPastExemplarSets from './public/mrkg-lead/past-exemplar-sets/past-exemplar-sets.service';
import publicMrkgLeadReliabilityResponseDetails from './public/mrkg-lead/reliability-response-details/reliability-response-details.service';
import publicMrkgLeadResponse from './public/mrkg-lead/response/response.service';
import publicMrkgLeadResponsesByPen from './public/mrkg-lead/responses-by-pen/responses-by-pen.service';
import publicMrkgLeadSearchPairByName from './public/mrkg-lead/search-pair-by-name/search-pair-by-name.service';
import publicMrkgLeadStage from './public/mrkg-lead/stage/stage.service';
import publicMrkgLeadStudentScores from './public/mrkg-lead/student-scores/student-scores.service';
import publicMrkgLeadTrainingSetControls from './public/mrkg-lead/training-set-controls/training-set-controls.service';
import publicMrkgLeadUser from './public/mrkg-lead/user/user.service';
import publicMrkgMrkrAttemptResponse from './public/mrkg-mrkr/attempt-response/attempt-response.service';
import publicMrkgMrkrCertTest from './public/mrkg-mrkr/cert-test/cert-test.service';
import publicMrkgMrkrCertTests from './public/mrkg-mrkr/cert-tests/cert-tests.service';
import publicMrkgMrkrCurrentBatchPosition from './public/mrkg-mrkr/current-batch-position/current-batch-position.service';
import publicMrkgMrkrGenerateResponseUrls from './public/mrkg-mrkr/generate-response-urls/generate-response-urls.service';
import publicMrkgMrkrItemAccountType from './public/mrkg-mrkr/item/account-type/account-type.service';
import publicMrkgMrkrItemBatchScoring from './public/mrkg-mrkr/item/batch-scoring/batch-scoring.service';
import publicMrkgMrkrItemComments from './public/mrkg-mrkr/item/comments/comments.service';
import publicMrkgMrkrItemPriority from './public/mrkg-mrkr/item/priority/priority.service';
import publicMrkgMrkrItems from './public/mrkg-mrkr/items/items.service';
import publicMrkgMrkrItemScoring from './public/mrkg-mrkr/item/scoring/scoring.service';
import publicMrkgMrkrMarkerStats from './public/mrkg-mrkr/marker-stats/marker-stats.service';
import publicMrkgMrkrMarkingPools from './public/mrkg-mrkr/marking-pools/marking-pools.service';
import publicMrkgMrkrMarkingTasks from './public/mrkg-mrkr/marking-tasks/marking-tasks.service';
import publicMrkgMrkrPause from './public/mrkg-mrkr/pause/pause.service';
import publicMrkgMrkrProfile from './public/mrkg-mrkr/profile/profile.service';
import publicMrkgMrkrQuestion from './public/mrkg-mrkr/question/question.service';
import publicMrkgMrkrQuestionResponse from './public/mrkg-mrkr/question-response/question-response.service';
import publicMrkgMrkrResponse from './public/mrkg-mrkr/response/response.service';
import publicMrkgMrkrResponseScores from './public/mrkg-mrkr/response-scores/response-scores.service';
import publicMrkgMrkrSupervisorName from './public/mrkg-mrkr/supervisor-name/supervisor-name.service';
import publicMrkgMrkrTime from './public/mrkg-mrkr/time/time.service';
import publicMrkgMrkrTrainingResponse from './public/mrkg-mrkr/training-response/training-response.service';
import publicMrkgMrkrUniqueItems from './public/mrkg-mrkr/unique-items/unique-items.service';
import publicMrkgMrkrUserNames from './public/mrkg-mrkr/user-names/user-names.service';
import publicMrkgMrkrWindow from './public/mrkg-mrkr/window/window.service';
import publicMrkgSuprMarkers from './public/mrkg-supr/markers/markers.service';
import publicMrkgSuprMarkingPoolAssignMode from './public/mrkg-supr/marking-pool/assign-mode/assign-mode.service';
import publicMrkgSuprMarkingPoolItemPaused from './public/mrkg-supr/marking-pool/item-paused/item-paused.service';
import publicMrkgSuprMarkingPoolItemPriority from './public/mrkg-supr/marking-pool/item-priority/item-priority.service';
import publicMrkgSuprMarkingPoolItemsPriority from './public/mrkg-supr/marking-pool/items-priority/items-priority.service';
import publicMrkgSuprMarkingPoolMarkerPause from './public/mrkg-supr/marking-pool/marker/pause/pause.service';
import publicMrkgSuprMarkingPoolMarkerPerformance from './public/mrkg-supr/marking-pool/marker/performance/performance.service';
import publicMrkgSuprMarkingPoolMarkerReCertify from './public/mrkg-supr/marking-pool/marker/re-certify/re-certify.service';
import publicMrkgSuprMarkingPoolMarkers from './public/mrkg-supr/marking-pool/markers/markers.service';
import publicMrkgSuprMarkingPoolMarkingItemComment from './public/mrkg-supr/marking-pool/marking/item/comment/comment.service';
import publicMrkgSuprMarkingPoolMarkingItemRevoke from './public/mrkg-supr/marking-pool/marking/item/revoke/revoke.service';
import publicMrkgSuprMarkingPoolMarkingItems from './public/mrkg-supr/marking-pool/marking/items/items.service';
import publicMrkgSuprMarkingPoolMarkingResponseComment from './public/mrkg-supr/marking-pool/marking/response/comment/comment.service';
import publicMrkgSuprMarkingPoolMarkingResponses from './public/mrkg-supr/marking-pool/marking/responses/responses.service';
import publicMrkgSuprMarkingPoolMarkingTaqrComments from './public/mrkg-supr/marking-pool/marking/taqr-comments/taqr-comments.service';
import publicMrkgSuprMarkingPoolRole from './public/mrkg-supr/marking-pool/role/role.service';
import publicMrkgSuprMarkingPools from './public/mrkg-supr/marking-pools/marking-pools.service';
import publicMrkgSuprMarkingPoolSchedule from './public/mrkg-supr/marking-pool/schedule/schedule.service';
import publicMrkgSuprMarkingPoolTime from './public/mrkg-supr/marking-pool/time/time.service';
import publicMrkgSuprProfile from './public/mrkg-supr/profile/profile.service';
import publicMrkgSuprWindow from './public/mrkg-supr/window/window.service';
import publicMrkgUpldProfile from './public/mrkg-upld/profile/profile.service';
import publicMrkgUpldResponse from './public/mrkg-upld/response/response.service';
import publicPaymentCtrlAlternativePayments from './public/payment-ctrl/alternative-payments/alternative-payments.service';
import publicPaymentCtrlRefundSetup from './public/payment-ctrl/refund-setup/refund-setup.service';
import publicPaymentCtrlSchoolAdminAgreements from './public/payment-ctrl/school-admin-agreements/school-admin-agreements.service';
import publicPaymentCtrlStripePayments from './public/payment-ctrl/stripe-payments/stripe-payments.service';
import publicPaymentCtrlStudentAttempts from './public/payment-ctrl/student-attempts/student-attempts.service';
import publicPaymentPolicy from './public/school-admin/payment-policy/payment-policy.service';
import publicPaymentSystem from './public/credits/payment-system/payment-system.service';
import publicScanRevwBatch from './public/scan-revw/batch/batch.service';
import publicScanRevwScan from './public/scan-revw/scan/scan.service';
import publicScanRevwStats from './public/scan-revw/stats/stats.service';
import publicScanRevwWindows from './public/scan-revw/windows/windows.service';
import publicSchoolAdminBcgradStudents from './public/school-admin/bcgrad-students/students/bcgrad-students.service';
import publicSchoolAdminBcgradStudentsEnrollment from './public/school-admin/bcgrad-students/enrollment/enrollment.service';
import publicSchoolAdminBcgradStudentsLookup from './public/school-admin/bcgrad-students/lookup/lookup.service';
import publicSchoolAdminBcgradStudentsLookup2 from './public/school-admin/bcgrad-students/lookup2/lookup2.service';
import publicSchoolAdminBcgradStudentsRegister from './public/school-admin/bcgrad-students/register/register.service';
import publicSchoolAdminBcgradStudentsRegistration from './public/school-admin/bcgrad-students/registration/registration.service';
import publicSchoolAdminBcgradStudentsRequests from './public/school-admin/bcgrad-students/requests/requests.service';
import publicSchoolAdminClasses from './public/school-admin/classes/classes.service';
import publicSchoolAdminEnrolmentStudents from './public/school-admin/enrolment/students/students.service';
import publicSchoolAdminInvigilateClass from './public/school-admin/invigilate-class/invigilate-class.service';
import publicSchoolAdminInvigilators from './public/school-admin/invigilators/invigilators.service';
import publicSchoolAdminInvite from './public/school-admin/invite/invite.service';
import publicSchoolAdminPayment from './public/school-admin/payment/payment.service';
import publicSchoolAdminPaymentAgreements from './public/school-admin/payment-agreements/payment-agreements.service';
import publicSchoolAdminPjReports from './public/school-admin/pj-reports/pj-reports.service';
import publicSchoolAdminReports from './public/school-admin/reports/reports.service';
import publicSchoolAdminRoles from './public/school-admin/roles/roles.service';
import publicSchoolAdminSchool from './public/school-admin/school/school.service';
import publicSchoolAdminSchoolAccess from './public/school-admin/school-access/school-access.service';
import publicSchoolAdminScoreProfile from './public/school-admin/score-profile/score-profile.service';
import publicSchoolAdminSession from './public/school-admin/session/session.service';
import publicSchoolAdminSessionComplete from './public/school-admin/session-complete/session-complete.service';
import publicSchoolAdminSessionPurchase from './public/school-admin/session-purchase/session-purchase.service';
import publicSchoolAdminStudent from './public/school-admin/student/student.service';
import publicSchoolAdminStudentAsmtInfoSignoff from './public/school-admin/student-asmt-info-signoff/student-asmt-info-signoff.service';
import publicSchoolAdminStudentAssign from './public/school-admin/student/assign/assign.service';
import publicSchoolAdminStudentDelete from './public/school-admin/student-delete/student-delete.service';
import publicSchoolAdminStudentOenSchool from './public/school-admin/oen-school/oen-school.service';
import publicSchoolAdminStudentsClassroom from './public/school-admin/students/classroom/classroom.service';
import publicSchoolAdminStudentsStudent from './public/school-admin/students/student/student.service';
import publicSchoolAdminStudentTwAbsence from './public/school-admin/student-tw/absence/absence.service';
import publicSchoolAdminStudentTwUtil from './public/school-admin/student-tw/util/util.service';
import publicSchoolAdminTeacher from './public/school-admin/teacher/teacher.service';
import publicSchoolAdminTeachers from './public/school-admin/teachers/teachers.service';
import publicSchoolAdminTeachersInvite from './public/school-admin/teachers/invite/invite.service';
import publicSchoolAdminTechReadiChecklist from './public/school-admin/tech-readi/checklist/checklist.service';
import publicSchoolAdminTechReadiIp from './public/school-admin/tech-readi/ip/ip.service';
import publicSchoolAdminTechReadiItContact from './public/school-admin/tech-readi/it-contact/it-contact.service';
import publicSchoolAdminTechReadiLockDownInfo from './public/school-admin/tech-readi/lock-down-info/lock-down-info.service';
import publicSchoolAdminTestWindow from './public/school-admin/test-window/test-window.service';
import publicSchoolAdminTwStatement from './public/school-admin/tw-statement/tw-statement.service';
import publicScoringScorerSummary from './public/scoring/scorer-summary/scorer-summary.service';
import publicScorLeadAccounts from './public/scor-lead/accounts/accounts.service';
import publicScorLeadAssessmentForms from './public/scor-lead/assessment-forms/assessment-forms.service';
import publicScorLeadBatchAllocPolicies from './public/scor-lead/batch-alloc-policies/batch-alloc-policies.service';
import publicScorLeadBatches from './public/scor-lead/batches/batches.service';
import publicScorLeadCumulRecalc from './public/scor-lead/cumul-recalc/cumul-recalc.service';
import publicScorLeadDuplicateRead from './public/scor-lead/duplicate-read/duplicate-read.service'
import publicScorLeadForceMarkerClaim from './public/scor-lead/force-marker-claim/force-marker-claim.service';
import publicScorLeadGetScorerAccess from './public/scor-lead/get-scorer-access/get-scorers-access.service';
import publicScorLeadInviteWave from './public/scor-lead/invite-wave/invite-wave.service';
import publicScorLeadItemScorer from './public/scor-lead/item-scorer/item-scorer.service';
import publicScorLeadItemStats from './public/scor-lead/item-stats/item-stats.service';
import publicScorLeadLocalScoringPooling from './public/scor-lead/local-scoring-pooling/local-scoring-pooling.service';
import publicScorLeadLocalScoringStats from './public/scor-lead/local-scoring-stats/local-scoring-stats.service';
import publicScorLeadMaterials from './public/scor-lead/materials/materials.service';
import publicScorLeadMessageCentre from './public/scor-lead/message-centre/message-centre.service';
import publicScorLeadProcessScoringSecondReads from './public/scor-lead/process-scoring-second-reads/process-scoring-second-reads.service';
import publicScorLeadProcessScoringThirdReads from './public/scor-lead/process-scoring-third-reads/process-scoring-third-reads.service'
import publicScorLeadProcessScoringFourthReads from './public/scor-lead/process-scoring-fourth-reads/process-scoring-fourth-reads.service'
import publicScorLeadReprocessScoringReads from './public/scor-lead/reprocess-scoring-reads/reprocess-scoring-reads.service';
import publicScorLeadReliabilitySummary from './public/scor-lead/reliability-summary/reliability-summary.service';
import publicScorLeadRescore from './public/scor-lead/rescore/rescore.service';
import publicScorLeadResponseScores from './public/scor-lead/response-scores/response-scores.service';
import publicScorLeadResponseSets from './public/scor-lead/response-sets/response-sets.service';
import publicScorLeadResponseSheetCropAdjustment from './public/scor-lead/response-sheet-crop-adjustment/response-sheet-crop-adjustment.service';
import publicScorLeadResponseRepooling from './public/scor-lead/response-repooling/response-repooling.service';
import publicScorLeadResponsesRandomRealloc from './public/scor-lead/responses/random-realloc/random-realloc.service';
import publicScorLeadResponsesResponses from './public/scor-lead/responses/responses/responses.service';
import publicScorLeadResponsesSensitive from './public/scor-lead/responses/sensitive/sensitive.service';
import publicScorLeadScoreFlags from './public/scor-lead/score-flags/score-flags.service';
import publicScorLeadScoreOptions from './public/scor-lead/score-options/score-options.service';
import publicScorLeadScoreProfileGroups from './public/scor-lead/score-profile-groups/score-profile-groups.service';
import publicScorLeadScoreProfiles from './public/scor-lead/score-profiles/score-profiles.service';
import publicScorLeadScorerItemRevoke from './public/scor-lead/scorer-item-revoke/scorer-item-revoke.service';
import publicScorLeadScorerRespRem from './public/scor-lead/scorer-resp-rem/scorer-resp-rem.service';
import publicScorLeadScorers from './public/scor-lead/scorers/scorers.service';
import publicScorLeadScorerStats from './public/scor-lead/scorer-stats/scorer-stats.service';
import publicScorLeadScorerStatusLog from './public/scor-lead/scorer-status-log/scorer-status-log.service';
import publicScorLeadScoringPooling from './public/scor-lead/scoring-pooling/scoring-pooling.service';
import publicScorLeadSysFlags from './public/scor-lead/sys-flags/sys-flags.service';
import publicScorLeadValidityComments from './public/scor-lead/validity/comments/comments.service';
import publicScorLeadValidityHistoricalResponse from './public/scor-lead/validity/historical-response/historical-response.service';
import publicScorLeadValidityItem from './public/scor-lead/validity/item/item.service';
import publicScorLeadValidityResponses from './public/scor-lead/validity/responses/responses.service';
import publicScorLeadValiditySet from './public/scor-lead/validity/set/set.service';
import publicScorLeadWindows from './public/scor-lead/windows/windows.service';
import publicScorScorAttest from './public/scor-scor/attest/attest.service';
import publicScorScorBatchesAvailable from './public/scor-scor/batches/available/available.service';
import publicScorScorBatchesAvailableClaims from './public/scor-scor/batches/available-claims/available-claims.service';
import publicScorScorBatchesBatchGroups from './public/scor-scor/batches/batch-groups/batch-groups.service';
import publicScorScorBatchesScoringReads from './public/scor-scor/batches/scoring-reads/scoring-reads.service';
import publicScorScorBatchesClaim from './public/scor-scor/batches/claim/claim.service';
import publicScorScorBatchesItemDisplay from './public/scor-scor/batches/item-display/item-display.service';
import publicScorScorBatchesMark from './public/scor-scor/batches/mark/mark.service';
import publicScorScorBatchesSession from './public/scor-scor/batches/session/session.service';
import publicScorScorBatchesValidity from './public/scor-scor/batches/validity/validity.service';
import publicScorScorEvent from './public/scor-scor/event/event.service';
import publicScorScorExemplarCredentialingMark from './public/scor-scor/exemplar/credentialing/mark/mark.service';
import publicScorScorExemplarCredentialingSession from './public/scor-scor/exemplar/credentialing/session/session.service';
import publicScorScorExemplarPracticeMark from './public/scor-scor/exemplar/practice/mark/mark.service';
import publicScorScorExemplarPracticeSession from './public/scor-scor/exemplar/practice/session/session.service';
import publicScorScorMarkingClaimedBatchResponses from './public/scor-scor/batches/marking-claimed-batch-responses/marking-claimed-batch-responses.service';
import publicScorScorMaterialsReading from './public/scor-scor/materials/reading/reading.service';
import publicScorScorMaterialsSummary from './public/scor-scor/materials/summary/summary.service';
import publicScorScorMessageCentre from './public/scor-scor/message-centre/message-centre.service';
import publicScorScorMrkgDura from './public/scor-scor/mrkg-dura/mrkg-dura.service';
import publicScorScorSummary from './public/scor-scor/summary/summary.service';
import publicStressStudent from './public/stress/student/student.service';
import publicStudentActiveSubsessionStatus from './public/student/active-subsession-status/active-subsession-status.service';
import publicStudentAsmtFilter from './public/student/asmt-filter/asmt-filter.service';
import publicStudentAttempt from './public/student/attempt/attempt.service';
import publicStudentChecklist from './public/student/checklist/checklist.service';
import publicStudentClassId from './public/student/class-id/class-id.service';
import publicStudentCoverPage from './public/student/asmt-cover-page/asmt-cover-page.service';
import publicStudentCurrentSubsessionInfo from './public/student/current-subsession-info/current-subsession-info.service';
import publicStudentDebug from './public/student/debug/debug.service';
import publicStudentExtractItemResponse from './public/student/extract-item-response/extract-item-response.service';
import publicStudentMap from './public/student/map/map.service';
import publicStudentNameInitial from './public/student/name-initial/name-initial.service';
import publicStudentReportResults from './public/educator/report-results/report-results.service';
import publicStudentSelfReg from './public/student/self-reg/self-reg.service';
import publicStudentSession from './public/student/session/session.service';
import publicStudentSessionQuestion from './public/student/session-question/session-question.service';
import publicStudentSessionQuestionAudit from './public/student/session-question-audit/session-question-audit.service';
import publicStudentSessionSlug from './public/student/session-slug/session-slug.service';
import publicStudentSoftLock from './public/student/soft-lock/soft-lock.service';
import publicStudentStageSubmission from './public/student/stage-submission/stage-submission.service';
import publicStudentTestWindow from './public/student/test-window/test-window.service';
import publicStudentVerifyStudent from './public/student/verify-student/verify-student.service';
import publicStudentWalkIn from './public/student/walk-in/walk-in.service';
import publicSupportAdminWindowTfPanelMapping from './public/support/admin-window-tf-panel-mapping/admin-window-tf-panel-mapping.service';
import publicSupportAssignStudentsTf from './public/support/assign-students-tf/assign-students-tf.service';
import publicSupportCreateStudents from './public/support/create-students/create-students.service';
import publicSupportDataMethods from './public/support/data-methods/data-methods.service';
import publicSupportDataTargets from './public/support/data-targets/data-targets.service';
import publicSupportDistAdminAccountInvite from './public/support/dist-admin/account-invite/account-invite.service';
import publicSupportG9RawReports from './public/support/g9-raw-reports/g9-raw-reports.service';
import publicSupportLogin from './public/support/login/login.service';
import publicSupportMailCampaign from './public/support/mail-campaign/mail-campaign.service';
import publicSupportMailCampaignBatch from './public/support/mail-campaign-batch/mail-campaign-batch.service';
import publicSupportMailCampaignTest from './public/support/mail-campaign-test/mail-campaign-test.service';
import publicSupportOctSuccessfulAttempt from './public/support/oct-successful-attempt/oct-successful-attempt.service';
import publicSupportPasswordAttemptReset from './public/support/password-attempt-reset/password-attempt-reset.service';
import publicSupportPrivateG9Access from './public/support/private-g9-access/private-g9-access.service';
import publicSupportReporting from './public/support/support-reporting/support-reporting.service';
import publicSupportReqSchoolClassSessionsByForeignid from './public/support/req-school-class-sessions-by-foreignid/req-school-class-sessions-by-foreignid.service';
import publicSupportResetNames from './public/support/reset-names/reset-names.service';
import publicSupportResetProgress from './public/support/reset-progress/reset-progress.service';
import publicSupportResetSection from './public/support/reset-section/reset-section.service';
import publicSupportResolutionTypes from './public/support/resolution-types/resolution-types.service';
import publicSupportRoleActions from './public/support/role-actions/role-actions.service';
import publicSupportRoleTypes from './public/support/role-types/role-types.service';
import publicSupportSchool from './public/support/school/school.service';
import publicSupportSchoolAdmin from './public/support/school-admin/school-admin.service';
import publicSupportSchoolAdminAccountInvite from './public/support/school-admin/account-invite/account-invite.service';
import publicSupportSchoolClassStudent from './public/support/school-class-student/school-class-student.service';
import publicSupportSchoolClassTestSession from './public/support/school-class-test-session/school-class-test-session.service';
import publicSupportSchoolDistricts from './public/support/school-districts/school-districts.service';
import publicSupportSchoolProfile from './public/support/school-profile/school-profile.service';
import publicSupportSchoolForeignId from './public/support/school/foreign_id/foreign_id.service';
import publicSupportSchools from './public/support/schools/schools.service';
import publicSupportSchoolsAdmin from './public/support/schools-admin/schools-admin.service';
import publicSupportSchoolsAdminInvite from './public/support/schools-admin-invite/schools-admin-invite.service';
import publicSupportSchoolsAdminMulti from './public/support/schools-admin-multi/schools-admin-multi.service';
import publicSupportSchoolYellowFlags from './public/support/school-yellow-flags/school-yellow-flags.service';
import publicSupportScoringCredentialingSet from './public/support/scoring/credentialing-set/credentialing-set.service';
import publicSupportScoringPracticeSet from './public/support/scoring/practice-set/practice-set.service';
import publicSupportScoringResponses from './public/support/scoring/responses/responses.service';
import publicSupportScoringScorer from './public/support/scoring/scorer/scorer.service';
import publicSupportScoringScorerTasks from './public/support/scoring/scorer-tasks/scorer-tasks.service';
import publicSupportSessionReopen from './public/support/session-reopen/session-reopen.service';
import publicSupportStatsPeak from './public/support/stats/peak/peak.service';
import publicSupportStudentBulkRename from './public/support/student-bulk-rename/student-bulk-rename.service';
import publicSupportStudentLookups from './public/support/student-lookups/student-lookups.service';
import publicSupportStudentNumbers from './public/support/student-numbers/student-numbers.service';
import publicSupportStudentTaqr from './public/support/student-taqr/student-taqr.service';
import publicSupportStudentTwClasses from './public/support/student-tw-classes/student-tw-classes.service';
import publicSupportStudentUnenroll from './public/support/student-unenroll/student-unenroll.service';
import publicSupportSysEmailRecent from './public/support/sys-email-recent/sys-email-recent.service';
import publicSupportSysFlags from './public/support/sys-flags/sys-flags.service';
import publicSupportTableSize from './public/support/table-size/table-size.service';
import publicSupportTaqrOverride from './public/support/taqr-override/taqr-override.service';
import publicSupportTestAttemptReset from './public/support/test-attempt-reset/test-attempt-reset.service';
import publicSupportTestFormModuleItems from './public/support/test-form-module-items/test-form-module-items.service';
import publicSupportTestFormPrint from './public/support/test-form-print/test-form-print.service';
import publicSupportTestLog from './public/support/test-log/test-log.service';
import publicSupportTestSessionDate from './public/support/test-session/date/date.service';
import publicSupportTestWindow from './public/support/test-window/test-window.service';
import publicSupportTtAttempts from './public/support/tt-attempts/tt-attempts.service';
import publicSupportTtBooking from './public/support/tt-booking/tt-booking.service';
import publicSupportTtBookingStatus from './public/support/tt-booking-status/tt-booking-status.service';
import publicSupportTtLangReq from './public/support/tt-lang-req/tt-lang-req.service';
import publicSupportTtNotifications from './public/support/tt-notifications/tt-notifications.service';
import publicSupportTtReportNotifications from './public/support/tt-report-notifications/tt-report-notifications.service';
import publicSupportTtUpcomingSessions from './public/support/tt-upcoming-sessions/tt-upcoming-sessions.service';
import publicSupportUInvitationCode from './public/support/u-invitation-code/u-invitation-code.service';
import publicSupportUserRoleMerge from './public/support/user-role-merge/user-role-merge.service';
import publicSupportUserRoles from './public/support/user-roles/user-roles.service';
import publicSupportValidateOctMember from './public/support/validate-oct-member/validate-oct-member.service';
import publicTestAdminAccommResponses from './public/test-admin/accomm/responses/responses.service';
import publicTestAdminAppealsAppeals from './public/test-admin/appeals/appeals/appeals.service';
import publicTestAdminAppealsEnabledAppeals from './public/test-admin/appeals/enabled-appeals/enabled-appeals.service';
import publicTestAdminInvigilationOnbehalfQuestionResponse from './public/test-admin/invigilation/onbehalf/question-response/question-response.service';
import publicTestAdminInvigilationOnbehalfTestAttempt from './public/test-admin/invigilation/onbehalf/test-attempt/test-attempt.service';
import publicTestAdminInvigilationReportApplicantIssue from './public/test-admin/invigilation/report-applicant-issue/report-applicant-issue.service';
import publicTestAdminInvigilationReportGeneralIssue from './public/test-admin/invigilation/report-general-issue/report-general-issue.service';
import publicTestAdminInvigilationUnlockSection from './public/test-admin/invigilation/unlock-section/unlock-section.service';
import publicTestAdminInvigilationUnsubmit from './public/test-admin/invigilation/unsubmit/unsubmit.service';
import publicTestAdminSebHeader from './public/test-admin/seb-header/seb-header.service';
import publicTestAdminStudentsBooking from './public/test-admin/students/booking/booking.service';
import publicTestAdminStudentsReporting from './public/test-admin/students/reporting/reporting.service';
import publicTestAdminStudentsSchool from './public/test-admin/students/school/school.service';
import publicTestAdminStudentsWalkIn from './public/test-admin/students/walk-in/walk-in.service';
import publicTestAdminTestSessionSetupTimezone from './public/test-admin/test-session-setup/timezone/timezone.service';
import publicTestAdminTestSessionsGroupId from './public/test-admin/test-sessions/group-id/group-id.service';
import publicTestAdminTestSessionsMySingle from './public/test-admin/test-sessions/my-single/my-single.service';
import publicTestAdminTestSessionsPrior from './public/test-admin/test-sessions/prior/prior.service';
import publicTestAdminTestSessionsSingle from './public/test-admin/test-sessions/single/single.service';
import publicTestAdminTestSessionsSoftLock from './public/test-admin/test-sessions/soft-lock/soft-lock.service';
import publicTestAdminTestSessionsStudents from './public/test-admin/test-sessions/students/students.service';
import publicTestAdminTestSessionVideostreamLink from './public/test-admin/test-session/videostream-link/videostream-link.service';
import publicTestAdminTimezone from './public/test-admin/timezone/timezone.service';
import publicTestAdminValidateSeb from './public/test-admin/validate-seb/validate-seb.service';
import publicTestAuthAccountsAccess from './public/test-auth/accounts/access/access.service';
import publicTestAuthAccountsResendInvite from './public/test-auth/accounts/resend-invite/resend-invite.service';
import publicTestAuthAssetGroups from './public/test-auth/asset-groups/asset-groups.service';
import publicTestAuthAssetLibraries from './public/test-auth/asset-libraries/asset-libraries.service';
import publicTestAuthAssetLibraryFields from './public/test-auth/asset-library-fields/asset-library-fields.service';
import publicTestAuthAssetLink from './public/test-auth/asset-link/asset-link.service';
import publicTestAuthAssets from './public/test-auth/assets/assets.service';
import publicTestAuthBatchAllocationPolicies from './public/test-auth/batch-alloc-policies/batch-alloc-policies.service';
import publicTestAuthAssetVersions from './public/test-auth/asset-versions/asset-versions.service';
import publicTestAuthContentSearch from './public/test-auth/content-search/content-search.service';
import publicTestAuthFrameworks from './public/test-auth/frameworks/frameworks.service';
import publicTestAuthFrameworksAudit from './public/test-auth/frameworks-audit/frameworks-audit.service';
import publicTestAuthGroup from './public/test-auth/group/group.service';
import publicTestAuthGroupMembers from './public/test-auth/group-members/group-members.service';
import publicTestAuthGroupRoles from './public/test-auth/group-roles/group-roles.service';
import publicTestAuthGroups from './public/test-auth/groups/groups.service';
import publicTestAuthIssues from './public/test-auth/issues/issues.service';
import publicTestAuthItemBlockTemplatesRetrieval from './public/test-auth/item-block-templates-retrieval/item-block-templates-retrieval.service';
import publicTestAuthItemImpressions from './public/test-auth/item-impressions/item-impressions.service';
import publicTestAuthItemSet from './public/test-auth/item-set/item-set.service';
import publicTestAuthItemSetAudits from './public/test-auth/item-set-audits/item-set-audits.service';
import publicTestAuthItemSetExpAns from './public/test-auth/item-set-exp-ans/item-set-exp-ans.service';
import publicTestAuthItemTagLink from './public/test-auth/item-tag-link/item-tag-link.service';
import publicTestAuthItemTags from './public/test-auth/item-tags/item-tags.service';
import publicTestAuthManageAuthor from './public/test-auth/manage-author/manage-author.service';
import publicTestAuthNoteFiles from './public/test-auth/note-files/note-files.service';
import publicTestAuthNotes from './public/test-auth/notes/notes.service';
import publicTestAuthValidationNotes from './public/test-auth/validation-notes/validation-notes.service';
import publicTestAuthNotesAudit from './public/test-auth/notes/audit/audit.service';
import publicTestAuthNotifications from './public/test-auth/notifications/notifications.service';
import publicTestAuthPanelShells from './public/test-auth/panel-shells/panel-shells.service';
import publicTestAuthPublicPwdProtected from './public/test-auth/public-pwd-protected/public-pwd-protected.service';
import publicTestAuthQuestionRevisions from './public/test-auth/question-revisions/question-revisions.service';
import publicTestAuthQuestions from './public/test-auth/questions/questions.service';
import publicTestAuthQuestionSetLists from './public/test-auth/question-set-lists/question-set-lists.service';
import publicTestAuthQuestionSetParameters from './public/test-auth/question-set-parameters/question-set-parameters.service';
import publicTestAuthQuestionSetParametersVersions from './public/test-auth/question-set-parameters-versions/question-set-parameters-versions.service';
import publicTestAuthSearch from './public/test-auth/search/search.service';
import publicTestAuthShowComments from './public/test-auth/show-comments/show-comments.service';
import publicTestAuthSingleGroup from './public/test-auth/single-group/single-group.service';
import publicTestAuthStyleProfiles from './public/test-auth/style-profiles/style-profiles.service';
import publicTestAuthSuggestions from './public/test-auth/suggestions/suggestions.service';
import publicTestAuthTestDesignSignOff from './public/test-auth/test-design-sign-off/test-design-sign-off.service';
import publicTestAuthTestDesignItemDiffSignOff from './public/test-auth/test-design-item-diff-sign-off/test-design-item-diff-sign-off.service';
import publicTestAuthTestDesigns from './public/test-auth/test-designs/test-designs.service';
import publicTestAuthTestDesignsForms from './public/test-auth/test-designs-forms/test-designs-forms.service';
import publicTestAuthTestDesignsIsPublic from './public/test-auth/test-designs/is-public/is-public.service';
import publicTestAuthTestQuestionRegisterGenericParamMap from './public/test-auth/test-question-register-generic-param-map/test-question-register-generic-param-map.service';
import publicTestAuthTestQuestionRegisterParamMap from './public/test-auth/test-question-register-param-map/test-question-register-param-map.service';
import publicTestAuthTestQuestionScoringCodes from './public/test-auth/test-question-scoring-codes/test-question-scoring-codes.service';
import publicTestAuthTestQuestionScoringInfo from './public/test-auth/test-question-scoring-info/test-question-scoring-info.service';
import publicTestAuthTestQuestionTemplateVersions from './public/test-auth/test-question-template-versions/test-question-template-versions.service';
import publicTestAuthTestQuestionTemplates from './public/test-auth/test-question-templates/test-question-templates.service';
import publicTestAuthTestQuestionTemplateAuthGroups from './public/test-auth/test-question-template-auth-groups/test-question-template-auth-groups.service';
import publicTestAuthTextVoice from './public/test-auth/text-voice/text-voice.service';
import publicTestCertDataFile from './public/test-cert/data-file/data-file.service';
import publicTestCertFailedTransfers from './public/test-cert/failed-transfers/failed-transfers.service';
import publicTestCertRoles from './public/test-cert/roles/roles.service';
import publicTestCertSebConfig from './public/test-cert/seb-config/seb-config.service';
import publicTestCtrlAccommodations from './public/test-ctrl/accommodations/accommodations.service';
import publicTestCtrlAccountsInstitutions from './public/test-ctrl/accounts/institutions/institutions.service';
import publicTestCtrlAccountsSchoolAdmins from './public/test-ctrl/accounts/school-admins/school-admins.service';
import publicTestCtrlAccountsTestAdminRevoke from './public/test-ctrl/accounts/test-admin-revoke/test-admin-revoke.service';
import publicTestCtrlAppeals from './public/test-ctrl/appeals/appeals.service';
import publicTestCtrlAssessmentSessionAnalysis from './public/test-ctrl/assessment-session/analysis/analysis.service';
import publicTestCtrlAssessmentSessionAnalysisItem from './public/test-ctrl/assessment-session/analysis-item/analysis-item.service';
import publicTestCtrlAssessmentSessionAnalysisResponse from './public/test-ctrl/assessment-session/analysis-response/analysis-response.service';
import publicTestCtrlAssessmentSessionAuditMcq from './public/test-ctrl/assessment-session/audit/mcq/mcq.service';
import publicTestCtrlAssessmentSessionSummary from './public/test-ctrl/assessment-session/summary/summary.service';
import publicTestCtrlDataExportExports from './public/test-ctrl-data-export/exports/exports.service';
import publicTestCtrlDataExportPsychRuns from './public/test-ctrl-data-export/psych-runs/psych-runs.service';
import publicTestCtrlDataFile from './public/test-ctrl/data-file/data-file.service';
import publicTestCtrlIssueRevwItemExceptions from './/public/test-ctrl-issue-revw/item-exceptions/item-exceptions.service';
import publicTestCtrlIssueRevwReportedIssues from './/public/test-ctrl-issue-revw/reported-issues/reported-issues.service';
import publicTestCtrlIssueRevwSchoolExceptions from './/public/test-ctrl-issue-revw/school-exceptions/school-exceptions.service';
import publicTestCtrlIssueRevwStudentExceptions from './/public/test-ctrl-issue-revw/student-exceptions/student-exceptions.service';
import publicTestCtrlIssueRevwStudentItemExceptions from './/public/test-ctrl-issue-revw/student-item-exceptions/student-item-exceptions.service';
import publicTestCtrlIssueRevwStudents from './/public/test-ctrl-issue-revw/students/students.service';
import publicTestCtrlIssueREvwSessionQuestionLogs from './/public/test-ctrl-issue-revw/session-question-logs/session-question-logs.service';
import publicTestCtrlIssueRevwTestWindows from './/public/test-ctrl-issue-revw/test-windows/test-windows.service';
import publicTestCtrlIssueRevwUnsubReq from './/public/test-ctrl-issue-revw/unsub-req/unsub-req.service';
import publicTestCtrlLangReq from './public/test-ctrl/lang-req/lang-req.service';
import publicTestCtrlOdiStuAsmt from './public/test-ctrl/odi/stu-asmt/stu-asmt.service';
import publicTestCtrlOdiStuAsmtCsv from './public/test-ctrl/odi/stu-asmt-csv/stu-asmt-csv.service';
import publicTestCtrlOdiStuAsmtCsvZip from './public/test-ctrl/odi/stu-asmt-csv-zip/stu-asmt-csv-zip.service';
import publicTestCtrlOdiStuAsmtLevel from './public/test-ctrl/odi/stu-asmt-level/stu-asmt-level.service';
import publicTestCtrlOdiStuItemCsv from './public/test-ctrl/odi/stu-item-csv/stu-item-csv.service';
import publicTestCtrlOdiStuItemCsvZip from './public/test-ctrl/odi/stu-item-csv-zip/stu-item-csv-zip.service';
import publicTestCtrlOdiStuItemLevel from './public/test-ctrl/odi/stu-item-level/stu-item-level.service';
import publicTestCtrlProcCertActiveSessions from './public/test-ctrl/proc-cert/active-sessions/active-sessions.service';
import publicTestCtrlProcCertCurrentSessions from './public/test-ctrl/proc-cert/current-sessions/current-sessions.service';
import publicTestCtrlReporting from './public/test-ctrl/reporting/reporting.service';
import publicTestCtrlRoles from './public/test-ctrl/roles/roles.service';
import publicTestCtrlScoringScoringWindow from './public/test-ctrl/scoring/scoring-window/scoring-window.service';
import publicTestCtrlScoringRoles from './public/test-ctrl/scoring-roles/scoring-roles.service';
import publicTestCtrlSchoolsBoards from './public/test-ctrl/schools/boards/boards.service';
import publicTestCtrlSchoolsClasses from './public/test-ctrl/schools/classes/classes.service';
import publicTestCtrlSchoolsDataDlAsmtFrmkBatchAllocationPolicies from './public/test-ctrl/schools/data-dl/asmt-frmk/batch-allocation-policies/batch-allocation-policies.service';
import publicTestCtrlSchoolsDataDlAsmtFrmkGenModuleItems from './public/test-ctrl/schools/data-dl/asmt-frmk/gen-module-items/gen-module-items.service';
import publicTestCtrlSchoolsDataDlAsmtFrmkGenModules from './public/test-ctrl/schools/data-dl/asmt-frmk/gen-modules/gen-modules.service';
import publicTestCtrlSchoolsDataDlAsmtFrmkGenPanels from './public/test-ctrl/schools/data-dl/asmt-frmk/gen-panels/gen-panels.service';
import publicTestCtrlSchoolsDataDlAsmtFrmkItemScoringProfileFlags from './public/test-ctrl/schools/data-dl/asmt-frmk/item-scoring-profile-flags/item-scoring-profile-flags.service';
import publicTestCtrlSchoolsDataDlAsmtFrmkItemScoringProfileOptions from './public/test-ctrl/schools/data-dl/asmt-frmk/item-scoring-profile-options/item-scoring-profile-options.service';
import publicTestCtrlSchoolsDataDlAsmtFrmkItemScoringProfiles from './public/test-ctrl/schools/data-dl/asmt-frmk/item-scoring-profiles/item-scoring-profiles.service';
import publicTestCtrlSchoolsDataDlAsmtFrmkModules from './public/test-ctrl/schools/data-dl/asmt-frmk/modules/modules.service';
import publicTestCtrlSchoolsDataDlAsmtFrmkReportingComponents from './public/test-ctrl/schools/data-dl/asmt-frmk/reporting-components/reporting-components.service';
import publicTestCtrlSchoolsDataDlAsmtFrmkReportingComponentScoringTypes from './public/test-ctrl/schools/data-dl/asmt-frmk/reporting-component-scoring-types/reporting-component-scoring-types.service';
import publicTestCtrlSchoolsDataDlAsmtFrmkReportingComponentTypes from './public/test-ctrl/schools/data-dl/asmt-frmk/reporting-component-types/reporting-component-types.service';
import publicTestCtrlSchoolsDataDlAsmtFrmkResponseSetTypes from './public/test-ctrl/schools/data-dl/asmt-frmk/response-set-types/response-set-types.service';
import publicTestCtrlSchoolsDataDlAsmtFrmkRouting from './public/test-ctrl/schools/data-dl/asmt-frmk/routing/routing.service';
import publicTestCtrlSchoolsDataDlAsmtFrmkScoreOptions from './public/test-ctrl/schools/data-dl/asmt-frmk/score-options/score-options.service';
import publicTestCtrlSchoolsDataDlAsmtFrmkSessions from './public/test-ctrl/schools/data-dl/asmt-frmk/sessions/sessions.service';
import publicTestCtrlSchoolsDataDlAsmtFrmkStages from './public/test-ctrl/schools/data-dl/asmt-frmk/stages/stages.service';
import publicTestCtrlSchoolsDataDlAsmtFrmkTaskComponentTypes from './public/test-ctrl/schools/data-dl/asmt-frmk/task-component-types/task-component-types.service';
import publicTestCtrlSchoolsDataDlAsmtFrmkTestDesigns from './public/test-ctrl/schools/data-dl/asmt-frmk/test-designs/test-designs.service';
import publicTestCtrlSchoolsDataDlAsmtFrmkTestDesignVersions from './public/test-ctrl/schools/data-dl/asmt-frmk/test-design-versions/test-design-versions.service';
import publicTestCtrlSchoolsDataDlItemBankFlatItemBank from './public/test-ctrl/schools/data-dl/item-bank/flat-item-bank/flat-item-bank.service';
import publicTestCtrlSchoolsDataDlItemBankItems from './public/test-ctrl/schools/data-dl/item-bank/items/items.service';
import publicTestCtrlSchoolsDataDlItemBankItemSpecificRubricCodes from './public/test-ctrl/schools/data-dl/item-bank/item-specific-rubric-codes/item-specific-rubric-codes.service';
import publicTestCtrlSchoolsDataDlItemBankQuestionnaire from './public/test-ctrl/schools/data-dl/item-bank/questionnaire/questionnaire.service';
import publicTestCtrlSchoolsDataDlItemBankQuestionnaireRespCodes from './public/test-ctrl/schools/data-dl/item-bank/questionnaire-resp-codes/questionnaire-resp-codes.service';
import publicTestCtrlSchoolsDataDlItemBankScoringWindowItems from './public/test-ctrl/schools/data-dl/item-bank/scoring-window-items/scoring-window-items.service';
import publicTestCtrlSchoolsDataDlItemBankTestPanelItems from './public/test-ctrl/schools/data-dl/item-bank/test-panel-items/test-panel-items.service';
import publicTestCtrlSchoolsDataDlItemBankTestPanels from './public/test-ctrl/schools/data-dl/item-bank/test-panels/test-panels.service';
import publicTestCtrlSchoolsDataDlRegistrationsBoards from './public/test-ctrl/schools/data-dl/registrations/boards/boards.service';
import publicTestCtrlSchoolsDataDlRegistrationsClass from './public/test-ctrl/schools/data-dl/registrations/class/class.service';
import publicTestCtrlSchoolsDataDlRegistrationsRangeFinders from './public/test-ctrl/schools/data-dl/registrations/range-finders/range-finders.service';
import publicTestCtrlSchoolsDataDlRegistrationsSchoolDistricts from './public/test-ctrl/schools/data-dl/registrations/school-districts/school-districts.service';
import publicTestCtrlSchoolsDataDlRegistrationsSchools from './public/test-ctrl/schools/data-dl/registrations/schools/schools.service';
import publicTestCtrlSchoolsDataDlRegistrationsScorerItemTasks from './public/test-ctrl/schools/data-dl/registrations/scorer-item-tasks/scorer-item-tasks.service';
import publicTestCtrlSchoolsDataDlRegistrationsScorers from './public/test-ctrl/schools/data-dl/registrations/scorers/scorers.service';
import publicTestCtrlSchoolsDataDlRegistrationsScoringLeaders from './public/test-ctrl/schools/data-dl/registrations/scoring-leaders/scoring-leaders.service';
import publicTestCtrlSchoolsDataDlRegistrationsScoringSupervisors from './public/test-ctrl/schools/data-dl/registrations/scoring-supervisors/scoring-supervisors.service';
import publicTestCtrlSchoolsDataDlRegistrationsScoringWindows from './public/test-ctrl/schools/data-dl/registrations/scoring-windows/scoring-windows.service';
import publicTestCtrlSchoolsDataDlRegistrationsScoringWindowTestWindow from './public/test-ctrl/schools/data-dl/registrations/scoring-window-test-window/scoring-window-test-window.service';
import publicTestCtrlSchoolsDataDlRegistrationsStudentIsrMetas from './public/test-ctrl/schools/data-dl/registrations/student-isr-metas/student-isr-metas.service';
import publicTestCtrlSchoolsDataDlRegistrationsStudents from './public/test-ctrl/schools/data-dl/registrations/students/students.service';
import publicTestCtrlSchoolsDataDlRegistrationsTeachers from './public/test-ctrl/schools/data-dl/registrations/teachers/teachers.service';
import publicTestCtrlSchoolsDataDlRegistrationsTestSessions from './public/test-ctrl/schools/data-dl/registrations/test-sessions/test-sessions.service';
import publicTestCtrlSchoolsDataDlRegistrationsTestWindowDesignAlloc from './public/test-ctrl/schools/data-dl/registrations/test-window-design-alloc/test-window-design-alloc.service';
import publicTestCtrlSchoolsDataDlRegistrationsTestWindows from './public/test-ctrl/schools/data-dl/registrations/test-windows/test-windows.service';
import publicTestCtrlSchoolsDataDlResponsesByBoard from './/public/test-ctrl/schools/data-dl/responses/by-board/by-board.service';
import publicTestCtrlSchoolsDataDlResponsesByDate from './/public/test-ctrl/schools/data-dl/responses/by-date/by-date.service';
import publicTestCtrlSchoolsDataDlResponsesBySchool from './/public/test-ctrl/schools/data-dl/responses/by-school/by-school.service';
import publicTestCtrlSchoolsDataDlResponsesCrSubm from './/public/test-ctrl/schools/data-dl/responses/cr-subm/cr-subm.service';
import publicTestCtrlSchoolsDataDlResponsesFlatTestAttResp from './public/test-ctrl/schools/data-dl/responses/flat-test-att-resp/flat-test-att-resp.service';
import publicTestCtrlSchoolsDataDlResponsesItemSumm from './public/test-ctrl/schools/data-dl/responses/item-summ/item-summ.service';
import publicTestCtrlSchoolsDataDlResponsesLangSubmSumm from './/public/test-ctrl/schools/data-dl/responses/lang-subm-summ/lang-subm-summ.service';
import publicTestCtrlSchoolsDataDlResponsesNumStuByPnum from './/public/test-ctrl/schools/data-dl/responses/num-stu-by-pnum/num-stu-by-pnum.service';
import publicTestCtrlSchoolsDataDlResponsesProcessDataDrawingTools from './public/test-ctrl/schools/data-dl/responses/process-data/drawing-tools/drawing-tools.service';
import publicTestCtrlSchoolsDataDlResponsesProcessDataItems from './public/test-ctrl/schools/data-dl/responses/process-data/items/items.service';
import publicTestCtrlSchoolsDataDlResponsesProcessDataRecordings from './public/test-ctrl/schools/data-dl/responses/process-data/recordings/recordings.service';
import publicTestCtrlSchoolsDataDlResponsesProcessDataScreens from './public/test-ctrl/schools/data-dl/responses/process-data/screens/screens.service';
import publicTestCtrlSchoolsDataDlResponsesProcessDataTeacherResponses from './public/test-ctrl/schools/data-dl/responses/process-data/teacher-responses/teacher-responses.service';
import publicTestCtrlSchoolsDataDlResponsesProcessDataTextToSpeech from './public/test-ctrl/schools/data-dl/responses/process-data/text-to-speech/text-to-speech.service';
import publicTestCtrlSchoolsDataDlResponsesReportedIssues from './public/test-ctrl/schools/data-dl/responses/reported-issues/reported-issues.service';
import publicTestCtrlSchoolsDataDlResponsesRespSumm from './public/test-ctrl/schools/data-dl/responses/resp-summ/resp-summ.service';
import publicTestCtrlSchoolsDataDlResponsesSchByDate from './/public/test-ctrl/schools/data-dl/responses/sch-by-date/sch-by-date.service';
import publicTestCtrlSchoolsDataDlResponsesSchIsrReportAccess from './/public/test-ctrl/schools/data-dl/responses/sch-isr-report-access/sch-isr-report-access.service';
import publicTestCtrlSchoolsDataDlResponsesStuAsmtVal from './/public/test-ctrl/schools/data-dl/responses/stu-asmt-val/stu-asmt-val.service';
import publicTestCtrlSchoolsDataDlResponsesStuAtt from './public/test-ctrl/schools/data-dl/responses/stu-att/stu-att.service';
import publicTestCtrlSchoolsDataDlResponsesSubm from './/public/test-ctrl/schools/data-dl/responses/subm/subm.service';
import publicTestCtrlSchoolsDataDlResponsesSubmItemResp from './public/test-ctrl/schools/data-dl/responses/subm-item-resp/subm-item-resp.service';
import publicTestCtrlSchoolsDataDlResponsesSubmSummary from './/public/test-ctrl/schools/data-dl/responses/subm-summary/subm-summary.service';
import publicTestCtrlSchoolsDataDlResponsesSubmTestAtt from './public/test-ctrl/schools/data-dl/responses/subm-test-att/subm-test-att.service';
import publicTestCtrlSchoolsDataDlScoringItemPsychometrics from './public/test-ctrl/schools/data-dl/scoring/item-psychometrics/item-psychometrics.service';
import publicTestCtrlSchoolsDataDlScoringQualifyingTestResponseSummary from './public/test-ctrl/schools/data-dl/scoring/qualifying-test-response-summary/qualifying-test-response-summary.service';
import publicTestCtrlSchoolsDataDlScoringRangeFindingSelections from './public/test-ctrl/schools/data-dl/scoring/range-finding-selections/range-finding-selections.service';
import publicTestCtrlSchoolsDataDlScoringResponseSetResponses from './public/test-ctrl/schools/data-dl/scoring/response-set-responses/response-set-responses.service';
import publicTestCtrlSchoolsDataDlScoringResponseSets from './public/test-ctrl/schools/data-dl/scoring/response-sets/response-sets.service';
import publicTestCtrlSchoolsDataDlScoringResponsesScore from './public/test-ctrl/schools/data-dl/scoring/responses-score/responses-score.service';
import publicTestCtrlSchoolsDataDlScoringScorerClaimedBatches from './public/test-ctrl/schools/data-dl/scoring/scorer-claimed-batches/scorer-claimed-batches.service';
import publicTestCtrlSchoolsDataDlScoringScorerClaimedResponses from './public/test-ctrl/schools/data-dl/scoring/scorer-claimed-responses/scorer-claimed-responses.service';
import publicTestCtrlSchoolsDataDlScoringScorerItemSummary from './public/test-ctrl/schools/data-dl/scoring/scorer-item-summary/scorer-item-summary.service';
import publicTestCtrlSchoolsDataDlScoringScorerPaceEvents from './public/test-ctrl/schools/data-dl/scoring/scorer-pace-events/scorer-pace-events.service';
import publicTestCtrlSchoolsDataDlScoringScorings from './public/test-ctrl/schools/data-dl/scoring/scorings/scorings.service';
import publicTestCtrlSchoolsDataDlScoringValidityResponseSummary from './public/test-ctrl/schools/data-dl/scoring/validity-response-summary/validity-response-summary.service';
import publicTestCtrlSchoolsReportedIssueComments from './public/test-ctrl/schools/reported-issue-comments/reported-issue-comments.service';
import publicTestCtrlSchoolsReportedIssues from './public/test-ctrl/schools/reported-issues/reported-issues.service';
import publicTestCtrlSchoolsReportedIssuesAssignees from './public/test-ctrl/schools/reported-issues-assignees/reported-issues-assignees.service';
import publicTestCtrlSchoolsReportedIssuesCategories from './public/test-ctrl/schools/reported-issues-categories/reported-issues-categories.service';
import publicTestCtrlSchoolsReportedIssueStudents from './public/test-ctrl/schools/reported-issue-students/reported-issue-students.service';
import publicTestCtrlSchoolsSchool from './public/test-ctrl/schools/school/school.service';
import publicTestCtrlSchoolsSchools from './public/test-ctrl/schools/schools/schools.service';
import publicTestCtrlSchoolsStudent from './public/test-ctrl/schools/student/student.service';
import publicTestCtrlSchoolsStudentAttemptResponses from './public/test-ctrl/schools/student-attempt-responses/student-attempt-responses.service';
import publicTestCtrlSchoolsStudentAttempts from './public/test-ctrl/schools/student-attempts/student-attempts.service';
import publicTestCtrlSchoolsStudentExceptions from './public/test-ctrl/schools/student-exceptions/student-exceptions.service';
import publicTestCtrlSchoolsStudentItemExceptions from './public/test-ctrl/schools/student-item-exceptions/student-item-exceptions.service';
import publicTestCtrlSchoolsStudents from './public/test-ctrl/schools/students/students.service';
import publicTestCtrlSchoolsStudentsTeachersClasses from './public/test-ctrl/schools/students-teachers-classes/students-teachers-classes.service';
import publicTestCtrlSchoolsSummary from './public/test-ctrl/schools/summary/summary.service';
import publicTestCtrlSchoolsTeachers from './public/test-ctrl/schools/teachers/teachers.service';
import publicTestCtrlSchooolsDataDlCodebookChangeLogTCV from './public/test-ctrl/schools/data-dl/codebook-change-log-tcv/codebook-change-log-tcv.service';
import publicTestCtrlSchooolsDataDlCodebookTCV from './public/test-ctrl/schools/data-dl/codebook-tcv/codebook-tcv.service';
import publicTestCtrlSchooolsDataDlTransferTableDl from './public/test-ctrl/schools/data-dl/transfer-table-dl/transfer-table-dl.service';
import publicTestCtrlSchooolsDataDlTransferTableTCV from './public/test-ctrl/schools/data-dl/transfer-table-tcv/transfer-table-tcv.service';
import publicTestCtrlSebConfig from './public/test-ctrl/seb-config/seb-config.service';
import publicTestCtrlSebDownloadLink from './public/test-admin/seb-download-link/seb-download-link.service';
import publicTestCtrlSessionsDate from './public/test-ctrl/sessions-date/sessions-date.service';
import publicTestCtrlSyncScoringAuthoring from './public/test-ctrl/sync-scoring-authoring/sync-scoring-authoring.service';
import publicTestCtrlTechnicalReadinessFormDownload from './public/test-ctrl/technical-readiness-form-download/technical-readiness-form-download.service';
import publicTestCtrlTechnicalReadinessTracking from './public/test-ctrl/technical-readiness-tracking/technical-readiness-tracking.service';
import publicTestCtrlTechnicalReadinessVerifyForm from './public/test-ctrl/technical-readiness-verify-form/technical-readiness-verify-form.service';
import publicTestCtrlTestDesigns from './public/test-ctrl/test-designs/test-designs.service';
import publicTestCtrlTestAttemptsInvalidateAttempts from './public/test-ctrl/test-attempts/invalidate-attempts/invalidate-attempts.service';
import publicTestCtrlTestWindowAdministrationWindows from './public/test-ctrl/test-window/administration-windows/administration-windows.service';
import publicTestCtrlTestWindowAssessmentPriceConfig from './public/test-ctrl/test-window/assessment-price-config/assessment-price-config.service';
import publicTestCtrlTestWindowConfig from './public/test-ctrl/test-window/config/config.service';
import publicTestCtrlTestWindowSummary from './public/test-ctrl/test-window/summary/summary.service';
import publicTestCtrlTwDataFile from './public/test-ctrl/tw-data-file/tw-data-file.service';
import publicTestCtrlValidationDates from './public/test-ctrl/validation/dates/dates.service';
import publicTestCtrlValidationIssue from './public/test-ctrl/validation/issue/issue.service';
import publicTestCtrlValidationIssueResolution from './public/test-ctrl/validation/issue-resolution/issue-resolution.service';
import publicTestCtrlValidationReady from './public/test-ctrl/validation/ready/ready.service';
import publicTestCtrlValidationTestAttempts from './public/test-ctrl/validation/test-attempts/test-attempts.service';
import publicTestCtrlValidationTestForm from './public/test-ctrl/validation/test-form/test-form.service';
import publicTestCtrlValidationTestSessions from './public/test-ctrl/validation/test-sessions/test-sessions.service';
import publicTestQuestionRegisterTqrGenericMap from './public/test-question-register/tqr-generic-map/tqr-generic-map.service';
import publicTestQuestionRegisterTqrMap from './public/test-question-register/tqr-map/tqr-map.service';
import publicTestQuestionRegisterTqrPublish from './public/test-question-register/tqr-publish/tqr-publish.service';
import publicTestTakerAllowAppeals from './public/test-taker/allow-appeals/allow-appeals.service';
import publicTestTakerAppealInit from './public/test-taker/appeal-init/appeal-init.service';
import publicTestTakerAppeals from './public/test-taker/appeals/appeals.service';
import publicTestTakerDataDownload from './public/test-taker/data/download/download.service';
import publicTestTakerInvigilationAttemptChecklist from './public/test-taker/invigilation/attempt-checklist/attempt-checklist.service';
import publicTestTakerInvigilationAttemptChecklistSeb from './public/test-taker/invigilation/attempt-checklist-seb/attempt-checklist-seb.service';
import publicTestTakerInvigilationHelp from './public/test-taker/invigilation/help/help.service';
import publicTestTakerInvigilationReportIssue from './public/test-taker/invigilation/report-issue/report-issue.service';
import publicTestTakerInvigilationTestAttemptAttest from './public/test-taker/invigilation/test-attempt-attest/test-attempt-attest.service';
import publicTestTakerInvigilationTestAttemptLang from './public/test-taker/invigilation/test-attempt-lang/test-attempt-lang.service';
import publicTestTakerInvigilationTestAttemptTime from './public/test-taker/invigilation/test-attempt-time/test-attempt-time.service';
import publicTestTakerReportResults from './public/test-taker/report/results/results.service';
import publicTestTakerSebDownloadLink from './public/test-taker/seb-download-link/seb-download-link.service';
import publicTestTakerSessionId from './public/test-taker/session-id/session-id.service';
import publicTestTakerTestSessionsInvitation from './public/test-taker/test-sessions/invitation/invitation.service';
import publicTransactionsStripeStripe from './public/transactions/stripe/stripe.service'
import publicTransactionsTransactions from './public/transactions/transactions/transactions.service';
import publicZoomAccessToken from './public/zoom/access-token/access-token.service';
import publicZoomMeetings from './public/zoom/meetings/meetings.service';
import responseSets from './public/test-ctrl/test-window/response-sets/response-sets.service';
import responsibiltyAgreement from "./public/school-admin/responsibility-agreement/responsibility-agreement.service";
import restAnalyticsEvent from './public/analytics/event/event.service';
import restAnalyticsSession from './public/analytics/session/session.service';
import restAuthConfirmEmail from './public/auth/confirm-email/confirm-email.service';
import restAuthRefreshToken from './public/auth/refresh-token/refresh-token.service';
import restAuthResetPassword from './public/auth/reset-password/reset-password.service';
import restAuthResetPasswordRequest from './public/auth/reset-password-request/reset-password-request.service';
import restAuthTestAdmin from './public/auth/test-admin/test-admin.service';
import restAuthTestCert from './public/auth/test-cert/test-cert.service';
import restAuthTestCtrl from './public/auth/test-ctrl/test-ctrl.service';
import restAuthTestTaker from './public/auth/test-taker/test-taker.service';
import restAuthTranslators from './public/auth/translators/translators.service';
import restAuthUserInfoCore from './public/auth/user-info-core/user-info-core.service';
import restPing from './public/ping/ping.service';
import restTestAdminAccommPendingRequests from './public/test-admin/accomm/pending-requests/pending-requests.service';
import restTestAdminAccommTransferReq from './public/test-admin/accomm/transfer-req/transfer-req.service';
import restTestAdminAccountsAccess from './public/test-admin/accounts/access/access.service';
import restTestAdminAccountsAssignedCoord from './public/test-admin/accounts/assigned-coord/assigned-coord.service';
import restTestAdminAccountsAssignedSessions from './public/test-admin/accounts/assigned-sessions/assigned-sessions.service';
import restTestAdminInstitution from './public/test-admin/institution/institution.service';
import restTestAdminInvigilationAbsenceReport from './public/test-admin/invigilation/absence-report/absence-report.service';
import restTestAdminInvigilationClose from './public/test-admin/invigilation/close/close.service';
import restTestAdminInvigilationGroupIssue from './public/test-admin/invigilation/group-issue/group-issue.service';
import restTestAdminInvigilationGroupTimeExtend from './public/test-admin/invigilation/group-time-extend/group-time-extend.service';
import restTestAdminInvigilationIdentityNo from './public/test-admin/invigilation/identity-no/identity-no.service';
import restTestAdminInvigilationIdentityVerif from './public/test-admin/invigilation/identity-verif/identity-verif.service';
import restTestAdminInvigilationIndivIssue from './public/test-admin/invigilation/indiv-issue/indiv-issue.service';
import restTestAdminInvigilationIndivTimeExtend from './public/test-admin/invigilation/indiv-time-extend/indiv-time-extend.service';
import restTestAdminInvigilationPause from './public/test-admin/invigilation/pause/pause.service';
import restTestAdminInvigilationSend from './public/test-admin/invigilation/send/send.service';
import restTestAdminInvigilationTempAccess from './public/test-admin/invigilation/temp-access/temp-access.service';
import restTestAdminRoles from './public/test-admin/roles/roles.service';
import restTestAdminTestSessionAssignedInvigilator from './public/test-admin/test-session/assigned-invigilator/assigned-invigilator.service';
import restTestAdminTestSessionBookings from './public/test-admin/test-session/bookings/bookings.service';
import restTestAdminTestSessionBookingsIndic from './public/test-admin/test-session/bookings-indic/bookings-indic.service';
import restTestAdminTestSessionBookingTransferReq from './public/test-admin/test-session/booking-transfer-req/booking-transfer-req.service';
import restTestAdminTestSessionCancellation from './public/test-admin/test-session/cancellation/cancellation.service';
import restTestAdminTestSessionCapacity from './public/test-admin/test-session/capacity/capacity.service';
import restTestAdminTestSessionInvitationCode from './public/test-admin/test-session/invitation-code/invitation-code.service';
import restTestAdminTestSessionRestrictions from './public/test-admin/test-session/restrictions/restrictions.service';
import restTestAdminTestSessionsAll from './public/test-admin/test-sessions/all/all.service';
import restTestAdminTestSessionSetupAccommPing from './public/test-admin/test-session-setup/accomm-ping/accomm-ping.service';
import restTestAdminTestSessionSetupActive from './public/test-admin/test-session-setup/active/active.service';
import restTestAdminTestSessionSetupChecklist from './public/test-admin/test-session-setup/checklist/checklist.service';
import restTestAdminTestSessionSetupLocations from './public/test-admin/test-session-setup/locations/locations.service';
import restTestAdminTestSessionSetupSessions from './public/test-admin/test-session-setup/sessions/sessions.service';
import restTestAdminTestSessionSetupTechReady from './public/test-admin/test-session-setup/tech-ready/tech-ready.service';
import restTestAdminTestSessionSetupTestWindows from './public/test-admin/test-session-setup/test-windows/test-windows.service';
import restTestAdminTestSessionSetupTimeSlotRestriction from './public/test-admin/test-session-setup/time-slot-restriction/time-slot-restriction.service';
import restTestAdminTestSessionSetupTimeSlots from './public/test-admin/test-session-setup/time-slots/time-slots.service';
import restTestAdminTestSessionsMyUpcoming from './public/test-admin/test-sessions/my-upcoming/my-upcoming.service';
import restTestAdminTestSessionsSummary from './public/test-admin/test-sessions/summary/summary.service';
import restTestAdminTestSessionStatusUpdate from './public/test-admin/test-session/status-update/status-update.service';
import restTestAdminTestSessionWaitlisters from './public/test-admin/test-session/waitlisters/waitlisters.service';
import restTestAdminTestSessionWaitlisterTransferReq from './public/test-admin/test-session/waitlister-transfer-req/waitlister-transfer-req.service';
import restTestAdminTestSessionWaitListingsIndic from './public/test-admin/test-session/wait-listings-indic/wait-listings-indic.service';
import restTestCertRetrievals from './public/test-cert/retrievals/retrievals.service';
import restTestCtrlAccountsTestAdmins from './public/test-ctrl/accounts/test-admins/test-admins.service';
import restTestCtrlAccountsTestCerts from './public/test-ctrl/accounts/test-certs/test-certs.service';
import restTestCtrlAccountsTestCtrls from './public/test-ctrl/accounts/test-ctrls/test-ctrls.service';
import restTestCtrlSummary from './public/test-ctrl/summary/summary.service';
import restTestCtrlTestWindowReleaseNotes from './public/test-ctrl/test-window/release-notes/release-notes.service';
import restTestCtrlTestWindowRetrievals from './public/test-ctrl/test-window/retrievals/retrievals.service';
import restTestCtrlTestWindowTestDesign from './public/test-ctrl/test-window/test-design/test-design.service';
import restTestCtrlTestWindowTestSessions from './public/test-ctrl/test-window/test-sessions/test-sessions.service';
import restTestletsLoft from './public/testlets/loft/loft.service';
import restTestTakerAccommodationsPendingReq from './public/test-taker/accommodations/pending-req/pending-req.service';
import restTestTakerCreditDetails from './public/test-taker/credit-details/credit-details.service';
import restTestTakerInvigilationQuestionContent from './public/test-taker/invigilation/question-content/question-content.service';
import restTestTakerInvigilationQuestionResponse from './public/test-taker/invigilation/question-response/question-response.service';
import restTestTakerInvigilationScreenSession from './public/test-taker/invigilation/screen-session/screen-session.service';
import restTestTakerInvigilationSectionSubmit from './public/test-taker/invigilation/section-submit/section-submit.service';
import restTestTakerInvigilationTestAttempt from './public/test-taker/invigilation/test-attempt/test-attempt.service';
import restTestTakerInvigilationTestForm from './public/test-taker/invigilation/test-form/test-form.service';
import restTestTakerTestSessionsAll from './public/test-taker/test-sessions/all/all.service';
import restTestTakerTestSessionsBooking from './public/test-taker/test-sessions/booking/booking.service';
import restTestTakerTestSessionsOctValidation from './public/test-taker/test-sessions/oct-validation/oct-validation.service';
import restTestTakerTestSessionsPastAttempts from './public/test-taker/test-sessions/past-attempts/past-attempts.service';
import restTestTakerTestSessionsPending from './public/test-taker/test-sessions/pending/pending.service';
import restTestTakerTestSessionsWaitlist from './public/test-taker/test-sessions/waitlist/waitlist.service';
import restTranslation from './public/translation/translation.service';
import roles from './public/support/scoring/roles/roles.service';
import schoolProfile from './public/school-admin/school-profile/school-profile.service';
import scoringWindowItems from './public/test-ctrl/test-window/scoring-window-items/scoring-window-items.service';
import scoringWindows from './public/test-ctrl/test-window/scoring-windows/scoring-windows.service';
import scoringWindowSetup from './public/test-ctrl/test-window/scoring-window-setup/scoring-window-setup.service';
import studentAccommodationsService from './public/educator/student-accommodations/student-accommodations.service';
import studentCreationForm from './public/school-admin/student-creation-form/student-creation-form.service';
import subsessionPresets from './public/educator/subsession-presets/subsession-presets.service';
import TestDesignQuestionVersions from './public/test-auth/test-design-question-versions/test-design-question-versions.service';
import thirdPartyOctAuthentication from './third-party/oct/oct-authentication/oct-authentication.service';
import thirdPartyOctSuccessfulAttempts from './third-party/oct/successful-attempts/successful-attempts.service';
import transactionsStripeStripeCheckout from './public/test-taker/test-sessions/stripe-checkout/stripe-checkout.service'
import users from './public/support/users/users.service';
import windows from './public/support/scoring/windows/windows.service';
import publicTestAuthQuestionChangeLog from './public/test-auth/question-change-log/question-change-log.service';
import publicTestAuthHighlightNotes from './public/test-auth/highlight-notes/highlight-notes.service';
import publicTestAuthQuestionWorkflowStages from './public/test-auth/question-workflow-stages/question-workflow-stages.service';
import publicTestAuthQuestionGraphicRequests from './public/test-auth/question-graphic-requests/question-graphic-requests.service';
import publicStudentAsmtCoverPage from './public/student/asmt-cover-page/asmt-cover-page.service';
import publicLandingPracticeTests from './public/landing/practice-tests/practice-tests.service';
import publicTestAuthInitWebsockets from './public/test-auth/init-websockets/init-websockets.service';
import publicScorLeadWindowStats from './public/scor-lead/window-stats/window-stats.service';
import publicScorLeadMaxReadProcess from './public/scor-lead/max-read-process/max-read-process.service';
import publicScorLeadMisalignedReads from './public/scor-lead/misaligned-reads/misaligned-reads.service';
import dictionaryService from './public/test-ctrl/dictionary/dictionary.service';
import publicTestCtrlLoadTestScorer from './public/test-ctrl/load-test/scorer/scorer.service';
import publicScorLeadBatchProcessesIncompleBatchSets from './public/scor-lead/batch-processes/incomple-batch-sets/incomple-batch-sets.service';
import publicAuthMyRoleTypes from './public/auth/my-role-types/my-role-types.service';
import publicEducatorClasses from './public/educator/classes/classes.service';
import publicEducatorClassRollover from './public/educator/class-rollover/class-rollover.service';
import publicEducatorLocalScoringFramework from './public/educator/local-scoring/scoring-framework/scoring-framework.service';
import publicEducatorLocalScoringStats from './public/educator/local-scoring/reports/reports.service';
import publicEducatorLocalScoringTemp from './public/educator/local-scoring-temp/local-scoring-temp.service';
import publicEducatorSessionTime from './public/educator/session-time/session-time.service';
import publicEducatorStudentSessionTime from './public/educator/student-session-time/student-session-time.service';
import publicStudentSessionTime from './public/student/session-time/session-time.service';
import publicDistAdminSignedUrl from './public/dist-admin/signed-url/signed-url.service';
import publicTestCtrlTestWindowSchoolsAllowed from './public/test-ctrl/test-window/schools-allowed/schools-allowed.service';
import publicTestCtrlTestWindowSchoolDateExceptions from './public/test-ctrl/test-window/school-date-exceptions/school-date-exceptions.service';
import publicTestAuthItemResponseFormatting from './public/test-auth/item-response-formatting/item-response-formatting.service';
import publicEducatorSoftLockSession from './public/educator/soft-lock-session/soft-lock-session.service';
import publicEducatorClassroom from './public/educator/classroom/classroom.service';
import publicTestAuthAdministrationWindowAllocs from './public/test-auth/administration-window-allocs/administration-window-allocs.service';
import publicTestCtrlTestWindowScheduledSessions from './public/test-ctrl/test-window/scheduled-sessions/scheduled-sessions.service';
import publicTestCtrlIssueRevwSchoolTwSummaries from './public/test-ctrl-issue-revw/school-tw-summaries/school-tw-summaries.service';
import publicError from './public/error/error.service';
import publicTestCtrlKnowledgeBase from './public/test-ctrl/knowledge-base/knowledge-base.service';
import publicTestCtrlSystemMessage from './public/test-ctrl/system-message/system-message.service';
import scoreEntry from './public/educator/score-entry/score-entry.service';
import summaryReport from './public/educator/summary-report/summary-report.service';
import individualReport from './public/educator/individual-report/individual-report.service';
import patIndividualReport from './public/educator/pat-individual-report/pat-individual-report.service';
import publicSchoolAdminReportsPrelim from './public/school-admin/reports-prelim/reports-prelim.service';
import testWindow from './public/school-board/test-window/test-window.service';
import daReports from './public/school-board/da-reports/da-reports.service';
import publicDataExporterDataExportQueries from './public/data-exporter/data-export-queries/data-export-queries.service';
import cutScoresDefinitions from './public/test-auth/cut-scores-definitions/cut-scores-definitions.service';
import publicTestAuthTwtarTestWindows from './public/test-auth/twtar/test-windows/test-windows.service';
import publicTestAuthTwtarTwTdTypes from './public/test-auth/twtar/tw-td-types/tw-td-types.service';
import publicTestAuthTwtarTestDesigns from './public/test-auth/twtar/test-designs/test-designs.service';
import publicTestAuthTwtarSignOffs from './public/test-auth/twtar/sign-offs/sign-offs.service';
import publicTestAuthGroupAccounts from './public/test-auth/group-accounts/group-accounts.service';
import publicTestCtrlSchoolsStudentResponses from './public/test-ctrl/schools/student-responses/student-responses.service';
import publicTestCtrlTestWindowSchoolLangExceptions from './public/test-ctrl/test-window/school-lang-exceptions/school-lang-exceptions.service';
import publicTestCtrlTestDesignPrint from './public/test-ctrl/test-design-print/test-design-print.service';
import authLockDownBrowser from './auth/lock-down-browser/lock-down-browser.service';
import publicEducatorClassArchive from './public/educator/class-archive/class-archive.service';
import publicStudentLockDown from './public/student/lock-down/lock-down.service';
import publicTestAuthAsmtCoverPage from './public/test-auth/asmt-cover-page/asmt-cover-page.service';
import publicScorLeadWithheldResponsesScoring from './public/scor-lead/withheld-responses-scoring/withheld-responses-scoring.service';
import publicCronTrackMemoryUsage from './public/cron/track-memory-usage/track-memory-usage.service';
import publicAnonLdbConfig from './public/anon/ldb-config/ldb-config.service';
import publicTestAuthItemStats from './public/test-auth/item-stats/item-stats.service';
import publicTestAuthItemWindowUses from './public/test-auth/item-window-uses/item-window-uses.service';
import publicScorLeadMarkingStandardsRangeTags from './public/scor-lead/marking-standards-range-tags/marking-standards-range-tags.service';
import publicTestCtrlSchoolsGlobalItemExceptions from './public/test-ctrl/schools/global-item-exceptions/global-item-exceptions.service';
import publicDataExporterDataAssetDefs from './public/data-exporter/data-asset-defs/data-asset-defs.service';
import publicDataExporterDataJobDefs from './public/data-exporter/data-job-defs/data-job-defs.service';
import publicDataExporterDataJobContents from './public/data-exporter/data-job-contents/data-job-contents.service';
import publicDataExporterAssetData from './public/data-exporter/asset-data/asset-data.service';
import publicSchoolAdminAlternativeVersionRequest from './public/school-admin/alternative-version-request/alternative-version-request.service';
import publicAltVersionCtrlAltVersionRequests from './public/alt-version-ctrl/alt-version-requests/alt-version-requests.service';
import publicAltVersionCtrlAltVersionRequestNotes from './public/alt-version-ctrl/alt-version-request-notes/alt-version-request-notes.service';
import publicAltVersionCtrlAccessFileDownloads from './public/alt-version-ctrl/access-file-downloads/access-file-downloads.service';
import publicAltVersionCtrlAccessFileNotes from './public/alt-version-ctrl/access-file-notes/access-file-notes.service';
import publicAltVersionCtrlAccessFiles from './public/alt-version-ctrl/access-files/access-files.service';
import publicSupportBulkScanning from './public/support/bulk-scanning/bulk-scanning.service';
import publicSchoolBoardSdAccess from './public/school-board/sd-access/sd-access.service';
import publicScorLeadStudentAttemptResponseScans from './public/scor-lead/student-attempt-response-scans/student-attempt-response-scans.service';
import publicUserAuthenticatedReportIssue from './public/user-authenticated/report-issue/report-issue.service';
import categoryDescriptors from './public/test-auth/category-descriptors/category-descriptors.service';
import publicDistAdminDataExporterPkExtraction from './public/dist-admin/data-exporter/pk-extraction/pk-extraction.service';
import publicScorScorBatchesAnnotations from './public/scor-scor/batches/annotations/annotations.service';
import publicDataExporterDataExportDagDataPackage from './public/data-exporter/data-export-dag-data-package/data-export-dag-data-package.service';
import publicSupportStudentAttempts from './public/support/student-attempts/student-attempts.service';
import publicSupportClassLookup from './public/support/class-lookup/class-lookup.service';
import publicTestAuthAssessmentProfilesStyleProfiles from './public/test-auth/assessment-profiles/style-profiles/style-profiles.service';
import publicTestAuthAssessmentProfilesTwProfiles from './public/test-auth/assessment-profiles/tw-profiles/tw-profiles.service';
import publicTestAuthAssessmentProfilesTwtarCourses from './public/test-auth/assessment-profiles/twtar-courses/twtar-courses.service';
import publicTestAuthAssessmentProfilesTwtarFormCardinality from './public/test-auth/assessment-profiles/twtar-form-cardinality/twtar-form-cardinality.service';
import publicTestAuthAssessmentProfilesTwtarTypeSlugs from './public/test-auth/assessment-profiles/twtar-type-slugs/twtar-type-slugs.service';
import publicTestAuthReportingProfilesCategorySchemas from './public/test-auth/reporting-profiles/category-schemas/category-schemas.service';
import publicTestAuthReportingProfilesCutScoreProfiles from './public/test-auth/reporting-profiles/cut-score-profiles/cut-score-profiles.service';
import publicTestAuthReportingProfilesCutScoreSchemas from './public/test-auth/reporting-profiles/cut-score-schemas/cut-score-schemas.service';
import publicTestAuthReportingProfilesDomainSchemas from './public/test-auth/reporting-profiles/domain-schemas/domain-schemas.service';
import publicTestAuthReportingProfilesReportingProfiles from './public/test-auth/reporting-profiles/reporting-profiles/reporting-profiles.service';
import publicTestAuthReportingProfilesLayoutProfiles from './public/test-auth/reporting-profiles/layout-profiles/layout-profiles.service';
import publicTestAuthReportingProfilesTextNodeRefs from './public/test-auth/reporting-profiles/text-node-refs/text-node-refs.service';
import publicTestAuthReportingProfilesLayoutConfigs from './public/test-auth/reporting-profiles/layout-configs/layout-configs.service';
import publicTestAuthReportingProfilesDiscontinuationProfiles from './public/test-auth/reporting-profiles/discontinuation-profiles/discontinuation-profiles.service';
import publicTestAuthReportingProfilesScalingFactorProfiles from './public/test-auth/reporting-profiles/scaling-factor-profiles/scaling-factor-profiles.service';
import publicTestAuthReportingProfilesAssessmentStructures from './public/test-auth/reporting-profiles/assessment-structures/assessment-structures.service'
import publicTestAuthReportingProfilesItemSetStructures from './public/test-auth/reporting-profiles/item-set-structures/item-set-structures.service'
import publicTestAuthItemSetSlug from './public/test-auth/item-set-slug/item-set-slug.service';
import publicTestCtrlTestWindowSchoolClassFormLock from './public/test-ctrl/test-window/school-class-form-lock/school-class-form-lock.service';
import publicScorScorBatchesConfirm from './public/scor-scor/batches/confirm/confirm.service';
import publicScorLeadScoringPairs from './public/scor-lead/scoring-pairs/scoring-pairs.service';
import publicTestAuthTwtarStats from './public/test-auth/twtar/stats/stats.service';
import publicDistAdminSaKitTestWindows from './public/dist-admin/sa-kit/test-windows/test-windows.service';
import publicDistAdminSaKitSchoolTwSummaries from './public/dist-admin/sa-kit/school-tw-summaries/school-tw-summaries.service';
import publicTestAuthGenResponseSheets from './public/test-auth/gen-response-sheets/gen-response-sheets.service';
import publicEducatorRespSheetUploadExtractFailedPages from './/public/educator/resp-sheet-upload/extract-failed-pages/extract-failed-pages.service';
import publicScorLeadMarkingGroups from './public/scor-lead/marking-groups/marking-groups.service';
import publicScorLeadMarkingGroupsUsers from './public/scor-lead/marking-groups-users/marking-groups-users.service';
import publicScorLeadValidityExpertScore from './public/scor-lead/validity/expert-score/expert-score.service';
import publicSchoolAdminSessionScanProgress from './public/school-admin/session-scan-progress/session-scan-progress.service';
// Don't remove this comment. It's needed to format import lines nicely.

export default function (app: Application) {
  dbConfigure(app);
  app.configure(abedSchoolsStudentValidate);
  app.configure(authFailedLogin);
  app.configure(authInvitation);
  app.configure(authOctCap);
  app.configure(authPasswordValidation);
  app.configure(authUserRoleActions);
  app.configure(connectedUsers);
  app.configure(constants);
  app.configure(creditsCredits);
  app.configure(creditsCreditTransactions);
  app.configure(currentBatch);
  app.configure(dataExportSlots);
  app.configure(dbAuths);
  app.configure(dbWriteAuths);
  app.configure(educatorResponsibilityAgreement);
  app.configure(eqaoSdcApi);
  app.configure(exemplarSelectionResponse);
  app.configure(exemplarTrainingReliabilitySet);
  app.configure(irt);
  app.configure(itemImpressions);
  app.configure(leaders);
  app.configure(mailCore);
  app.configure(mailInviteTestAdmin);
  app.configure(mailInviteTestCert);
  app.configure(mailInviteTestCtrl);
  app.configure(mailNotificationAccommReq);
  app.configure(mailNotificationSessionAssign);
  app.configure(mailNotificationSessionCancellation);
  app.configure(mailNotificationSessionNew);
  app.configure(mailNotificationSessionResults);
  app.configure(mailNotificationSessionStatus);
  app.configure(mailNotificationSessionTransferReq);
  app.configure(mailNotificationSessionUnassign);
  app.configure(mailNotificationWaitlistUpgrade);
  app.configure(mailRegistrationConfirmation);
  app.configure(mailResetPassword);
  app.configure(markedCountByMarker);
  app.configure(markedResponsesOfItems);
  app.configure(nbedSchoolsStudentValidate);
  app.configure(pairs);
  app.configure(policy);
  app.configure(privateSchoolsStudentValidate);
  app.configure(PsychConfigHistory);
  app.configure(PsychPipelineConfigs);
  app.configure(publicAbedDataImport);
  app.configure(publicAbedPasi);
  app.configure(publicAbedPed);
  app.configure(publicAnonSampleTestDesignForm);
  app.configure(publicAssessmentsInfo);
  app.configure(publicAuthRegisterMfa);
  app.configure(publicBcAdminCoordinatorAccounts);
  app.configure(publicBcAdminCoordinatorActivityLogs);
  app.configure(publicBcAdminCoordinatorAssessmentComponent);
  app.configure(publicBcAdminCoordinatorBcgradStudentsAssessment);
  app.configure(publicBcAdminCoordinatorBcgradStudentsRegister);
  app.configure(publicBcAdminCoordinatorBcgradStudentsRequests);
  app.configure(publicBcAdminCoordinatorBcgradStudentsStudents);
  app.configure(publicBcAdminCoordinatorBcgradStudentsTestSessions);
  app.configure(publicBcAdminCoordinatorConfirmationCodes);
  app.configure(publicBcAdminCoordinatorDistricts);
  app.configure(publicBcAdminCoordinatorEdfmc2);
  app.configure(publicBcAdminCoordinatorExemption);
  app.configure(publicBcAdminCoordinatorIrtReady);
  app.configure(publicBcAdminCoordinatorIrtScore);
  app.configure(publicBcAdminCoordinatorPrintOrders);
  app.configure(publicBcAdminCoordinatorPrintPackages);
  app.configure(publicBcAdminCoordinatorRegistration);
  app.configure(publicBcAdminCoordinatorReports);
  app.configure(publicBcAdminCoordinatorSchoolParticipation);
  app.configure(publicBcAdminCoordinatorSchools);
  app.configure(publicBcAdminCoordinatorScoreEntryLogs);
  app.configure(publicBcAdminCoordinatorSessionPasswords);
  app.configure(publicBcAdminCoordinatorSimulation);
  app.configure(publicBcAdminCoordinatorSpecialMaterialRequests);
  app.configure(publicBcAdminCoordinatorSrResults);
  app.configure(publicBcAdminCoordinatorStudents);
  app.configure(publicBcAdminCoordinatorSubmissionProgress);
  app.configure(publicBcAdminCoordinatorTestAttemptMetas);
  app.configure(publicBcAdminCoordinatorTestWindow);
  app.configure(publicBcAdminCoordinatorUnsubmissions);
  app.configure(publicBcAdminCoordinatorValidationCode);
  app.configure(publicBcgScanConstructedResponse);
  app.configure(publicBcgScanGenpdf);
  app.configure(publicBoardCurrLeadSchools);
  app.configure(publicClSysExamExamineeKeys);
  app.configure(publicCreditSystem);
  app.configure(publicCronComputeReliability);
  app.configure(publicCronDailyScoringPooling);
  app.configure(publicCronEQAOScheduledSessions);
  app.configure(publicCronExpiredBatchesRollingValidity);
  app.configure(publicCronInvigilationAttemptChecklist);
  app.configure(publicCronMarkingItemScoringSummary);
  app.configure(publicCronProcessData);
  app.configure(publicCronProcessDataExports);
  app.configure(publicCronProcessDataExportQueue);
  app.configure(publicCronProcessPsychRunQueue);
  app.configure(publicCronProcessResponseBuffer);
  app.configure(publicCronPtSummaryByItem);
  app.configure(publicCronQtSummaryByItem);
  app.configure(publicCronReliabilityResponsesAlloc);
  app.configure(publicCronReporting);
  app.configure(publicCronResponsesNonResponseCheck);
  app.configure(publicCronScorerStatusUpdate);
  app.configure(publicCronScoringBatchExpiry);
  app.configure(publicCronTestAuthAssetCopyright);
  app.configure(publicCronTestCtrlGenerateSummaries);
  app.configure(publicCronValiditySummaryByItem);
  app.configure(publicCronWeeklyResetScorerTime);
  app.configure(publicDataExporterDataExport);
  app.configure(publicDataExportDataExportSlots);
  app.configure(publicDataExporterPsychRun);
  app.configure(publicDistAdminContact);
  app.configure(publicDistAdminDistrict);
  app.configure(publicDistAdminDistrictRoster);
  app.configure(publicDistAdminInvite);
  app.configure(publicDistAdminOssltReport);
  app.configure(publicDistAdminPenLookup);
  app.configure(publicDistAdminRoles);
  app.configure(publicDistAdminSchool);
  app.configure(publicDistAdminSchoolAdmin);
  app.configure(publicDistAdminSchoolClass);
  app.configure(publicDistAdminSchoolRoster);
  app.configure(publicDistAdminScoreEntry);
  app.configure(publicDistAdminSessions);
  app.configure(publicDistAdminStudent);
  app.configure(publicDistAdminStudentsReporting);
  app.configure(publicDistAdminSummary);
  app.configure(publicDistAdminTechReadiChecklist);
  app.configure(publicDistAdminTechReadiLockDownInfo);
  app.configure(publicEducatorChecklist);
  app.configure(publicEducatorClassAssessments);
  app.configure(publicEducatorClassScanInfo);
  app.configure(publicEducatorConfidentialityAgreement);
  app.configure(publicEducatorEducatorQuestionnaireUnsubmit);
  app.configure(publicEducatorGuestStudents);
  app.configure(publicEducatorHealthCheck);
  app.configure(publicEducatorInvigilators);
  app.configure(publicEducatorPasiStatuses);
  app.configure(publicEducatorReportIssuesCategories);
  app.configure(publicEducatorResponseSheetConfirmation);
  app.configure(publicEducatorRespSheetUploadQrcodeContents);
  app.configure(publicEducatorRespSheetUploadUploadResponseSheets);
  app.configure(publicEducatorSchool);
  app.configure(publicEducatorSchoolSemesterTwDates);
  app.configure(publicEducatorSession);
  app.configure(publicEducatorSessionQuestionnaire);
  app.configure(publicEducatorSessionReport);
  app.configure(publicEducatorSessionReportedIssue);
  app.configure(publicEducatorSessionSub);
  app.configure(publicEducatorSoftLock);
  app.configure(publicEducatorStudentReport);
  app.configure(publicEducatorStudents);
  app.configure(publicEducatorStudentSession);
  app.configure(publicEducatorStudentSessionAbsence);
  app.configure(publicEducatorStudentSessionExtraTime);
  app.configure(publicEducatorStudentTestAttempt);
  app.configure(publicEducatorSurveySession);
  app.configure(publicEducatorSurveySessionQuestion);
  app.configure(publicEducatorTestAttemptUnsubmissionReasons);
  app.configure(publicEducatorUpdateTestForm);
  app.configure(publicEducatorUserMetas);
  app.configure(publicEducatorWalkInStudents);
  app.configure(publicEducatorWebsocketConnectedUsers);
  app.configure(publicEducatorWebsocketStudentSoftlock);
  app.configure(publicEducatorWebsocketStudentSubsessionNotification);
  app.configure(publicFieldTesterSession);
  app.configure(publicFieldTesterSessionQuestion);
  app.configure(publicGenResponseSheets);
  app.configure(publicHealth);
  app.configure(publicLandingApplicantRegistration);
  app.configure(publicLandingConfirmUserRole);
  app.configure(publicLandingKnowledgeBase);
  app.configure(publicLog);
  app.configure(publicMrkgCandCertTest);
  app.configure(publicMrkgCandCertTests);
  app.configure(publicMrkgCandProfile);
  app.configure(publicMrkgCoordApplicantInfo);
  app.configure(publicMrkgCoordCredentialingSetup);
  app.configure(publicMrkgCoordMarkingSessionSettings);
  app.configure(publicMrkgCtrlMarkingPoolItems);
  app.configure(publicMrkgCtrlMarkingPoolMarkingItems);
  app.configure(publicMrkgCtrlMarkingWindow);
  app.configure(publicMrkgCtrlMarkingWindows);
  app.configure(publicMrkgCtrlMarkingWindowsAccountsMrkrs);
  app.configure(publicMrkgCtrlMarkingWindowsAccountsSuprs);
  app.configure(publicMrkgCtrlMarkingWindowsAllocate);
  app.configure(publicMrkgCtrlMarkingWindowsCertTests);
  app.configure(publicMrkgCtrlMarkingWindowsDate);
  app.configure(publicMrkgCtrlMarkingWindowsItemOptions);
  app.configure(publicMrkgCtrlMarkingWindowsName);
  app.configure(publicMrkgCtrlMarkingWindowsStatus);
  app.configure(publicMrkgCtrlMrkrs);
  app.configure(publicMrkgCtrlProfile);
  app.configure(publicMrkgCtrlSuprs);
  app.configure(publicMrkgLeadAssignedMarkingSession);
  app.configure(publicMrkgLeadCalculateMarkerStats);
  app.configure(publicMrkgLeadCalculateReliabilityRelease);
  app.configure(publicMrkgLeadChatContacts);
  app.configure(publicMrkgLeadConstructedResponseScores);
  app.configure(publicMrkgLeadDocument);
  app.configure(publicMrkgLeadEcasImportExport);
  app.configure(publicMrkgLeadExemplarSelectionOverview);
  app.configure(publicMrkgLeadExemplarSelectionTable);
  app.configure(publicMrkgLeadExemplarSetPdf);
  app.configure(publicMrkgLeadExportFieldTestData);
  app.configure(publicMrkgLeadFsaDiscrepancyReport);
  app.configure(publicMrkgLeadFsaScanRequests);
  app.configure(publicMrkgLeadHasExtendedAccess);
  app.configure(publicMrkgLeadHistoricalPacing);
  app.configure(publicMrkgLeadInspectionOverview);
  app.configure(publicMrkgLeadItem);
  app.configure(publicMrkgLeadItemTemplate);
  app.configure(publicMrkgLeadItemThresholds);
  app.configure(publicMrkgLeadLeaderSendInvitationEmail);
  app.configure(publicMrkgLeadManageLeaders);
  app.configure(publicMrkgLeadManageMarkers);
  app.configure(publicMrkgLeadMarkerEventPacing);
  app.configure(publicMrkgLeadMarkersAssignedItem);
  app.configure(publicMrkgLeadMarkerSendAccessKeys);
  app.configure(publicMrkgLeadMarkingChat);
  app.configure(publicMrkgLeadMarkingExemplarSets);
  app.configure(publicMrkgLeadMarkingLeaderAssignment);
  app.configure(publicMrkgLeadMarkingPairs);
  app.configure(publicMrkgLeadMarkingPairsUsers);
  app.configure(publicMrkgLeadMarkingReliabilitySetResponses);
  app.configure(publicMrkgLeadMarkingSession);
  app.configure(publicMrkgLeadMarkingSessionAssignmentJson);
  app.configure(publicMrkgLeadMarkingSessionParticipants);
  app.configure(publicMrkgLeadMarkingSessionPens);
  app.configure(publicMrkgLeadMarkingTriggerRemark);
  app.configure(publicMrkgLeadNewDemoData);
  app.configure(publicMrkgLeadOverwritePairs);
  app.configure(publicMrkgLeadPastExemplarSets);
  app.configure(publicMrkgLeadReliabilityResponseDetails);
  app.configure(publicMrkgLeadResponse);
  app.configure(publicMrkgLeadResponsesByPen);
  app.configure(publicMrkgLeadSearchPairByName);
  app.configure(publicMrkgLeadStage);
  app.configure(publicMrkgLeadStudentScores);
  app.configure(publicMrkgLeadTrainingSetControls);
  app.configure(publicMrkgLeadUser);
  app.configure(publicMrkgMrkrAttemptResponse);
  app.configure(publicMrkgMrkrCertTest);
  app.configure(publicMrkgMrkrCertTests);
  app.configure(publicMrkgMrkrCurrentBatchPosition);
  app.configure(publicMrkgMrkrGenerateResponseUrls);
  app.configure(publicMrkgMrkrItemAccountType);
  app.configure(publicMrkgMrkrItemBatchScoring);
  app.configure(publicMrkgMrkrItemComments);
  app.configure(publicMrkgMrkrItemPriority);
  app.configure(publicMrkgMrkrItems);
  app.configure(publicMrkgMrkrItemScoring);
  app.configure(publicMrkgMrkrMarkerStats);
  app.configure(publicMrkgMrkrMarkingPools);
  app.configure(publicMrkgMrkrMarkingTasks);
  app.configure(publicMrkgMrkrPause);
  app.configure(publicMrkgMrkrProfile);
  app.configure(publicMrkgMrkrQuestion);
  app.configure(publicMrkgMrkrQuestionResponse);
  app.configure(publicMrkgMrkrResponse);
  app.configure(publicMrkgMrkrResponseScores);
  app.configure(publicMrkgMrkrSupervisorName);
  app.configure(publicMrkgMrkrTime);
  app.configure(publicMrkgMrkrTrainingResponse);
  app.configure(publicMrkgMrkrUniqueItems);
  app.configure(publicMrkgMrkrUserNames);
  app.configure(publicMrkgMrkrWindow);
  app.configure(publicMrkgSuprMarkers);
  app.configure(publicMrkgSuprMarkingPoolAssignMode);
  app.configure(publicMrkgSuprMarkingPoolItemPaused);
  app.configure(publicMrkgSuprMarkingPoolItemPriority);
  app.configure(publicMrkgSuprMarkingPoolItemsPriority);
  app.configure(publicMrkgSuprMarkingPoolMarkerPause);
  app.configure(publicMrkgSuprMarkingPoolMarkerPerformance);
  app.configure(publicMrkgSuprMarkingPoolMarkerReCertify);
  app.configure(publicMrkgSuprMarkingPoolMarkers);
  app.configure(publicMrkgSuprMarkingPoolMarkingItemComment);
  app.configure(publicMrkgSuprMarkingPoolMarkingItemRevoke);
  app.configure(publicMrkgSuprMarkingPoolMarkingItems);
  app.configure(publicMrkgSuprMarkingPoolMarkingResponseComment);
  app.configure(publicMrkgSuprMarkingPoolMarkingResponses);
  app.configure(publicMrkgSuprMarkingPoolMarkingTaqrComments);
  app.configure(publicMrkgSuprMarkingPoolRole);
  app.configure(publicMrkgSuprMarkingPools);
  app.configure(publicMrkgSuprMarkingPoolSchedule);
  app.configure(publicMrkgSuprMarkingPoolTime);
  app.configure(publicMrkgSuprProfile);
  app.configure(publicMrkgSuprWindow);
  app.configure(publicMrkgUpldProfile);
  app.configure(publicMrkgUpldResponse);
  app.configure(publicPaymentCtrlAlternativePayments);
  app.configure(publicPaymentCtrlRefundSetup);
  app.configure(publicPaymentCtrlSchoolAdminAgreements);
  app.configure(publicPaymentCtrlStripePayments);
  app.configure(publicPaymentCtrlStudentAttempts);
  app.configure(publicPaymentPolicy);
  app.configure(publicPaymentSystem);
  app.configure(publicScanRevwBatch);
  app.configure(publicScanRevwScan);
  app.configure(publicScanRevwStats);
  app.configure(publicScanRevwWindows);
  app.configure(publicSchoolAdminBcgradStudents);
  app.configure(publicSchoolAdminBcgradStudentsEnrollment);
  app.configure(publicSchoolAdminBcgradStudentsLookup);
  app.configure(publicSchoolAdminBcgradStudentsLookup2);
  app.configure(publicSchoolAdminBcgradStudentsRegister);
  app.configure(publicSchoolAdminBcgradStudentsRegistration);
  app.configure(publicSchoolAdminBcgradStudentsRequests);
  app.configure(publicSchoolAdminClasses);
  app.configure(publicSchoolAdminEnrolmentStudents);
  app.configure(publicSchoolAdminInvigilateClass);
  app.configure(publicSchoolAdminInvigilators);
  app.configure(publicSchoolAdminInvite);
  app.configure(publicSchoolAdminPayment);
  app.configure(publicSchoolAdminPaymentAgreements);
  app.configure(publicSchoolAdminPjReports);
  app.configure(publicSchoolAdminReports);
  app.configure(publicSchoolAdminRoles);
  app.configure(publicSchoolAdminSchool);
  app.configure(publicSchoolAdminSchoolAccess);
  app.configure(publicSchoolAdminScoreProfile);
  app.configure(publicSchoolAdminSession);
  app.configure(publicSchoolAdminSessionComplete);
  app.configure(publicSchoolAdminSessionPurchase);
  app.configure(publicSchoolAdminStudent);
  app.configure(publicSchoolAdminStudentAsmtInfoSignoff);
  app.configure(publicSchoolAdminStudentAssign);
  app.configure(publicSchoolAdminStudentDelete);
  app.configure(publicSchoolAdminStudentOenSchool);
  app.configure(publicSchoolAdminStudentsClassroom);
  app.configure(publicSchoolAdminStudentsStudent);
  app.configure(publicSchoolAdminStudentTwAbsence);
  app.configure(publicSchoolAdminStudentTwUtil);
  app.configure(publicSchoolAdminTeacher);
  app.configure(publicSchoolAdminTeachers);
  app.configure(publicSchoolAdminTeachersInvite);
  app.configure(publicSchoolAdminTechReadiChecklist);
  app.configure(publicSchoolAdminTechReadiIp);
  app.configure(publicSchoolAdminTechReadiItContact);
  app.configure(publicSchoolAdminTechReadiLockDownInfo);
  app.configure(publicSchoolAdminTestWindow);
  app.configure(publicSchoolAdminTwStatement);
  app.configure(publicScoringScorerSummary);
  app.configure(publicScorLeadAccounts);
  app.configure(publicScorLeadAssessmentForms);
  app.configure(publicScorLeadBatchAllocPolicies);
  app.configure(publicScorLeadBatches);
  app.configure(publicScorLeadCumulRecalc);
  app.configure(publicScorLeadDuplicateRead);
  app.configure(publicScorLeadForceMarkerClaim);
  app.configure(publicScorLeadGetScorerAccess);
  app.configure(publicScorLeadInviteWave);
  app.configure(publicScorLeadItemScorer);
  app.configure(publicScorLeadItemStats);
  app.configure(publicScorLeadLocalScoringPooling);
  app.configure(publicScorLeadLocalScoringStats);
  app.configure(publicScorLeadMaterials);
  app.configure(publicScorLeadMessageCentre);
  app.configure(publicScorLeadProcessScoringSecondReads);
  app.configure(publicScorLeadProcessScoringThirdReads);
  app.configure(publicScorLeadProcessScoringFourthReads);
  app.configure(publicScorLeadReprocessScoringReads);
  app.configure(publicScorLeadReliabilitySummary);
  app.configure(publicScorLeadRescore);
  app.configure(publicScorLeadResponseScores);
  app.configure(publicScorLeadResponseSets);
  app.configure(publicScorLeadResponseSheetCropAdjustment);
  app.configure(publicScorLeadResponseRepooling);
  app.configure(publicScorLeadResponsesRandomRealloc);
  app.configure(publicScorLeadResponsesResponses);
  app.configure(publicScorLeadResponsesSensitive);
  app.configure(publicScorLeadScoreFlags);
  app.configure(publicScorLeadScoreOptions);
  app.configure(publicScorLeadScoreProfileGroups);
  app.configure(publicScorLeadScoreProfiles);
  app.configure(publicScorLeadScorerItemRevoke);
  app.configure(publicScorLeadScorerRespRem);
  app.configure(publicScorLeadScorers);
  app.configure(publicScorLeadScorerStats);
  app.configure(publicScorLeadScorerStatusLog);
  app.configure(publicScorLeadScoringPooling);
  app.configure(publicScorLeadSysFlags);
  app.configure(publicScorLeadValidityComments);
  app.configure(publicScorLeadValidityHistoricalResponse);
  app.configure(publicScorLeadValidityItem);
  app.configure(publicScorLeadValidityResponses);
  app.configure(publicScorLeadValiditySet);
  app.configure(publicScorLeadWindows);
  app.configure(publicScorScorAttest);
  app.configure(publicScorScorBatchesAvailable);
  app.configure(publicScorScorBatchesAvailableClaims);
  app.configure(publicScorScorBatchesBatchGroups);
  app.configure(publicScorScorBatchesScoringReads);
  app.configure(publicScorScorBatchesClaim);
  app.configure(publicScorScorBatchesItemDisplay);
  app.configure(publicScorScorBatchesMark);
  app.configure(publicScorScorBatchesSession);
  app.configure(publicScorScorBatchesValidity);
  app.configure(publicScorScorEvent);
  app.configure(publicScorScorExemplarCredentialingMark);
  app.configure(publicScorScorExemplarCredentialingSession);
  app.configure(publicScorScorExemplarPracticeMark);
  app.configure(publicScorScorExemplarPracticeSession);
  app.configure(publicScorScorMarkingClaimedBatchResponses);
  app.configure(publicScorScorMaterialsReading);
  app.configure(publicScorScorMaterialsSummary);
  app.configure(publicScorScorMessageCentre);
  app.configure(publicScorScorMrkgDura);
  app.configure(publicScorScorSummary);
  app.configure(publicStressStudent);
  app.configure(publicStudentActiveSubsessionStatus);
  app.configure(publicStudentAsmtFilter);
  app.configure(publicStudentAttempt);
  app.configure(publicStudentChecklist);
  app.configure(publicStudentClassId);
  app.configure(publicStudentCoverPage);
  app.configure(publicStudentCurrentSubsessionInfo);
  app.configure(publicStudentDebug);
  app.configure(publicStudentExtractItemResponse);
  app.configure(publicStudentMap);
  app.configure(publicStudentNameInitial);
  app.configure(publicStudentReportResults);
  app.configure(publicStudentSelfReg);
  app.configure(publicStudentSession);
  app.configure(publicStudentSessionQuestion);
  app.configure(publicStudentSessionQuestionAudit);
  app.configure(publicStudentSessionSlug);
  app.configure(publicStudentSoftLock);
  app.configure(publicStudentStageSubmission);
  app.configure(publicStudentTestWindow);
  app.configure(publicStudentVerifyStudent);
  app.configure(publicStudentWalkIn);
  app.configure(publicSupportAdminWindowTfPanelMapping);
  app.configure(publicSupportAssignStudentsTf);
  app.configure(publicSupportCreateStudents);
  app.configure(publicSupportDataMethods);
  app.configure(publicSupportDataTargets);
  app.configure(publicSupportDistAdminAccountInvite);
  app.configure(publicSupportG9RawReports);
  app.configure(publicSupportLogin);
  app.configure(publicSupportMailCampaign);
  app.configure(publicSupportMailCampaignBatch);
  app.configure(publicSupportMailCampaignTest);
  app.configure(publicSupportOctSuccessfulAttempt);
  app.configure(publicSupportPasswordAttemptReset);
  app.configure(publicSupportPrivateG9Access);
  app.configure(publicSupportReporting);
  app.configure(publicSupportReqSchoolClassSessionsByForeignid);
  app.configure(publicSupportResetNames);
  app.configure(publicSupportResetProgress);
  app.configure(publicSupportResetSection);
  app.configure(publicSupportResolutionTypes);
  app.configure(publicSupportRoleActions);
  app.configure(publicSupportRoleTypes);
  app.configure(publicSupportSchool);
  app.configure(publicSupportSchoolAdmin);
  app.configure(publicSupportSchoolAdminAccountInvite);
  app.configure(publicSupportSchoolClassStudent);
  app.configure(publicSupportSchoolClassTestSession);
  app.configure(publicSupportSchoolDistricts);
  app.configure(publicSupportSchoolProfile);
  app.configure(publicSupportSchoolForeignId);
  app.configure(publicSupportSchools);
  app.configure(publicSupportSchoolsAdmin);
  app.configure(publicSupportSchoolsAdminInvite);
  app.configure(publicSupportSchoolsAdminMulti);
  app.configure(publicSupportSchoolYellowFlags);
  app.configure(publicSupportScoringCredentialingSet);
  app.configure(publicSupportScoringPracticeSet);
  app.configure(publicSupportScoringResponses);
  app.configure(publicSupportScoringScorer);
  app.configure(publicSupportScoringScorerTasks);
  app.configure(publicSupportSessionReopen);
  app.configure(publicSupportStatsPeak);
  app.configure(publicSupportStudentBulkRename);
  app.configure(publicSupportStudentLookups);
  app.configure(publicSupportStudentNumbers);
  app.configure(publicSupportStudentTaqr);
  app.configure(publicSupportStudentTwClasses);
  app.configure(publicSupportStudentUnenroll);
  app.configure(publicSupportSysEmailRecent);
  app.configure(publicSupportSysFlags);
  app.configure(publicSupportTableSize);
  app.configure(publicSupportTaqrOverride);
  app.configure(publicSupportTestAttemptReset);
  app.configure(publicSupportTestFormModuleItems);
  app.configure(publicSupportTestFormPrint);
  app.configure(publicSupportTestLog);
  app.configure(publicSupportTestSessionDate);
  app.configure(publicSupportTestWindow);
  app.configure(publicSupportTtAttempts);
  app.configure(publicSupportTtBooking);
  app.configure(publicSupportTtBookingStatus);
  app.configure(publicSupportTtLangReq);
  app.configure(publicSupportTtNotifications);
  app.configure(publicSupportTtReportNotifications);
  app.configure(publicSupportTtUpcomingSessions);
  app.configure(publicSupportUInvitationCode);
  app.configure(publicSupportUserRoleMerge);
  app.configure(publicSupportUserRoles);
  app.configure(publicSupportValidateOctMember);
  app.configure(publicTestAdminAccommResponses);
  app.configure(publicTestAdminAppealsAppeals);
  app.configure(publicTestAdminAppealsEnabledAppeals);
  app.configure(publicTestAdminInvigilationOnbehalfQuestionResponse);
  app.configure(publicTestAdminInvigilationOnbehalfTestAttempt);
  app.configure(publicTestAdminInvigilationReportApplicantIssue);
  app.configure(publicTestAdminInvigilationReportGeneralIssue);
  app.configure(publicTestAdminInvigilationUnlockSection);
  app.configure(publicTestAdminInvigilationUnsubmit);
  app.configure(publicTestAdminSebHeader);
  app.configure(publicTestAdminStudentsBooking);
  app.configure(publicTestAdminStudentsReporting);
  app.configure(publicTestAdminStudentsSchool);
  app.configure(publicTestAdminStudentsWalkIn);
  app.configure(publicTestAdminTestSessionSetupTimezone);
  app.configure(publicTestAdminTestSessionsGroupId);
  app.configure(publicTestAdminTestSessionsMySingle);
  app.configure(publicTestAdminTestSessionsPrior);
  app.configure(publicTestAdminTestSessionsSingle);
  app.configure(publicTestAdminTestSessionsSoftLock);
  app.configure(publicTestAdminTestSessionsStudents);
  app.configure(publicTestAdminTestSessionVideostreamLink);
  app.configure(publicTestAdminTimezone);
  app.configure(publicTestAdminValidateSeb);
  app.configure(publicTestAuthAccountsAccess);
  app.configure(publicTestAuthAccountsResendInvite);
  app.configure(publicTestAuthAssetGroups);
  app.configure(publicTestAuthAssetLibraries);
  app.configure(publicTestAuthAssetLibraryFields);
  app.configure(publicTestAuthAssetLink);
  app.configure(publicTestAuthAssets);
  app.configure(publicTestAuthBatchAllocationPolicies);
  app.configure(publicTestAuthAssetVersions);
  app.configure(publicTestAuthContentSearch);
  app.configure(publicTestAuthFrameworks);
  app.configure(publicTestAuthFrameworksAudit);
  app.configure(publicTestAuthGroup);
  app.configure(publicTestAuthGroupMembers);
  app.configure(publicTestAuthGroupRoles);
  app.configure(publicTestAuthGroups);
  app.configure(publicTestAuthIssues);
  app.configure(publicTestAuthItemBlockTemplatesRetrieval);
  app.configure(publicTestAuthItemImpressions);
  app.configure(publicTestAuthItemSet);
  app.configure(publicTestAuthItemSetAudits);
  app.configure(publicTestAuthItemSetExpAns);
  app.configure(publicTestAuthItemTagLink);
  app.configure(publicTestAuthItemTags);
  app.configure(publicTestAuthItemWindowUses);
  app.configure(publicTestAuthManageAuthor);
  app.configure(publicTestAuthNoteFiles);
  app.configure(publicTestAuthNotes);
  app.configure(publicTestAuthNotesAudit);
  app.configure(publicTestAuthNotifications);
  app.configure(publicTestAuthPanelShells);
  app.configure(publicTestAuthPublicPwdProtected);
  app.configure(publicTestAuthQuestionRevisions);
  app.configure(publicTestAuthQuestions);
  app.configure(publicTestAuthQuestionSetLists);
  app.configure(publicTestAuthQuestionSetParameters);
  app.configure(publicTestAuthQuestionSetParametersVersions);
  app.configure(publicTestAuthSearch);
  app.configure(publicTestAuthShowComments);
  app.configure(publicTestAuthSingleGroup);
  app.configure(publicTestAuthStyleProfiles);
  app.configure(publicTestAuthSuggestions);
  app.configure(publicTestAuthTestDesigns);
  app.configure(publicTestAuthTestDesignsForms);
  app.configure(publicTestAuthTestDesignsIsPublic);
  app.configure(publicTestAuthTestQuestionRegisterGenericParamMap);
  app.configure(publicTestAuthTestQuestionRegisterParamMap);
  app.configure(publicTestAuthTestDesignSignOff);
  app.configure(publicTestAuthTestDesignItemDiffSignOff);
  app.configure(publicTestAuthTestQuestionScoringCodes);
  app.configure(publicTestAuthTestQuestionScoringInfo);
  app.configure(publicTestAuthTestQuestionTemplateVersions);
  app.configure(publicTestAuthTestQuestionTemplates);
  app.configure(publicTestAuthTestQuestionTemplateAuthGroups);
  app.configure(publicTestAuthTextVoice);
  app.configure(publicTestAuthValidationNotes);
  app.configure(publicTestCertDataFile);
  app.configure(publicTestCertFailedTransfers);
  app.configure(publicTestCertRoles);
  app.configure(publicTestCertSebConfig);
  app.configure(publicTestCtrlAccommodations);
  app.configure(publicTestCtrlAccountsInstitutions);
  app.configure(publicTestCtrlAccountsSchoolAdmins);
  app.configure(publicTestCtrlAccountsTestAdminRevoke);
  app.configure(publicTestCtrlAppeals);
  app.configure(publicTestCtrlAssessmentSessionAnalysis);
  app.configure(publicTestCtrlAssessmentSessionAnalysisItem);
  app.configure(publicTestCtrlAssessmentSessionAnalysisResponse);
  app.configure(publicTestCtrlAssessmentSessionAuditMcq);
  app.configure(publicTestCtrlAssessmentSessionSummary);
  app.configure(publicTestCtrlDataExportExports);
  app.configure(publicTestCtrlDataExportPsychRuns);
  app.configure(publicTestCtrlDataFile);
  app.configure(publicTestCtrlIssueREvwSessionQuestionLogs);
  app.configure(publicTestCtrlIssueRevwUnsubReq);
  app.configure(publicTestCtrlLangReq);
  app.configure(publicTestCtrlOdiStuAsmt);
  app.configure(publicTestCtrlOdiStuAsmtCsv);
  app.configure(publicTestCtrlOdiStuAsmtCsvZip);
  app.configure(publicTestCtrlOdiStuAsmtLevel);
  app.configure(publicTestCtrlOdiStuItemCsv);
  app.configure(publicTestCtrlOdiStuItemCsvZip);
  app.configure(publicTestCtrlOdiStuItemLevel);
  app.configure(publicTestCtrlProcCertActiveSessions);
  app.configure(publicTestCtrlProcCertCurrentSessions);
  app.configure(publicTestCtrlReporting);
  app.configure(publicTestCtrlRoles);
  app.configure(publicTestCtrlScoringScoringWindow);
  app.configure(publicTestCtrlScoringRoles);
  app.configure(publicTestCtrlSchoolsBoards);
  app.configure(publicTestCtrlSchoolsClasses);
  app.configure(publicTestCtrlSchoolsDataDlAsmtFrmkBatchAllocationPolicies);
  app.configure(publicTestCtrlSchoolsDataDlAsmtFrmkGenModuleItems);
  app.configure(publicTestCtrlSchoolsDataDlAsmtFrmkGenModules);
  app.configure(publicTestCtrlSchoolsDataDlAsmtFrmkGenPanels);
  app.configure(publicTestCtrlSchoolsDataDlAsmtFrmkItemScoringProfileFlags);
  app.configure(publicTestCtrlSchoolsDataDlAsmtFrmkItemScoringProfileOptions);
  app.configure(publicTestCtrlSchoolsDataDlAsmtFrmkItemScoringProfiles);
  app.configure(publicTestCtrlSchoolsDataDlAsmtFrmkModules);
  app.configure(publicTestCtrlSchoolsDataDlAsmtFrmkReportingComponents);
  app.configure(publicTestCtrlSchoolsDataDlAsmtFrmkReportingComponentScoringTypes);
  app.configure(publicTestCtrlSchoolsDataDlAsmtFrmkReportingComponentTypes);
  app.configure(publicTestCtrlSchoolsDataDlAsmtFrmkResponseSetTypes);
  app.configure(publicTestCtrlSchoolsDataDlAsmtFrmkRouting);
  app.configure(publicTestCtrlSchoolsDataDlAsmtFrmkScoreOptions);
  app.configure(publicTestCtrlSchoolsDataDlAsmtFrmkSessions);
  app.configure(publicTestCtrlSchoolsDataDlAsmtFrmkStages);
  app.configure(publicTestCtrlSchoolsDataDlAsmtFrmkTaskComponentTypes);
  app.configure(publicTestCtrlSchoolsDataDlAsmtFrmkTestDesigns);
  app.configure(publicTestCtrlSchoolsDataDlAsmtFrmkTestDesignVersions);
  app.configure(publicTestCtrlSchoolsDataDlItemBankFlatItemBank);
  app.configure(publicTestCtrlSchoolsDataDlItemBankItems);
  app.configure(publicTestCtrlSchoolsDataDlItemBankItemSpecificRubricCodes);
  app.configure(publicTestCtrlSchoolsDataDlItemBankQuestionnaire);
  app.configure(publicTestCtrlSchoolsDataDlItemBankQuestionnaireRespCodes);
  app.configure(publicTestCtrlSchoolsDataDlItemBankScoringWindowItems);
  app.configure(publicTestCtrlSchoolsDataDlItemBankTestPanelItems);
  app.configure(publicTestCtrlSchoolsDataDlItemBankTestPanels);
  app.configure(publicTestCtrlSchoolsDataDlRegistrationsBoards);
  app.configure(publicTestCtrlSchoolsDataDlRegistrationsClass);
  app.configure(publicTestCtrlSchoolsDataDlRegistrationsRangeFinders);
  app.configure(publicTestCtrlSchoolsDataDlRegistrationsSchoolDistricts);
  app.configure(publicTestCtrlSchoolsDataDlRegistrationsSchools);
  app.configure(publicTestCtrlSchoolsDataDlRegistrationsScorerItemTasks);
  app.configure(publicTestCtrlSchoolsDataDlRegistrationsScorers);
  app.configure(publicTestCtrlSchoolsDataDlRegistrationsScoringLeaders);
  app.configure(publicTestCtrlSchoolsDataDlRegistrationsScoringSupervisors);
  app.configure(publicTestCtrlSchoolsDataDlRegistrationsScoringWindows);
  app.configure(publicTestCtrlSchoolsDataDlRegistrationsScoringWindowTestWindow);
  app.configure(publicTestCtrlSchoolsDataDlRegistrationsStudentIsrMetas);
  app.configure(publicTestCtrlSchoolsDataDlRegistrationsStudents);
  app.configure(publicTestCtrlSchoolsDataDlRegistrationsTeachers);
  app.configure(publicTestCtrlSchoolsDataDlRegistrationsTestSessions);
  app.configure(publicTestCtrlSchoolsDataDlRegistrationsTestWindowDesignAlloc);
  app.configure(publicTestCtrlSchoolsDataDlRegistrationsTestWindows);
  app.configure(publicTestCtrlSchoolsDataDlResponsesByBoard);
  app.configure(publicTestCtrlSchoolsDataDlResponsesByDate);
  app.configure(publicTestCtrlSchoolsDataDlResponsesBySchool);
  app.configure(publicTestCtrlSchoolsDataDlResponsesCrSubm);
  app.configure(publicTestCtrlSchoolsDataDlResponsesFlatTestAttResp);
  app.configure(publicTestCtrlSchoolsDataDlResponsesItemSumm);
  app.configure(publicTestCtrlSchoolsDataDlResponsesLangSubmSumm);
  app.configure(publicTestCtrlSchoolsDataDlResponsesNumStuByPnum);
  app.configure(publicTestCtrlSchoolsDataDlResponsesProcessDataDrawingTools);
  app.configure(publicTestCtrlSchoolsDataDlResponsesProcessDataItems);
  app.configure(publicTestCtrlSchoolsDataDlResponsesProcessDataRecordings);
  app.configure(publicTestCtrlSchoolsDataDlResponsesProcessDataScreens);
  app.configure(publicTestCtrlSchoolsDataDlResponsesProcessDataTeacherResponses);
  app.configure(publicTestCtrlSchoolsDataDlResponsesProcessDataTextToSpeech);
  app.configure(publicTestCtrlSchoolsDataDlResponsesReportedIssues);
  app.configure(publicTestCtrlSchoolsDataDlResponsesRespSumm);
  app.configure(publicTestCtrlSchoolsDataDlResponsesSchByDate);
  app.configure(publicTestCtrlSchoolsDataDlResponsesSchIsrReportAccess);
  app.configure(publicTestCtrlSchoolsDataDlResponsesStuAsmtVal);
  app.configure(publicTestCtrlSchoolsDataDlResponsesStuAtt);
  app.configure(publicTestCtrlSchoolsDataDlResponsesSubm);
  app.configure(publicTestCtrlSchoolsDataDlResponsesSubmItemResp);
  app.configure(publicTestCtrlSchoolsDataDlResponsesSubmSummary);
  app.configure(publicTestCtrlSchoolsDataDlResponsesSubmTestAtt);
  app.configure(publicTestCtrlSchoolsDataDlScoringItemPsychometrics);
  app.configure(publicTestCtrlSchoolsDataDlScoringQualifyingTestResponseSummary);
  app.configure(publicTestCtrlSchoolsDataDlScoringRangeFindingSelections);
  app.configure(publicTestCtrlSchoolsDataDlScoringResponseSetResponses);
  app.configure(publicTestCtrlSchoolsDataDlScoringResponseSets);
  app.configure(publicTestCtrlSchoolsDataDlScoringResponsesScore);
  app.configure(publicTestCtrlSchoolsDataDlScoringScorerClaimedBatches);
  app.configure(publicTestCtrlSchoolsDataDlScoringScorerClaimedResponses);
  app.configure(publicTestCtrlSchoolsDataDlScoringScorerItemSummary);
  app.configure(publicTestCtrlSchoolsDataDlScoringScorerPaceEvents);
  app.configure(publicTestCtrlSchoolsDataDlScoringScorings);
  app.configure(publicTestCtrlSchoolsDataDlScoringValidityResponseSummary);
  app.configure(publicTestCtrlSchoolsReportedIssueComments);
  app.configure(publicTestCtrlSchoolsReportedIssues);
  app.configure(publicTestCtrlSchoolsReportedIssuesAssignees);
  app.configure(publicTestCtrlSchoolsReportedIssuesCategories);
  app.configure(publicTestCtrlSchoolsReportedIssueStudents);
  app.configure(publicTestCtrlSchoolsSchool);
  app.configure(publicTestCtrlSchoolsSchools);
  app.configure(publicTestCtrlSchoolsStudent);
  app.configure(publicTestCtrlSchoolsStudentAttemptResponses);
  app.configure(publicTestCtrlSchoolsStudentAttempts);
  app.configure(publicTestCtrlSchoolsStudentExceptions);
  app.configure(publicTestCtrlSchoolsStudentItemExceptions);
  app.configure(publicTestCtrlSchoolsStudents);
  app.configure(publicTestCtrlSchoolsStudentsTeachersClasses);
  app.configure(publicTestCtrlSchoolsSummary);
  app.configure(publicTestCtrlSchoolsTeachers);
  app.configure(publicTestCtrlSchooolsDataDlCodebookChangeLogTCV);
  app.configure(publicTestCtrlSchooolsDataDlCodebookTCV);
  app.configure(publicTestCtrlSchooolsDataDlTransferTableDl);
  app.configure(publicTestCtrlSchooolsDataDlTransferTableTCV);
  app.configure(publicTestCtrlSebConfig);
  app.configure(publicTestCtrlSebDownloadLink);
  app.configure(publicTestCtrlSessionsDate);
  app.configure(publicTestCtrlSyncScoringAuthoring);
  app.configure(publicTestCtrlTechnicalReadinessFormDownload);
  app.configure(publicTestCtrlTechnicalReadinessTracking);
  app.configure(publicTestCtrlTechnicalReadinessVerifyForm);
  app.configure(publicTestCtrlTestDesigns);
  app.configure(publicTestCtrlTestAttemptsInvalidateAttempts);
  app.configure(publicTestCtrlTestWindowAdministrationWindows);
  app.configure(publicTestCtrlTestWindowAssessmentPriceConfig);
  app.configure(publicTestCtrlTestWindowConfig);
  app.configure(publicTestCtrlTestWindowSummary);
  app.configure(publicTestCtrlTwDataFile);
  app.configure(publicTestCtrlValidationDates);
  app.configure(publicTestCtrlValidationIssue);
  app.configure(publicTestCtrlValidationIssueResolution);
  app.configure(publicTestCtrlValidationReady);
  app.configure(publicTestCtrlValidationTestAttempts);
  app.configure(publicTestCtrlValidationTestForm);
  app.configure(publicTestCtrlValidationTestSessions);
  app.configure(publicTestQuestionRegisterTqrGenericMap);
  app.configure(publicTestQuestionRegisterTqrMap);
  app.configure(publicTestQuestionRegisterTqrPublish);
  app.configure(publicTestTakerAllowAppeals);
  app.configure(publicTestTakerAppealInit);
  app.configure(publicTestTakerAppeals);
  app.configure(publicTestTakerDataDownload);
  app.configure(publicTestTakerInvigilationAttemptChecklist);
  app.configure(publicTestTakerInvigilationAttemptChecklistSeb);
  app.configure(publicTestTakerInvigilationHelp);
  app.configure(publicTestTakerInvigilationReportIssue);
  app.configure(publicTestTakerInvigilationTestAttemptAttest);
  app.configure(publicTestTakerInvigilationTestAttemptLang);
  app.configure(publicTestTakerInvigilationTestAttemptTime);
  app.configure(publicTestTakerReportResults);
  app.configure(publicTestTakerSebDownloadLink);
  app.configure(publicTestTakerSessionId);
  app.configure(publicTestTakerTestSessionsInvitation);
  app.configure(publicTransactionsStripeStripe);
  app.configure(publicTransactionsTransactions);
  app.configure(publicZoomAccessToken);
  app.configure(publicZoomMeetings);
  app.configure(responseSets);
  app.configure(responsibiltyAgreement);
  app.configure(restAnalyticsEvent);
  app.configure(restAnalyticsSession);
  app.configure(restAuthConfirmEmail);
  app.configure(restAuthRefreshToken);
  app.configure(restAuthResetPassword);
  app.configure(restAuthResetPasswordRequest);
  app.configure(restAuthTestAdmin);
  app.configure(restAuthTestCert);
  app.configure(restAuthTestCtrl);
  app.configure(restAuthTestTaker);
  app.configure(restAuthTranslators);
  app.configure(restAuthUserInfoCore);
  app.configure(restPing);
  app.configure(restTestAdminAccommPendingRequests);
  app.configure(restTestAdminAccommTransferReq);
  app.configure(restTestAdminAccountsAccess);
  app.configure(restTestAdminAccountsAssignedCoord);
  app.configure(restTestAdminAccountsAssignedSessions);
  app.configure(restTestAdminInstitution);
  app.configure(restTestAdminInvigilationAbsenceReport);
  app.configure(restTestAdminInvigilationClose);
  app.configure(restTestAdminInvigilationGroupIssue);
  app.configure(restTestAdminInvigilationGroupTimeExtend);
  app.configure(restTestAdminInvigilationIdentityNo);
  app.configure(restTestAdminInvigilationIdentityVerif);
  app.configure(restTestAdminInvigilationIndivIssue);
  app.configure(restTestAdminInvigilationIndivTimeExtend);
  app.configure(restTestAdminInvigilationPause);
  app.configure(restTestAdminInvigilationSend);
  app.configure(restTestAdminInvigilationTempAccess);
  app.configure(restTestAdminRoles);
  app.configure(restTestAdminTestSessionAssignedInvigilator);
  app.configure(restTestAdminTestSessionBookings);
  app.configure(restTestAdminTestSessionBookingsIndic);
  app.configure(restTestAdminTestSessionBookingTransferReq);
  app.configure(restTestAdminTestSessionCancellation);
  app.configure(restTestAdminTestSessionCapacity);
  app.configure(restTestAdminTestSessionInvitationCode);
  app.configure(restTestAdminTestSessionRestrictions);
  app.configure(restTestAdminTestSessionsAll);
  app.configure(restTestAdminTestSessionSetupAccommPing);
  app.configure(restTestAdminTestSessionSetupActive);
  app.configure(restTestAdminTestSessionSetupChecklist);
  app.configure(restTestAdminTestSessionSetupLocations);
  app.configure(restTestAdminTestSessionSetupSessions);
  app.configure(restTestAdminTestSessionSetupTechReady);
  app.configure(restTestAdminTestSessionSetupTestWindows);
  app.configure(restTestAdminTestSessionSetupTimeSlotRestriction);
  app.configure(restTestAdminTestSessionSetupTimeSlots);
  app.configure(restTestAdminTestSessionsMyUpcoming);
  app.configure(restTestAdminTestSessionsSummary);
  app.configure(restTestAdminTestSessionStatusUpdate);
  app.configure(restTestAdminTestSessionWaitlisters);
  app.configure(restTestAdminTestSessionWaitlisterTransferReq);
  app.configure(restTestAdminTestSessionWaitListingsIndic);
  app.configure(restTestCertRetrievals);
  app.configure(restTestCtrlAccountsTestAdmins);
  app.configure(restTestCtrlAccountsTestCerts);
  app.configure(restTestCtrlAccountsTestCtrls);
  app.configure(restTestCtrlSummary);
  app.configure(restTestCtrlTestWindowReleaseNotes);
  app.configure(restTestCtrlTestWindowRetrievals);
  app.configure(restTestCtrlTestWindowTestDesign);
  app.configure(restTestCtrlTestWindowTestSessions);
  app.configure(restTestletsLoft);
  app.configure(restTestTakerAccommodationsPendingReq);
  app.configure(restTestTakerCreditDetails);
  app.configure(restTestTakerInvigilationQuestionContent);
  app.configure(restTestTakerInvigilationQuestionResponse);
  app.configure(restTestTakerInvigilationScreenSession);
  app.configure(restTestTakerInvigilationSectionSubmit);
  app.configure(restTestTakerInvigilationTestAttempt);
  app.configure(restTestTakerInvigilationTestForm);
  app.configure(restTestTakerTestSessionsAll);
  app.configure(restTestTakerTestSessionsBooking);
  app.configure(restTestTakerTestSessionsOctValidation);
  app.configure(restTestTakerTestSessionsPastAttempts);
  app.configure(restTestTakerTestSessionsPending);
  app.configure(restTestTakerTestSessionsWaitlist);
  app.configure(restTranslation);
  app.configure(roles);
  app.configure(schoolProfile);
  app.configure(scoringWindowItems);
  app.configure(scoringWindows);
  app.configure(scoringWindowSetup);
  app.configure(studentAccommodationsService);
  app.configure(studentCreationForm);
  app.configure(subsessionPresets);
  app.configure(TestDesignQuestionVersions);
  app.configure(thirdPartyOctAuthentication);
  app.configure(thirdPartyOctSuccessfulAttempts);
  app.configure(transactionsStripeStripeCheckout);
  app.configure(users);
  app.configure(windows);
  app.configure(publicSupportStudentTaqr);
  app.configure(publicTestAuthItemBlockTemplatesRetrieval);
  app.configure(publicTestAuthQuestionChangeLog);
  app.configure(publicTestAuthHighlightNotes);
  app.configure(publicTestAuthQuestionWorkflowStages);
  app.configure(publicTestAuthQuestionGraphicRequests);
  app.configure(publicStudentAsmtCoverPage);
  app.configure(publicLandingPracticeTests);
  app.configure(publicTestAuthInitWebsockets);
  app.configure(publicScorLeadWindowStats);
  app.configure(publicScorLeadMaxReadProcess);
  app.configure(publicScorLeadMisalignedReads);
  app.configure(dictionaryService);
  app.configure(publicTestCtrlLoadTestScorer);
  app.configure(publicScorLeadBatchProcessesIncompleBatchSets);
  app.configure(publicAuthMyRoleTypes);
  app.configure(publicEducatorClasses);
  app.configure(publicEducatorClassRollover);
  app.configure(publicEducatorLocalScoringStats);
  app.configure(publicEducatorLocalScoringFramework);
  app.configure(publicEducatorLocalScoringTemp);
  app.configure(publicEducatorSessionTime);
  app.configure(publicEducatorStudentSessionTime);
  app.configure(publicStudentSessionTime);
  app.configure(publicDistAdminSignedUrl);
  app.configure(publicTestCtrlTestWindowSchoolsAllowed);
  app.configure(publicTestCtrlTestWindowSchoolDateExceptions);
  app.configure(publicTestAuthItemResponseFormatting);
  app.configure(publicEducatorSoftLockSession);
  app.configure(publicEducatorClassroom);
  app.configure(publicTestAuthAdministrationWindowAllocs);
  app.configure(publicTestCtrlTestWindowScheduledSessions);
  app.configure(publicTestCtrlIssueRevwSchoolTwSummaries);
  app.configure(publicError);
  app.configure(publicTestCtrlKnowledgeBase);
  app.configure(publicTestCtrlSystemMessage);
  app.configure(scoreEntry);
  app.configure(summaryReport);
  app.configure(individualReport);
  app.configure(patIndividualReport);
  app.configure(publicSchoolAdminReportsPrelim);
  app.configure(testWindow);
  app.configure(daReports);
  app.configure(publicDataExporterDataExportQueries);
  app.configure(cutScoresDefinitions);
  app.configure(publicTestAuthTwtarTestWindows);
  app.configure(publicTestAuthTwtarTwTdTypes);
  app.configure(publicTestAuthTwtarTestDesigns);
  app.configure(publicTestAuthTwtarSignOffs);
  app.configure(publicTestAuthGroupAccounts);
  app.configure(publicTestCtrlSchoolsStudentResponses);
  app.configure(publicTestCtrlTestWindowSchoolLangExceptions);
  app.configure(publicTestCtrlTestDesignPrint);
  app.configure(authLockDownBrowser);
  app.configure(publicEducatorClassArchive);
  app.configure(publicStudentLockDown);
  app.configure(publicTestAuthAsmtCoverPage);
  app.configure(publicScorLeadWithheldResponsesScoring);
  app.configure(publicCronTrackMemoryUsage);
  app.configure(publicAnonLdbConfig);
  app.configure(publicTestAuthItemStats);
  app.configure(publicTestAuthItemWindowUses);
  app.configure(publicScorLeadMarkingStandardsRangeTags);
  app.configure(publicTestCtrlSchoolsGlobalItemExceptions);
  app.configure(publicDataExporterDataAssetDefs);
  app.configure(publicDataExporterDataJobDefs);
  app.configure(publicDataExporterDataJobContents);
  app.configure(publicDataExporterAssetData);
  app.configure(publicSchoolAdminAlternativeVersionRequest);
  app.configure(publicAltVersionCtrlAltVersionRequests);
  app.configure(publicAltVersionCtrlAltVersionRequestNotes);
  app.configure(publicAltVersionCtrlAccessFileDownloads);
  app.configure(publicAltVersionCtrlAccessFileNotes);
  app.configure(publicAltVersionCtrlAccessFiles);
  app.configure(publicSupportBulkScanning);
  app.configure(publicSchoolBoardSdAccess);
  app.configure(publicScorLeadStudentAttemptResponseScans);
  app.configure(publicUserAuthenticatedReportIssue);
  app.configure(categoryDescriptors);
  app.configure(publicDistAdminDataExporterPkExtraction);
  app.configure(publicScorScorBatchesAnnotations);
  app.configure(publicDataExporterDataExportDagDataPackage);
  app.configure(publicSupportStudentAttempts);
  app.configure(publicSupportClassLookup);
  app.configure(publicTestAuthAssessmentProfilesStyleProfiles);
  app.configure(publicTestAuthAssessmentProfilesTwProfiles);
  app.configure(publicTestAuthAssessmentProfilesTwtarCourses);
  app.configure(publicTestAuthAssessmentProfilesTwtarFormCardinality);
  app.configure(publicTestAuthAssessmentProfilesTwtarTypeSlugs);
  app.configure(publicTestAuthReportingProfilesCategorySchemas);
  app.configure(publicTestAuthReportingProfilesCutScoreProfiles);
  app.configure(publicTestAuthReportingProfilesCutScoreSchemas);
  app.configure(publicTestAuthReportingProfilesDomainSchemas);
  app.configure(publicTestAuthReportingProfilesReportingProfiles);
  app.configure(publicTestAuthReportingProfilesLayoutProfiles);
  app.configure(publicTestAuthReportingProfilesTextNodeRefs);
  app.configure(publicTestAuthReportingProfilesLayoutConfigs);
  app.configure(publicTestAuthReportingProfilesDiscontinuationProfiles);
  app.configure(publicTestAuthReportingProfilesScalingFactorProfiles);
  app.configure(publicTestAuthReportingProfilesAssessmentStructures);
  app.configure(publicTestAuthReportingProfilesItemSetStructures);
  app.configure(publicTestAuthItemSetSlug);
  app.configure(publicTestCtrlTestWindowSchoolClassFormLock);
  app.configure(publicScorScorBatchesConfirm);
  app.configure(publicScorLeadScoringPairs);
  app.configure(publicTestAuthTwtarStats);
  app.configure(publicDistAdminSaKitTestWindows);
  app.configure(publicDistAdminSaKitSchoolTwSummaries);
  app.configure(publicTestAuthGenResponseSheets);
  app.configure(publicEducatorRespSheetUploadExtractFailedPages);
  app.configure(publicScorLeadMarkingGroups);
  app.configure(publicScorLeadMarkingGroupsUsers);
  app.configure(publicScorLeadValidityExpertScore);
  app.configure(publicSchoolAdminSessionScanProgress);
}

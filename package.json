{"name": "mtic3", "version": "1.0.0", "engines": {"node": "^16.19.0", "npm": ">=8.19.3"}, "resolutions": {"webpack": "^5.0.0"}, "scripts": {"ng": "ng", "start-mac": "node --max_old_space_size=14000 ./node_modules/@angular/cli/bin/ng serve --port 4401", "start-alt": "set NODE_OPTIONS=--openssl-legacy-provider && node --max_old_space_size=14000 ./node_modules/@angular/cli/bin/ng serve --port 4402", "start-win": "set NODE_OPTIONS=--openssl-legacy-provider && node --max_old_space_size=14000 ./node_modules/@angular/cli/bin/ng serve", "build": "ng build --configuration production", "build-opt": "node --max_old_space_size=14000 ./node_modules/@angular/cli/bin/ng build --configuration production --optimization && /bin/bash package-check.sh", "build-opt-with-src-maps": "NODE_OPTS='--max_old_space_size=14000' ng build --configuration production --optimization --sourceMap=true && /bin/bash package-check.sh", "test": "ng test --watch false", "lint": "ng lint", "e2e": "cypress open", "build:all": "ng build --configuration production && ng run mtic3:server && npm run webpack:prerender && npm run prerender", "serve:fast": "node --max_old_space_size=14000 ./node_modules/@angular/cli/bin/ng serve", "serve:prerender": "http-server dist/mtic3 -c-1", "webpack:prerender": "webpack --config webpack.prerender.config.js", "prerender": "node dist/mtic3/prerender.js", "cypress": "concurrently \"ng serve\" \"cypress open\"", "build:stats": "ng build --stats-json", "analyze-deprecated": "webpack-bundle-analyzer dist/mtic3/stats-es2015.json", "analyze": "source-map-explorer dist/mtic3/*.js", "postinstall": "patch-package"}, "private": true, "dependencies": {"@ag-grid-community/locale": "^32.2.2", "@angular/animations": "^13.2.3", "@angular/cdk": "^13.2.3", "@angular/common": "^13.2.3", "@angular/compiler": "^13.2.3", "@angular/core": "^13.2.3", "@angular/fire": "^7.6.1", "@angular/forms": "^13.2.3", "@angular/material": "^13.2.3", "@angular/material-moment-adapter": "13.2.3", "@angular/platform-browser": "^13.2.3", "@angular/platform-browser-dynamic": "^13.2.3", "@angular/platform-server": "^13.2.3", "@angular/router": "^13.2.3", "@angular/service-worker": "^13.2.3", "@ckeditor/ckeditor5-angular": "^1.2.3", "@ckeditor/ckeditor5-basic-styles": "^25.0.0", "@ckeditor/ckeditor5-build-classic": "^25.0.0", "@ctrl/ngx-codemirror": "^4.1.1", "@ctrl/ngx-emoji-mart": "^4.0.2", "@feathersjs/authentication-client": "^4.3.10", "@feathersjs/feathers": "^4.3.10", "@feathersjs/rest-client": "^4.3.10", "@fortawesome/angular-fontawesome": "^0.10.2", "@fortawesome/fontawesome-svg-core": "^1.2.32", "@fortawesome/free-solid-svg-icons": "^5.15.1", "@fullcalendar/angular": "^5.11.3", "@fullcalendar/daygrid": "^5.11.3", "@fullcalendar/interaction": "^5.11.3", "@swimlane/ngx-charts": "^16.0.0", "@tweenjs/tween.js": "^17.4.0", "@types/diff": "^5.0.0", "@types/jquery": "^3.5.1", "accounting-js": "1.1.1", "ag-grid-angular": "^25.3.0", "ag-grid-community": "^25.3.0", "amazon-chime-sdk-js": "^2.27.0", "ang-jsoneditor": "^1.10.5", "angular-split": "^3.0.3", "apexcharts": "^3.19.2", "async-mutex": "^0.3.2", "ckeditor-custom": "https://vea-cdn.vretta.com/lib/ckeditor5-custom-build-with-spell-check/ckeditor5-custom-build-with-spell-check-v0.0.5.tgz", "ckeditor5-build-classic-alignment-highlight": "^12.1.1", "ckeditor5-build-classic-all-plugin": "^16.0.5", "ckeditor5-build-classic-balloon": "^1.0.0", "codemirror": "^5.65.14", "core-js": "^3.8.3", "deep-diff": "^1.0.2", "diff": "^5.0.0", "docx": "^9.0.2", "file-saver": "^2.0.5", "firebase": "^9.23.0", "fuse": "^0.4.0", "fuse.js": "^3.4.0", "ip-cidr": "^2.1.4", "jquery": "^3.5.1", "js-yaml": "^4.1.0", "jsoneditor": "^9.7.2", "jszip": "^3.7.1", "jwt-decode": "^3.1.2", "katex": "^0.16.8", "lodash": "^4.17.21", "mathlive": "^0.98.6", "moment": "^2.30.1", "moment-timezone": "^0.5.27", "ng": "0.0.0", "ng-apexcharts": "1.7.0", "ng-jcrop": "^2.2.1", "ng2-file-upload": "^1.4.0", "ng2-tooltip-directive-ng13fix": "2.10.0", "ngx-autosize-input": "13.1.1", "ngx-clipboard": "^15.1.0", "ngx-json-viewer": "3.0.2", "ngx-markdown": "13.1.0", "ngx-mask": "^12.0.0", "normalize-diacritics": "^4.0.3", "npm": "^6.14.15", "papaparse": "^5.3.0", "peerjs": "^1.3.2", "pixi.js-legacy": "5.3.11", "postcss-loader": "3.0.0", "qrcode": "^1.5.0", "recorder-js": "^1.0.7", "rxjs": "^6.6.0", "socket.io-client": "^2.3.0", "sockette": "^2.0.6", "start": "^5.1.0", "tslib": "^2.0.0", "upgrade": "^1.1.0", "vidar": "^0.8.1", "words-to-numbers": "^1.5.1", "xlsx": "https://cdn.sheetjs.com/xlsx-0.18.7/xlsx-0.18.7.tgz", "zone.js": "~0.11.8"}, "devDependencies": {"@angular-devkit/build-angular": "^13.2.3", "@angular/cli": "^13.2.3", "@angular/compiler-cli": "^13.2.3", "@angular/language-service": "^13.2.3", "@cypress/webpack-preprocessor": "^2.0.1", "@types/angular": "^1.7.0", "@types/chance": "^1.0.1", "@types/jasmine": "~2.8.6", "@types/jasminewd2": "~2.0.3", "@types/node": "^12.20.55", "@types/tween.js": "^17.2.0", "bulma": "^0.8.1", "chance": "^1.0.16", "codelyzer": "^6.0.2", "concurrently": "^5.2.0", "cypress": "^3.4.0", "fs-extra": "^6.0.1", "jasmine-core": "~3.5.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.3", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "1.5.4", "patch-package": "^8.0.0", "protractor": "~7.0.0", "sass": "^1.54.0", "source-map-explorer": "^2.5.2", "ts-loader": "^4.3.0", "ts-node": "~5.0.1", "tslint": "~6.1.0", "typescript": "4.6.4", "webpack-bundle-analyzer": "^3.9.0", "webpack-cli": "^2.1.3", "ws": "^5.1.1", "xmlhttprequest": "^1.8.0"}}
import assert from 'assert';
import fs from 'fs';

import { cast_col } from '../../../../../../src/services/public/data-exporter/data-export-queries/transforms/cast-col'

describe('data-exporter | data-export-queries | transforms :: cast_col', () => {
  it('works for empty input DataFrame', () => {
    const df_input = [];
    const expected = [];
  
    const col_target = "score";
    const target_type = "number";
  
    const result = cast_col(df_input, col_target, target_type);
  
    assert.deepStrictEqual(result, expected, "Works when input DataFrame is empty");
  });

  it('works for valid number conversion', () => {
    const df_input = [
      { item_id: "1", score: "1" },
      { item_id: "2", score: "2" },
    ];
    const expected = [
      { item_id: "1", score: 1 },
      { item_id: "2", score: 2 },
    ];
  
    const col_target = "score";
    const target_type = "number";
  
    const result = cast_col(df_input, col_target, target_type);
  
    assert.deepStrictEqual(result, expected, "Correctly converts string to number");
  });

  it('throws error for invalid number conversion', () => {
    const df_input = [
      { item_id: "1", score: "100" },
      { item_id: "2", score: "abc" },
    ];
  
    const col_target = "score";
    const target_type = "number";
  
    assert.throws(
      () => cast_col(df_input, col_target, target_type),
      /Invalid value for number conversion: abc/,
      "Throws error for invalid number conversion"
    );
  });

  it('works for null and undefined values', () => {
    const df_input = [
      { id: "1", value: null },
      { id: "2", value: undefined },
      { id: "3", value: "100" },
    ];
    const expected = [
      { id: "1", value: null },
      { id: "2", value: undefined },
      { id: "3", value: 100 },
    ];
  
    const col_target = "value";
    const target_type = "number";
  
    const result = cast_col(df_input, col_target, target_type);
  
    assert.deepStrictEqual(result, expected, "Handles null and undefined values gracefully");
  });
});

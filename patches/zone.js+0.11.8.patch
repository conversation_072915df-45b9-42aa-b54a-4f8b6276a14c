diff --git a/node_modules/zone.js/dist/zone.js b/node_modules/zone.js/dist/zone.js
index a284aa4..51eb5ae 100755
--- a/node_modules/zone.js/dist/zone.js
+++ b/node_modules/zone.js/dist/zone.js
@@ -1946,6 +1946,9 @@ var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
                     if (!delegate) {
                         return nativeListener.apply(this, arguments);
                     }
+                    if (typeof arguments[2] === 'object' && typeof arguments[2].signal !== 'undefined') {
+                        return nativeListener.apply(this, arguments);
+                    }
                     if (isNode && eventName === 'uncaughtException') {
                         // don't patch uncaughtException of nodejs to prevent endless loop
                         return nativeListener.apply(this, arguments);

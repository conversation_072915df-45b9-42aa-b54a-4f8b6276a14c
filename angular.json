{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"mtic3": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"aot": true, "outputPath": "dist/mtic3", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets", "src/manifest.json"], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.scss", "node_modules/katex/dist/katex.min.css"], "scripts": ["node_modules/marked/lib/marked.esm.js", "node_modules/katex/dist/katex.min.js", "node_modules/apexcharts/dist/apexcharts.min.js"]}, "configurations": {"production": {"budgets": [{"type": "anyComponentStyle", "maximumWarning": "40kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": true, "buildOptimizer": true, "serviceWorker": true}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "mtic3:build"}, "configurations": {"production": {"browserTarget": "mtic3:build:production"}, "development": {"browserTarget": "mtic3:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "mtic3:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "karmaConfig": "src/karma.conf.js", "styles": ["src/styles.scss", "node_modules/katex/dist/katex.min.css"], "scripts": ["./node_modules/@angular/material/prebuilt-themes/deeppurple-amber.css", "node_modules/marked/lib/marked.esm.js", "node_modules/katex/dist/katex.min.js"], "assets": ["src/favicon.ico", "src/assets", "src/manifest.json"]}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["src/tsconfig.app.json", "src/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}, "server": {"builder": "@angular-devkit/build-angular:server", "options": {"outputPath": "dist/mtic3-server", "main": "src/main.server.ts", "tsConfig": "src/tsconfig.server.json"}}}}, "mtic3-e2e": {"root": "e2e/", "projectType": "application", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "mtic3:serve"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": "e2e/tsconfig.e2e.json", "exclude": ["**/node_modules/**"]}}}}}, "defaultProject": "mtic3", "cli": {"analytics": false}}
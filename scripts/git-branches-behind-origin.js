const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');

function fetchUpdates() {
    execSync('git fetch', { stdio: 'inherit' });
}

function getBranches() {
    const result = execSync('git branch -vv', { encoding: 'utf8' });
    const branches = result.split('\n').filter(branch => branch.trim() !== '');
    return branches;
}

function identifyBehindBranches(branches) {
    const behindPattern = /\[behind (\d+)\]/;
    const behindBranches = [];

    branches.forEach(branch => {
        const match = behindPattern.exec(branch);
        if (match) {
            const branchName = branch.trim().split(/\s+/)[1];
            const behindCount = parseInt(match[1], 10);
            behindBranches.push({ branchName, behindCount });
        }
    });

    return behindBranches;
}

function saveToYamlFile(data, filePath) {
    try {
        const yamlStr = yaml.dump(data);
        fs.writeFileSync(filePath, yamlStr, 'utf8');
    } catch (error) {
        console.error('Error writing to YAML file:', error.message);
    }
}

function main() {
    try {
        fetchUpdates();
        const branches = getBranches();
        const behindBranches = identifyBehindBranches(branches);

        const filePath = path.join(__dirname, 'datacontainer', 'git-branches-behind-origin.yml');
        saveToYamlFile(behindBranches, filePath);

        if (behindBranches.length > 0) {
            console.log("Branches that are behind origin:");
            behindBranches.forEach(({ branchName, behindCount }) => {
                console.log(`${branchName} is behind by ${behindCount} commits`);
            });
        } else {
            console.log("No branches are behind origin.");
        }
    } catch (error) {
        console.error('Error:', error.message);
    }
}

main();

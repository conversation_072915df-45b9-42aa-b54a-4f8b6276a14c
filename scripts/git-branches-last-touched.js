const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const moment = require('moment');
const yaml = require('js-yaml');

function getBranches() {
    const result = execSync('git branch', { encoding: 'utf8' });
    const branches = result.split('\n')
        .filter(branch => branch.trim() !== '')
        .map(branch => branch.trim().replace(/^\* /, '')); // Remove leading asterisk from current branch
    return branches;
}

function getLastCommitDate(branch, authorEmail) {
    try {
        const result = execSync(`git log -1 --pretty=format:"%cd" --author="${authorEmail}" --date=iso ${branch}`, { encoding: 'utf8' });
        return result.trim() ? moment(result.trim()) : null;
    } catch (error) {
        return null;
    }
}

function saveToYamlFile(data, filePath) {
    try {
        const yamlStr = yaml.dump(data);
        fs.writeFileSync(filePath, yamlStr, 'utf8');
    } catch (error) {
        console.error('Error writing to YAML file:', error.message);
    }
}

function main() {
    try {
        const authorEmail = execSync('git config user.email', { encoding: 'utf8' }).trim();
        const branches = getBranches();
        const branchData = branches.map(branch => {
            const lastCommitDate = getLastCommitDate(branch, authorEmail);
            return {
                branch,
                lastCommitDate: lastCommitDate ? lastCommitDate.format('YYYY-MM-DD HH:mm:ss') : 'No commits by you'
            };
        });

        branchData.sort((a, b) => {
            if (a.lastCommitDate === 'No commits by you') return 1;
            if (b.lastCommitDate === 'No commits by you') return -1;
            return moment(b.lastCommitDate).diff(moment(a.lastCommitDate));
        });

        const filePath = path.join(__dirname, 'datacontainer', 'git-branches-last-touched.yml');
        saveToYamlFile(branchData, filePath);

        console.log("Branches sorted by last commit date by you and saved to git-branches-last-touched.yml:");
        branchData.forEach(({ branch, lastCommitDate }) => {
            console.log(`${branch}: ${lastCommitDate}`);
        });
    } catch (error) {
        console.error('Error:', error.message);
    }
}

main();

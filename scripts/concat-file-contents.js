#!/usr/bin/env node

/*
node scripts/concat-file-contents.js
*/

const fs = require('fs');
const path = require('path');

const fileListPath = 'files.txt'; // adjust as needed 
const outputFile = 'combined.md';

try {
  const lines = fs.readFileSync(fileListPath, 'utf8')
    .split('\n')
    .map(l => l.trim())
    .filter(l => l.length > 0);

  let output = '# Combined File Contents\n\n';

  for (const filePath of lines) {
    if (!fs.existsSync(filePath)) {
      console.warn(`Warning: File not found: ${filePath}`);
      continue;
    }

    const fileName = path.basename(filePath);
    const content = fs.readFileSync(filePath, 'utf8');

    output += `## ${fileName}\n\n`;
    output += '```\n';
    output += content;
    output += '\n```\n\n';
  }

  fs.writeFileSync(outputFile, output, 'utf8');
  console.log(`Successfully created ${outputFile}`);

} catch (err) {
  console.error('Error:', err);
  process.exit(1);
}

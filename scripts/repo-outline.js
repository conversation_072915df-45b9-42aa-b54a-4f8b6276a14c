#!/usr/bin/env node

/*
node scripts/repo-outline.js > files.txt
*/

const fs = require('fs');
const path = require('path');

const targetDir = process.argv[2] || process.cwd();

function shouldIgnore(filePath) {
  const basename = path.basename(filePath);
  return basename === 'node_modules' || basename.startsWith('.');
}

function getFilesAndDirsRecursively(dir) {
  let results = {
    files: [],
    dirs: []
  };
  const list = fs.readdirSync(dir);
  results.dirs.push(dir);
  for (const file of list) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    if (shouldIgnore(filePath)) continue;
    if (stat && stat.isDirectory()) {
      const subResults = getFilesAndDirsRecursively(filePath);
      results.files = results.files.concat(subResults.files);
      results.dirs = results.dirs.concat(subResults.dirs);
    } else {
      results.files.push(filePath);
    }
  }
  return results;
}

function countLines(filePath) {
  const content = fs.readFileSync(filePath, 'utf-8');
  return content.split('\n').length;
}

function summarizeFiles(files) {
  const summary = {
    totalFiles: 0,
    totalLines: 0,
    byExtension: {}
  };

  for (const file of files) {
    const ext = path.extname(file) || 'no_ext';
    const lines = countLines(file);
    summary.totalFiles += 1;
    summary.totalLines += lines;
    if (!summary.byExtension[ext]) {
      summary.byExtension[ext] = { count: 0, lines: 0 };
    }
    summary.byExtension[ext].count += 1;
    summary.byExtension[ext].lines += lines;
  }

  return summary;
}

function printSummary(summary) {
  console.log('SUMMARY OF FILES:');
  console.log(`Total files: ${summary.totalFiles}`);
  console.log(`Total lines: ${summary.totalLines}`);
  console.log('By extension:');
  for (const ext of Object.keys(summary.byExtension)) {
    const info = summary.byExtension[ext];
    console.log(`  ${ext}: ${info.count} files, ${info.lines} lines`);
  }
  console.log('');
}

function buildDirectoryTree(dir) {
  const name = path.basename(dir);
  const children = [];
  const items = fs.readdirSync(dir);
  for (const item of items) {
    const itemPath = path.join(dir, item);
    if (shouldIgnore(itemPath)) continue;
    const stat = fs.statSync(itemPath);
    if (stat.isDirectory()) {
      children.push(buildDirectoryTree(itemPath));
    }
  }
  return { name, path: dir, children };
}

function printDirectoryTree(node, indent = '') {
  console.log(`${indent}${node.name}/`);
  for (const child of node.children) {
    printDirectoryTree(child, indent + '  ');
  }
}

try {
  const { files, dirs } = getFilesAndDirsRecursively(targetDir);
  const summary = summarizeFiles(files);

  // Print summary
  printSummary(summary);

  // Print folder list (hierarchical)
  console.log('FOLDER LIST (HIERARCHICAL):');
  const tree = buildDirectoryTree(targetDir);
  printDirectoryTree(tree);
  console.log('');

  // Print flat file list (including paths)
  console.log('FILE LIST (FLAT, WITH PATHS):');
  files.sort();
  for (const file of files) {
    console.log(file);
  }

} catch (err) {
  console.error('Error summarizing repository:', err);
  process.exit(1);
}
